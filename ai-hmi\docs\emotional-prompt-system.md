# 🎭 情感化壁纸提示词生成系统

## 📋 项目概述

针对 ai-hmi 项目场景切换时壁纸生成提示词过于公式化的问题，我们设计并实现了一个基于LLM的动态情感化提示词生成系统。

## 🎯 解决的核心问题

### 原有问题
- **机械化的提示词**: 使用固定的英文描述（如 `"modern glass building, transparent texture"`）
- **缺乏情感温度**: 没有考虑用户真实感受和心理状态
- **场景单一**: 不同时间、天气、情绪下使用相同的提示词
- **个性化不足**: 无法根据用户当前状态生成合适的描述

### 解决方案
- **LLM驱动**: 使用大语言模型生成有温度的描述性提示词
- **情感共鸣**: 基于用户情绪、时间、天气等上下文生成共情提示词
- **动态适应**: 根据不同场景和用户状态实时调整提示词内容
- **人性化表达**: 生成符合真实车主感受的描述性语言

## 🏗️ 系统架构

### 核心组件

#### 1. EmotionalPromptGenerator（情感化提示词生成器）
- **位置**: `src/services/EmotionalPromptGenerator.js`
- **功能**: 调用LLM生成情感化的壁纸提示词
- **特点**: 
  - 支持缓存机制，提高性能
  - 降级处理，确保系统稳定性
  - 场景特定的情感增强词库

#### 2. SceneContextManager（场景上下文管理器）
- **位置**: `src/services/SceneContextManager.js`
- **功能**: 管理和推断用户当前的上下文状态
- **特点**:
  - 时间、天气、情绪等多维度上下文
  - 自动推断用户心理状态
  - 实时更新驾驶状态信息

#### 3. 集成组件
- **SceneManager**: 集成情感化提示词生成
- **ImmersiveWallpaperInterface**: 预设按钮的情感化增强
- **App.vue**: 统一处理提示词数据流

## 🎨 生成效果对比

### 原始提示词（示例）
```
"modern glass building, transparent texture, blurred background, warm morning light, suitable for car display"
```

### 情感化提示词（示例）
```
清晨的第一缕阳光透过车窗，照亮了温馨的车内空间。作为一位负责的父亲，你正带着孩子驶向学校，心中充满对家庭的关爱。车内弥漫着温暖柔和的晨光，玻璃质感的中控台反射着金色的光芒，营造出既现代又充满家庭温暖的氛围。整个环境简洁而不失温度，适合亲子互动的驾驶空间，体现着现代家庭出行的美好时光。
```

## 🚀 核心特性

### 1. 情感感知
- **时间情绪**: 根据一天中不同时间推断用户情绪
- **天气适应**: 结合天气状况调整氛围描述
- **角色识别**: 基于乘客情况识别用户角色
- **状态感知**: 考虑驾驶时长、疲劳度等因素

### 2. 场景定制
- **家庭出行**: 强调温馨、关爱、亲子时光
- **专注通勤**: 突出效率、清醒、目标导向
- **雨夜模式**: 营造安全、宁静、温暖的感觉
- **浪漫氛围**: 创造私密、温馨、二人世界的环境

### 3. 智能降级
- **LLM失败**: 使用场景特定的增强提示词
- **网络异常**: 基于模板的智能组合
- **性能优化**: 本地缓存常用场景提示词

## 📊 上下文维度

### 时间维度
- **清晨** (5-8点): 清新宁静，带着期待开始新的一天
- **上午** (8-12点): 充满活力，专注当前任务
- **傍晚** (17-19点): 期待放松，渴望回到温暖的家
- **夜晚** (19-22点): 宁静温馨，享受夜的安宁

### 用户状态
- **独自驾驶**: 安静私密，个人空间
- **与孩子同行**: 温馨活泼，家庭互动
- **与伴侣同行**: 亲密温馨，二人世界
- **疲劳驾驶**: 需要提神，安全第一

### 环境因素
- **天气状况**: 晴朗、多云、雨天、雪天
- **道路类型**: 城市道路、高速公路、乡村道路
- **行程目的**: 通勤、出游、购物、探亲

## 🔧 技术实现

### LLM集成
```javascript
// 构建情感化提示词生成请求
const emotionalPrompt = this.buildEmotionalPrompt(sceneInfo, context)

// 调用LLM生成提示词
const generatedPrompt = await this.llmService.generateResponse(emotionalPrompt)
```

### 上下文管理
```javascript
// 获取用于提示词生成的上下文
const promptContext = {
  timeOfDay: this.context.timeOfDay,
  weather: this.context.weather,
  mood: this.context.mood,
  userRole: this.getUserRole(passengers),
  passengers: passengers.join(', ') || '独自',
  // ... 更多上下文信息
}
```

### 事件流
```
场景切换 → 上下文更新 → LLM生成 → 壁纸更新 → 用户感知
```

## 🧪 测试验证

### 测试页面
- **位置**: `public/test-emotional-prompts.html`
- **功能**: 可视化测试各场景的提示词生成效果
- **包含**: 上下文显示、单个场景测试、批量测试、性能测试

### 测试场景
1. **家庭出行模式**: 测试温馨关爱的提示词生成
2. **专注通勤模式**: 测试效率导向的提示词生成
3. **雨夜模式**: 测试安全宁静的提示词生成
4. **浪漫模式**: 测试私密温馨的提示词生成

## 📈 性能优化

### 缓存机制
- **内存缓存**: 最多保存50个生成的提示词
- **智能淘汰**: LRU算法淘汰最旧的缓存
- **键值策略**: 基于场景和上下文生成唯一键

### 降级策略
- **LLM服务不可用**: 使用预定义的情感增强词
- **网络超时**: 返回基础场景描述
- **异常处理**: 多层级错误捕获和处理

## 🎯 使用效果

### 用户体验提升
- **情感共鸣**: 用户感觉系统更"懂自己"
- **场景贴合**: 壁纸与当前状态高度匹配
- **个性化**: 不同用户、不同时间有不同体验
- **自然流畅**: 避免了机械化的模板感

### 系统智能化
- **自适应**: 根据上下文自动调整
- **可扩展**: 易于添加新的场景和情感维度
- **可维护**: 清晰的模块化架构
- **可测试**: 完善的测试和验证机制

## 🔮 未来扩展

### 短期优化
1. **更多情感维度**: 添加更细粒度的情绪识别
2. **个性化学习**: 基于用户反馈优化提示词生成
3. **多模态输入**: 结合语音、图像等多模态信息

### 长期规划
1. **用户画像**: 建立用户偏好模型
2. **场景推荐**: 主动推荐适合的场景和壁纸
3. **情感交互**: 更自然的情感化交互体验

## 📝 总结

通过引入情感化提示词生成系统，ai-hmi项目的壁纸生成从机械化的模板描述转变为有温度、有共情的个性化表达。系统现在能够：

- ✅ 理解用户的真实感受和心理状态
- ✅ 生成符合场景氛围的描述性提示词
- ✅ 提供更加自然和人性化的用户体验
- ✅ 保持系统的稳定性和性能

这个改进不仅解决了技术问题，更重要的是提升了用户的情感体验，让车载HMI系统真正成为"懂用户"的智能助手。