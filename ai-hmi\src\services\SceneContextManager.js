// 场景感知上下文管理器
// 专门为车载HMI系统设计的用户状态感知服务
export default class SceneContextManager {
  constructor() {
    this.context = this.initializeContext()
    this.observers = []
    this.lastUpdate = Date.now()
  }

  /**
   * 初始化上下文信息
   */
  initializeContext() {
    return {
      // 基础时间信息
      timestamp: Date.now(),
      timeOfDay: this.getCurrentTimeOfDay(),
      isWeekend: this.isWeekend(),
      season: this.getCurrentSeason(),
      
      // 环境信息
      weather: 'clear', // 默认晴朗，可以通过API获取
      temperature: 22, // 默认温度
      
      // 驾驶状态
      drivingDuration: 0,
      isDriving: false,
      gear: 'P',
      speed: 0,
      
      // 用户信息
      userRole: 'driver',
      mood: 'neutral',
      stressLevel: 0,
      fatigueLevel: 0,
      
      // 乘客信息
      passengers: [],
      passengerCount: 1,
      
      // 行程信息
      destination: '',
      destinationType: '',
      tripPurpose: 'unknown',
      
      // 车辆状态
      fuelLevel: 80,
      batteryLevel: 85,
      vehicleStatus: 'normal',
      
      // 位置信息
      locationType: 'city',
      roadType: 'city',
      
      // 历史信息
      recentScenes: [],
      sceneSwitchCount: 0,
      
      // 个性化信息
      userPreferences: {
        musicStyle: 'balanced',
        lightingPreference: 'auto',
        voiceAssistantVolume: 70
      }
    }
  }

  /**
   * 获取当前时间段描述
   */
  getCurrentTimeOfDay() {
    const hour = new Date().getHours()
    
    if (hour >= 5 && hour < 8) return 'earlyMorning'
    if (hour >= 8 && hour < 12) return 'morning'
    if (hour >= 12 && hour < 14) return 'noon'
    if (hour >= 14 && hour < 17) return 'afternoon'
    if (hour >= 17 && hour < 19) return 'evening'
    if (hour >= 19 && hour < 22) return 'night'
    return 'lateNight'
  }

  /**
   * 判断是否为周末
   */
  isWeekend() {
    const day = new Date().getDay()
    return day === 0 || day === 6
  }

  /**
   * 获取当前季节
   */
  getCurrentSeason() {
    const month = new Date().getMonth()
    
    if (month >= 2 && month <= 4) return 'spring'
    if (month >= 5 && month <= 7) return 'summer'
    if (month >= 8 && month <= 10) return 'autumn'
    return 'winter'
  }

  /**
   * 更新上下文信息
   */
  updateContext(updates) {
    const oldContext = { ...this.context }
    this.context = { ...this.context, ...updates, timestamp: Date.now() }
    
    // 触发观察者通知
    this.notifyObservers(oldContext, this.context)
    
    // 更新时间相关信息
    this.updateTimeContext()
    
    this.lastUpdate = Date.now()
  }

  /**
   * 更新时间相关上下文
   */
  updateTimeContext() {
    this.context.timeOfDay = this.getCurrentTimeOfDay()
    this.context.isWeekend = this.isWeekend()
    this.context.season = this.getCurrentSeason()
  }

  /**
   * 推断用户情绪状态
   */
  inferUserMood() {
    const { timeOfDay, isWeekend, drivingDuration, fatigueLevel, stressLevel, passengerCount } = this.context
    
    // 基于时间推断情绪
    let mood = 'neutral'
    
    // 早高峰情绪推断
    if (timeOfDay === 'earlyMorning' || timeOfDay === 'morning') {
      if (!isWeekend) {
        mood = passengerCount > 1 ? 'family_care' : 'focused_commute'
      } else {
        mood = 'relaxed_weekend'
      }
    }
    
    // 下班时间情绪推断
    else if (timeOfDay === 'evening') {
      mood = 'relieved_tired'
    }
    
    // 深夜情绪推断
    else if (timeOfDay === 'lateNight') {
      mood = 'tired_quiet'
    }
    
    // 周末情绪推断
    else if (isWeekend) {
      mood = 'relaxed_happy'
    }
    
    // 基于驾驶时长调整情绪
    if (drivingDuration > 7200000) { // 超过2小时
      mood = mood.includes('tired') ? mood : 'driving_fatigue'
    }
    
    // 基于疲劳程度调整
    if (fatigueLevel > 7) {
      mood = 'very_tired'
    }
    
    // 基于压力程度调整
    if (stressLevel > 7) {
      mood = 'stressed'
    }
    
    this.context.mood = mood
    return mood
  }

  /**
   * 推断行程目的
   */
  inferTripPurpose() {
    const { timeOfDay, isWeekend, destinationType, passengers } = this.context
    
    let purpose = 'unknown'
    
    // 工作日早高峰
    if (!isWeekend && (timeOfDay === 'earlyMorning' || timeOfDay === 'morning')) {
      if (destinationType === 'work' || destinationType === 'office') {
        purpose = 'commute_to_work'
      } else if (passengers.includes('child')) {
        purpose = 'take_child_to_school'
      }
    }
    
    // 工作日晚高峰
    else if (!isWeekend && timeOfDay === 'evening') {
      if (destinationType === 'home') {
        purpose = 'return_home'
      }
    }
    
    // 周末
    else if (isWeekend) {
      if (destinationType === 'park' || destinationType === 'recreation') {
        purpose = 'weekend_trip'
      } else if (destinationType === 'restaurant') {
        purpose = 'dining_out'
      } else if (destinationType === 'shopping') {
        purpose = 'shopping'
      }
    }
    
    this.context.tripPurpose = purpose
    return purpose
  }

  /**
   * 获取情感化上下文描述
   */
  getEmotionalContext() {
    const { timeOfDay, weather, mood, passengers, destination, season } = this.context
    
    const timeEmotion = this.getTimeEmotion(timeOfDay)
    const weatherEmotion = this.getWeatherEmotion(weather)
    const moodEmotion = this.getMoodEmotion(mood)
    const passengerEmotion = this.getPassengerEmotion(passengers)
    
    return {
      ...timeEmotion,
      ...weatherEmotion,
      ...moodEmotion,
      ...passengerEmotion,
      season,
      destination,
      userRole: this.getUserRole(passengers)
    }
  }

  /**
   * 获取时间情感描述
   */
  getTimeEmotion(timeOfDay) {
    const emotions = {
      earlyMorning: {
        timeOfDay: '清晨',
        mood: '清新宁静',
        lighting: '柔和的晨光',
        atmosphere: '宁静祥和',
        userFeeling: '带着期待开始新的一天'
      },
      morning: {
        timeOfDay: '上午',
        mood: '充满活力',
        lighting: '明亮的日光',
        atmosphere: '活跃向上',
        userFeeling: '精力充沛，专注当前任务'
      },
      noon: {
        timeOfDay: '中午',
        mood: '略带疲惫',
        lighting: '强烈的阳光',
        atmosphere: '热情充沛',
        userFeeling: '需要短暂的休息和调整'
      },
      afternoon: {
        timeOfDay: '下午',
        mood: '专注沉稳',
        lighting: '倾斜的阳光',
        atmosphere: '沉稳专注',
        userFeeling: '保持专注，可能有些疲劳'
      },
      evening: {
        timeOfDay: '傍晚',
        mood: '期待放松',
        lighting: '温暖的夕阳',
        atmosphere: '温暖归家',
        userFeeling: '期待着回到温暖的家'
      },
      night: {
        timeOfDay: '夜晚',
        mood: '宁静温馨',
        lighting: '温暖的灯光',
        atmosphere: '温馨舒适',
        userFeeling: '享受夜晚的宁静时光'
      },
      lateNight: {
        timeOfDay: '深夜',
        mood: '宁静安详',
        lighting: '柔和的月光',
        atmosphere: '宁静神秘',
        userFeeling: '准备休息，享受宁静'
      }
    }
    
    return emotions[timeOfDay] || emotions.morning
  }

  /**
   * 获取天气情感描述
   */
  getWeatherEmotion(weather) {
    const emotions = {
      clear: {
        weather: '晴朗',
        mood: '愉悦开朗',
        lighting: '明媚的阳光',
        atmosphere: '明亮开朗',
        visualElements: '蓝天白云，阳光透过车窗'
      },
      cloudy: {
        weather: '多云',
        mood: '平静舒缓',
        lighting: '柔和的光线',
        atmosphere: '柔和舒适',
        visualElements: '云层柔和，光线散射'
      },
      rainy: {
        weather: '下雨',
        mood: '宁静沉思',
        lighting: '雨滴的折射光',
        atmosphere: '清新宁静',
        visualElements: '雨滴在车窗上滑落，湿润的光线'
      },
      night: {
        weather: '夜晚',
        mood: '神秘安静',
        lighting: '温暖的灯光',
        atmosphere: '宁静神秘',
        visualElements: '温暖的灯光，夜色中的车流'
      }
    }
    
    return emotions[weather] || emotions.clear
  }

  /**
   * 获取情绪情感描述
   */
  getMoodEmotion(mood) {
    const emotions = {
      neutral: {
        mood: '平静',
        emotionalState: '心态平和',
        psychologicalNeeds: '稳定舒适的环境',
        colorPreference: '中性色调'
      },
      focused_commute: {
        mood: '专注通勤',
        emotionalState: '目标导向，略带紧迫',
        psychologicalNeeds: '高效清晰的信息展示',
        colorPreference: '清爽的蓝色调'
      },
      family_care: {
        mood: '家庭关爱',
        emotionalState: '温馨负责',
        psychologicalNeeds: '安全舒适的氛围',
        colorPreference: '温暖的橙黄色调'
      },
      relieved_tired: {
        mood: '疲惫释然',
        emotionalState: '放松但有些疲劳',
        psychologicalNeeds: '舒缓放松的环境',
        colorPreference: '柔和的暖色调'
      },
      relaxed_weekend: {
        mood: '周末放松',
        emotionalState: '轻松愉悦',
        psychologicalNeeds: '轻松愉快的氛围',
        colorPreference: '明亮的色彩'
      },
      driving_fatigue: {
        mood: '驾驶疲劳',
        emotionalState: '需要休息',
        psychologicalNeeds: '提神醒脑的环境',
        colorPreference: '清新的绿色调'
      },
      stressed: {
        mood: '压力较大',
        emotionalState: '紧张焦虑',
        psychologicalNeeds: '舒缓压力的环境',
        colorPreference: '平静的蓝色调'
      },
      very_tired: {
        mood: '非常疲劳',
        emotionalState: '极度需要休息',
        psychologicalNeeds: '极度舒适的环境',
        colorPreference: '柔和的深色调'
      }
    }
    
    return emotions[mood] || emotions.neutral
  }

  /**
   * 获取乘客情感描述
   */
  getPassengerEmotion(passengers) {
    const passengerCount = passengers.length
    
    if (passengerCount === 0) {
      return {
        passengerSituation: '独自驾驶',
        socialAtmosphere: '安静私密',
        interactionNeeds: '个人空间',
        emotionalTone: '独立自主'
      }
    } else if (passengers.includes('child')) {
      return {
        passengerSituation: '与孩子同行',
        socialAtmosphere: '温馨活泼',
        interactionNeeds: '家庭互动',
        emotionalTone: '关爱负责'
      }
    } else if (passengers.includes('spouse')) {
      return {
        passengerSituation: '与伴侣同行',
        socialAtmosphere: '亲密温馨',
        interactionNeeds: '二人世界',
        emotionalTone: '浪漫亲密'
      }
    } else if (passengers.includes('friend')) {
      return {
        passengerSituation: '与朋友同行',
        socialAtmosphere: '轻松愉快',
        interactionNeeds: '朋友交流',
        emotionalTone: '轻松友好'
      }
    } else if (passengerCount > 2) {
      return {
        passengerSituation: '多人同行',
        socialAtmosphere: '热闹活跃',
        interactionNeeds: '群体互动',
        emotionalTone: '热闹欢快'
      }
    } else {
      return {
        passengerSituation: '有乘客同行',
        socialAtmosphere: '舒适友好',
        interactionNeeds: '正常交流',
        emotionalTone: '平和友好'
      }
    }
  }

  /**
   * 获取用户角色
   */
  getUserRole(passengers) {
    if (passengers.length === 0) return '独自驾驶'
    if (passengers.includes('child')) return '家长'
    if (passengers.includes('spouse')) return '伴侣'
    if (passengers.includes('friend')) return '朋友'
    return '驾驶者'
  }

  /**
   * 添加观察者
   */
  addObserver(callback) {
    this.observers.push(callback)
  }

  /**
   * 移除观察者
   */
  removeObserver(callback) {
    const index = this.observers.indexOf(callback)
    if (index > -1) {
      this.observers.splice(index, 1)
    }
  }

  /**
   * 通知观察者
   */
  notifyObservers(oldContext, newContext) {
    this.observers.forEach(callback => {
      try {
        callback(oldContext, newContext)
      } catch (error) {
        console.error('Context observer notification failed:', error)
      }
    })
  }

  /**
   * 获取用于提示词生成的上下文
   */
  getPromptGenerationContext() {
    this.inferUserMood()
    this.inferTripPurpose()
    
    return {
      timeOfDay: this.context.timeOfDay,
      weather: this.context.weather,
      mood: this.context.mood,
      userRole: this.getUserRole(this.context.passengers),
      passengers: this.context.passengers.join(', ') || '独自',
      destination: this.context.destination,
      isWeekend: this.context.isWeekend,
      drivingDuration: this.formatDuration(this.context.drivingDuration),
      season: this.context.season,
      tripPurpose: this.context.tripPurpose
    }
  }

  /**
   * 格式化时长
   */
  formatDuration(milliseconds) {
    const minutes = Math.floor(milliseconds / 60000)
    if (minutes < 60) {
      return `${minutes}分钟`
    } else {
      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60
      return `${hours}小时${remainingMinutes > 0 ? remainingMinutes + '分钟' : ''}`
    }
  }

  /**
   * 模拟驾驶状态更新
   */
  simulateDrivingUpdate() {
    if (this.context.isDriving) {
      this.updateContext({
        drivingDuration: this.context.drivingDuration + 60000, // 增加1分钟
        fatigueLevel: Math.min(10, this.context.fatigueLevel + 0.1)
      })
    }
  }

  /**
   * 获取完整上下文信息
   */
  getContext() {
    return { ...this.context }
  }

  /**
   * 重置上下文
   */
  reset() {
    this.context = this.initializeContext()
    this.lastUpdate = Date.now()
  }

  /**
   * 获取上下文统计信息
   */
  getStatistics() {
    return {
      lastUpdate: this.lastUpdate,
      sceneSwitchCount: this.context.sceneSwitchCount,
      drivingDuration: this.formatDuration(this.context.drivingDuration),
      currentMood: this.context.mood,
      tripPurpose: this.context.tripPurpose,
      observerCount: this.observers.length
    }
  }
}