<template>
  <div class="test-scene-generation">
    <div class="test-header">
      <h2>🧪 AI-HMI 场景生成测试</h2>
      <p>测试语音输入 → LLM优化 → Kolors生图 → 玻璃态主题应用的完整流程</p>
    </div>

    <div class="test-controls">
      <div class="input-section">
        <h3>📝 输入测试</h3>
        <div class="input-group">
          <label>文本输入:</label>
          <input 
            v-model="testInput" 
            type="text" 
            placeholder="例如：温暖的日落场景，现代玻璃建筑"
            @keyup.enter="testTextToImage"
          />
          <button @click="testTextToImage" :disabled="isGenerating">
            {{ isGenerating ? '生成中...' : '生成壁纸' }}
          </button>
        </div>
        
        <div class="voice-group">
          <label>语音输入:</label>
          <button @click="testVoiceInput" :disabled="isListening">
            {{ isListening ? '🎤 听取中...' : '🎤 开始语音输入' }}
          </button>
          <span v-if="voiceResult" class="voice-result">识别结果: {{ voiceResult }}</span>
        </div>
      </div>

      <div class="scene-section">
        <h3>🎭 场景测试</h3>
        <div class="scene-buttons">
          <button 
            v-for="scene in testScenes" 
            :key="scene.id"
            @click="testScene(scene)"
            class="scene-btn"
          >
            <i :class="scene.icon"></i>
            {{ scene.name }}
          </button>
        </div>
      </div>
    </div>

    <div class="test-results">
      <div class="result-section">
        <h3>📊 测试结果</h3>
        <div v-if="currentResult" class="result-display">
          <div class="result-item">
            <strong>原始输入:</strong> {{ currentResult.input }}
          </div>
          <div class="result-item">
            <strong>LLM优化后:</strong> {{ currentResult.optimizedPrompt }}
          </div>
          <div class="result-item">
            <strong>生成状态:</strong> 
            <span :class="currentResult.status">{{ currentResult.statusText }}</span>
          </div>
          <div v-if="currentResult.imageUrl" class="result-item">
            <strong>生成图片:</strong>
            <img :src="currentResult.imageUrl" alt="生成的壁纸" class="generated-image" />
          </div>
          <div v-if="currentResult.colors" class="result-item">
            <strong>提取颜色:</strong>
            <div class="color-palette">
              <div 
                v-for="(color, key) in currentResult.colors" 
                :key="key"
                class="color-item"
                :style="{ backgroundColor: color }"
                :title="`${key}: ${color}`"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <div class="log-section">
        <h3>📝 测试日志</h3>
        <div class="log-display">
          <div 
            v-for="(log, index) in testLogs" 
            :key="index"
            class="log-item"
            :class="log.type"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import ImageGenerationService from '@/services/ImageGenerationService'
import LlmService from '@/services/LlmService'
import AsrService from '@/services/AsrService'
// import ColorExtractor from '@/utils/ColorExtractor' // 已被AIColorAnalyzer替代
import AIColorAnalyzer from '@/utils/AIColorAnalyzer'

export default {
  name: 'TestSceneGeneration',
  emits: ['colors-extracted'],
  setup(props, { emit }) {
    const testInput = ref('温暖的日落场景，现代玻璃建筑')
    const isGenerating = ref(false)
    const isListening = ref(false)
    const voiceResult = ref('')
    const currentResult = ref(null)
    const testLogs = ref([])

    const testScenes = ref([
      { id: 'morning', name: '早晨通勤', icon: 'fas fa-sun', prompt: '清晨阳光，现代玻璃建筑，商务氛围' },
      { id: 'evening', name: '晚间放松', icon: 'fas fa-moon', prompt: '温暖夜景，柔和灯光，舒适氛围' },
      { id: 'rainy', name: '雨夜模式', icon: 'fas fa-cloud-rain', prompt: '雨夜街景，霓虹灯光，玻璃反射' },
      { id: 'family', name: '家庭出行', icon: 'fas fa-car', prompt: '温馨家庭，阳光明媚，愉快旅程' }
    ])

    const addLog = (message, type = 'info') => {
      testLogs.value.unshift({
        time: new Date().toLocaleTimeString(),
        message,
        type
      })
      if (testLogs.value.length > 50) {
        testLogs.value.pop()
      }
    }

    const testTextToImage = async () => {
      if (!testInput.value.trim()) {
        addLog('请输入测试文本', 'error')
        return
      }

      isGenerating.value = true
      addLog(`开始测试: ${testInput.value}`, 'info')

      try {
        currentResult.value = {
          input: testInput.value,
          optimizedPrompt: '',
          status: 'processing',
          statusText: '处理中...',
          imageUrl: null,
          colors: null
        }

        // 1. LLM优化提示词
        addLog('步骤1: LLM优化提示词...', 'info')
        const llmService = new LlmService()
        const optimizedPrompt = await llmService.generateResponse(testInput.value)
        currentResult.value.optimizedPrompt = optimizedPrompt
        addLog(`LLM优化完成: ${optimizedPrompt}`, 'success')

        // 2. 调用Kolors生图
        addLog('步骤2: 调用Kolors生图API...', 'info')
        const imageService = new ImageGenerationService()
        const imageResult = await imageService.generateWallpaper(optimizedPrompt)
        currentResult.value.imageUrl = imageResult.imageUrl
        addLog(`图片生成完成: ${imageResult.imageUrl}`, 'success')

        // 3. AI智能配色分析
        addLog('步骤3: AI智能配色分析...', 'info')
        const intelligentColors = await AIColorAnalyzer.analyzeWallpaperAndGenerateColors(
          imageResult.imageUrl,
          testInput.value,
          optimizedPrompt
        )
        currentResult.value.colors = intelligentColors
        addLog('AI智能配色完成', 'success')

        // 显示AI分析结果
        if (intelligentColors.aiAnalysis) {
          addLog(`AI分析: ${intelligentColors.aiAnalysis.mood}氛围, ${intelligentColors.aiAnalysis.brightness}亮度`, 'info')
        }

        // 发送颜色到父组件
        emit('colors-extracted', intelligentColors)

        currentResult.value.status = 'success'
        currentResult.value.statusText = '✅ 生成成功'
        addLog('🎉 完整流程测试成功！', 'success')

      } catch (error) {
        console.error('测试失败:', error)
        currentResult.value.status = 'error'
        currentResult.value.statusText = '❌ 生成失败'
        addLog(`测试失败: ${error.message}`, 'error')
      } finally {
        isGenerating.value = false
      }
    }

    const testVoiceInput = async () => {
      if (!AsrService.isSupported()) {
        addLog('浏览器不支持语音识别', 'error')
        return
      }

      isListening.value = true
      addLog('开始语音识别...', 'info')

      try {
        const result = await AsrService.startListening()
        voiceResult.value = result
        testInput.value = result
        addLog(`语音识别结果: ${result}`, 'success')
        
        // 自动进行文生图测试
        await testTextToImage()
      } catch (error) {
        addLog(`语音识别失败: ${error.message}`, 'error')
      } finally {
        isListening.value = false
      }
    }

    const testScene = async (scene) => {
      testInput.value = scene.prompt
      addLog(`测试场景: ${scene.name}`, 'info')
      await testTextToImage()
    }

    onMounted(() => {
      addLog('AI-HMI 场景生成测试组件已加载', 'info')
      addLog('theme_backend 服务应运行在 http://localhost:8000', 'info')
    })

    return {
      testInput,
      isGenerating,
      isListening,
      voiceResult,
      currentResult,
      testLogs,
      testScenes,
      testTextToImage,
      testVoiceInput,
      testScene
    }
  }
}
</script>

<style scoped>
.test-scene-generation {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 15px;
}

.test-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.input-section, .scene-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 20px;
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.input-group, .voice-group {
  margin-bottom: 15px;
}

.input-group input {
  width: 100%;
  padding: 10px;
  margin: 5px 0;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
}

.scene-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.scene-btn {
  padding: 12px;
  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));
  border-radius: 8px;
  background: var(--button-bg, rgba(74, 144, 226, 0.8));
  color: var(--button-color, white);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  text-shadow: var(--button-text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(4px);
}

.scene-btn:hover {
  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.test-results {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
}

.result-section, .log-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 20px;
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.result-display {
  background: white;
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.result-item {
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.generated-image {
  max-width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  margin-top: 10px;
}

.color-palette {
  display: flex;
  gap: 5px;
  margin-top: 10px;
}

.color-item {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.log-display {
  max-height: 400px;
  overflow-y: auto;
  background: #1a1a1a;
  padding: 15px;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-item {
  margin-bottom: 5px;
  display: flex;
  gap: 10px;
}

.log-time {
  color: #888;
  min-width: 80px;
}

.log-item.info .log-message { color: #4fc3f7; }
.log-item.success .log-message { color: #66bb6a; }
.log-item.error .log-message { color: #ef5350; }

.status.success { color: #4caf50; }
.status.error { color: #f44336; }
.status.processing { color: #ff9800; }

button {
  padding: 10px 15px;
  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));
  border-radius: 8px;
  background: var(--button-bg, rgba(74, 144, 226, 0.8));
  color: var(--button-color, white);
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 5px;
  font-weight: 500;
  text-shadow: var(--button-text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(4px);
}

button:hover:not(:disabled) {
  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: rgba(128, 128, 128, 0.5);
}

.voice-result {
  color: #4caf50;
  font-style: italic;
  margin-left: 10px;
}

@media (max-width: 768px) {
  .test-controls,
  .test-results {
    grid-template-columns: 1fr;
  }
  
  .scene-buttons {
    grid-template-columns: 1fr;
  }
}
</style>
