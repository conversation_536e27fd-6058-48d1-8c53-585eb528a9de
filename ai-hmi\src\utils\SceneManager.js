// 场景配置管理器 - 管理所有AI-HMI场景的配置
export default class SceneManager {
  constructor() {
    this.currentScene = 'default'
    this.scenes = this.initializeScenes()
    this.sceneHistory = []
    this.maxHistorySize = 10
  }

  initializeScenes() {
    return {
      // 默认场景
      default: {
        id: 'default',
        name: '默认模式',
        description: '沉浸式桌面壁纸生成界面',
        wallpaper: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        layout: 'immersive',
        cards: ['vpaWidget'],
        theme: 'immersive',
        priority: 0,
        autoSwitch: false
      },

      // 场景一：早高峰通勤（家庭出行）
      morningCommuteFamily: {
        id: 'morningCommuteFamily',
        name: '家庭出行模式',
        description: '送孩子上学的家庭通勤',
        wallpaper: 'modern glass building, transparent texture, blurred background, warm morning light, suitable for car display',
        layout: 'family',
        cards: ['navigation', 'kidEducation', 'pedia', 'vpaWidget'],
        theme: 'warm',
        priority: 8,
        autoSwitch: true,
        triggers: ['time:7-9', 'passengers:child', 'destination:school']
      },

      // 场景二：早高峰通勤（专注模式）
      morningCommuteFocus: {
        id: 'morningCommuteFocus',
        name: '专注通勤模式',
        description: '独自前往公司的专注通勤',
        wallpaper: 'relaxing music dynamic wallpaper, glass texture, minimalist style',
        layout: 'focus',
        cards: ['navigation', 'music', 'schedule', 'order', 'smarthome', 'vpaWidget'],
        theme: 'calm',
        priority: 7,
        autoSwitch: true,
        triggers: ['time:7-9', 'passengers:single', 'destination:work']
      },

      // 场景三：下班通勤
      eveningCommute: {
        id: 'eveningCommute',
        name: '下班通勤模式',
        description: '工作日下班回家',
        wallpaper: 'evening city street view, glass reflection, warm lighting',
        layout: 'relax',
        cards: ['navigation', 'music', 'smartHome', 'todo', 'vpaWidget'],
        theme: 'evening',
        priority: 6,
        autoSwitch: true,
        triggers: ['time:17-19', 'destination:home']
      },

      // 场景四：车内等待/摸鱼
      waitingMode: {
        id: 'waitingMode',
        name: '等待休息模式',
        description: '驻车等待时的娱乐模式',
        wallpaper: 'peaceful lake view, glass texture, relaxing atmosphere',
        layout: 'entertainment',
        cards: ['videoPlayer', 'news', 'ambientSound', 'vpaWidget'],
        theme: 'relax',
        priority: 5,
        autoSwitch: true,
        triggers: ['gear:P', 'duration:1min']
      },

      // 场景五：雨夜归途
      rainyNight: {
        id: 'rainyNight',
        name: '雨夜模式',
        description: '雨夜驾驶的安全模式',
        wallpaper: 'blurry city night view with raindrops, glass texture, warm lighting',
        layout: 'minimal',
        cards: ['navigation', 'music', 'vpaWidget'],
        theme: 'dark',
        priority: 9,
        autoSwitch: true,
        triggers: ['weather:rain', 'time:night']
      },

      // 场景六：周末家庭出游
      familyTrip: {
        id: 'familyTrip',
        name: '家庭出游模式',
        description: '周末家庭旅行',
        wallpaper: 'forest park, sunny day, glass texture, family atmosphere',
        layout: 'family',
        cards: ['navigation', 'rearSeatControl', 'facilityFinder', 'tripReminder', 'vpaWidget'],
        theme: 'bright',
        priority: 6,
        autoSwitch: true,
        triggers: ['time:weekend', 'destination:park', 'passengers:family']
      },

      // 场景七：长途高速驾驶
      longDistance: {
        id: 'longDistance',
        name: '长途驾驶模式',
        description: '高速公路长途驾驶',
        wallpaper: 'highway landscape, glass texture, minimalist style',
        layout: 'driving',
        cards: ['navigation', 'serviceArea', 'driverStatus', 'vehicleStatus', 'vpaWidget'],
        theme: 'functional',
        priority: 8,
        autoSwitch: true,
        triggers: ['road:highway', 'duration:1hour']
      },

      // 场景八：访客/代驾模式
      guestMode: {
        id: 'guestMode',
        name: '访客模式',
        description: '保护隐私的访客模式',
        wallpaper: 'neutral default wallpaper, simple glass texture',
        layout: 'basic',
        cards: ['tempNavigation', 'basicMusic', 'basicControl', 'vpaWidget'],
        theme: 'neutral',
        priority: 4,
        autoSwitch: false,
        triggers: ['manual:guest']
      },

      // 场景九：宠物模式
      petMode: {
        id: 'petMode',
        name: '宠物模式',
        description: '宠物留在车内的安全模式',
        wallpaper: 'cute pet animation background, comfortable temperature display',
        layout: 'pet',
        cards: ['petInfo', 'climateControl', 'vpaWidget'],
        theme: 'cute',
        priority: 7,
        autoSwitch: false,
        triggers: ['manual:pet']
      },

      // 场景十：洗车模式
      carWashMode: {
        id: 'carWashMode',
        name: '洗车模式',
        description: '自动洗车准备模式',
        wallpaper: 'car wash station background, glass texture',
        layout: 'checklist',
        cards: ['carWashChecklist', 'vpaWidget'],
        theme: 'functional',
        priority: 3,
        autoSwitch: false,
        triggers: ['manual:carWash']
      },

      // 场景十一：浪漫二人世界
      romanticMode: {
        id: 'romanticMode',
        name: '浪漫模式',
        description: '私密浪漫氛围',
        wallpaper: 'dynamic starry sky or fireplace video, glass texture',
        layout: 'romantic',
        cards: ['romanticMusic', 'ambientLight', 'vpaWidget'],
        theme: 'romantic',
        priority: 5,
        autoSwitch: false,
        triggers: ['manual:romantic', 'calendar:anniversary']
      },

      // 场景十二：智能充电场景
      chargingMode: {
        id: 'chargingMode',
        name: '充电模式',
        description: '电动车充电管理',
        wallpaper: 'charging station environment, glass texture',
        layout: 'charging',
        cards: ['chargingStatus', 'entertainment', 'nearbyShops', 'vpaWidget'],
        theme: 'tech',
        priority: 8,
        autoSwitch: true,
        triggers: ['battery:<30%', 'location:chargingStation']
      },

      // 场景十三：疲劳驾驶检测与干预
      fatigueDetection: {
        id: 'fatigueDetection',
        name: '疲劳驾驶预警',
        description: '疲劳驾驶安全干预',
        wallpaper: 'warning color gradient background, glass texture',
        layout: 'warning',
        cards: ['fatigueWarning', 'restArea', 'refreshment', 'emergencyContact', 'vpaWidget'],
        theme: 'warning',
        priority: 10,
        autoSwitch: true,
        triggers: ['fatigue:detected', 'driving:2hours']
      },

      // 场景十四：多用户识别与切换
      userSwitch: {
        id: 'userSwitch',
        name: '用户切换模式',
        description: '多用户配置选择',
        wallpaper: 'neutral user selection background, glass texture',
        layout: 'userSelection',
        cards: ['userSelector', 'userPreferences', 'privacySettings', 'vpaWidget'],
        theme: 'neutral',
        priority: 6,
        autoSwitch: true,
        triggers: ['newUser:detected', 'manual:userSwitch']
      },

      // 场景十五：智能泊车辅助
      parkingMode: {
        id: 'parkingMode',
        name: '智能泊车模式',
        description: '全方位泊车辅助',
        wallpaper: 'parking lot environment, glass texture',
        layout: 'parking',
        cards: ['parkingSearch', 'parkingAssist', 'costInfo', 'vpaWidget'],
        theme: 'functional',
        priority: 7,
        autoSwitch: true,
        triggers: ['destination:near', 'speed:slow', 'manual:parking']
      },

      // 场景十六：紧急情况处理
      emergencyMode: {
        id: 'emergencyMode',
        name: '紧急情况模式',
        description: '紧急救援处理',
        wallpaper: 'emergency red background, flashing warning',
        layout: 'emergency',
        cards: ['emergencyInfo', 'firstAid', 'emergencyContact', 'vpaWidget'],
        theme: 'emergency',
        priority: 11,
        autoSwitch: true,
        triggers: ['accident:detected', 'airbag:deployed', 'manual:emergency']
      }
    }
  }

  // 获取当前场景
  getCurrentScene() {
    return this.scenes[this.currentScene] || this.scenes.default
  }

  // 切换场景
  switchScene(sceneId, reason = 'manual') {
    if (!this.scenes[sceneId]) {
      console.warn(`Scene ${sceneId} not found`)
      return false
    }

    // 记录历史
    this.sceneHistory.unshift({
      from: this.currentScene,
      to: sceneId,
      reason,
      timestamp: new Date().toISOString()
    })

    // 限制历史记录大小
    if (this.sceneHistory.length > this.maxHistorySize) {
      this.sceneHistory = this.sceneHistory.slice(0, this.maxHistorySize)
    }

    this.currentScene = sceneId
    console.log(`Scene switched to ${sceneId} (${reason})`)
    return true
  }

  // 获取所有场景
  getAllScenes() {
    return Object.values(this.scenes)
  }

  // 获取可自动切换的场景
  getAutoSwitchScenes() {
    return Object.values(this.scenes).filter(scene => scene.autoSwitch)
  }

  // 根据触发条件获取推荐场景
  getRecommendedScenes(context = {}) {
    const recommendations = []
    
    for (const [sceneId, scene] of Object.entries(this.scenes)) {
      if (!scene.autoSwitch) continue
      
      let matchScore = 0
      for (const trigger of scene.triggers || []) {
        if (this.evaluateTrigger(trigger, context)) {
          matchScore += scene.priority
        }
      }
      
      if (matchScore > 0) {
        recommendations.push({
          sceneId,
          scene,
          score: matchScore
        })
      }
    }
    
    return recommendations.sort((a, b) => b.score - a.score)
  }

  // 评估触发条件
  evaluateTrigger(trigger, context) {
    const [type, value] = trigger.split(':')
    
    switch (type) {
      case 'time':
        return this.evaluateTimeTrigger(value)
      case 'passengers':
        return this.evaluatePassengerTrigger(value, context)
      case 'destination':
        return this.evaluateDestinationTrigger(value, context)
      case 'gear':
        return context.gear === value
      case 'duration':
        return this.evaluateDurationTrigger(value, context)
      case 'weather':
        return context.weather === value
      case 'road':
        return context.roadType === value
      case 'battery':
        return this.evaluateBatteryTrigger(value, context)
      case 'location':
        return context.locationType === value
      case 'fatigue':
        return context.fatigueDetected
      case 'driving':
        return this.evaluateDrivingTrigger(value, context)
      case 'manual':
        return context.manualTrigger === value
      case 'calendar':
        return context.calendarEvents?.includes(value)
      case 'newUser':
        return context.newUserDetected
      case 'accident':
        return context.accidentDetected
      case 'airbag':
        return context.airbagDeployed
      default:
        return false
    }
  }

  // 时间触发条件评估
  evaluateTimeTrigger(value) {
    // eslint-disable-next-line no-unused-vars
    const now = new Date()
    // eslint-disable-next-line no-unused-vars
    const hour = now.getHours()
    // eslint-disable-next-line no-unused-vars
    const day = now.getDay()
    
    if (value === '7-9') return hour >= 7 && hour <= 9
    if (value === '17-19') return hour >= 17 && hour <= 19
    if (value === 'night') return hour >= 20 || hour <= 6
    if (value === 'weekend') return day === 0 || day === 6
    
    return false
  }

  // 乘客触发条件评估
  evaluatePassengerTrigger(value, context) {
    if (value === 'child') return context.passengers?.includes('child')
    if (value === 'single') return context.passengerCount === 1
    if (value === 'family') return context.passengerCount > 2
    
    return false
  }

  // 目的地触发条件评估
  evaluateDestinationTrigger(value, context) {
    if (!context.destination) return false
    
    const dest = context.destination.toLowerCase()
    if (value === 'school') return dest.includes('school') || dest.includes('学校')
    if (value === 'work') return dest.includes('work') || dest.includes('公司')
    if (value === 'home') return dest.includes('home') || dest.includes('家')
    if (value === 'park') return dest.includes('park') || dest.includes('公园')
    
    return false
  }

  // 持续时间触发条件评估
  evaluateDurationTrigger(value, context) {
    if (!context.drivingDuration) return false
    
    if (value === '1min') return context.drivingDuration >= 60000
    if (value === '1hour') return context.drivingDuration >= 3600000
    if (value === '2hours') return context.drivingDuration >= 7200000
    
    return false
  }

  // 电量触发条件评估
  evaluateBatteryTrigger(value, context) {
    if (!context.batteryLevel) return false
    
    if (value === '<30%') return context.batteryLevel < 30
    if (value === '<20%') return context.batteryLevel < 20
    if (value === '<10%') return context.batteryLevel < 10
    
    return false
  }

  // 驾驶时间触发条件评估
  evaluateDrivingTrigger(value, context) {
    if (!context.drivingDuration) return false
    
    if (value === '2hours') return context.drivingDuration >= 7200000
    
    return false
  }

  // 获取场景历史
  getSceneHistory() {
    return this.sceneHistory
  }

  // 清除历史
  clearHistory() {
    this.sceneHistory = []
  }

  // 获取场景统计
  getSceneStatistics() {
    const stats = {}
    for (const history of this.sceneHistory) {
      stats[history.to] = (stats[history.to] || 0) + 1
    }
    return stats
  }
}