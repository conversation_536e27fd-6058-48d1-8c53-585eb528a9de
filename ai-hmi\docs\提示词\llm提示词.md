// 从 webhook 获取用户场景描述
const input = $input.all()[0].json;
const userSceneDescription = input.scene_description || input.description || '早高峰通勤，需要送孩子上学然后去公司';

// 知识库数据 - 场景库
const sceneElementKB = [
  {
    "id": "natural_commute",
    "name": "自然通勤",
    "description": "在宁静的乡村或城市绿道中通勤，强调放松和自然连接。",
    "elements": ["柔和的阳光", "窗外的绿植", "木纹内饰", "舒缓的音乐"]
  },
  {
    "id": "cyberpunk_drive",
    "name": "赛博朋克驾驶",
    "description": "在充满霓虹灯和未来建筑的雨夜都市中驾驶，充满紧张和科技感。",
    "elements": ["闪烁的霓虹灯广告牌", "全息仪表盘", "雨滴滑过车窗", "电子脉冲音乐"]
  },
  {
    "id": "glassmorphism_wait",
    "name": "玻璃拟物质感等待",
    "description": "在安静、明亮的空间中等待，例如在充电站或自动驾驶中，界面强调通透和信息清晰度。",
    "elements": ["窗外模糊的景色", "悬浮的信息面板", "柔和的光晕", "安静的环境音"]
  },
  {
    "id": "neumorphism_rainy",
    "name": "新拟物雨天",
    "description": "雨天车内的宁静时刻，界面采用柔软的拟物风格，提供舒适和安全感。",
    "elements": ["车窗上的雨滴", "温暖的车内光线", "柔软的座椅材质", "轻柔的雨声"]
  },
  {
    "id": "kawaii_family_trip",
    "name": "可爱家庭旅行",
    "description": "和家人一起的愉快公路旅行，充满欢笑和多彩的风景。",
    "elements": ["多彩的气球", "卡通化的路标", "家庭成员的笑脸", "欢快的流行音乐"]
  }
];

// 风格库
const styleKB = [
  {
    "id": "natural",
    "name": "自然通勤",
    "properties": {
      "theme-color-primary": "#7D8A6E",
      "theme-color-background": "#F0F2EB",
      "font-family": "'Noto Sans', sans-serif",
      "border-radius": "12px"
    }
  },
  {
    "id": "cyberpunk",
    "name": "赛博朋克",
    "properties": {
      "theme-color-primary": "#EA00D9",
      "theme-color-secondary": "#00F0FF",
      "theme-color-background": "#0A021A",
      "theme-color-text": "#F0F0F0",
      "font-family": "'Orbitron', sans-serif"
    }
  },
  {
    "id": "glassmorphism",
    "name": "玻璃拟物",
    "properties": {
      "background-blur": "15px",
      "border": "1px solid rgba(255, 255, 255, 0.25)",
      "background-color": "rgba(255, 255, 255, 0.1)",
      "border-radius": "16px"
    }
  },
  {
    "id": "neumorphism",
    "name": "新拟物",
    "properties": {
      "background-color": "#E0E5EC",
      "box-shadow": "9px 9px 16px #A3B1C6, -9px -9px 16px #FFFFFF",
      "border-radius": "20px"
    }
  },
  {
    "id": "kawaii",
    "name": "可爱卡通",
    "properties": {
      "theme-color-primary": "#FFC0CB",
      "theme-color-background": "#FFF8E1",
      "font-family": "'Comic Sans MS', cursive, sans-serif",
      "border-radius": "50px"
    }
  }
];

// 组件库
const componentKB = [
  {"id": "vpa-interaction-panel", "name": "VPA交互面板", "description": "VPA主交互面板，用于显示对话和结果"},
  {"id": "vpa-avatar-widget", "name": "VPA头像小部件", "description": "VPA的悬浮头像，用于实时语音反馈"},
  {"id": "dynamic-island", "name": "灵动岛", "description": "位于顶部的状态栏，用于显示关键通知和实时活动"},
  {"id": "weather-card", "name": "天气卡片", "description": "显示当前或未来的天气信息"},
  {"id": "music-control-card", "name": "音乐控制卡片", "description": "提供音乐播放、暂停、切歌等媒体控制功能"},
  {"id": "todo-card", "name": "待办事项卡片", "description": "显示用户的待办事项列表或日历事件"},
  {"id": "quick-action-card", "name": "快捷操作卡片", "description": "提供一组常用操作的按钮"},
  {"id": "navigation-detail-card", "name": "导航详情卡片", "description": "显示详细的地图和转向导航信息"},
  {"id": "news-feed-card", "name": "新闻源卡片", "description": "展示一个滚动的新闻或信息流"}
];

// 布局库
const layoutKB = [
  {
    "id": "two-column-container",
    "name": "双栏容器",
    "slots": [{"id": "left-panel", "description": "左侧栏"}, {"id": "main-content", "description": "主内容区"}]
  },
  {
    "id": "vertical-stack-container",
    "name": "垂直堆叠容器",
    "slots": [{"id": "slot-1"}, {"id": "slot-2"}, {"id": "slot-3"}]
  },
  {
    "id": "three-column-grid-container",
    "name": "三栏网格容器",
    "slots": [{"id": "col-1"}, {"id": "col-2"}, {"id": "col-3"}]
  }
];

// 构建完整的提示词
const prompt = `**角色:** 你是一位专业的HMI布局设计师和场景分析专家。

**任务:** 
1. 根据用户提供的场景描述，深入分析场景特点、用户需求和使用环境
2. 从知识库中选择最合适的场景ID、风格ID、组件和布局容器
3. 生成完整的布局JSON结构，确保布局合理且符合场景需求
4. 将布局渲染为完整的HTML代码，包含适当的样式和交互效果

**知识库:**

**场景库:**
${JSON.stringify(sceneElementKB, null, 2)}

**风格库:**
${JSON.stringify(styleKB, null, 2)}

**组件库:**
${JSON.stringify(componentKB, null, 2)}

**布局库:**
${JSON.stringify(layoutKB, null, 2)}

**用户场景描述:** "${userSceneDescription}"

**输出要求:**
请按以下格式输出：

## 场景分析
[分析用户场景，说明选择的场景ID和风格ID的理由]

## 布局设计
[说明布局设计思路和组件选择理由]

## 生成的布局JSON
\`\`\`json
[完整的布局JSON结构]
\`\`\`

## 完整HTML代码
\`\`\`html
[完整的HTML代码，包含CSS样式]
\`\`\`

**注意事项:**
1. 布局JSON必须严格遵循知识库中定义的容器和组件ID
2. HTML代码要包含完整的CSS样式，体现选择的风格特征
3. 组件内容要根据场景生成贴切、生动的占位内容
4. 确保界面布局合理，符合HMI设计原则`;

return [{
  json: {
    prompt: prompt,
    userSceneDescription: userSceneDescription,
    timestamp: new Date().toISOString()
  }
}];