{"ast": null, "code": "import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';\nimport BaseCard from '@/components/cards/BaseCard.vue';\nimport mockDataService from '@/services/MockDataService.js';\nexport default {\n  name: 'AIOrderCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    size: {\n      type: String,\n      default: 'large',\n      validator: value => ['small', 'medium', 'large'].includes(value)\n    },\n    position: {\n      type: Object,\n      default: () => ({\n        x: 0,\n        y: 0\n      })\n    },\n    theme: {\n      type: String,\n      default: 'glassmorphism'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({})\n    },\n    displayMode: {\n      type: String,\n      default: 'full',\n      validator: value => ['compact', 'standard', 'full'].includes(value)\n    },\n    showQuickOrders: {\n      type: Boolean,\n      default: true\n    },\n    showOrderHistory: {\n      type: Boolean,\n      default: true\n    },\n    autoRefresh: {\n      type: Boolean,\n      default: true\n    },\n    refreshInterval: {\n      type: Number,\n      default: 60000 // 60秒\n    }\n  },\n  emits: ['order-placed', 'order-tracked', 'promotion-used', 'recommendation-viewed'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式数据\n    const isLoading = ref(false);\n    const isProcessing = ref(false);\n    const loadingText = ref('正在加载订单信息...');\n    const quickOrdersExpanded = ref(true);\n    const currentOrders = ref([]);\n    const aiRecommendations = ref([]);\n    const recentOrders = ref([]);\n    const promotions = ref([]);\n    const smartReminders = ref([]);\n    const recommendationReason = ref('');\n    const quickOrderTypes = ref([{\n      id: 1,\n      label: '外卖',\n      subtitle: '美食配送',\n      icon: 'fas fa-utensils',\n      type: 'food'\n    }, {\n      id: 2,\n      label: '购物',\n      subtitle: '日用百货',\n      icon: 'fas fa-shopping-cart',\n      type: 'shopping'\n    }, {\n      id: 3,\n      label: '打车',\n      subtitle: '出行服务',\n      icon: 'fas fa-car',\n      type: 'ride'\n    }, {\n      id: 4,\n      label: '快递',\n      subtitle: '同城配送',\n      icon: 'fas fa-shipping-fast',\n      type: 'delivery'\n    }, {\n      id: 5,\n      label: '服务',\n      subtitle: '生活服务',\n      icon: 'fas fa-concierge-bell',\n      type: 'service'\n    }, {\n      id: 6,\n      label: '更多',\n      subtitle: '全部分类',\n      icon: 'fas fa-th',\n      type: 'more'\n    }]);\n\n    // 计算属性\n    const cardTitle = computed(() => {\n      const activeCount = currentOrders.value.length;\n      if (activeCount > 0) {\n        return `订单管理 (${activeCount}个进行中)`;\n      }\n      return 'AI订单助手';\n    });\n    const hasAnyContent = computed(() => {\n      return currentOrders.value.length > 0 || aiRecommendations.value.length > 0 || recentOrders.value.length > 0 || promotions.value.length > 0 || smartReminders.value.length > 0;\n    });\n\n    // 方法\n    const loadOrderData = async () => {\n      try {\n        isLoading.value = true;\n        loadingText.value = '正在加载订单信息...';\n        const orderData = await mockDataService.getOrderData();\n        currentOrders.value = orderData.currentOrders || [];\n        aiRecommendations.value = orderData.aiRecommendations || [];\n        recentOrders.value = orderData.recentOrders || [];\n        promotions.value = orderData.promotions || [];\n        smartReminders.value = orderData.smartReminders || [];\n        recommendationReason.value = orderData.recommendationReason || '';\n      } catch (error) {\n        console.error('Failed to load order data:', error);\n      } finally {\n        isLoading.value = false;\n      }\n    };\n    const getOrderIcon = type => {\n      const icons = {\n        food: 'fas fa-utensils',\n        shopping: 'fas fa-shopping-bag',\n        ride: 'fas fa-car',\n        delivery: 'fas fa-shipping-fast',\n        service: 'fas fa-concierge-bell',\n        grocery: 'fas fa-apple-alt',\n        pharmacy: 'fas fa-pills',\n        gas: 'fas fa-gas-pump'\n      };\n      return icons[type] || 'fas fa-shopping-cart';\n    };\n    const getStatusText = status => {\n      const statusTexts = {\n        pending: '待确认',\n        confirmed: '已确认',\n        preparing: '准备中',\n        shipping: '配送中',\n        delivered: '已送达',\n        completed: '已完成',\n        cancelled: '已取消'\n      };\n      return statusTexts[status] || '未知状态';\n    };\n    const formatDate = dateString => {\n      const date = new Date(dateString);\n      const now = new Date();\n      const diffTime = Math.abs(now - date);\n      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n      if (diffDays === 1) {\n        return '昨天';\n      } else if (diffDays <= 7) {\n        return `${diffDays}天前`;\n      } else {\n        return date.toLocaleDateString('zh-CN', {\n          month: 'short',\n          day: 'numeric'\n        });\n      }\n    };\n    const viewOrderDetails = order => {\n      console.log('Viewing order details:', order);\n      // 实际应用中会打开订单详情页面\n    };\n    const trackOrder = order => {\n      console.log('Tracking order:', order);\n      // 实际应用中会打开订单跟踪页面\n    };\n    const contactMerchant = order => {\n      console.log('Contacting merchant for order:', order);\n      // 实际应用中会打开联系商家功能\n    };\n    const viewRecommendation = recommendation => {\n      console.log('Viewing recommendation:', recommendation);\n      emit('recommendation-viewed', recommendation);\n      // 实际应用中会打开商品详情页面\n    };\n    const quickOrder = async recommendation => {\n      try {\n        isProcessing.value = true;\n        const orderResult = await mockDataService.placeQuickOrder(recommendation);\n\n        // 添加到当前订单列表\n        currentOrders.value.unshift(orderResult);\n        emit('order-placed', orderResult);\n\n        // 重新加载数据以获取更新的推荐\n        await loadOrderData();\n      } catch (error) {\n        console.error('Failed to place quick order:', error);\n      } finally {\n        isProcessing.value = false;\n      }\n    };\n    const toggleQuickOrdersExpanded = () => {\n      quickOrdersExpanded.value = !quickOrdersExpanded.value;\n    };\n    const startQuickOrder = quickOrderType => {\n      console.log('Starting quick order:', quickOrderType);\n      // 实际应用中会打开对应的订单页面\n    };\n    const viewAllOrders = () => {\n      console.log('Viewing all orders');\n      // 实际应用中会打开订单历史页面\n    };\n    const reorderItem = async order => {\n      try {\n        isProcessing.value = true;\n        const reorderResult = await mockDataService.reorderItem(order.id);\n\n        // 添加到当前订单列表\n        currentOrders.value.unshift(reorderResult);\n        emit('order-placed', reorderResult);\n      } catch (error) {\n        console.error('Failed to reorder item:', error);\n      } finally {\n        isProcessing.value = false;\n      }\n    };\n    const usePromotion = promotion => {\n      console.log('Using promotion:', promotion);\n      emit('promotion-used', promotion);\n      // 实际应用中会应用优惠券\n    };\n    const handleReminderAction = reminder => {\n      console.log('Handling reminder action:', reminder);\n      // 根据提醒类型执行相应操作\n    };\n    const dismissReminder = reminder => {\n      const index = smartReminders.value.findIndex(r => r.id === reminder.id);\n      if (index > -1) {\n        smartReminders.value.splice(index, 1);\n      }\n    };\n    const startBrowsing = () => {\n      console.log('Starting to browse');\n      // 实际应用中会打开商品浏览页面\n    };\n\n    // 生命周期\n    let refreshTimer = null;\n    onMounted(async () => {\n      await mockDataService.initialize();\n      await loadOrderData();\n\n      // 设置自动刷新\n      if (props.autoRefresh) {\n        refreshTimer = setInterval(() => {\n          loadOrderData();\n        }, props.refreshInterval);\n      }\n    });\n    onUnmounted(() => {\n      if (refreshTimer) {\n        clearInterval(refreshTimer);\n      }\n    });\n    return {\n      // 响应式数据\n      isLoading,\n      isProcessing,\n      loadingText,\n      quickOrdersExpanded,\n      currentOrders,\n      aiRecommendations,\n      recentOrders,\n      promotions,\n      smartReminders,\n      recommendationReason,\n      quickOrderTypes,\n      // 计算属性\n      cardTitle,\n      hasAnyContent,\n      // 方法\n      getOrderIcon,\n      getStatusText,\n      formatDate,\n      viewOrderDetails,\n      trackOrder,\n      contactMerchant,\n      viewRecommendation,\n      quickOrder,\n      toggleQuickOrdersExpanded,\n      startQuickOrder,\n      viewAllOrders,\n      reorderItem,\n      usePromotion,\n      handleReminderAction,\n      dismissReminder,\n      startBrowsing\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "onUnmounted", "BaseCard", "mockDataService", "name", "components", "props", "size", "type", "String", "default", "validator", "value", "includes", "position", "Object", "x", "y", "theme", "themeColors", "displayMode", "showQuickOrders", "Boolean", "showOrderHistory", "autoRefresh", "refreshInterval", "Number", "emits", "setup", "emit", "isLoading", "isProcessing", "loadingText", "quickOrdersExpanded", "currentOrders", "aiRecommendations", "recentOrders", "promotions", "smartReminders", "recommendationReason", "quickOrderTypes", "id", "label", "subtitle", "icon", "cardTitle", "activeCount", "length", "has<PERSON>ny<PERSON><PERSON>nt", "loadOrderData", "orderData", "getOrderData", "error", "console", "getOrderIcon", "icons", "food", "shopping", "ride", "delivery", "service", "grocery", "pharmacy", "gas", "getStatusText", "status", "statusTexts", "pending", "confirmed", "preparing", "shipping", "delivered", "completed", "cancelled", "formatDate", "dateString", "date", "Date", "now", "diffTime", "Math", "abs", "diffDays", "ceil", "toLocaleDateString", "month", "day", "viewOrderDetails", "order", "log", "trackOrder", "contactMerchant", "viewRecommendation", "recommendation", "quickOrder", "orderResult", "placeQuickOrder", "unshift", "toggleQuickOrdersExpanded", "startQuickOrder", "quickOrderType", "viewAllOrders", "reorderItem", "reorderResult", "usePromotion", "promotion", "handleReminderAction", "reminder", "dismiss<PERSON><PERSON><PERSON>", "index", "findIndex", "r", "splice", "startBrowsing", "refreshTimer", "initialize", "setInterval", "clearInterval"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\AIOrderCard.vue"], "sourcesContent": ["<template>\n  <BaseCard\n    :card-type=\"'ai-order'\"\n    :size=\"size\"\n    :position=\"position\"\n    :theme=\"theme\"\n    :theme-colors=\"themeColors\"\n    :show-header=\"true\"\n    :show-footer=\"false\"\n    :title=\"cardTitle\"\n    :icon=\"'fas fa-shopping-cart'\"\n    class=\"ai-order-card\"\n  >\n    <div class=\"order-container\" :class=\"[`size-${size}`, `mode-${displayMode}`]\">\n      <!-- 当前订单状态 -->\n      <div class=\"current-orders\" v-if=\"currentOrders.length > 0\">\n        <div class=\"orders-header\">\n          <h3>进行中的订单</h3>\n          <div class=\"orders-count\">{{ currentOrders.length }}个</div>\n        </div>\n        \n        <div class=\"orders-list\">\n          <div \n            v-for=\"order in currentOrders\" \n            :key=\"order.id\"\n            :class=\"['order-item', `status-${order.status}`]\"\n            @click=\"viewOrderDetails(order)\"\n          >\n            <div class=\"order-icon\">\n              <i :class=\"getOrderIcon(order.type)\"></i>\n            </div>\n            \n            <div class=\"order-info\">\n              <div class=\"order-title\">{{ order.title }}</div>\n              <div class=\"order-subtitle\">{{ order.subtitle }}</div>\n              <div class=\"order-status-text\">{{ getStatusText(order.status) }}</div>\n            </div>\n            \n            <div class=\"order-meta\">\n              <div class=\"order-time\">{{ order.estimatedTime }}</div>\n              <div class=\"order-price\" v-if=\"order.price\">¥{{ order.price }}</div>\n            </div>\n            \n            <div class=\"order-actions\">\n              <button \n                v-if=\"order.trackable\"\n                @click.stop=\"trackOrder(order)\"\n                class=\"track-btn\"\n              >\n                <i class=\"fas fa-map-marker-alt\"></i>\n              </button>\n              <button \n                v-if=\"order.contactable\"\n                @click.stop=\"contactMerchant(order)\"\n                class=\"contact-btn\"\n              >\n                <i class=\"fas fa-phone\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- AI推荐订单 -->\n      <div class=\"ai-recommendations\" v-if=\"aiRecommendations.length > 0\">\n        <div class=\"recommendations-header\">\n          <div class=\"header-content\">\n            <i class=\"fas fa-magic\"></i>\n            <span>AI智能推荐</span>\n          </div>\n          <div class=\"recommendation-reason\">{{ recommendationReason }}</div>\n        </div>\n        \n        <div class=\"recommendations-list\">\n          <div \n            v-for=\"recommendation in aiRecommendations\" \n            :key=\"recommendation.id\"\n            class=\"recommendation-item\"\n            @click=\"viewRecommendation(recommendation)\"\n          >\n            <div class=\"recommendation-image\">\n              <img :src=\"recommendation.image\" :alt=\"recommendation.title\" />\n              <div class=\"recommendation-badge\" v-if=\"recommendation.badge\">\n                {{ recommendation.badge }}\n              </div>\n            </div>\n            \n            <div class=\"recommendation-content\">\n              <div class=\"recommendation-title\">{{ recommendation.title }}</div>\n              <div class=\"recommendation-description\">{{ recommendation.description }}</div>\n              \n              <div class=\"recommendation-details\">\n                <div class=\"detail-item\">\n                  <i class=\"fas fa-star\"></i>\n                  <span>{{ recommendation.rating }}</span>\n                </div>\n                <div class=\"detail-item\">\n                  <i class=\"fas fa-clock\"></i>\n                  <span>{{ recommendation.deliveryTime }}</span>\n                </div>\n                <div class=\"detail-item\">\n                  <i class=\"fas fa-shipping-fast\"></i>\n                  <span>{{ recommendation.shippingFee }}</span>\n                </div>\n              </div>\n              \n              <div class=\"recommendation-price\">\n                <span class=\"current-price\">¥{{ recommendation.price }}</span>\n                <span class=\"original-price\" v-if=\"recommendation.originalPrice\">\n                  ¥{{ recommendation.originalPrice }}\n                </span>\n                <span class=\"discount\" v-if=\"recommendation.discount\">\n                  {{ recommendation.discount }}折\n                </span>\n              </div>\n            </div>\n            \n            <div class=\"recommendation-actions\">\n              <button \n                @click.stop=\"quickOrder(recommendation)\"\n                class=\"quick-order-btn\"\n                :disabled=\"isProcessing\"\n              >\n                <i class=\"fas fa-plus\"></i>\n                <span>快速下单</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 快速订单 -->\n      <div class=\"quick-orders\" v-if=\"showQuickOrders\">\n        <div class=\"quick-orders-header\">\n          <h3>快速订单</h3>\n          <button @click=\"toggleQuickOrdersExpanded\" class=\"expand-btn\">\n            <i :class=\"quickOrdersExpanded ? 'fas fa-chevron-up' : 'fas fa-chevron-down'\"></i>\n          </button>\n        </div>\n        \n        <div class=\"quick-orders-grid\" v-show=\"quickOrdersExpanded\">\n          <button \n            v-for=\"quickOrder in quickOrderTypes\" \n            :key=\"quickOrder.id\"\n            @click=\"startQuickOrder(quickOrder)\"\n            class=\"quick-order-type\"\n            :disabled=\"isProcessing\"\n          >\n            <div class=\"quick-order-icon\">\n              <i :class=\"quickOrder.icon\"></i>\n            </div>\n            <div class=\"quick-order-label\">{{ quickOrder.label }}</div>\n            <div class=\"quick-order-subtitle\">{{ quickOrder.subtitle }}</div>\n          </button>\n        </div>\n      </div>\n\n      <!-- 订单历史 -->\n      <div class=\"order-history\" v-if=\"showOrderHistory && recentOrders.length > 0\">\n        <div class=\"history-header\">\n          <h3>最近订单</h3>\n          <button @click=\"viewAllOrders\" class=\"view-all-btn\">\n            查看全部\n          </button>\n        </div>\n        \n        <div class=\"history-list\">\n          <div \n            v-for=\"order in recentOrders.slice(0, 3)\" \n            :key=\"order.id\"\n            class=\"history-item\"\n            @click=\"reorderItem(order)\"\n          >\n            <div class=\"history-icon\">\n              <i :class=\"getOrderIcon(order.type)\"></i>\n            </div>\n            \n            <div class=\"history-info\">\n              <div class=\"history-title\">{{ order.title }}</div>\n              <div class=\"history-date\">{{ formatDate(order.date) }}</div>\n            </div>\n            \n            <div class=\"history-actions\">\n              <button \n                @click.stop=\"reorderItem(order)\"\n                class=\"reorder-btn\"\n                :disabled=\"isProcessing\"\n              >\n                <i class=\"fas fa-redo\"></i>\n                <span>再来一单</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 优惠券和活动 -->\n      <div class=\"promotions\" v-if=\"promotions.length > 0\">\n        <div class=\"promotions-header\">\n          <i class=\"fas fa-gift\"></i>\n          <span>优惠活动</span>\n        </div>\n        \n        <div class=\"promotions-list\">\n          <div \n            v-for=\"promotion in promotions\" \n            :key=\"promotion.id\"\n            :class=\"['promotion-item', `type-${promotion.type}`]\"\n            @click=\"usePromotion(promotion)\"\n          >\n            <div class=\"promotion-icon\">\n              <i :class=\"promotion.icon\"></i>\n            </div>\n            \n            <div class=\"promotion-content\">\n              <div class=\"promotion-title\">{{ promotion.title }}</div>\n              <div class=\"promotion-description\">{{ promotion.description }}</div>\n              <div class=\"promotion-validity\">{{ promotion.validity }}</div>\n            </div>\n            \n            <div class=\"promotion-value\">\n              <div class=\"value-text\">{{ promotion.value }}</div>\n              <div class=\"value-type\">{{ promotion.valueType }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 智能提醒 -->\n      <div class=\"smart-reminders\" v-if=\"smartReminders.length > 0\">\n        <div class=\"reminders-header\">\n          <i class=\"fas fa-bell\"></i>\n          <span>智能提醒</span>\n        </div>\n        \n        <div class=\"reminders-list\">\n          <div \n            v-for=\"reminder in smartReminders\" \n            :key=\"reminder.id\"\n            :class=\"['reminder-item', `priority-${reminder.priority}`]\"\n          >\n            <div class=\"reminder-icon\">\n              <i :class=\"reminder.icon\"></i>\n            </div>\n            \n            <div class=\"reminder-content\">\n              <div class=\"reminder-text\">{{ reminder.text }}</div>\n              <div class=\"reminder-time\">{{ reminder.time }}</div>\n            </div>\n            \n            <div class=\"reminder-actions\">\n              <button \n                v-if=\"reminder.actionable\"\n                @click=\"handleReminderAction(reminder)\"\n                class=\"reminder-action-btn\"\n              >\n                {{ reminder.actionText }}\n              </button>\n              <button \n                @click=\"dismissReminder(reminder)\"\n                class=\"dismiss-btn\"\n              >\n                <i class=\"fas fa-times\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 无订单状态 -->\n      <div v-if=\"!hasAnyContent\" class=\"no-orders\">\n        <div class=\"no-orders-icon\">\n          <i class=\"fas fa-shopping-bag\"></i>\n        </div>\n        <div class=\"no-orders-text\">\n          <h3>暂无订单</h3>\n          <p>开始您的第一个订单吧</p>\n        </div>\n        <div class=\"start-ordering\">\n          <button @click=\"startBrowsing\" class=\"start-browse-btn\">\n            <i class=\"fas fa-search\"></i>\n            <span>开始浏览</span>\n          </button>\n        </div>\n      </div>\n\n      <!-- 加载状态 -->\n      <div v-if=\"isLoading\" class=\"loading-overlay\">\n        <div class=\"loading-spinner\"></div>\n        <div class=\"loading-text\">{{ loadingText }}</div>\n      </div>\n    </div>\n  </BaseCard>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, onUnmounted } from 'vue'\nimport BaseCard from '@/components/cards/BaseCard.vue'\nimport mockDataService from '@/services/MockDataService.js'\n\nexport default {\n  name: 'AIOrderCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    size: {\n      type: String,\n      default: 'large',\n      validator: (value) => ['small', 'medium', 'large'].includes(value)\n    },\n    position: {\n      type: Object,\n      default: () => ({ x: 0, y: 0 })\n    },\n    theme: {\n      type: String,\n      default: 'glassmorphism'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({})\n    },\n    displayMode: {\n      type: String,\n      default: 'full',\n      validator: (value) => ['compact', 'standard', 'full'].includes(value)\n    },\n    showQuickOrders: {\n      type: Boolean,\n      default: true\n    },\n    showOrderHistory: {\n      type: Boolean,\n      default: true\n    },\n    autoRefresh: {\n      type: Boolean,\n      default: true\n    },\n    refreshInterval: {\n      type: Number,\n      default: 60000 // 60秒\n    }\n  },\n  emits: ['order-placed', 'order-tracked', 'promotion-used', 'recommendation-viewed'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const isLoading = ref(false)\n    const isProcessing = ref(false)\n    const loadingText = ref('正在加载订单信息...')\n    const quickOrdersExpanded = ref(true)\n    \n    const currentOrders = ref([])\n    const aiRecommendations = ref([])\n    const recentOrders = ref([])\n    const promotions = ref([])\n    const smartReminders = ref([])\n    const recommendationReason = ref('')\n    \n    const quickOrderTypes = ref([\n      {\n        id: 1,\n        label: '外卖',\n        subtitle: '美食配送',\n        icon: 'fas fa-utensils',\n        type: 'food'\n      },\n      {\n        id: 2,\n        label: '购物',\n        subtitle: '日用百货',\n        icon: 'fas fa-shopping-cart',\n        type: 'shopping'\n      },\n      {\n        id: 3,\n        label: '打车',\n        subtitle: '出行服务',\n        icon: 'fas fa-car',\n        type: 'ride'\n      },\n      {\n        id: 4,\n        label: '快递',\n        subtitle: '同城配送',\n        icon: 'fas fa-shipping-fast',\n        type: 'delivery'\n      },\n      {\n        id: 5,\n        label: '服务',\n        subtitle: '生活服务',\n        icon: 'fas fa-concierge-bell',\n        type: 'service'\n      },\n      {\n        id: 6,\n        label: '更多',\n        subtitle: '全部分类',\n        icon: 'fas fa-th',\n        type: 'more'\n      }\n    ])\n\n    // 计算属性\n    const cardTitle = computed(() => {\n      const activeCount = currentOrders.value.length\n      if (activeCount > 0) {\n        return `订单管理 (${activeCount}个进行中)`\n      }\n      return 'AI订单助手'\n    })\n    \n    const hasAnyContent = computed(() => {\n      return currentOrders.value.length > 0 || \n             aiRecommendations.value.length > 0 || \n             recentOrders.value.length > 0 || \n             promotions.value.length > 0 || \n             smartReminders.value.length > 0\n    })\n\n    // 方法\n    const loadOrderData = async () => {\n      try {\n        isLoading.value = true\n        loadingText.value = '正在加载订单信息...'\n        \n        const orderData = await mockDataService.getOrderData()\n        \n        currentOrders.value = orderData.currentOrders || []\n        aiRecommendations.value = orderData.aiRecommendations || []\n        recentOrders.value = orderData.recentOrders || []\n        promotions.value = orderData.promotions || []\n        smartReminders.value = orderData.smartReminders || []\n        recommendationReason.value = orderData.recommendationReason || ''\n        \n      } catch (error) {\n        console.error('Failed to load order data:', error)\n      } finally {\n        isLoading.value = false\n      }\n    }\n    \n    const getOrderIcon = (type) => {\n      const icons = {\n        food: 'fas fa-utensils',\n        shopping: 'fas fa-shopping-bag',\n        ride: 'fas fa-car',\n        delivery: 'fas fa-shipping-fast',\n        service: 'fas fa-concierge-bell',\n        grocery: 'fas fa-apple-alt',\n        pharmacy: 'fas fa-pills',\n        gas: 'fas fa-gas-pump'\n      }\n      return icons[type] || 'fas fa-shopping-cart'\n    }\n    \n    const getStatusText = (status) => {\n      const statusTexts = {\n        pending: '待确认',\n        confirmed: '已确认',\n        preparing: '准备中',\n        shipping: '配送中',\n        delivered: '已送达',\n        completed: '已完成',\n        cancelled: '已取消'\n      }\n      return statusTexts[status] || '未知状态'\n    }\n    \n    const formatDate = (dateString) => {\n      const date = new Date(dateString)\n      const now = new Date()\n      const diffTime = Math.abs(now - date)\n      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n      \n      if (diffDays === 1) {\n        return '昨天'\n      } else if (diffDays <= 7) {\n        return `${diffDays}天前`\n      } else {\n        return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })\n      }\n    }\n    \n    const viewOrderDetails = (order) => {\n      console.log('Viewing order details:', order)\n      // 实际应用中会打开订单详情页面\n    }\n    \n    const trackOrder = (order) => {\n      console.log('Tracking order:', order)\n      // 实际应用中会打开订单跟踪页面\n    }\n    \n    const contactMerchant = (order) => {\n      console.log('Contacting merchant for order:', order)\n      // 实际应用中会打开联系商家功能\n    }\n    \n    const viewRecommendation = (recommendation) => {\n      console.log('Viewing recommendation:', recommendation)\n      emit('recommendation-viewed', recommendation)\n      // 实际应用中会打开商品详情页面\n    }\n    \n    const quickOrder = async (recommendation) => {\n      try {\n        isProcessing.value = true\n        \n        const orderResult = await mockDataService.placeQuickOrder(recommendation)\n        \n        // 添加到当前订单列表\n        currentOrders.value.unshift(orderResult)\n        \n        emit('order-placed', orderResult)\n        \n        // 重新加载数据以获取更新的推荐\n        await loadOrderData()\n        \n      } catch (error) {\n        console.error('Failed to place quick order:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n    \n    const toggleQuickOrdersExpanded = () => {\n      quickOrdersExpanded.value = !quickOrdersExpanded.value\n    }\n    \n    const startQuickOrder = (quickOrderType) => {\n      console.log('Starting quick order:', quickOrderType)\n      // 实际应用中会打开对应的订单页面\n    }\n    \n    const viewAllOrders = () => {\n      console.log('Viewing all orders')\n      // 实际应用中会打开订单历史页面\n    }\n    \n    const reorderItem = async (order) => {\n      try {\n        isProcessing.value = true\n        \n        const reorderResult = await mockDataService.reorderItem(order.id)\n        \n        // 添加到当前订单列表\n        currentOrders.value.unshift(reorderResult)\n        \n        emit('order-placed', reorderResult)\n        \n      } catch (error) {\n        console.error('Failed to reorder item:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n    \n    const usePromotion = (promotion) => {\n      console.log('Using promotion:', promotion)\n      emit('promotion-used', promotion)\n      // 实际应用中会应用优惠券\n    }\n    \n    const handleReminderAction = (reminder) => {\n      console.log('Handling reminder action:', reminder)\n      // 根据提醒类型执行相应操作\n    }\n    \n    const dismissReminder = (reminder) => {\n      const index = smartReminders.value.findIndex(r => r.id === reminder.id)\n      if (index > -1) {\n        smartReminders.value.splice(index, 1)\n      }\n    }\n    \n    const startBrowsing = () => {\n      console.log('Starting to browse')\n      // 实际应用中会打开商品浏览页面\n    }\n\n    // 生命周期\n    let refreshTimer = null\n    \n    onMounted(async () => {\n      await mockDataService.initialize()\n      await loadOrderData()\n      \n      // 设置自动刷新\n      if (props.autoRefresh) {\n        refreshTimer = setInterval(() => {\n          loadOrderData()\n        }, props.refreshInterval)\n      }\n    })\n\n    onUnmounted(() => {\n      if (refreshTimer) {\n        clearInterval(refreshTimer)\n      }\n    })\n\n    return {\n      // 响应式数据\n      isLoading,\n      isProcessing,\n      loadingText,\n      quickOrdersExpanded,\n      currentOrders,\n      aiRecommendations,\n      recentOrders,\n      promotions,\n      smartReminders,\n      recommendationReason,\n      quickOrderTypes,\n      \n      // 计算属性\n      cardTitle,\n      hasAnyContent,\n      \n      // 方法\n      getOrderIcon,\n      getStatusText,\n      formatDate,\n      viewOrderDetails,\n      trackOrder,\n      contactMerchant,\n      viewRecommendation,\n      quickOrder,\n      toggleQuickOrdersExpanded,\n      startQuickOrder,\n      viewAllOrders,\n      reorderItem,\n      usePromotion,\n      handleReminderAction,\n      dismissReminder,\n      startBrowsing\n    }\n  }\n}\n</script>\n\n<style scoped>\n.ai-order-card {\n  height: 100%;\n}\n\n.order-container {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  padding: 16px;\n  position: relative;\n  overflow-y: auto;\n}\n\n/* 当前订单 */\n.current-orders {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.orders-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.orders-header h3 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.orders-count {\n  background: rgba(74, 144, 226, 0.3);\n  color: #4a90e2;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.orders-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.order-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border-left: 3px solid transparent;\n}\n\n.order-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateX(2px);\n}\n\n.order-item.status-pending {\n  border-left-color: #f59e0b;\n}\n\n.order-item.status-confirmed,\n.order-item.status-preparing {\n  border-left-color: #4a90e2;\n}\n\n.order-item.status-shipping {\n  border-left-color: #8b5cf6;\n}\n\n.order-item.status-delivered,\n.order-item.status-completed {\n  border-left-color: #10b981;\n}\n\n.order-icon {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.8);\n  flex-shrink: 0;\n}\n\n.order-info {\n  flex: 1;\n}\n\n.order-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n}\n\n.order-subtitle {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n  margin-bottom: 2px;\n}\n\n.order-status-text {\n  font-size: 11px;\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.order-meta {\n  text-align: right;\n  margin-right: 8px;\n}\n\n.order-time {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.8);\n  margin-bottom: 2px;\n}\n\n.order-price {\n  font-size: 14px;\n  font-weight: 600;\n  color: #7ed321;\n}\n\n.order-actions {\n  display: flex;\n  gap: 4px;\n}\n\n.track-btn,\n.contact-btn {\n  width: 28px;\n  height: 28px;\n  border: none;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0.2);\n  color: rgba(255, 255, 255, 0.7);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n}\n\n.track-btn:hover {\n  background: rgba(74, 144, 226, 0.3);\n  color: #4a90e2;\n}\n\n.contact-btn:hover {\n  background: rgba(126, 211, 33, 0.3);\n  color: #7ed321;\n}\n\n/* AI推荐 */\n.ai-recommendations {\n  background: rgba(126, 211, 33, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.recommendations-header {\n  margin-bottom: 12px;\n}\n\n.header-content {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  color: #7ed321;\n  margin-bottom: 4px;\n}\n\n.recommendation-reason {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.recommendations-list {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.recommendation-item {\n  display: flex;\n  gap: 12px;\n  padding: 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.recommendation-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateY(-1px);\n}\n\n.recommendation-image {\n  width: 60px;\n  height: 60px;\n  border-radius: 8px;\n  overflow: hidden;\n  position: relative;\n  flex-shrink: 0;\n}\n\n.recommendation-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.recommendation-badge {\n  position: absolute;\n  top: 4px;\n  right: 4px;\n  background: #ef4444;\n  color: white;\n  font-size: 10px;\n  padding: 2px 4px;\n  border-radius: 4px;\n}\n\n.recommendation-content {\n  flex: 1;\n}\n\n.recommendation-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 4px;\n}\n\n.recommendation-description {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n  margin-bottom: 8px;\n}\n\n.recommendation-details {\n  display: flex;\n  gap: 12px;\n  margin-bottom: 8px;\n}\n\n.detail-item {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 11px;\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.recommendation-price {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.current-price {\n  font-size: 16px;\n  font-weight: 600;\n  color: #7ed321;\n}\n\n.original-price {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.5);\n  text-decoration: line-through;\n}\n\n.discount {\n  background: #ef4444;\n  color: white;\n  font-size: 10px;\n  padding: 2px 4px;\n  border-radius: 4px;\n}\n\n.recommendation-actions {\n  display: flex;\n  align-items: center;\n}\n\n.quick-order-btn {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 8px 12px;\n  border: none;\n  border-radius: 6px;\n  background: rgba(126, 211, 33, 0.3);\n  color: #7ed321;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.quick-order-btn:hover:not(:disabled) {\n  background: rgba(126, 211, 33, 0.5);\n  transform: scale(1.05);\n}\n\n.quick-order-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* 快速订单 */\n.quick-orders {\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.quick-orders-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.quick-orders-header h3 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.expand-btn {\n  width: 24px;\n  height: 24px;\n  border: none;\n  border-radius: 4px;\n  background: rgba(255, 255, 255, 0.2);\n  color: rgba(255, 255, 255, 0.7);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n}\n\n.expand-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n}\n\n.quick-orders-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 8px;\n}\n\n.quick-order-type {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 6px;\n  padding: 12px 8px;\n  border: none;\n  border-radius: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  color: rgba(255, 255, 255, 0.8);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-align: center;\n}\n\n.quick-order-type:hover:not(:disabled) {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateY(-2px);\n}\n\n.quick-order-type:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.quick-order-icon {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n}\n\n.quick-order-label {\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.quick-order-subtitle {\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n/* 订单历史 */\n.order-history {\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.history-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.history-header h3 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.view-all-btn {\n  background: none;\n  border: none;\n  color: #4a90e2;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.view-all-btn:hover {\n  color: #7ed321;\n}\n\n.history-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.history-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.history-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n}\n\n.history-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.7);\n  flex-shrink: 0;\n}\n\n.history-info {\n  flex: 1;\n}\n\n.history-title {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n}\n\n.history-date {\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.history-actions {\n  display: flex;\n}\n\n.reorder-btn {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  padding: 4px 8px;\n  border: none;\n  border-radius: 4px;\n  background: rgba(74, 144, 226, 0.2);\n  color: #4a90e2;\n  font-size: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.reorder-btn:hover:not(:disabled) {\n  background: rgba(74, 144, 226, 0.3);\n}\n\n.reorder-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* 优惠券和活动 */\n.promotions {\n  background: rgba(245, 158, 11, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.promotions-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  color: #f59e0b;\n  margin-bottom: 12px;\n}\n\n.promotions-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.promotion-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.promotion-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateX(2px);\n}\n\n.promotion-icon {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: rgba(245, 158, 11, 0.3);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  color: #f59e0b;\n  flex-shrink: 0;\n}\n\n.promotion-content {\n  flex: 1;\n}\n\n.promotion-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n}\n\n.promotion-description {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n  margin-bottom: 2px;\n}\n\n.promotion-validity {\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.5);\n}\n\n.promotion-value {\n  text-align: right;\n}\n\n.value-text {\n  font-size: 16px;\n  font-weight: 600;\n  color: #f59e0b;\n}\n\n.value-type {\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n/* 智能提醒 */\n.smart-reminders {\n  background: rgba(139, 92, 246, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.reminders-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  color: #8b5cf6;\n  margin-bottom: 12px;\n}\n\n.reminders-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.reminder-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n  border-left: 3px solid transparent;\n}\n\n.reminder-item.priority-high {\n  border-left-color: #ef4444;\n}\n\n.reminder-item.priority-medium {\n  border-left-color: #f59e0b;\n}\n\n.reminder-item.priority-low {\n  border-left-color: #10b981;\n}\n\n.reminder-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background: rgba(139, 92, 246, 0.3);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  color: #8b5cf6;\n  flex-shrink: 0;\n}\n\n.reminder-content {\n  flex: 1;\n}\n\n.reminder-text {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n}\n\n.reminder-time {\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.reminder-actions {\n  display: flex;\n  gap: 4px;\n}\n\n.reminder-action-btn {\n  padding: 4px 8px;\n  border: none;\n  border-radius: 4px;\n  background: rgba(139, 92, 246, 0.3);\n  color: #8b5cf6;\n  font-size: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.reminder-action-btn:hover {\n  background: rgba(139, 92, 246, 0.5);\n}\n\n.dismiss-btn {\n  width: 20px;\n  height: 20px;\n  border: none;\n  border-radius: 4px;\n  background: rgba(255, 255, 255, 0.2);\n  color: rgba(255, 255, 255, 0.6);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n}\n\n.dismiss-btn:hover {\n  background: rgba(239, 68, 68, 0.3);\n  color: #ef4444;\n}\n\n/* 无订单状态 */\n.no-orders {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  padding: 32px 16px;\n  flex: 1;\n}\n\n.no-orders-icon {\n  font-size: 48px;\n  color: rgba(255, 255, 255, 0.3);\n  margin-bottom: 16px;\n}\n\n.no-orders-text h3 {\n  margin: 0 0 8px 0;\n  font-size: 18px;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.no-orders-text p {\n  margin: 0 0 24px 0;\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.start-ordering {\n  width: 100%;\n  max-width: 200px;\n}\n\n.start-browse-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  width: 100%;\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  background: rgba(74, 144, 226, 0.3);\n  color: #4a90e2;\n  font-size: 14px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.start-browse-btn:hover {\n  background: rgba(74, 144, 226, 0.5);\n  transform: translateY(-2px);\n}\n\n/* 加载状态 */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.3);\n  backdrop-filter: blur(5px);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 12px;\n  border-radius: 12px;\n}\n\n.loading-spinner {\n  width: 32px;\n  height: 32px;\n  border: 3px solid rgba(255, 255, 255, 0.2);\n  border-top: 3px solid #4a90e2;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.loading-text {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* 动画 */\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* 响应式设计 */\n.size-small .order-container {\n  padding: 12px;\n  gap: 12px;\n}\n\n.size-small .quick-orders-grid {\n  grid-template-columns: repeat(2, 1fr);\n}\n\n.mode-compact .ai-recommendations,\n.mode-compact .order-history,\n.mode-compact .promotions,\n.mode-compact .smart-reminders {\n  display: none;\n}\n\n.mode-compact .quick-orders-grid {\n  grid-template-columns: repeat(4, 1fr);\n}\n</style>"], "mappings": "AAwSA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAU,QAAS,KAAI;AACpE,OAAOC,QAAO,MAAO,iCAAgC;AACrD,OAAOC,eAAc,MAAO,+BAA8B;AAE1D,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE;IACVH;EACF,CAAC;EACDI,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAGC,KAAK,IAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAACC,QAAQ,CAACD,KAAK;IACnE,CAAC;IACDE,QAAQ,EAAE;MACRN,IAAI,EAAEO,MAAM;MACZL,OAAO,EAAEA,CAAA,MAAO;QAAEM,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;IAChC,CAAC;IACDC,KAAK,EAAE;MACLV,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDS,WAAW,EAAE;MACXX,IAAI,EAAEO,MAAM;MACZL,OAAO,EAAEA,CAAA,MAAO,CAAC,CAAC;IACpB,CAAC;IACDU,WAAW,EAAE;MACXZ,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAGC,KAAK,IAAK,CAAC,SAAS,EAAE,UAAU,EAAE,MAAM,CAAC,CAACC,QAAQ,CAACD,KAAK;IACtE,CAAC;IACDS,eAAe,EAAE;MACfb,IAAI,EAAEc,OAAO;MACbZ,OAAO,EAAE;IACX,CAAC;IACDa,gBAAgB,EAAE;MAChBf,IAAI,EAAEc,OAAO;MACbZ,OAAO,EAAE;IACX,CAAC;IACDc,WAAW,EAAE;MACXhB,IAAI,EAAEc,OAAO;MACbZ,OAAO,EAAE;IACX,CAAC;IACDe,eAAe,EAAE;MACfjB,IAAI,EAAEkB,MAAM;MACZhB,OAAO,EAAE,KAAI,CAAE;IACjB;EACF,CAAC;EACDiB,KAAK,EAAE,CAAC,cAAc,EAAE,eAAe,EAAE,gBAAgB,EAAE,uBAAuB,CAAC;EACnFC,KAAKA,CAACtB,KAAK,EAAE;IAAEuB;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,SAAQ,GAAIjC,GAAG,CAAC,KAAK;IAC3B,MAAMkC,YAAW,GAAIlC,GAAG,CAAC,KAAK;IAC9B,MAAMmC,WAAU,GAAInC,GAAG,CAAC,aAAa;IACrC,MAAMoC,mBAAkB,GAAIpC,GAAG,CAAC,IAAI;IAEpC,MAAMqC,aAAY,GAAIrC,GAAG,CAAC,EAAE;IAC5B,MAAMsC,iBAAgB,GAAItC,GAAG,CAAC,EAAE;IAChC,MAAMuC,YAAW,GAAIvC,GAAG,CAAC,EAAE;IAC3B,MAAMwC,UAAS,GAAIxC,GAAG,CAAC,EAAE;IACzB,MAAMyC,cAAa,GAAIzC,GAAG,CAAC,EAAE;IAC7B,MAAM0C,oBAAmB,GAAI1C,GAAG,CAAC,EAAE;IAEnC,MAAM2C,eAAc,GAAI3C,GAAG,CAAC,CAC1B;MACE4C,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,MAAM;MAChBC,IAAI,EAAE,iBAAiB;MACvBpC,IAAI,EAAE;IACR,CAAC,EACD;MACEiC,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,MAAM;MAChBC,IAAI,EAAE,sBAAsB;MAC5BpC,IAAI,EAAE;IACR,CAAC,EACD;MACEiC,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,MAAM;MAChBC,IAAI,EAAE,YAAY;MAClBpC,IAAI,EAAE;IACR,CAAC,EACD;MACEiC,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,MAAM;MAChBC,IAAI,EAAE,sBAAsB;MAC5BpC,IAAI,EAAE;IACR,CAAC,EACD;MACEiC,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,MAAM;MAChBC,IAAI,EAAE,uBAAuB;MAC7BpC,IAAI,EAAE;IACR,CAAC,EACD;MACEiC,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,MAAM;MAChBC,IAAI,EAAE,WAAW;MACjBpC,IAAI,EAAE;IACR,EACD;;IAED;IACA,MAAMqC,SAAQ,GAAI9C,QAAQ,CAAC,MAAM;MAC/B,MAAM+C,WAAU,GAAIZ,aAAa,CAACtB,KAAK,CAACmC,MAAK;MAC7C,IAAID,WAAU,GAAI,CAAC,EAAE;QACnB,OAAO,SAASA,WAAW,OAAM;MACnC;MACA,OAAO,QAAO;IAChB,CAAC;IAED,MAAME,aAAY,GAAIjD,QAAQ,CAAC,MAAM;MACnC,OAAOmC,aAAa,CAACtB,KAAK,CAACmC,MAAK,GAAI,KAC7BZ,iBAAiB,CAACvB,KAAK,CAACmC,MAAK,GAAI,KACjCX,YAAY,CAACxB,KAAK,CAACmC,MAAK,GAAI,KAC5BV,UAAU,CAACzB,KAAK,CAACmC,MAAK,GAAI,KAC1BT,cAAc,CAAC1B,KAAK,CAACmC,MAAK,GAAI;IACvC,CAAC;;IAED;IACA,MAAME,aAAY,GAAI,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFnB,SAAS,CAAClB,KAAI,GAAI,IAAG;QACrBoB,WAAW,CAACpB,KAAI,GAAI,aAAY;QAEhC,MAAMsC,SAAQ,GAAI,MAAM/C,eAAe,CAACgD,YAAY,CAAC;QAErDjB,aAAa,CAACtB,KAAI,GAAIsC,SAAS,CAAChB,aAAY,IAAK,EAAC;QAClDC,iBAAiB,CAACvB,KAAI,GAAIsC,SAAS,CAACf,iBAAgB,IAAK,EAAC;QAC1DC,YAAY,CAACxB,KAAI,GAAIsC,SAAS,CAACd,YAAW,IAAK,EAAC;QAChDC,UAAU,CAACzB,KAAI,GAAIsC,SAAS,CAACb,UAAS,IAAK,EAAC;QAC5CC,cAAc,CAAC1B,KAAI,GAAIsC,SAAS,CAACZ,cAAa,IAAK,EAAC;QACpDC,oBAAoB,CAAC3B,KAAI,GAAIsC,SAAS,CAACX,oBAAmB,IAAK,EAAC;MAElE,EAAE,OAAOa,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK;MACnD,UAAU;QACRtB,SAAS,CAAClB,KAAI,GAAI,KAAI;MACxB;IACF;IAEA,MAAM0C,YAAW,GAAK9C,IAAI,IAAK;MAC7B,MAAM+C,KAAI,GAAI;QACZC,IAAI,EAAE,iBAAiB;QACvBC,QAAQ,EAAE,qBAAqB;QAC/BC,IAAI,EAAE,YAAY;QAClBC,QAAQ,EAAE,sBAAsB;QAChCC,OAAO,EAAE,uBAAuB;QAChCC,OAAO,EAAE,kBAAkB;QAC3BC,QAAQ,EAAE,cAAc;QACxBC,GAAG,EAAE;MACP;MACA,OAAOR,KAAK,CAAC/C,IAAI,KAAK,sBAAqB;IAC7C;IAEA,MAAMwD,aAAY,GAAKC,MAAM,IAAK;MAChC,MAAMC,WAAU,GAAI;QAClBC,OAAO,EAAE,KAAK;QACdC,SAAS,EAAE,KAAK;QAChBC,SAAS,EAAE,KAAK;QAChBC,QAAQ,EAAE,KAAK;QACfC,SAAS,EAAE,KAAK;QAChBC,SAAS,EAAE,KAAK;QAChBC,SAAS,EAAE;MACb;MACA,OAAOP,WAAW,CAACD,MAAM,KAAK,MAAK;IACrC;IAEA,MAAMS,UAAS,GAAKC,UAAU,IAAK;MACjC,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,UAAU;MAChC,MAAMG,GAAE,GAAI,IAAID,IAAI,CAAC;MACrB,MAAME,QAAO,GAAIC,IAAI,CAACC,GAAG,CAACH,GAAE,GAAIF,IAAI;MACpC,MAAMM,QAAO,GAAIF,IAAI,CAACG,IAAI,CAACJ,QAAO,IAAK,IAAG,GAAI,EAAC,GAAI,EAAC,GAAI,EAAE,CAAC;MAE3D,IAAIG,QAAO,KAAM,CAAC,EAAE;QAClB,OAAO,IAAG;MACZ,OAAO,IAAIA,QAAO,IAAK,CAAC,EAAE;QACxB,OAAO,GAAGA,QAAQ,IAAG;MACvB,OAAO;QACL,OAAON,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;UAAEC,KAAK,EAAE,OAAO;UAAEC,GAAG,EAAE;QAAU,CAAC;MAC5E;IACF;IAEA,MAAMC,gBAAe,GAAKC,KAAK,IAAK;MAClCnC,OAAO,CAACoC,GAAG,CAAC,wBAAwB,EAAED,KAAK;MAC3C;IACF;IAEA,MAAME,UAAS,GAAKF,KAAK,IAAK;MAC5BnC,OAAO,CAACoC,GAAG,CAAC,iBAAiB,EAAED,KAAK;MACpC;IACF;IAEA,MAAMG,eAAc,GAAKH,KAAK,IAAK;MACjCnC,OAAO,CAACoC,GAAG,CAAC,gCAAgC,EAAED,KAAK;MACnD;IACF;IAEA,MAAMI,kBAAiB,GAAKC,cAAc,IAAK;MAC7CxC,OAAO,CAACoC,GAAG,CAAC,yBAAyB,EAAEI,cAAc;MACrDhE,IAAI,CAAC,uBAAuB,EAAEgE,cAAc;MAC5C;IACF;IAEA,MAAMC,UAAS,GAAI,MAAOD,cAAc,IAAK;MAC3C,IAAI;QACF9D,YAAY,CAACnB,KAAI,GAAI,IAAG;QAExB,MAAMmF,WAAU,GAAI,MAAM5F,eAAe,CAAC6F,eAAe,CAACH,cAAc;;QAExE;QACA3D,aAAa,CAACtB,KAAK,CAACqF,OAAO,CAACF,WAAW;QAEvClE,IAAI,CAAC,cAAc,EAAEkE,WAAW;;QAEhC;QACA,MAAM9C,aAAa,CAAC;MAEtB,EAAE,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK;MACrD,UAAU;QACRrB,YAAY,CAACnB,KAAI,GAAI,KAAI;MAC3B;IACF;IAEA,MAAMsF,yBAAwB,GAAIA,CAAA,KAAM;MACtCjE,mBAAmB,CAACrB,KAAI,GAAI,CAACqB,mBAAmB,CAACrB,KAAI;IACvD;IAEA,MAAMuF,eAAc,GAAKC,cAAc,IAAK;MAC1C/C,OAAO,CAACoC,GAAG,CAAC,uBAAuB,EAAEW,cAAc;MACnD;IACF;IAEA,MAAMC,aAAY,GAAIA,CAAA,KAAM;MAC1BhD,OAAO,CAACoC,GAAG,CAAC,oBAAoB;MAChC;IACF;IAEA,MAAMa,WAAU,GAAI,MAAOd,KAAK,IAAK;MACnC,IAAI;QACFzD,YAAY,CAACnB,KAAI,GAAI,IAAG;QAExB,MAAM2F,aAAY,GAAI,MAAMpG,eAAe,CAACmG,WAAW,CAACd,KAAK,CAAC/C,EAAE;;QAEhE;QACAP,aAAa,CAACtB,KAAK,CAACqF,OAAO,CAACM,aAAa;QAEzC1E,IAAI,CAAC,cAAc,EAAE0E,aAAa;MAEpC,EAAE,OAAOnD,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK;MAChD,UAAU;QACRrB,YAAY,CAACnB,KAAI,GAAI,KAAI;MAC3B;IACF;IAEA,MAAM4F,YAAW,GAAKC,SAAS,IAAK;MAClCpD,OAAO,CAACoC,GAAG,CAAC,kBAAkB,EAAEgB,SAAS;MACzC5E,IAAI,CAAC,gBAAgB,EAAE4E,SAAS;MAChC;IACF;IAEA,MAAMC,oBAAmB,GAAKC,QAAQ,IAAK;MACzCtD,OAAO,CAACoC,GAAG,CAAC,2BAA2B,EAAEkB,QAAQ;MACjD;IACF;IAEA,MAAMC,eAAc,GAAKD,QAAQ,IAAK;MACpC,MAAME,KAAI,GAAIvE,cAAc,CAAC1B,KAAK,CAACkG,SAAS,CAACC,CAAA,IAAKA,CAAC,CAACtE,EAAC,KAAMkE,QAAQ,CAAClE,EAAE;MACtE,IAAIoE,KAAI,GAAI,CAAC,CAAC,EAAE;QACdvE,cAAc,CAAC1B,KAAK,CAACoG,MAAM,CAACH,KAAK,EAAE,CAAC;MACtC;IACF;IAEA,MAAMI,aAAY,GAAIA,CAAA,KAAM;MAC1B5D,OAAO,CAACoC,GAAG,CAAC,oBAAoB;MAChC;IACF;;IAEA;IACA,IAAIyB,YAAW,GAAI,IAAG;IAEtBlH,SAAS,CAAC,YAAY;MACpB,MAAMG,eAAe,CAACgH,UAAU,CAAC;MACjC,MAAMlE,aAAa,CAAC;;MAEpB;MACA,IAAI3C,KAAK,CAACkB,WAAW,EAAE;QACrB0F,YAAW,GAAIE,WAAW,CAAC,MAAM;UAC/BnE,aAAa,CAAC;QAChB,CAAC,EAAE3C,KAAK,CAACmB,eAAe;MAC1B;IACF,CAAC;IAEDxB,WAAW,CAAC,MAAM;MAChB,IAAIiH,YAAY,EAAE;QAChBG,aAAa,CAACH,YAAY;MAC5B;IACF,CAAC;IAED,OAAO;MACL;MACApF,SAAS;MACTC,YAAY;MACZC,WAAW;MACXC,mBAAmB;MACnBC,aAAa;MACbC,iBAAiB;MACjBC,YAAY;MACZC,UAAU;MACVC,cAAc;MACdC,oBAAoB;MACpBC,eAAe;MAEf;MACAK,SAAS;MACTG,aAAa;MAEb;MACAM,YAAY;MACZU,aAAa;MACbU,UAAU;MACVa,gBAAgB;MAChBG,UAAU;MACVC,eAAe;MACfC,kBAAkB;MAClBE,UAAU;MACVI,yBAAyB;MACzBC,eAAe;MACfE,aAAa;MACbC,WAAW;MACXE,YAAY;MACZE,oBAAoB;MACpBE,eAAe;MACfK;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}