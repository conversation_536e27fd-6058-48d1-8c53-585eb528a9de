<template>
  <div 
    :class="['base-card', `card-${cardType}`, `size-${size}`, themeClass]"
    :style="cardStyles"
    @click="handleClick"
  >
    <!-- 卡片头部 -->
    <div v-if="showHeader" class="card-header">
      <div class="header-left">
        <i v-if="icon" :class="icon" class="card-icon"></i>
        <h3 v-if="title" class="card-title">{{ title }}</h3>
      </div>
      <div class="header-right">
        <slot name="header-actions">
          <button v-if="closable" @click.stop="$emit('close')" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </slot>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content" :class="{ 'no-header': !showHeader }">
      <slot>
        <div class="default-content">
          <p v-if="description">{{ description }}</p>
        </div>
      </slot>
    </div>

    <!-- 卡片底部 -->
    <div v-if="showFooter" class="card-footer">
      <slot name="footer">
        <div class="footer-actions">
          <button v-if="actionable" @click.stop="handleAction" class="action-btn">
            {{ actionText || '操作' }}
          </button>
        </div>
      </slot>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useLayoutStore } from '@/store'

export default {
  name: 'BaseCard',
  props: {
    // 卡片类型
    cardType: {
      type: String,
      default: 'default'
    },
    
    // 卡片尺寸
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large', 'custom'].includes(value)
    },
    
    // 自定义尺寸（当size为custom时使用）
    customSize: {
      type: Object,
      default: () => ({ width: 4, height: 2 })
    },
    
    // 网格位置
    position: {
      type: Object,
      default: () => ({ x: 1, y: 1 })
    },
    
    // 卡片标题
    title: {
      type: String,
      default: ''
    },
    
    // 卡片描述
    description: {
      type: String,
      default: ''
    },
    
    // 卡片图标
    icon: {
      type: String,
      default: ''
    },
    
    // 主题
    theme: {
      type: String,
      default: 'glass',
      validator: (value) => ['glass', 'solid', 'minimal', 'gradient'].includes(value)
    },
    
    // 主题颜色
    themeColors: {
      type: Object,
      default: () => ({})
    },
    
    // 显示配置
    showHeader: {
      type: Boolean,
      default: true
    },
    
    showFooter: {
      type: Boolean,
      default: false
    },
    
    // 交互配置
    clickable: {
      type: Boolean,
      default: false
    },
    
    actionable: {
      type: Boolean,
      default: false
    },
    
    actionText: {
      type: String,
      default: ''
    },
    
    closable: {
      type: Boolean,
      default: false
    },
    
    // 状态
    loading: {
      type: Boolean,
      default: false
    },
    
    disabled: {
      type: Boolean,
      default: false
    }
  },
  
  emits: ['click', 'action', 'close'],
  
  setup(props, { emit }) {
    const layoutStore = useLayoutStore()
    
    // 计算卡片样式
    const cardStyles = computed(() => {
      let gridStyle = {}
      
      // 根据尺寸类型获取网格样式
      if (props.size === 'custom') {
        gridStyle = layoutStore.getComponentPosition('custom', props.position)
        // 注册自定义尺寸
        layoutStore.registerComponentSize('custom', props.customSize)
      } else {
        const sizeMap = {
          small: 'cardSmall',
          medium: 'cardMedium', 
          large: 'cardLarge'
        }
        gridStyle = layoutStore.getComponentPosition(sizeMap[props.size], props.position)
      }
      
      // 主题颜色样式
      const themeStyle = {
        '--card-primary-color': props.themeColors.primary || '#4a90e2',
        '--card-secondary-color': props.themeColors.secondary || '#7ed321',
        '--card-background': props.themeColors.background || 'rgba(255, 255, 255, 0.1)',
        '--card-text-color': props.themeColors.text || '#ffffff'
      }
      
      return {
        ...gridStyle,
        ...themeStyle
      }
    })
    
    // 主题类名
    const themeClass = computed(() => {
      return `theme-${props.theme}`
    })
    
    // 处理点击事件
    const handleClick = () => {
      if (props.clickable && !props.disabled) {
        emit('click')
      }
    }
    
    // 处理操作事件
    const handleAction = () => {
      if (props.actionable && !props.disabled) {
        emit('action')
      }
    }
    
    return {
      cardStyles,
      themeClass,
      handleClick,
      handleAction
    }
  }
}
</script>

<style scoped>
.base-card {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: default;
  display: flex;
  flex-direction: column;
}

/* 主题样式 */
.theme-glass {
  background: var(--card-background, rgba(255, 255, 255, 0.1));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.theme-solid {
  background: var(--card-primary-color, #4a90e2);
  border: none;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.theme-minimal {
  background: transparent;
  border: 1px solid var(--card-primary-color, #4a90e2);
  box-shadow: none;
}

.theme-gradient {
  background: linear-gradient(135deg, 
    var(--card-primary-color, #4a90e2) 0%, 
    var(--card-secondary-color, #7ed321) 100%);
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* 可点击状态 */
.base-card.clickable {
  cursor: pointer;
}

.base-card.clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
}

/* 禁用状态 */
.base-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.card-icon {
  font-size: 18px;
  color: var(--card-primary-color, #4a90e2);
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--card-text-color, #ffffff);
}

.close-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: var(--card-text-color, #ffffff);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 卡片内容 */
.card-content {
  flex: 1;
  padding: 20px;
  color: var(--card-text-color, #ffffff);
  overflow: hidden;
}

.card-content.no-header {
  padding-top: 20px;
}

.default-content p {
  margin: 0;
  opacity: 0.8;
  line-height: 1.5;
}

/* 卡片底部 */
.card-footer {
  padding: 15px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.action-btn {
  padding: 8px 16px;
  border: 1px solid var(--card-primary-color, #4a90e2);
  background: var(--card-primary-color, #4a90e2);
  color: white;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: var(--card-secondary-color, #7ed321);
  border-color: var(--card-secondary-color, #7ed321);
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(5px);
}

.loading-spinner {
  color: var(--card-primary-color, #4a90e2);
  font-size: 24px;
}
</style>
