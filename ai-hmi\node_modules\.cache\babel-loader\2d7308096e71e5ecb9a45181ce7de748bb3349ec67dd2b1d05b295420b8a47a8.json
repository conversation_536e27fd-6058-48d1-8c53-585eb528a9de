{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, reactive, computed, onMounted, onUnmounted } from 'vue';\nimport BaseCard from '@/components/cards/BaseCard.vue';\nimport mockDataService from '@/services/MockDataService.js';\nexport default {\n  name: 'AIScheduleAssistantCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    size: {\n      type: String,\n      default: 'medium',\n      validator: value => ['small', 'medium', 'large'].includes(value)\n    },\n    position: {\n      type: Object,\n      default: () => ({\n        x: 0,\n        y: 0\n      })\n    },\n    theme: {\n      type: String,\n      default: 'glassmorphism'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({})\n    },\n    displayMode: {\n      type: String,\n      default: 'full',\n      validator: value => ['compact', 'standard', 'full'].includes(value)\n    },\n    showTimeManagement: {\n      type: Boolean,\n      default: true\n    },\n    showQuickActions: {\n      type: Boolean,\n      default: true\n    },\n    autoRefresh: {\n      type: Boolean,\n      default: true\n    },\n    refreshInterval: {\n      type: Number,\n      default: 60000 // 1分钟\n    }\n  },\n  emits: ['event-click', 'navigation-start', 'suggestion-applied', 'action-triggered'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式数据\n    const isLoading = ref(false);\n    const isProcessing = ref(false);\n    const todayEvents = ref([]);\n    const aiOptimization = ref(null);\n    const currentDate = ref('');\n    const travelTimeToNext = ref('--');\n    const suggestedDepartureTime = ref('--');\n    const timeBuffer = ref('5分钟');\n    const bufferPercentage = ref(75);\n\n    // 快速操作\n    const quickActions = ref([{\n      id: 1,\n      label: '添加事件',\n      icon: 'fas fa-plus',\n      action: 'addEvent'\n    }, {\n      id: 2,\n      label: '查看周视图',\n      icon: 'fas fa-calendar-week',\n      action: 'weekView'\n    }, {\n      id: 3,\n      label: 'AI优化',\n      icon: 'fas fa-magic',\n      action: 'aiOptimize'\n    }]);\n\n    // 计算属性\n    const cardTitle = computed(() => {\n      const upcomingCount = todayEvents.value.filter(e => e.status === 'upcoming').length;\n      return upcomingCount > 0 ? `日程助手 (${upcomingCount}个待办)` : '日程助手';\n    });\n\n    // 方法\n    const updateCurrentDate = () => {\n      const now = new Date();\n      currentDate.value = now.toLocaleDateString('zh-CN', {\n        month: 'long',\n        day: 'numeric',\n        weekday: 'long'\n      });\n    };\n    const getTimeStatus = event => {\n      const now = new Date();\n      const eventTime = new Date();\n      const [hours, minutes] = event.time.split(':').map(Number);\n      eventTime.setHours(hours, minutes, 0, 0);\n      const timeDiff = eventTime.getTime() - now.getTime();\n      const minutesDiff = Math.floor(timeDiff / (1000 * 60));\n      if (minutesDiff < 0) return 'past';\n      if (minutesDiff <= 15) return 'imminent';\n      if (minutesDiff <= 60) return 'soon';\n      return 'future';\n    };\n    const loadScheduleData = async () => {\n      try {\n        isLoading.value = true;\n        const scheduleData = await mockDataService.getScheduleData();\n        todayEvents.value = scheduleData.todayEvents || [];\n        aiOptimization.value = scheduleData.aiOptimization || null;\n\n        // 更新时间管理信息\n        updateTimeManagement();\n      } catch (error) {\n        console.error('Failed to load schedule data:', error);\n      } finally {\n        isLoading.value = false;\n      }\n    };\n    const updateTimeManagement = () => {\n      const upcomingEvents = todayEvents.value.filter(e => e.status === 'upcoming');\n      if (upcomingEvents.length > 0) {\n        const nextEvent = upcomingEvents[0];\n\n        // 模拟计算行程时间\n        travelTimeToNext.value = '15分钟';\n\n        // 计算建议出发时间\n        const eventTime = new Date();\n        const [hours, minutes] = nextEvent.time.split(':').map(Number);\n        eventTime.setHours(hours, minutes, 0, 0);\n        const departureTime = new Date(eventTime.getTime() - 20 * 60 * 1000); // 提前20分钟\n        suggestedDepartureTime.value = departureTime.toLocaleTimeString('zh-CN', {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n      }\n    };\n    const handleEventClick = event => {\n      emit('event-click', event);\n    };\n    const startNavigation = event => {\n      if (!event.location) return;\n      emit('navigation-start', {\n        destination: event.location,\n        event: event\n      });\n    };\n    const toggleEventReminder = event => {\n      event.reminderEnabled = !event.reminderEnabled;\n      // 这里可以调用API保存提醒设置\n    };\n    const applySuggestion = async () => {\n      if (!aiOptimization.value) return;\n      try {\n        isProcessing.value = true;\n\n        // 模拟应用建议的过程\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        emit('suggestion-applied', aiOptimization.value);\n\n        // 重新加载数据\n        await loadScheduleData();\n      } catch (error) {\n        console.error('Failed to apply suggestion:', error);\n      } finally {\n        isProcessing.value = false;\n      }\n    };\n    const handleQuickAction = async action => {\n      emit('action-triggered', action);\n      switch (action.action) {\n        case 'aiOptimize':\n          await performAIOptimization();\n          break;\n        case 'addEvent':\n          // 触发添加事件对话框\n          break;\n        case 'weekView':\n          // 切换到周视图\n          break;\n      }\n    };\n    const performAIOptimization = async () => {\n      try {\n        isProcessing.value = true;\n\n        // 模拟AI优化过程\n        await new Promise(resolve => setTimeout(resolve, 2000));\n\n        // 生成新的优化建议\n        aiOptimization.value = {\n          suggestion: '建议将下午会议提前30分钟，避开交通高峰',\n          timeSaved: '15分钟',\n          confidence: 0.88\n        };\n      } catch (error) {\n        console.error('AI optimization failed:', error);\n      } finally {\n        isProcessing.value = false;\n      }\n    };\n\n    // 生命周期\n    let refreshTimer = null;\n    onMounted(async () => {\n      await mockDataService.initialize();\n      updateCurrentDate();\n      await loadScheduleData();\n\n      // 设置自动刷新\n      if (props.autoRefresh) {\n        refreshTimer = setInterval(() => {\n          updateCurrentDate();\n          updateTimeManagement();\n        }, props.refreshInterval);\n      }\n    });\n    onUnmounted(() => {\n      if (refreshTimer) {\n        clearInterval(refreshTimer);\n      }\n    });\n    return {\n      // 响应式数据\n      isLoading,\n      isProcessing,\n      todayEvents,\n      aiOptimization,\n      currentDate,\n      travelTimeToNext,\n      suggestedDepartureTime,\n      timeBuffer,\n      bufferPercentage,\n      quickActions,\n      // 计算属性\n      cardTitle,\n      // 方法\n      getTimeStatus,\n      handleEventClick,\n      startNavigation,\n      toggleEventReminder,\n      applySuggestion,\n      handleQuickAction\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "onUnmounted", "BaseCard", "mockDataService", "name", "components", "props", "size", "type", "String", "default", "validator", "value", "includes", "position", "Object", "x", "y", "theme", "themeColors", "displayMode", "showTimeManagement", "Boolean", "showQuickActions", "autoRefresh", "refreshInterval", "Number", "emits", "setup", "emit", "isLoading", "isProcessing", "todayEvents", "aiOptimization", "currentDate", "travelTimeToNext", "suggestedDepartureTime", "timeBuffer", "bufferPercentage", "quickActions", "id", "label", "icon", "action", "cardTitle", "upcomingCount", "filter", "e", "status", "length", "updateCurrentDate", "now", "Date", "toLocaleDateString", "month", "day", "weekday", "getTimeStatus", "event", "eventTime", "hours", "minutes", "time", "split", "map", "setHours", "timeDiff", "getTime", "minutesDiff", "Math", "floor", "loadScheduleData", "scheduleData", "getScheduleData", "updateTimeManagement", "error", "console", "upcomingEvents", "nextEvent", "departureTime", "toLocaleTimeString", "hour", "minute", "handleEventClick", "startNavigation", "location", "destination", "toggleEventReminder", "reminderEnabled", "applySuggestion", "Promise", "resolve", "setTimeout", "handleQuickAction", "performAIOptimization", "suggestion", "timeSaved", "confidence", "refreshTimer", "initialize", "setInterval", "clearInterval"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\AIScheduleAssistantCard.vue"], "sourcesContent": ["<template>\n  <BaseCard\n    :card-type=\"'ai-schedule'\"\n    :size=\"size\"\n    :position=\"position\"\n    :theme=\"theme\"\n    :theme-colors=\"themeColors\"\n    :show-header=\"true\"\n    :show-footer=\"false\"\n    :title=\"cardTitle\"\n    :icon=\"'fas fa-calendar-alt'\"\n    class=\"ai-schedule-assistant-card\"\n  >\n    <div class=\"schedule-container\" :class=\"[`size-${size}`, `mode-${displayMode}`]\">\n      <!-- AI优化建议横幅 -->\n      <div v-if=\"aiOptimization && aiOptimization.suggestion\" class=\"ai-optimization-banner\">\n        <div class=\"optimization-icon\">\n          <i class=\"fas fa-magic\"></i>\n        </div>\n        <div class=\"optimization-content\">\n          <div class=\"optimization-title\">AI智能建议</div>\n          <div class=\"optimization-text\">{{ aiOptimization.suggestion }}</div>\n          <div class=\"optimization-meta\">\n            <span class=\"time-saved\">节省 {{ aiOptimization.timeSaved }}</span>\n            <span class=\"confidence\">可信度 {{ Math.round(aiOptimization.confidence * 100) }}%</span>\n          </div>\n        </div>\n        <button class=\"apply-suggestion-btn\" @click=\"applySuggestion\">\n          <i class=\"fas fa-check\"></i>\n        </button>\n      </div>\n\n      <!-- 今日日程列表 -->\n      <div class=\"schedule-list\">\n        <div class=\"schedule-header\">\n          <h3 class=\"schedule-title\">今日日程</h3>\n          <div class=\"schedule-date\">{{ currentDate }}</div>\n        </div>\n        \n        <div class=\"events-container\">\n          <div \n            v-for=\"event in todayEvents\" \n            :key=\"event.id\"\n            :class=\"['event-item', `priority-${event.priority}`, `status-${event.status}`]\"\n            @click=\"handleEventClick(event)\"\n          >\n            <div class=\"event-time\">\n              <div class=\"time-text\">{{ event.time }}</div>\n              <div class=\"time-indicator\" :class=\"getTimeStatus(event)\"></div>\n            </div>\n            \n            <div class=\"event-content\">\n              <div class=\"event-title\">{{ event.title }}</div>\n              <div class=\"event-location\" v-if=\"event.location\">\n                <i class=\"fas fa-map-marker-alt\"></i>\n                <span>{{ event.location }}</span>\n              </div>\n              \n              <!-- AI建议 -->\n              <div v-if=\"event.aiSuggestion\" class=\"event-ai-suggestion\">\n                <i class=\"fas fa-lightbulb\"></i>\n                <span>{{ event.aiSuggestion }}</span>\n              </div>\n            </div>\n            \n            <div class=\"event-actions\">\n              <button \n                v-if=\"event.status === 'upcoming'\"\n                @click.stop=\"startNavigation(event)\"\n                class=\"nav-btn\"\n                :disabled=\"!event.location\"\n              >\n                <i class=\"fas fa-route\"></i>\n              </button>\n              \n              <button \n                @click.stop=\"toggleEventReminder(event)\"\n                :class=\"['reminder-btn', { active: event.reminderEnabled }]\"\n              >\n                <i class=\"fas fa-bell\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 智能时间管理 -->\n      <div class=\"time-management\" v-if=\"showTimeManagement\">\n        <div class=\"management-header\">\n          <i class=\"fas fa-clock\"></i>\n          <span>智能时间管理</span>\n        </div>\n        \n        <div class=\"management-content\">\n          <div class=\"travel-time-analysis\">\n            <div class=\"analysis-item\">\n              <span class=\"label\">当前位置到下个会议</span>\n              <span class=\"value\">{{ travelTimeToNext }}</span>\n            </div>\n            <div class=\"analysis-item\">\n              <span class=\"label\">建议出发时间</span>\n              <span class=\"value highlight\">{{ suggestedDepartureTime }}</span>\n            </div>\n          </div>\n          \n          <div class=\"time-buffer\">\n            <div class=\"buffer-info\">\n              <span>缓冲时间: {{ timeBuffer }}</span>\n              <div class=\"buffer-bar\">\n                <div class=\"buffer-fill\" :style=\"{ width: bufferPercentage + '%' }\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 快速操作 -->\n      <div class=\"quick-actions\" v-if=\"showQuickActions\">\n        <button \n          v-for=\"action in quickActions\" \n          :key=\"action.id\"\n          @click=\"handleQuickAction(action)\"\n          class=\"quick-action-btn\"\n          :disabled=\"isProcessing\"\n        >\n          <i :class=\"action.icon\"></i>\n          <span>{{ action.label }}</span>\n        </button>\n      </div>\n\n      <!-- 加载状态 -->\n      <div v-if=\"isLoading\" class=\"loading-overlay\">\n        <div class=\"loading-spinner\"></div>\n        <div class=\"loading-text\">正在优化您的日程...</div>\n      </div>\n    </div>\n  </BaseCard>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, onUnmounted } from 'vue'\nimport BaseCard from '@/components/cards/BaseCard.vue'\nimport mockDataService from '@/services/MockDataService.js'\n\nexport default {\n  name: 'AIScheduleAssistantCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    size: {\n      type: String,\n      default: 'medium',\n      validator: (value) => ['small', 'medium', 'large'].includes(value)\n    },\n    position: {\n      type: Object,\n      default: () => ({ x: 0, y: 0 })\n    },\n    theme: {\n      type: String,\n      default: 'glassmorphism'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({})\n    },\n    displayMode: {\n      type: String,\n      default: 'full',\n      validator: (value) => ['compact', 'standard', 'full'].includes(value)\n    },\n    showTimeManagement: {\n      type: Boolean,\n      default: true\n    },\n    showQuickActions: {\n      type: Boolean,\n      default: true\n    },\n    autoRefresh: {\n      type: Boolean,\n      default: true\n    },\n    refreshInterval: {\n      type: Number,\n      default: 60000 // 1分钟\n    }\n  },\n  emits: ['event-click', 'navigation-start', 'suggestion-applied', 'action-triggered'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const isLoading = ref(false)\n    const isProcessing = ref(false)\n    const todayEvents = ref([])\n    const aiOptimization = ref(null)\n    const currentDate = ref('')\n    const travelTimeToNext = ref('--')\n    const suggestedDepartureTime = ref('--')\n    const timeBuffer = ref('5分钟')\n    const bufferPercentage = ref(75)\n    \n    // 快速操作\n    const quickActions = ref([\n      {\n        id: 1,\n        label: '添加事件',\n        icon: 'fas fa-plus',\n        action: 'addEvent'\n      },\n      {\n        id: 2,\n        label: '查看周视图',\n        icon: 'fas fa-calendar-week',\n        action: 'weekView'\n      },\n      {\n        id: 3,\n        label: 'AI优化',\n        icon: 'fas fa-magic',\n        action: 'aiOptimize'\n      }\n    ])\n\n    // 计算属性\n    const cardTitle = computed(() => {\n      const upcomingCount = todayEvents.value.filter(e => e.status === 'upcoming').length\n      return upcomingCount > 0 ? `日程助手 (${upcomingCount}个待办)` : '日程助手'\n    })\n\n    // 方法\n    const updateCurrentDate = () => {\n      const now = new Date()\n      currentDate.value = now.toLocaleDateString('zh-CN', {\n        month: 'long',\n        day: 'numeric',\n        weekday: 'long'\n      })\n    }\n\n    const getTimeStatus = (event) => {\n      const now = new Date()\n      const eventTime = new Date()\n      const [hours, minutes] = event.time.split(':').map(Number)\n      eventTime.setHours(hours, minutes, 0, 0)\n      \n      const timeDiff = eventTime.getTime() - now.getTime()\n      const minutesDiff = Math.floor(timeDiff / (1000 * 60))\n      \n      if (minutesDiff < 0) return 'past'\n      if (minutesDiff <= 15) return 'imminent'\n      if (minutesDiff <= 60) return 'soon'\n      return 'future'\n    }\n\n    const loadScheduleData = async () => {\n      try {\n        isLoading.value = true\n        const scheduleData = await mockDataService.getScheduleData()\n        \n        todayEvents.value = scheduleData.todayEvents || []\n        aiOptimization.value = scheduleData.aiOptimization || null\n        \n        // 更新时间管理信息\n        updateTimeManagement()\n        \n      } catch (error) {\n        console.error('Failed to load schedule data:', error)\n      } finally {\n        isLoading.value = false\n      }\n    }\n\n    const updateTimeManagement = () => {\n      const upcomingEvents = todayEvents.value.filter(e => e.status === 'upcoming')\n      if (upcomingEvents.length > 0) {\n        const nextEvent = upcomingEvents[0]\n        \n        // 模拟计算行程时间\n        travelTimeToNext.value = '15分钟'\n        \n        // 计算建议出发时间\n        const eventTime = new Date()\n        const [hours, minutes] = nextEvent.time.split(':').map(Number)\n        eventTime.setHours(hours, minutes, 0, 0)\n        \n        const departureTime = new Date(eventTime.getTime() - 20 * 60 * 1000) // 提前20分钟\n        suggestedDepartureTime.value = departureTime.toLocaleTimeString('zh-CN', {\n          hour: '2-digit',\n          minute: '2-digit'\n        })\n      }\n    }\n\n    const handleEventClick = (event) => {\n      emit('event-click', event)\n    }\n\n    const startNavigation = (event) => {\n      if (!event.location) return\n      \n      emit('navigation-start', {\n        destination: event.location,\n        event: event\n      })\n    }\n\n    const toggleEventReminder = (event) => {\n      event.reminderEnabled = !event.reminderEnabled\n      // 这里可以调用API保存提醒设置\n    }\n\n    const applySuggestion = async () => {\n      if (!aiOptimization.value) return\n      \n      try {\n        isProcessing.value = true\n        \n        // 模拟应用建议的过程\n        await new Promise(resolve => setTimeout(resolve, 1000))\n        \n        emit('suggestion-applied', aiOptimization.value)\n        \n        // 重新加载数据\n        await loadScheduleData()\n        \n      } catch (error) {\n        console.error('Failed to apply suggestion:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n\n    const handleQuickAction = async (action) => {\n      emit('action-triggered', action)\n      \n      switch (action.action) {\n        case 'aiOptimize':\n          await performAIOptimization()\n          break\n        case 'addEvent':\n          // 触发添加事件对话框\n          break\n        case 'weekView':\n          // 切换到周视图\n          break\n      }\n    }\n\n    const performAIOptimization = async () => {\n      try {\n        isProcessing.value = true\n        \n        // 模拟AI优化过程\n        await new Promise(resolve => setTimeout(resolve, 2000))\n        \n        // 生成新的优化建议\n        aiOptimization.value = {\n          suggestion: '建议将下午会议提前30分钟，避开交通高峰',\n          timeSaved: '15分钟',\n          confidence: 0.88\n        }\n        \n      } catch (error) {\n        console.error('AI optimization failed:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n\n    // 生命周期\n    let refreshTimer = null\n    \n    onMounted(async () => {\n      await mockDataService.initialize()\n      updateCurrentDate()\n      await loadScheduleData()\n      \n      // 设置自动刷新\n      if (props.autoRefresh) {\n        refreshTimer = setInterval(() => {\n          updateCurrentDate()\n          updateTimeManagement()\n        }, props.refreshInterval)\n      }\n    })\n\n    onUnmounted(() => {\n      if (refreshTimer) {\n        clearInterval(refreshTimer)\n      }\n    })\n\n    return {\n      // 响应式数据\n      isLoading,\n      isProcessing,\n      todayEvents,\n      aiOptimization,\n      currentDate,\n      travelTimeToNext,\n      suggestedDepartureTime,\n      timeBuffer,\n      bufferPercentage,\n      quickActions,\n      \n      // 计算属性\n      cardTitle,\n      \n      // 方法\n      getTimeStatus,\n      handleEventClick,\n      startNavigation,\n      toggleEventReminder,\n      applySuggestion,\n      handleQuickAction\n    }\n  }\n}\n</script>\n\n<style scoped>\n.ai-schedule-assistant-card {\n  height: 100%;\n}\n\n.schedule-container {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  padding: 16px;\n  position: relative;\n}\n\n/* AI优化建议横幅 */\n.ai-optimization-banner {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background: linear-gradient(135deg, rgba(74, 144, 226, 0.2) 0%, rgba(126, 211, 33, 0.2) 100%);\n  border-radius: 12px;\n  border: 1px solid rgba(74, 144, 226, 0.3);\n  backdrop-filter: blur(10px);\n}\n\n.optimization-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #4a90e2 0%, #7ed321 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 16px;\n  flex-shrink: 0;\n}\n\n.optimization-content {\n  flex: 1;\n}\n\n.optimization-title {\n  font-size: 12px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 4px;\n}\n\n.optimization-text {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.8);\n  line-height: 1.4;\n  margin-bottom: 6px;\n}\n\n.optimization-meta {\n  display: flex;\n  gap: 12px;\n  font-size: 11px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.time-saved {\n  color: #7ed321;\n}\n\n.confidence {\n  color: #4a90e2;\n}\n\n.apply-suggestion-btn {\n  width: 36px;\n  height: 36px;\n  border: none;\n  border-radius: 8px;\n  background: rgba(126, 211, 33, 0.3);\n  color: #7ed321;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.apply-suggestion-btn:hover {\n  background: rgba(126, 211, 33, 0.5);\n  transform: scale(1.05);\n}\n\n/* 日程列表 */\n.schedule-list {\n  flex: 1;\n  min-height: 0;\n}\n\n.schedule-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.schedule-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n  margin: 0;\n}\n\n.schedule-date {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.events-container {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.event-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 12px;\n  border-left: 4px solid transparent;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.event-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateX(2px);\n}\n\n.event-item.priority-high {\n  border-left-color: #ef4444;\n}\n\n.event-item.priority-medium {\n  border-left-color: #f59e0b;\n}\n\n.event-item.priority-low {\n  border-left-color: #10b981;\n}\n\n.event-item.status-past {\n  opacity: 0.6;\n}\n\n.event-time {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 4px;\n  min-width: 60px;\n}\n\n.time-text {\n  font-size: 14px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.time-indicator {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.3);\n}\n\n.time-indicator.imminent {\n  background: #ef4444;\n  animation: pulse 1.5s infinite;\n}\n\n.time-indicator.soon {\n  background: #f59e0b;\n}\n\n.time-indicator.future {\n  background: #10b981;\n}\n\n.time-indicator.past {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.event-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.event-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.event-location {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.event-ai-suggestion {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 11px;\n  color: #7ed321;\n  background: rgba(126, 211, 33, 0.1);\n  padding: 4px 8px;\n  border-radius: 6px;\n  margin-top: 4px;\n}\n\n.event-actions {\n  display: flex;\n  gap: 4px;\n}\n\n.nav-btn,\n.reminder-btn {\n  width: 28px;\n  height: 28px;\n  border: none;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0.2);\n  color: rgba(255, 255, 255, 0.7);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n}\n\n.nav-btn:hover,\n.reminder-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.reminder-btn.active {\n  background: rgba(74, 144, 226, 0.3);\n  color: #4a90e2;\n}\n\n/* 智能时间管理 */\n.time-management {\n  padding: 12px;\n  background: rgba(0, 0, 0, 0.1);\n  border-radius: 12px;\n}\n\n.management-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 12px;\n}\n\n.management-content {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.travel-time-analysis {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n}\n\n.analysis-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 12px;\n}\n\n.analysis-item .label {\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.analysis-item .value {\n  color: rgba(255, 255, 255, 0.9);\n  font-weight: 500;\n}\n\n.analysis-item .value.highlight {\n  color: #7ed321;\n}\n\n.time-buffer {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n}\n\n.buffer-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.buffer-bar {\n  height: 4px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 2px;\n  overflow: hidden;\n}\n\n.buffer-fill {\n  height: 100%;\n  background: linear-gradient(90deg, #7ed321 0%, #4a90e2 100%);\n  transition: width 0.3s ease;\n}\n\n/* 快速操作 */\n.quick-actions {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.quick-action-btn {\n  flex: 1;\n  min-width: 80px;\n  padding: 8px 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border: none;\n  border-radius: 8px;\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 6px;\n}\n\n.quick-action-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n  transform: translateY(-1px);\n}\n\n.quick-action-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* 加载状态 */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.3);\n  backdrop-filter: blur(5px);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 12px;\n  border-radius: 12px;\n}\n\n.loading-spinner {\n  width: 32px;\n  height: 32px;\n  border: 3px solid rgba(255, 255, 255, 0.2);\n  border-top: 3px solid #4a90e2;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.loading-text {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* 动画 */\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n}\n\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* 响应式设计 */\n.size-small .schedule-container {\n  padding: 12px;\n  gap: 12px;\n}\n\n.size-small .event-item {\n  padding: 8px;\n}\n\n.size-small .optimization-banner {\n  padding: 8px;\n}\n\n.size-large .schedule-container {\n  padding: 20px;\n  gap: 20px;\n}\n\n.mode-compact .events-container {\n  max-height: 200px;\n}\n\n.mode-compact .time-management,\n.mode-compact .quick-actions {\n  display: none;\n}\n</style>"], "mappings": ";;;AA4IA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAU,QAAS,KAAI;AACpE,OAAOC,QAAO,MAAO,iCAAgC;AACrD,OAAOC,eAAc,MAAO,+BAA8B;AAE1D,eAAe;EACbC,IAAI,EAAE,yBAAyB;EAC/BC,UAAU,EAAE;IACVH;EACF,CAAC;EACDI,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBC,SAAS,EAAGC,KAAK,IAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAACC,QAAQ,CAACD,KAAK;IACnE,CAAC;IACDE,QAAQ,EAAE;MACRN,IAAI,EAAEO,MAAM;MACZL,OAAO,EAAEA,CAAA,MAAO;QAAEM,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;IAChC,CAAC;IACDC,KAAK,EAAE;MACLV,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDS,WAAW,EAAE;MACXX,IAAI,EAAEO,MAAM;MACZL,OAAO,EAAEA,CAAA,MAAO,CAAC,CAAC;IACpB,CAAC;IACDU,WAAW,EAAE;MACXZ,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAGC,KAAK,IAAK,CAAC,SAAS,EAAE,UAAU,EAAE,MAAM,CAAC,CAACC,QAAQ,CAACD,KAAK;IACtE,CAAC;IACDS,kBAAkB,EAAE;MAClBb,IAAI,EAAEc,OAAO;MACbZ,OAAO,EAAE;IACX,CAAC;IACDa,gBAAgB,EAAE;MAChBf,IAAI,EAAEc,OAAO;MACbZ,OAAO,EAAE;IACX,CAAC;IACDc,WAAW,EAAE;MACXhB,IAAI,EAAEc,OAAO;MACbZ,OAAO,EAAE;IACX,CAAC;IACDe,eAAe,EAAE;MACfjB,IAAI,EAAEkB,MAAM;MACZhB,OAAO,EAAE,KAAI,CAAE;IACjB;EACF,CAAC;EACDiB,KAAK,EAAE,CAAC,aAAa,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,kBAAkB,CAAC;EACpFC,KAAKA,CAACtB,KAAK,EAAE;IAAEuB;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,SAAQ,GAAIjC,GAAG,CAAC,KAAK;IAC3B,MAAMkC,YAAW,GAAIlC,GAAG,CAAC,KAAK;IAC9B,MAAMmC,WAAU,GAAInC,GAAG,CAAC,EAAE;IAC1B,MAAMoC,cAAa,GAAIpC,GAAG,CAAC,IAAI;IAC/B,MAAMqC,WAAU,GAAIrC,GAAG,CAAC,EAAE;IAC1B,MAAMsC,gBAAe,GAAItC,GAAG,CAAC,IAAI;IACjC,MAAMuC,sBAAqB,GAAIvC,GAAG,CAAC,IAAI;IACvC,MAAMwC,UAAS,GAAIxC,GAAG,CAAC,KAAK;IAC5B,MAAMyC,gBAAe,GAAIzC,GAAG,CAAC,EAAE;;IAE/B;IACA,MAAM0C,YAAW,GAAI1C,GAAG,CAAC,CACvB;MACE2C,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,aAAa;MACnBC,MAAM,EAAE;IACV,CAAC,EACD;MACEH,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE,sBAAsB;MAC5BC,MAAM,EAAE;IACV,CAAC,EACD;MACEH,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,cAAc;MACpBC,MAAM,EAAE;IACV,EACD;;IAED;IACA,MAAMC,SAAQ,GAAI7C,QAAQ,CAAC,MAAM;MAC/B,MAAM8C,aAAY,GAAIb,WAAW,CAACpB,KAAK,CAACkC,MAAM,CAACC,CAAA,IAAKA,CAAC,CAACC,MAAK,KAAM,UAAU,CAAC,CAACC,MAAK;MAClF,OAAOJ,aAAY,GAAI,IAAI,SAASA,aAAa,MAAK,GAAI,MAAK;IACjE,CAAC;;IAED;IACA,MAAMK,iBAAgB,GAAIA,CAAA,KAAM;MAC9B,MAAMC,GAAE,GAAI,IAAIC,IAAI,CAAC;MACrBlB,WAAW,CAACtB,KAAI,GAAIuC,GAAG,CAACE,kBAAkB,CAAC,OAAO,EAAE;QAClDC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE,SAAS;QACdC,OAAO,EAAE;MACX,CAAC;IACH;IAEA,MAAMC,aAAY,GAAKC,KAAK,IAAK;MAC/B,MAAMP,GAAE,GAAI,IAAIC,IAAI,CAAC;MACrB,MAAMO,SAAQ,GAAI,IAAIP,IAAI,CAAC;MAC3B,MAAM,CAACQ,KAAK,EAAEC,OAAO,IAAIH,KAAK,CAACI,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACtC,MAAM;MACzDiC,SAAS,CAACM,QAAQ,CAACL,KAAK,EAAEC,OAAO,EAAE,CAAC,EAAE,CAAC;MAEvC,MAAMK,QAAO,GAAIP,SAAS,CAACQ,OAAO,CAAC,IAAIhB,GAAG,CAACgB,OAAO,CAAC;MACnD,MAAMC,WAAU,GAAIC,IAAI,CAACC,KAAK,CAACJ,QAAO,IAAK,IAAG,GAAI,EAAE,CAAC;MAErD,IAAIE,WAAU,GAAI,CAAC,EAAE,OAAO,MAAK;MACjC,IAAIA,WAAU,IAAK,EAAE,EAAE,OAAO,UAAS;MACvC,IAAIA,WAAU,IAAK,EAAE,EAAE,OAAO,MAAK;MACnC,OAAO,QAAO;IAChB;IAEA,MAAMG,gBAAe,GAAI,MAAAA,CAAA,KAAY;MACnC,IAAI;QACFzC,SAAS,CAAClB,KAAI,GAAI,IAAG;QACrB,MAAM4D,YAAW,GAAI,MAAMrE,eAAe,CAACsE,eAAe,CAAC;QAE3DzC,WAAW,CAACpB,KAAI,GAAI4D,YAAY,CAACxC,WAAU,IAAK,EAAC;QACjDC,cAAc,CAACrB,KAAI,GAAI4D,YAAY,CAACvC,cAAa,IAAK,IAAG;;QAEzD;QACAyC,oBAAoB,CAAC;MAEvB,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK;MACtD,UAAU;QACR7C,SAAS,CAAClB,KAAI,GAAI,KAAI;MACxB;IACF;IAEA,MAAM8D,oBAAmB,GAAIA,CAAA,KAAM;MACjC,MAAMG,cAAa,GAAI7C,WAAW,CAACpB,KAAK,CAACkC,MAAM,CAACC,CAAA,IAAKA,CAAC,CAACC,MAAK,KAAM,UAAU;MAC5E,IAAI6B,cAAc,CAAC5B,MAAK,GAAI,CAAC,EAAE;QAC7B,MAAM6B,SAAQ,GAAID,cAAc,CAAC,CAAC;;QAElC;QACA1C,gBAAgB,CAACvB,KAAI,GAAI,MAAK;;QAE9B;QACA,MAAM+C,SAAQ,GAAI,IAAIP,IAAI,CAAC;QAC3B,MAAM,CAACQ,KAAK,EAAEC,OAAO,IAAIiB,SAAS,CAAChB,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACtC,MAAM;QAC7DiC,SAAS,CAACM,QAAQ,CAACL,KAAK,EAAEC,OAAO,EAAE,CAAC,EAAE,CAAC;QAEvC,MAAMkB,aAAY,GAAI,IAAI3B,IAAI,CAACO,SAAS,CAACQ,OAAO,CAAC,IAAI,EAAC,GAAI,EAAC,GAAI,IAAI,GAAE;QACrE/B,sBAAsB,CAACxB,KAAI,GAAImE,aAAa,CAACC,kBAAkB,CAAC,OAAO,EAAE;UACvEC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;QACV,CAAC;MACH;IACF;IAEA,MAAMC,gBAAe,GAAKzB,KAAK,IAAK;MAClC7B,IAAI,CAAC,aAAa,EAAE6B,KAAK;IAC3B;IAEA,MAAM0B,eAAc,GAAK1B,KAAK,IAAK;MACjC,IAAI,CAACA,KAAK,CAAC2B,QAAQ,EAAE;MAErBxD,IAAI,CAAC,kBAAkB,EAAE;QACvByD,WAAW,EAAE5B,KAAK,CAAC2B,QAAQ;QAC3B3B,KAAK,EAAEA;MACT,CAAC;IACH;IAEA,MAAM6B,mBAAkB,GAAK7B,KAAK,IAAK;MACrCA,KAAK,CAAC8B,eAAc,GAAI,CAAC9B,KAAK,CAAC8B,eAAc;MAC7C;IACF;IAEA,MAAMC,eAAc,GAAI,MAAAA,CAAA,KAAY;MAClC,IAAI,CAACxD,cAAc,CAACrB,KAAK,EAAE;MAE3B,IAAI;QACFmB,YAAY,CAACnB,KAAI,GAAI,IAAG;;QAExB;QACA,MAAM,IAAI8E,OAAO,CAACC,OAAM,IAAKC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC;QAEtD9D,IAAI,CAAC,oBAAoB,EAAEI,cAAc,CAACrB,KAAK;;QAE/C;QACA,MAAM2D,gBAAgB,CAAC;MAEzB,EAAE,OAAOI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK;MACpD,UAAU;QACR5C,YAAY,CAACnB,KAAI,GAAI,KAAI;MAC3B;IACF;IAEA,MAAMiF,iBAAgB,GAAI,MAAOlD,MAAM,IAAK;MAC1Cd,IAAI,CAAC,kBAAkB,EAAEc,MAAM;MAE/B,QAAQA,MAAM,CAACA,MAAM;QACnB,KAAK,YAAY;UACf,MAAMmD,qBAAqB,CAAC;UAC5B;QACF,KAAK,UAAU;UACb;UACA;QACF,KAAK,UAAU;UACb;UACA;MACJ;IACF;IAEA,MAAMA,qBAAoB,GAAI,MAAAA,CAAA,KAAY;MACxC,IAAI;QACF/D,YAAY,CAACnB,KAAI,GAAI,IAAG;;QAExB;QACA,MAAM,IAAI8E,OAAO,CAACC,OAAM,IAAKC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC;;QAEtD;QACA1D,cAAc,CAACrB,KAAI,GAAI;UACrBmF,UAAU,EAAE,sBAAsB;UAClCC,SAAS,EAAE,MAAM;UACjBC,UAAU,EAAE;QACd;MAEF,EAAE,OAAOtB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK;MAChD,UAAU;QACR5C,YAAY,CAACnB,KAAI,GAAI,KAAI;MAC3B;IACF;;IAEA;IACA,IAAIsF,YAAW,GAAI,IAAG;IAEtBlG,SAAS,CAAC,YAAY;MACpB,MAAMG,eAAe,CAACgG,UAAU,CAAC;MACjCjD,iBAAiB,CAAC;MAClB,MAAMqB,gBAAgB,CAAC;;MAEvB;MACA,IAAIjE,KAAK,CAACkB,WAAW,EAAE;QACrB0E,YAAW,GAAIE,WAAW,CAAC,MAAM;UAC/BlD,iBAAiB,CAAC;UAClBwB,oBAAoB,CAAC;QACvB,CAAC,EAAEpE,KAAK,CAACmB,eAAe;MAC1B;IACF,CAAC;IAEDxB,WAAW,CAAC,MAAM;MAChB,IAAIiG,YAAY,EAAE;QAChBG,aAAa,CAACH,YAAY;MAC5B;IACF,CAAC;IAED,OAAO;MACL;MACApE,SAAS;MACTC,YAAY;MACZC,WAAW;MACXC,cAAc;MACdC,WAAW;MACXC,gBAAgB;MAChBC,sBAAsB;MACtBC,UAAU;MACVC,gBAAgB;MAChBC,YAAY;MAEZ;MACAK,SAAS;MAET;MACAa,aAAa;MACb0B,gBAAgB;MAChBC,eAAe;MACfG,mBAAmB;MACnBE,eAAe;MACfI;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}