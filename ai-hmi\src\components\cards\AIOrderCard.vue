<template>
  <BaseCard
    :card-type="'ai-order'"
    :size="size"
    :position="position"
    :theme="theme"
    :theme-colors="themeColors"
    :show-header="true"
    :show-footer="false"
    :title="cardTitle"
    :icon="'fas fa-shopping-cart'"
    class="ai-order-card"
  >
    <div class="order-container" :class="[`size-${size}`, `mode-${displayMode}`]">
      <!-- 当前订单状态 -->
      <div class="current-orders" v-if="currentOrders.length > 0">
        <div class="orders-header">
          <h3>进行中的订单</h3>
          <div class="orders-count">{{ currentOrders.length }}个</div>
        </div>
        
        <div class="orders-list">
          <div 
            v-for="order in currentOrders" 
            :key="order.id"
            :class="['order-item', `status-${order.status}`]"
            @click="viewOrderDetails(order)"
          >
            <div class="order-icon">
              <i :class="getOrderIcon(order.type)"></i>
            </div>
            
            <div class="order-info">
              <div class="order-title">{{ order.title }}</div>
              <div class="order-subtitle">{{ order.subtitle }}</div>
              <div class="order-status-text">{{ getStatusText(order.status) }}</div>
            </div>
            
            <div class="order-meta">
              <div class="order-time">{{ order.estimatedTime }}</div>
              <div class="order-price" v-if="order.price">¥{{ order.price }}</div>
            </div>
            
            <div class="order-actions">
              <button 
                v-if="order.trackable"
                @click.stop="trackOrder(order)"
                class="track-btn"
              >
                <i class="fas fa-map-marker-alt"></i>
              </button>
              <button 
                v-if="order.contactable"
                @click.stop="contactMerchant(order)"
                class="contact-btn"
              >
                <i class="fas fa-phone"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- AI推荐订单 -->
      <div class="ai-recommendations" v-if="aiRecommendations.length > 0">
        <div class="recommendations-header">
          <div class="header-content">
            <i class="fas fa-magic"></i>
            <span>AI智能推荐</span>
          </div>
          <div class="recommendation-reason">{{ recommendationReason }}</div>
        </div>
        
        <div class="recommendations-list">
          <div 
            v-for="recommendation in aiRecommendations" 
            :key="recommendation.id"
            class="recommendation-item"
            @click="viewRecommendation(recommendation)"
          >
            <div class="recommendation-image">
              <img :src="recommendation.image" :alt="recommendation.title" />
              <div class="recommendation-badge" v-if="recommendation.badge">
                {{ recommendation.badge }}
              </div>
            </div>
            
            <div class="recommendation-content">
              <div class="recommendation-title">{{ recommendation.title }}</div>
              <div class="recommendation-description">{{ recommendation.description }}</div>
              
              <div class="recommendation-details">
                <div class="detail-item">
                  <i class="fas fa-star"></i>
                  <span>{{ recommendation.rating }}</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-clock"></i>
                  <span>{{ recommendation.deliveryTime }}</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-shipping-fast"></i>
                  <span>{{ recommendation.shippingFee }}</span>
                </div>
              </div>
              
              <div class="recommendation-price">
                <span class="current-price">¥{{ recommendation.price }}</span>
                <span class="original-price" v-if="recommendation.originalPrice">
                  ¥{{ recommendation.originalPrice }}
                </span>
                <span class="discount" v-if="recommendation.discount">
                  {{ recommendation.discount }}折
                </span>
              </div>
            </div>
            
            <div class="recommendation-actions">
              <button 
                @click.stop="quickOrder(recommendation)"
                class="quick-order-btn"
                :disabled="isProcessing"
              >
                <i class="fas fa-plus"></i>
                <span>快速下单</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速订单 -->
      <div class="quick-orders" v-if="showQuickOrders">
        <div class="quick-orders-header">
          <h3>快速订单</h3>
          <button @click="toggleQuickOrdersExpanded" class="expand-btn">
            <i :class="quickOrdersExpanded ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
          </button>
        </div>
        
        <div class="quick-orders-grid" v-show="quickOrdersExpanded">
          <button 
            v-for="quickOrder in quickOrderTypes" 
            :key="quickOrder.id"
            @click="startQuickOrder(quickOrder)"
            class="quick-order-type"
            :disabled="isProcessing"
          >
            <div class="quick-order-icon">
              <i :class="quickOrder.icon"></i>
            </div>
            <div class="quick-order-label">{{ quickOrder.label }}</div>
            <div class="quick-order-subtitle">{{ quickOrder.subtitle }}</div>
          </button>
        </div>
      </div>

      <!-- 订单历史 -->
      <div class="order-history" v-if="showOrderHistory && recentOrders.length > 0">
        <div class="history-header">
          <h3>最近订单</h3>
          <button @click="viewAllOrders" class="view-all-btn">
            查看全部
          </button>
        </div>
        
        <div class="history-list">
          <div 
            v-for="order in recentOrders.slice(0, 3)" 
            :key="order.id"
            class="history-item"
            @click="reorderItem(order)"
          >
            <div class="history-icon">
              <i :class="getOrderIcon(order.type)"></i>
            </div>
            
            <div class="history-info">
              <div class="history-title">{{ order.title }}</div>
              <div class="history-date">{{ formatDate(order.date) }}</div>
            </div>
            
            <div class="history-actions">
              <button 
                @click.stop="reorderItem(order)"
                class="reorder-btn"
                :disabled="isProcessing"
              >
                <i class="fas fa-redo"></i>
                <span>再来一单</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 优惠券和活动 -->
      <div class="promotions" v-if="promotions.length > 0">
        <div class="promotions-header">
          <i class="fas fa-gift"></i>
          <span>优惠活动</span>
        </div>
        
        <div class="promotions-list">
          <div 
            v-for="promotion in promotions" 
            :key="promotion.id"
            :class="['promotion-item', `type-${promotion.type}`]"
            @click="usePromotion(promotion)"
          >
            <div class="promotion-icon">
              <i :class="promotion.icon"></i>
            </div>
            
            <div class="promotion-content">
              <div class="promotion-title">{{ promotion.title }}</div>
              <div class="promotion-description">{{ promotion.description }}</div>
              <div class="promotion-validity">{{ promotion.validity }}</div>
            </div>
            
            <div class="promotion-value">
              <div class="value-text">{{ promotion.value }}</div>
              <div class="value-type">{{ promotion.valueType }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 智能提醒 -->
      <div class="smart-reminders" v-if="smartReminders.length > 0">
        <div class="reminders-header">
          <i class="fas fa-bell"></i>
          <span>智能提醒</span>
        </div>
        
        <div class="reminders-list">
          <div 
            v-for="reminder in smartReminders" 
            :key="reminder.id"
            :class="['reminder-item', `priority-${reminder.priority}`]"
          >
            <div class="reminder-icon">
              <i :class="reminder.icon"></i>
            </div>
            
            <div class="reminder-content">
              <div class="reminder-text">{{ reminder.text }}</div>
              <div class="reminder-time">{{ reminder.time }}</div>
            </div>
            
            <div class="reminder-actions">
              <button 
                v-if="reminder.actionable"
                @click="handleReminderAction(reminder)"
                class="reminder-action-btn"
              >
                {{ reminder.actionText }}
              </button>
              <button 
                @click="dismissReminder(reminder)"
                class="dismiss-btn"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 无订单状态 -->
      <div v-if="!hasAnyContent" class="no-orders">
        <div class="no-orders-icon">
          <i class="fas fa-shopping-bag"></i>
        </div>
        <div class="no-orders-text">
          <h3>暂无订单</h3>
          <p>开始您的第一个订单吧</p>
        </div>
        <div class="start-ordering">
          <button @click="startBrowsing" class="start-browse-btn">
            <i class="fas fa-search"></i>
            <span>开始浏览</span>
          </button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">{{ loadingText }}</div>
      </div>
    </div>
  </BaseCard>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import BaseCard from '@/components/cards/BaseCard.vue'
import mockDataService from '@/services/MockDataService.js'

export default {
  name: 'AIOrderCard',
  components: {
    BaseCard
  },
  props: {
    size: {
      type: String,
      default: 'large',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    position: {
      type: Object,
      default: () => ({ x: 0, y: 0 })
    },
    theme: {
      type: String,
      default: 'glassmorphism'
    },
    themeColors: {
      type: Object,
      default: () => ({})
    },
    displayMode: {
      type: String,
      default: 'full',
      validator: (value) => ['compact', 'standard', 'full'].includes(value)
    },
    showQuickOrders: {
      type: Boolean,
      default: true
    },
    showOrderHistory: {
      type: Boolean,
      default: true
    },
    autoRefresh: {
      type: Boolean,
      default: true
    },
    refreshInterval: {
      type: Number,
      default: 60000 // 60秒
    }
  },
  emits: ['order-placed', 'order-tracked', 'promotion-used', 'recommendation-viewed'],
  setup(props, { emit }) {
    // 响应式数据
    const isLoading = ref(false)
    const isProcessing = ref(false)
    const loadingText = ref('正在加载订单信息...')
    const quickOrdersExpanded = ref(true)
    
    const currentOrders = ref([])
    const aiRecommendations = ref([])
    const recentOrders = ref([])
    const promotions = ref([])
    const smartReminders = ref([])
    const recommendationReason = ref('')
    
    const quickOrderTypes = ref([
      {
        id: 1,
        label: '外卖',
        subtitle: '美食配送',
        icon: 'fas fa-utensils',
        type: 'food'
      },
      {
        id: 2,
        label: '购物',
        subtitle: '日用百货',
        icon: 'fas fa-shopping-cart',
        type: 'shopping'
      },
      {
        id: 3,
        label: '打车',
        subtitle: '出行服务',
        icon: 'fas fa-car',
        type: 'ride'
      },
      {
        id: 4,
        label: '快递',
        subtitle: '同城配送',
        icon: 'fas fa-shipping-fast',
        type: 'delivery'
      },
      {
        id: 5,
        label: '服务',
        subtitle: '生活服务',
        icon: 'fas fa-concierge-bell',
        type: 'service'
      },
      {
        id: 6,
        label: '更多',
        subtitle: '全部分类',
        icon: 'fas fa-th',
        type: 'more'
      }
    ])

    // 计算属性
    const cardTitle = computed(() => {
      const activeCount = currentOrders.value.length
      if (activeCount > 0) {
        return `订单管理 (${activeCount}个进行中)`
      }
      return 'AI订单助手'
    })
    
    const hasAnyContent = computed(() => {
      return currentOrders.value.length > 0 || 
             aiRecommendations.value.length > 0 || 
             recentOrders.value.length > 0 || 
             promotions.value.length > 0 || 
             smartReminders.value.length > 0
    })

    // 方法
    const loadOrderData = async () => {
      try {
        isLoading.value = true
        loadingText.value = '正在加载订单信息...'
        
        const orderData = await mockDataService.getOrderData()
        
        currentOrders.value = orderData.currentOrders || []
        aiRecommendations.value = orderData.aiRecommendations || []
        recentOrders.value = orderData.recentOrders || []
        promotions.value = orderData.promotions || []
        smartReminders.value = orderData.smartReminders || []
        recommendationReason.value = orderData.recommendationReason || ''
        
      } catch (error) {
        console.error('Failed to load order data:', error)
      } finally {
        isLoading.value = false
      }
    }
    
    const getOrderIcon = (type) => {
      const icons = {
        food: 'fas fa-utensils',
        shopping: 'fas fa-shopping-bag',
        ride: 'fas fa-car',
        delivery: 'fas fa-shipping-fast',
        service: 'fas fa-concierge-bell',
        grocery: 'fas fa-apple-alt',
        pharmacy: 'fas fa-pills',
        gas: 'fas fa-gas-pump'
      }
      return icons[type] || 'fas fa-shopping-cart'
    }
    
    const getStatusText = (status) => {
      const statusTexts = {
        pending: '待确认',
        confirmed: '已确认',
        preparing: '准备中',
        shipping: '配送中',
        delivered: '已送达',
        completed: '已完成',
        cancelled: '已取消'
      }
      return statusTexts[status] || '未知状态'
    }
    
    const formatDate = (dateString) => {
      const date = new Date(dateString)
      const now = new Date()
      const diffTime = Math.abs(now - date)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      
      if (diffDays === 1) {
        return '昨天'
      } else if (diffDays <= 7) {
        return `${diffDays}天前`
      } else {
        return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
      }
    }
    
    const viewOrderDetails = (order) => {
      console.log('Viewing order details:', order)
      // 实际应用中会打开订单详情页面
    }
    
    const trackOrder = (order) => {
      console.log('Tracking order:', order)
      // 实际应用中会打开订单跟踪页面
    }
    
    const contactMerchant = (order) => {
      console.log('Contacting merchant for order:', order)
      // 实际应用中会打开联系商家功能
    }
    
    const viewRecommendation = (recommendation) => {
      console.log('Viewing recommendation:', recommendation)
      emit('recommendation-viewed', recommendation)
      // 实际应用中会打开商品详情页面
    }
    
    const quickOrder = async (recommendation) => {
      try {
        isProcessing.value = true
        
        const orderResult = await mockDataService.placeQuickOrder(recommendation)
        
        // 添加到当前订单列表
        currentOrders.value.unshift(orderResult)
        
        emit('order-placed', orderResult)
        
        // 重新加载数据以获取更新的推荐
        await loadOrderData()
        
      } catch (error) {
        console.error('Failed to place quick order:', error)
      } finally {
        isProcessing.value = false
      }
    }
    
    const toggleQuickOrdersExpanded = () => {
      quickOrdersExpanded.value = !quickOrdersExpanded.value
    }
    
    const startQuickOrder = (quickOrderType) => {
      console.log('Starting quick order:', quickOrderType)
      // 实际应用中会打开对应的订单页面
    }
    
    const viewAllOrders = () => {
      console.log('Viewing all orders')
      // 实际应用中会打开订单历史页面
    }
    
    const reorderItem = async (order) => {
      try {
        isProcessing.value = true
        
        const reorderResult = await mockDataService.reorderItem(order.id)
        
        // 添加到当前订单列表
        currentOrders.value.unshift(reorderResult)
        
        emit('order-placed', reorderResult)
        
      } catch (error) {
        console.error('Failed to reorder item:', error)
      } finally {
        isProcessing.value = false
      }
    }
    
    const usePromotion = (promotion) => {
      console.log('Using promotion:', promotion)
      emit('promotion-used', promotion)
      // 实际应用中会应用优惠券
    }
    
    const handleReminderAction = (reminder) => {
      console.log('Handling reminder action:', reminder)
      // 根据提醒类型执行相应操作
    }
    
    const dismissReminder = (reminder) => {
      const index = smartReminders.value.findIndex(r => r.id === reminder.id)
      if (index > -1) {
        smartReminders.value.splice(index, 1)
      }
    }
    
    const startBrowsing = () => {
      console.log('Starting to browse')
      // 实际应用中会打开商品浏览页面
    }

    // 生命周期
    let refreshTimer = null
    
    onMounted(async () => {
      await mockDataService.initialize()
      await loadOrderData()
      
      // 设置自动刷新
      if (props.autoRefresh) {
        refreshTimer = setInterval(() => {
          loadOrderData()
        }, props.refreshInterval)
      }
    })

    onUnmounted(() => {
      if (refreshTimer) {
        clearInterval(refreshTimer)
      }
    })

    return {
      // 响应式数据
      isLoading,
      isProcessing,
      loadingText,
      quickOrdersExpanded,
      currentOrders,
      aiRecommendations,
      recentOrders,
      promotions,
      smartReminders,
      recommendationReason,
      quickOrderTypes,
      
      // 计算属性
      cardTitle,
      hasAnyContent,
      
      // 方法
      getOrderIcon,
      getStatusText,
      formatDate,
      viewOrderDetails,
      trackOrder,
      contactMerchant,
      viewRecommendation,
      quickOrder,
      toggleQuickOrdersExpanded,
      startQuickOrder,
      viewAllOrders,
      reorderItem,
      usePromotion,
      handleReminderAction,
      dismissReminder,
      startBrowsing
    }
  }
}
</script>

<style scoped>
.ai-order-card {
  height: 100%;
}

.order-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  position: relative;
  overflow-y: auto;
}

/* 当前订单 */
.current-orders {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
}

.orders-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.orders-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.orders-count {
  background: rgba(74, 144, 226, 0.3);
  color: #4a90e2;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.orders-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.order-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.order-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(2px);
}

.order-item.status-pending {
  border-left-color: #f59e0b;
}

.order-item.status-confirmed,
.order-item.status-preparing {
  border-left-color: #4a90e2;
}

.order-item.status-shipping {
  border-left-color: #8b5cf6;
}

.order-item.status-delivered,
.order-item.status-completed {
  border-left-color: #10b981;
}

.order-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  flex-shrink: 0;
}

.order-info {
  flex: 1;
}

.order-title {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2px;
}

.order-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 2px;
}

.order-status-text {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
}

.order-meta {
  text-align: right;
  margin-right: 8px;
}

.order-time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2px;
}

.order-price {
  font-size: 14px;
  font-weight: 600;
  color: #7ed321;
}

.order-actions {
  display: flex;
  gap: 4px;
}

.track-btn,
.contact-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.track-btn:hover {
  background: rgba(74, 144, 226, 0.3);
  color: #4a90e2;
}

.contact-btn:hover {
  background: rgba(126, 211, 33, 0.3);
  color: #7ed321;
}

/* AI推荐 */
.ai-recommendations {
  background: rgba(126, 211, 33, 0.1);
  border-radius: 12px;
  padding: 16px;
}

.recommendations-header {
  margin-bottom: 12px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #7ed321;
  margin-bottom: 4px;
}

.recommendation-reason {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.recommendation-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.recommendation-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.recommendation-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
}

.recommendation-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.recommendation-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  background: #ef4444;
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 4px;
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 4px;
}

.recommendation-description {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 8px;
}

.recommendation-details {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
}

.recommendation-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-price {
  font-size: 16px;
  font-weight: 600;
  color: #7ed321;
}

.original-price {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  text-decoration: line-through;
}

.discount {
  background: #ef4444;
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 4px;
}

.recommendation-actions {
  display: flex;
  align-items: center;
}

.quick-order-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background: rgba(126, 211, 33, 0.3);
  color: #7ed321;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-order-btn:hover:not(:disabled) {
  background: rgba(126, 211, 33, 0.5);
  transform: scale(1.05);
}

.quick-order-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 快速订单 */
.quick-orders {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 16px;
}

.quick-orders-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.quick-orders-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.expand-btn {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.expand-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.quick-orders-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.quick-order-type {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 12px 8px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.quick-order-type:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.quick-order-type:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quick-order-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.quick-order-label {
  font-size: 12px;
  font-weight: 500;
}

.quick-order-subtitle {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
}

/* 订单历史 */
.order-history {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 16px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.history-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.view-all-btn {
  background: none;
  border: none;
  color: #4a90e2;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-all-btn:hover {
  color: #7ed321;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.history-item:hover {
  background: rgba(255, 255, 255, 0.15);
}

.history-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  flex-shrink: 0;
}

.history-info {
  flex: 1;
}

.history-title {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2px;
}

.history-date {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
}

.history-actions {
  display: flex;
}

.reorder-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  background: rgba(74, 144, 226, 0.2);
  color: #4a90e2;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reorder-btn:hover:not(:disabled) {
  background: rgba(74, 144, 226, 0.3);
}

.reorder-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 优惠券和活动 */
.promotions {
  background: rgba(245, 158, 11, 0.1);
  border-radius: 12px;
  padding: 16px;
}

.promotions-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #f59e0b;
  margin-bottom: 12px;
}

.promotions-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.promotion-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.promotion-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(2px);
}

.promotion-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(245, 158, 11, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #f59e0b;
  flex-shrink: 0;
}

.promotion-content {
  flex: 1;
}

.promotion-title {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2px;
}

.promotion-description {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 2px;
}

.promotion-validity {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
}

.promotion-value {
  text-align: right;
}

.value-text {
  font-size: 16px;
  font-weight: 600;
  color: #f59e0b;
}

.value-type {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
}

/* 智能提醒 */
.smart-reminders {
  background: rgba(139, 92, 246, 0.1);
  border-radius: 12px;
  padding: 16px;
}

.reminders-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #8b5cf6;
  margin-bottom: 12px;
}

.reminders-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.reminder-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  border-left: 3px solid transparent;
}

.reminder-item.priority-high {
  border-left-color: #ef4444;
}

.reminder-item.priority-medium {
  border-left-color: #f59e0b;
}

.reminder-item.priority-low {
  border-left-color: #10b981;
}

.reminder-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(139, 92, 246, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #8b5cf6;
  flex-shrink: 0;
}

.reminder-content {
  flex: 1;
}

.reminder-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2px;
}

.reminder-time {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
}

.reminder-actions {
  display: flex;
  gap: 4px;
}

.reminder-action-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  background: rgba(139, 92, 246, 0.3);
  color: #8b5cf6;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reminder-action-btn:hover {
  background: rgba(139, 92, 246, 0.5);
}

.dismiss-btn {
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
}

.dismiss-btn:hover {
  background: rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

/* 无订单状态 */
.no-orders {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 32px 16px;
  flex: 1;
}

.no-orders-icon {
  font-size: 48px;
  color: rgba(255, 255, 255, 0.3);
  margin-bottom: 16px;
}

.no-orders-text h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
}

.no-orders-text p {
  margin: 0 0 24px 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

.start-ordering {
  width: 100%;
  max-width: 200px;
}

.start-browse-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  background: rgba(74, 144, 226, 0.3);
  color: #4a90e2;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.start-browse-btn:hover {
  background: rgba(74, 144, 226, 0.5);
  transform: translateY(-2px);
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  border-radius: 12px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-top: 3px solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

/* 动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
.size-small .order-container {
  padding: 12px;
  gap: 12px;
}

.size-small .quick-orders-grid {
  grid-template-columns: repeat(2, 1fr);
}

.mode-compact .ai-recommendations,
.mode-compact .order-history,
.mode-compact .promotions,
.mode-compact .smart-reminders {
  display: none;
}

.mode-compact .quick-orders-grid {
  grid-template-columns: repeat(4, 1fr);
}
</style>