{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nexport default {\n  name: 'BaseCard',\n  props: {\n    // 尺寸配置\n    gridSize: {\n      type: String,\n      default: '4x2',\n      validator: value => /^\\d+x\\d+$/.test(value)\n    },\n    // 内容配置\n    title: {\n      type: String,\n      default: ''\n    },\n    titleIcon: {\n      type: [String, Object],\n      default: null\n    },\n    contentComponent: {\n      type: [String, Object],\n      default: null\n    },\n    contentProps: {\n      type: Object,\n      default: () => ({})\n    },\n    // 主题配置\n    theme: {\n      type: String,\n      default: 'glassmorphism',\n      validator: value => ['glassmorphism', 'minimal', 'dark', 'light'].includes(value)\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({})\n    },\n    // 布局配置\n    position: {\n      type: Object,\n      default: () => ({\n        x: 0,\n        y: 0\n      })\n    },\n    size: {\n      type: String,\n      default: 'medium',\n      validator: value => ['small', 'medium', 'large'].includes(value)\n    },\n    // 显示控制\n    showHeader: {\n      type: <PERSON><PERSON><PERSON>,\n      default: true\n    },\n    showFooter: {\n      type: Boolean,\n      default: false\n    },\n    showActions: {\n      type: Boolean,\n      default: false\n    },\n    // 交互配置\n    clickable: {\n      type: Boolean,\n      default: false\n    },\n    disabled: {\n      type: Boolean,\n      default: false\n    },\n    // 状态\n    loading: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: ['click', 'content-update'],\n  computed: {\n    cardClasses() {\n      return ['base-card', `card-${this.theme}`, `card-size-${this.size}`, `grid-${this.gridSize}`, {\n        'card-clickable': this.clickable,\n        'card-disabled': this.disabled,\n        'card-loading': this.loading\n      }];\n    },\n    cardStyles() {\n      const styles = {\n        '--card-x': this.position.x,\n        '--card-y': this.position.y\n      };\n\n      // 应用主题颜色\n      if (this.themeColors) {\n        Object.keys(this.themeColors).forEach(key => {\n          styles[`--theme-${key}`] = this.themeColors[key];\n        });\n      }\n      return styles;\n    }\n  },\n  methods: {\n    handleClick(event) {\n      if (!this.disabled && this.clickable) {\n        this.$emit('click', event);\n      }\n    },\n    handleContentUpdate(data) {\n      this.$emit('content-update', data);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "gridSize", "type", "String", "default", "validator", "value", "test", "title", "titleIcon", "Object", "contentComponent", "contentProps", "theme", "includes", "themeColors", "position", "x", "y", "size", "showHeader", "Boolean", "showFooter", "showActions", "clickable", "disabled", "loading", "emits", "computed", "cardClasses", "cardStyles", "styles", "keys", "for<PERSON>ach", "key", "methods", "handleClick", "event", "$emit", "handleContentUpdate", "data"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\BaseCard.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"cardClasses\"\n    :style=\"cardStyles\"\n    @click=\"handleClick\"\n  >\n    <!-- 卡片头部 -->\n    <div v-if=\"showHeader\" class=\"card-header\">\n      <slot name=\"header\">\n        <div class=\"card-title\">\n          <component :is=\"titleIcon\" v-if=\"titleIcon\" class=\"title-icon\" />\n          <span>{{ title }}</span>\n        </div>\n        <div v-if=\"showActions\" class=\"card-actions\">\n          <slot name=\"actions\" />\n        </div>\n      </slot>\n    </div>\n\n    <!-- 卡片内容 -->\n    <div class=\"card-content\">\n      <slot name=\"content\">\n        <component\n          :is=\"contentComponent\"\n          v-bind=\"contentProps\"\n          @update=\"handleContentUpdate\"\n        />\n      </slot>\n    </div>\n\n    <!-- 卡片底部 -->\n    <div v-if=\"showFooter\" class=\"card-footer\">\n      <slot name=\"footer\" />\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'BaseCard',\n  props: {\n    // 尺寸配置\n    gridSize: {\n      type: String,\n      default: '4x2',\n      validator: (value) => /^\\d+x\\d+$/.test(value)\n    },\n\n    // 内容配置\n    title: {\n      type: String,\n      default: ''\n    },\n    titleIcon: {\n      type: [String, Object],\n      default: null\n    },\n    contentComponent: {\n      type: [String, Object],\n      default: null\n    },\n    contentProps: {\n      type: Object,\n      default: () => ({})\n    },\n\n    // 主题配置\n    theme: {\n      type: String,\n      default: 'glassmorphism',\n      validator: (value) => ['glassmorphism', 'minimal', 'dark', 'light'].includes(value)\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({})\n    },\n\n    // 布局配置\n    position: {\n      type: Object,\n      default: () => ({ x: 0, y: 0 })\n    },\n    size: {\n      type: String,\n      default: 'medium',\n      validator: (value) => ['small', 'medium', 'large'].includes(value)\n    },\n\n    // 显示控制\n    showHeader: {\n      type: Boolean,\n      default: true\n    },\n    showFooter: {\n      type: Boolean,\n      default: false\n    },\n    showActions: {\n      type: Boolean,\n      default: false\n    },\n\n    // 交互配置\n    clickable: {\n      type: Boolean,\n      default: false\n    },\n    disabled: {\n      type: Boolean,\n      default: false\n    },\n\n    // 状态\n    loading: {\n      type: Boolean,\n      default: false\n    }\n  },\n\n  emits: ['click', 'content-update'],\n\n  computed: {\n    cardClasses() {\n      return [\n        'base-card',\n        `card-${this.theme}`,\n        `card-size-${this.size}`,\n        `grid-${this.gridSize}`,\n        {\n          'card-clickable': this.clickable,\n          'card-disabled': this.disabled,\n          'card-loading': this.loading\n        }\n      ]\n    },\n\n    cardStyles() {\n      const styles = {\n        '--card-x': this.position.x,\n        '--card-y': this.position.y\n      }\n\n      // 应用主题颜色\n      if (this.themeColors) {\n        Object.keys(this.themeColors).forEach(key => {\n          styles[`--theme-${key}`] = this.themeColors[key]\n        })\n      }\n\n      return styles\n    }\n  },\n\n  methods: {\n    handleClick(event) {\n      if (!this.disabled && this.clickable) {\n        this.$emit('click', event)\n      }\n    },\n\n    handleContentUpdate(data) {\n      this.$emit('content-update', data)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.base-card {\n  position: relative;\n  border-radius: 16px;\n  overflow: hidden;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n/* 主题样式 */\n.card-glassmorphism {\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.card-minimal {\n  background: rgba(255, 255, 255, 0.95);\n  border: 1px solid rgba(0, 0, 0, 0.1);\n  backdrop-filter: none;\n}\n\n.card-dark {\n  background: rgba(0, 0, 0, 0.8);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  color: white;\n}\n\n.card-light {\n  background: rgba(255, 255, 255, 0.9);\n  border: 1px solid rgba(0, 0, 0, 0.1);\n  color: #333;\n}\n\n/* 尺寸样式 */\n.card-size-small {\n  min-height: 120px;\n  padding: 12px;\n}\n\n.card-size-medium {\n  min-height: 180px;\n  padding: 16px;\n}\n\n.card-size-large {\n  min-height: 240px;\n  padding: 20px;\n}\n\n/* 网格尺寸 */\n.grid-2x2 {\n  grid-column: span 2;\n  grid-row: span 2;\n}\n\n.grid-4x2 {\n  grid-column: span 4;\n  grid-row: span 2;\n}\n\n.grid-4x4 {\n  grid-column: span 4;\n  grid-row: span 4;\n}\n\n.grid-8x4 {\n  grid-column: span 8;\n  grid-row: span 4;\n}\n\n.grid-8x9 {\n  grid-column: span 8;\n  grid-row: span 9;\n}\n\n.grid-16x1 {\n  grid-column: span 16;\n  grid-row: span 1;\n}\n\n/* 交互状态 */\n.card-clickable {\n  cursor: pointer;\n}\n\n.card-clickable:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n}\n\n.card-disabled {\n  opacity: 0.6;\n  pointer-events: none;\n}\n\n.card-loading {\n  position: relative;\n}\n\n.card-loading::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(2px);\n}\n\n/* 卡片结构 */\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.card-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  color: var(--theme-text, rgba(255, 255, 255, 0.9));\n}\n\n.title-icon {\n  width: 20px;\n  height: 20px;\n  opacity: 0.8;\n}\n\n.card-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.card-content {\n  flex: 1;\n  min-height: 0;\n}\n\n.card-footer {\n  margin-top: 16px;\n  padding-top: 16px;\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .card-size-small {\n    min-height: 100px;\n    padding: 10px;\n  }\n\n  .card-size-medium {\n    min-height: 150px;\n    padding: 14px;\n  }\n\n  .card-size-large {\n    min-height: 200px;\n    padding: 16px;\n  }\n\n  .card-title {\n    font-size: 14px;\n  }\n}\n</style>\n"], "mappings": ";;AAsCA,eAAe;EACbA,IAAI,EAAE,UAAU;EAChBC,KAAK,EAAE;IACL;IACAC,QAAQ,EAAE;MACRC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAGC,KAAK,IAAK,WAAW,CAACC,IAAI,CAACD,KAAK;IAC9C,CAAC;IAED;IACAE,KAAK,EAAE;MACLN,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDK,SAAS,EAAE;MACTP,IAAI,EAAE,CAACC,MAAM,EAAEO,MAAM,CAAC;MACtBN,OAAO,EAAE;IACX,CAAC;IACDO,gBAAgB,EAAE;MAChBT,IAAI,EAAE,CAACC,MAAM,EAAEO,MAAM,CAAC;MACtBN,OAAO,EAAE;IACX,CAAC;IACDQ,YAAY,EAAE;MACZV,IAAI,EAAEQ,MAAM;MACZN,OAAO,EAAEA,CAAA,MAAO,CAAC,CAAC;IACpB,CAAC;IAED;IACAS,KAAK,EAAE;MACLX,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,eAAe;MACxBC,SAAS,EAAGC,KAAK,IAAK,CAAC,eAAe,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,CAACQ,QAAQ,CAACR,KAAK;IACpF,CAAC;IACDS,WAAW,EAAE;MACXb,IAAI,EAAEQ,MAAM;MACZN,OAAO,EAAEA,CAAA,MAAO,CAAC,CAAC;IACpB,CAAC;IAED;IACAY,QAAQ,EAAE;MACRd,IAAI,EAAEQ,MAAM;MACZN,OAAO,EAAEA,CAAA,MAAO;QAAEa,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;IAChC,CAAC;IACDC,IAAI,EAAE;MACJjB,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBC,SAAS,EAAGC,KAAK,IAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAACQ,QAAQ,CAACR,KAAK;IACnE,CAAC;IAED;IACAc,UAAU,EAAE;MACVlB,IAAI,EAAEmB,OAAO;MACbjB,OAAO,EAAE;IACX,CAAC;IACDkB,UAAU,EAAE;MACVpB,IAAI,EAAEmB,OAAO;MACbjB,OAAO,EAAE;IACX,CAAC;IACDmB,WAAW,EAAE;MACXrB,IAAI,EAAEmB,OAAO;MACbjB,OAAO,EAAE;IACX,CAAC;IAED;IACAoB,SAAS,EAAE;MACTtB,IAAI,EAAEmB,OAAO;MACbjB,OAAO,EAAE;IACX,CAAC;IACDqB,QAAQ,EAAE;MACRvB,IAAI,EAAEmB,OAAO;MACbjB,OAAO,EAAE;IACX,CAAC;IAED;IACAsB,OAAO,EAAE;MACPxB,IAAI,EAAEmB,OAAO;MACbjB,OAAO,EAAE;IACX;EACF,CAAC;EAEDuB,KAAK,EAAE,CAAC,OAAO,EAAE,gBAAgB,CAAC;EAElCC,QAAQ,EAAE;IACRC,WAAWA,CAAA,EAAG;MACZ,OAAO,CACL,WAAW,EACX,QAAQ,IAAI,CAAChB,KAAK,EAAE,EACpB,aAAa,IAAI,CAACM,IAAI,EAAE,EACxB,QAAQ,IAAI,CAAClB,QAAQ,EAAE,EACvB;QACE,gBAAgB,EAAE,IAAI,CAACuB,SAAS;QAChC,eAAe,EAAE,IAAI,CAACC,QAAQ;QAC9B,cAAc,EAAE,IAAI,CAACC;MACvB,EACF;IACF,CAAC;IAEDI,UAAUA,CAAA,EAAG;MACX,MAAMC,MAAK,GAAI;QACb,UAAU,EAAE,IAAI,CAACf,QAAQ,CAACC,CAAC;QAC3B,UAAU,EAAE,IAAI,CAACD,QAAQ,CAACE;MAC5B;;MAEA;MACA,IAAI,IAAI,CAACH,WAAW,EAAE;QACpBL,MAAM,CAACsB,IAAI,CAAC,IAAI,CAACjB,WAAW,CAAC,CAACkB,OAAO,CAACC,GAAE,IAAK;UAC3CH,MAAM,CAAC,WAAWG,GAAG,EAAE,IAAI,IAAI,CAACnB,WAAW,CAACmB,GAAG;QACjD,CAAC;MACH;MAEA,OAAOH,MAAK;IACd;EACF,CAAC;EAEDI,OAAO,EAAE;IACPC,WAAWA,CAACC,KAAK,EAAE;MACjB,IAAI,CAAC,IAAI,CAACZ,QAAO,IAAK,IAAI,CAACD,SAAS,EAAE;QACpC,IAAI,CAACc,KAAK,CAAC,OAAO,EAAED,KAAK;MAC3B;IACF,CAAC;IAEDE,mBAAmBA,CAACC,IAAI,EAAE;MACxB,IAAI,CAACF,KAAK,CAAC,gBAAgB,EAAEE,IAAI;IACnC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}