{"ast": null, "code": "import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue';\nimport BaseCard from '@/components/cards/BaseCard.vue';\nimport mockDataService from '@/services/MockDataService.js';\nexport default {\n  name: 'AIPediaCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    size: {\n      type: String,\n      default: 'large',\n      validator: value => ['small', 'medium', 'large'].includes(value)\n    },\n    position: {\n      type: Object,\n      default: () => ({\n        x: 0,\n        y: 0\n      })\n    },\n    theme: {\n      type: String,\n      default: 'glassmorphism'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({})\n    },\n    displayMode: {\n      type: String,\n      default: 'full',\n      validator: value => ['compact', 'standard', 'full'].includes(value)\n    },\n    showSearch: {\n      type: Boolean,\n      default: true\n    },\n    showCategories: {\n      type: Boolean,\n      default: true\n    },\n    showHistory: {\n      type: Boolean,\n      default: true\n    },\n    showStats: {\n      type: Boolean,\n      default: true\n    },\n    autoRefresh: {\n      type: <PERSON>olean,\n      default: true\n    },\n    refreshInterval: {\n      type: Number,\n      default: 300000 // 5分钟\n    }\n  },\n  emits: ['question-asked', 'answer-received', 'category-explored', 'knowledge-shared'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式数据\n    const isSearching = ref(false);\n    const loadingText = ref('正在思考中...');\n    const searchQuery = ref('');\n    const currentAnswer = ref(null);\n    const searchSuggestions = ref([]);\n    const knowledgeCategories = ref([]);\n    const dailyRecommendations = ref([]);\n    const searchHistory = ref([]);\n    const learningStats = ref({\n      questionsAsked: 0,\n      knowledgeGained: 0,\n      readingTime: '0分钟',\n      streak: 0,\n      achievements: []\n    });\n    const quickStartQuestions = ref([{\n      id: 1,\n      text: '什么是人工智能？'\n    }, {\n      id: 2,\n      text: '如何保持健康的生活方式？'\n    }, {\n      id: 3,\n      text: '太阳系有多少颗行星？'\n    }, {\n      id: 4,\n      text: '如何学习编程？'\n    }]);\n\n    // 计算属性\n    const cardTitle = computed(() => {\n      if (currentAnswer.value) {\n        return 'AI解答';\n      }\n      return 'AI百科';\n    });\n    const hasAnyContent = computed(() => {\n      return currentAnswer.value || searchSuggestions.value.length > 0 || knowledgeCategories.value.length > 0 || dailyRecommendations.value.length > 0 || searchHistory.value.length > 0;\n    });\n\n    // 方法\n    const loadPediaData = async () => {\n      try {\n        const pediaData = await mockDataService.getPediaData();\n        searchSuggestions.value = pediaData.searchSuggestions || [];\n        knowledgeCategories.value = pediaData.knowledgeCategories || [];\n        dailyRecommendations.value = pediaData.dailyRecommendations || [];\n        searchHistory.value = pediaData.searchHistory || [];\n        learningStats.value = {\n          ...learningStats.value,\n          ...pediaData.learningStats\n        };\n      } catch (error) {\n        console.error('Failed to load pedia data:', error);\n      }\n    };\n    const performSearch = async () => {\n      if (!searchQuery.value.trim() || isSearching.value) return;\n      try {\n        isSearching.value = true;\n        loadingText.value = '正在搜索知识库...';\n        const question = searchQuery.value.trim();\n        emit('question-asked', question);\n        const answer = await mockDataService.searchKnowledge(question);\n        currentAnswer.value = {\n          question,\n          answer: answer.content,\n          confidence: answer.confidence,\n          images: answer.images || [],\n          links: answer.links || [],\n          liked: false,\n          disliked: false,\n          timestamp: new Date()\n        };\n\n        // 添加到搜索历史\n        searchHistory.value.unshift({\n          id: Date.now(),\n          question,\n          timestamp: new Date()\n        });\n\n        // 限制历史记录数量\n        if (searchHistory.value.length > 20) {\n          searchHistory.value = searchHistory.value.slice(0, 20);\n        }\n        emit('answer-received', currentAnswer.value);\n\n        // 清空搜索框\n        searchQuery.value = '';\n      } catch (error) {\n        console.error('Failed to search knowledge:', error);\n      } finally {\n        isSearching.value = false;\n      }\n    };\n    const onSearchInput = () => {\n      // 实时搜索建议可以在这里实现\n    };\n    const clearSearch = () => {\n      searchQuery.value = '';\n      currentAnswer.value = null;\n    };\n    const selectSuggestion = suggestion => {\n      searchQuery.value = suggestion.text;\n      performSearch();\n    };\n    const exploreCategory = category => {\n      console.log('Exploring category:', category);\n      emit('category-explored', category);\n      // 实际应用中会显示该分类下的知识内容\n    };\n    const viewRecommendation = recommendation => {\n      console.log('Viewing recommendation:', recommendation);\n      // 实际应用中会显示推荐内容的详情\n    };\n    const searchFromHistory = historyItem => {\n      searchQuery.value = historyItem.question;\n      performSearch();\n    };\n    const removeFromHistory = historyItem => {\n      const index = searchHistory.value.findIndex(item => item.id === historyItem.id);\n      if (index > -1) {\n        searchHistory.value.splice(index, 1);\n      }\n    };\n    const clearHistory = () => {\n      searchHistory.value = [];\n    };\n    const askQuickQuestion = question => {\n      searchQuery.value = question.text;\n      performSearch();\n    };\n    const formatAnswer = answer => {\n      // 简单的文本格式化\n      return answer.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>').replace(/\\*(.*?)\\*/g, '<em>$1</em>').replace(/\\n/g, '<br>');\n    };\n    const formatDate = date => {\n      return date.toLocaleDateString('zh-CN', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      });\n    };\n    const formatTime = timestamp => {\n      const date = new Date(timestamp);\n      const now = new Date();\n      const diffTime = Math.abs(now - date);\n      const diffMinutes = Math.ceil(diffTime / (1000 * 60));\n      if (diffMinutes < 60) {\n        return `${diffMinutes}分钟前`;\n      } else if (diffMinutes < 1440) {\n        return `${Math.ceil(diffMinutes / 60)}小时前`;\n      } else {\n        return date.toLocaleDateString('zh-CN', {\n          month: 'short',\n          day: 'numeric'\n        });\n      }\n    };\n    const likeAnswer = () => {\n      if (currentAnswer.value) {\n        currentAnswer.value.liked = !currentAnswer.value.liked;\n        if (currentAnswer.value.liked) {\n          currentAnswer.value.disliked = false;\n        }\n      }\n    };\n    const dislikeAnswer = () => {\n      if (currentAnswer.value) {\n        currentAnswer.value.disliked = !currentAnswer.value.disliked;\n        if (currentAnswer.value.disliked) {\n          currentAnswer.value.liked = false;\n        }\n      }\n    };\n    const shareAnswer = () => {\n      if (currentAnswer.value) {\n        console.log('Sharing answer:', currentAnswer.value);\n        emit('knowledge-shared', currentAnswer.value);\n        // 实际应用中会打开分享功能\n      }\n    };\n    const saveAnswer = () => {\n      if (currentAnswer.value) {\n        console.log('Saving answer:', currentAnswer.value);\n        // 实际应用中会保存到收藏夹\n      }\n    };\n    const viewImage = image => {\n      console.log('Viewing image:', image);\n      // 实际应用中会打开图片查看器\n    };\n\n    // 生命周期\n    let refreshTimer = null;\n    onMounted(async () => {\n      await mockDataService.initialize();\n      await loadPediaData();\n\n      // 设置自动刷新\n      if (props.autoRefresh) {\n        refreshTimer = setInterval(() => {\n          loadPediaData();\n        }, props.refreshInterval);\n      }\n    });\n    onUnmounted(() => {\n      if (refreshTimer) {\n        clearInterval(refreshTimer);\n      }\n    });\n    return {\n      // 响应式数据\n      isSearching,\n      loadingText,\n      searchQuery,\n      currentAnswer,\n      searchSuggestions,\n      knowledgeCategories,\n      dailyRecommendations,\n      searchHistory,\n      learningStats,\n      quickStartQuestions,\n      // 计算属性\n      cardTitle,\n      hasAnyContent,\n      // 方法\n      performSearch,\n      onSearchInput,\n      clearSearch,\n      selectSuggestion,\n      exploreCategory,\n      viewRecommendation,\n      searchFromHistory,\n      removeFromHistory,\n      clearHistory,\n      askQuickQuestion,\n      formatAnswer,\n      formatDate,\n      formatTime,\n      likeAnswer,\n      dislikeAnswer,\n      shareAnswer,\n      saveAnswer,\n      viewImage\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "onUnmounted", "watch", "BaseCard", "mockDataService", "name", "components", "props", "size", "type", "String", "default", "validator", "value", "includes", "position", "Object", "x", "y", "theme", "themeColors", "displayMode", "showSearch", "Boolean", "showCategories", "showHistory", "showStats", "autoRefresh", "refreshInterval", "Number", "emits", "setup", "emit", "isSearching", "loadingText", "searchQuery", "currentAnswer", "searchSuggestions", "knowledgeCategories", "dailyRecommendations", "searchHistory", "learningStats", "questionsAsked", "knowledgeGained", "readingTime", "streak", "achievements", "quickStartQuestions", "id", "text", "cardTitle", "has<PERSON>ny<PERSON><PERSON>nt", "length", "loadPediaData", "pediaData", "getPediaData", "error", "console", "performSearch", "trim", "question", "answer", "searchKnowledge", "content", "confidence", "images", "links", "liked", "disliked", "timestamp", "Date", "unshift", "now", "slice", "onSearchInput", "clearSearch", "selectSuggestion", "suggestion", "exploreCategory", "category", "log", "viewRecommendation", "recommendation", "searchFromHistory", "historyItem", "removeFromHistory", "index", "findIndex", "item", "splice", "clearHistory", "askQuickQuestion", "formatAnswer", "replace", "formatDate", "date", "toLocaleDateString", "year", "month", "day", "formatTime", "diffTime", "Math", "abs", "diffMinutes", "ceil", "likeAnswer", "dislikeAnswer", "shareAnswer", "saveAnswer", "viewImage", "image", "refreshTimer", "initialize", "setInterval", "clearInterval"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\AIPediaCard.vue"], "sourcesContent": ["<template>\n  <BaseCard\n    :card-type=\"'ai-pedia'\"\n    :size=\"size\"\n    :position=\"position\"\n    :theme=\"theme\"\n    :theme-colors=\"themeColors\"\n    :show-header=\"true\"\n    :show-footer=\"false\"\n    :title=\"cardTitle\"\n    :icon=\"'fas fa-brain'\"\n    class=\"ai-pedia-card\"\n  >\n    <div class=\"pedia-container\" :class=\"[`size-${size}`, `mode-${displayMode}`]\">\n      <!-- 搜索输入 -->\n      <div class=\"search-section\" v-if=\"showSearch\">\n        <div class=\"search-input-container\">\n          <input \n            v-model=\"searchQuery\"\n            @keyup.enter=\"performSearch\"\n            @input=\"onSearchInput\"\n            placeholder=\"问我任何问题...\"\n            class=\"search-input\"\n            :disabled=\"isSearching\"\n          >\n          <button \n            @click=\"performSearch\" \n            class=\"search-btn\"\n            :disabled=\"!searchQuery.trim() || isSearching\"\n          >\n            <i :class=\"isSearching ? 'fas fa-spinner fa-spin' : 'fas fa-search'\"></i>\n          </button>\n          <button \n            v-if=\"searchQuery\"\n            @click=\"clearSearch\" \n            class=\"clear-btn\"\n          >\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n        \n        <!-- 搜索建议 -->\n        <div class=\"search-suggestions\" v-if=\"searchSuggestions.length > 0 && !isSearching\">\n          <div class=\"suggestions-header\">热门问题</div>\n          <div class=\"suggestions-list\">\n            <button\n              v-for=\"suggestion in searchSuggestions.slice(0, 3)\"\n              :key=\"suggestion.id\"\n              @click=\"selectSuggestion(suggestion)\"\n              class=\"suggestion-item\"\n            >\n              <i :class=\"suggestion.icon\"></i>\n              <span>{{ suggestion.text }}</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 当前问答 -->\n      <div class=\"current-qa\" v-if=\"currentAnswer\">\n        <div class=\"question-section\">\n          <div class=\"question-header\">\n            <i class=\"fas fa-question-circle\"></i>\n            <span>您的问题</span>\n          </div>\n          <div class=\"question-text\">{{ currentAnswer.question }}</div>\n        </div>\n        \n        <div class=\"answer-section\">\n          <div class=\"answer-header\">\n            <i class=\"fas fa-lightbulb\"></i>\n            <span>AI解答</span>\n            <div class=\"confidence-indicator\">\n              <span class=\"confidence-text\">可信度</span>\n              <div class=\"confidence-bar\">\n                <div \n                  class=\"confidence-fill\" \n                  :style=\"{ width: `${currentAnswer.confidence * 100}%` }\"\n                ></div>\n              </div>\n              <span class=\"confidence-value\">{{ Math.round(currentAnswer.confidence * 100) }}%</span>\n            </div>\n          </div>\n          \n          <div class=\"answer-content\">\n            <div class=\"answer-text\" v-html=\"formatAnswer(currentAnswer.answer)\"></div>\n            \n            <!-- 相关图片 -->\n            <div class=\"answer-images\" v-if=\"currentAnswer.images && currentAnswer.images.length > 0\">\n              <div class=\"images-grid\">\n                <div \n                  v-for=\"(image, index) in currentAnswer.images.slice(0, 3)\" \n                  :key=\"index\"\n                  class=\"image-item\"\n                  @click=\"viewImage(image)\"\n                >\n                  <img :src=\"image.url\" :alt=\"image.caption\" />\n                  <div class=\"image-caption\">{{ image.caption }}</div>\n                </div>\n              </div>\n            </div>\n            \n            <!-- 相关链接 -->\n            <div class=\"related-links\" v-if=\"currentAnswer.links && currentAnswer.links.length > 0\">\n              <div class=\"links-header\">相关资料</div>\n              <div class=\"links-list\">\n                <a \n                  v-for=\"link in currentAnswer.links.slice(0, 3)\" \n                  :key=\"link.id\"\n                  :href=\"link.url\"\n                  target=\"_blank\"\n                  class=\"link-item\"\n                >\n                  <i class=\"fas fa-external-link-alt\"></i>\n                  <span>{{ link.title }}</span>\n                </a>\n              </div>\n            </div>\n          </div>\n          \n          <div class=\"answer-actions\">\n            <button @click=\"likeAnswer\" :class=\"['action-btn', { active: currentAnswer.liked }]\">\n              <i class=\"fas fa-thumbs-up\"></i>\n              <span>有用</span>\n            </button>\n            <button @click=\"dislikeAnswer\" :class=\"['action-btn', { active: currentAnswer.disliked }]\">\n              <i class=\"fas fa-thumbs-down\"></i>\n              <span>无用</span>\n            </button>\n            <button @click=\"shareAnswer\" class=\"action-btn\">\n              <i class=\"fas fa-share\"></i>\n              <span>分享</span>\n            </button>\n            <button @click=\"saveAnswer\" class=\"action-btn\">\n              <i class=\"fas fa-bookmark\"></i>\n              <span>收藏</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 知识分类 -->\n      <div class=\"knowledge-categories\" v-if=\"showCategories && !currentAnswer\">\n        <div class=\"categories-header\">\n          <h3>知识分类</h3>\n          <div class=\"categories-subtitle\">探索不同领域的知识</div>\n        </div>\n        \n        <div class=\"categories-grid\">\n          <div \n            v-for=\"category in knowledgeCategories\" \n            :key=\"category.id\"\n            @click=\"exploreCategory(category)\"\n            class=\"category-item\"\n          >\n            <div class=\"category-icon\">\n              <i :class=\"category.icon\"></i>\n            </div>\n            <div class=\"category-content\">\n              <div class=\"category-name\">{{ category.name }}</div>\n              <div class=\"category-description\">{{ category.description }}</div>\n              <div class=\"category-count\">{{ category.count }}个话题</div>\n            </div>\n            <div class=\"category-arrow\">\n              <i class=\"fas fa-chevron-right\"></i>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 今日推荐 -->\n      <div class=\"daily-recommendations\" v-if=\"dailyRecommendations.length > 0\">\n        <div class=\"recommendations-header\">\n          <div class=\"header-content\">\n            <i class=\"fas fa-calendar-day\"></i>\n            <span>今日推荐</span>\n          </div>\n          <div class=\"date-info\">{{ formatDate(new Date()) }}</div>\n        </div>\n        \n        <div class=\"recommendations-list\">\n          <div \n            v-for=\"recommendation in dailyRecommendations\" \n            :key=\"recommendation.id\"\n            @click=\"viewRecommendation(recommendation)\"\n            class=\"recommendation-item\"\n          >\n            <div class=\"recommendation-icon\">\n              <i :class=\"recommendation.icon\"></i>\n            </div>\n            \n            <div class=\"recommendation-content\">\n              <div class=\"recommendation-title\">{{ recommendation.title }}</div>\n              <div class=\"recommendation-summary\">{{ recommendation.summary }}</div>\n              \n              <div class=\"recommendation-meta\">\n                <div class=\"meta-item\">\n                  <i class=\"fas fa-tag\"></i>\n                  <span>{{ recommendation.category }}</span>\n                </div>\n                <div class=\"meta-item\">\n                  <i class=\"fas fa-clock\"></i>\n                  <span>{{ recommendation.readTime }}分钟阅读</span>\n                </div>\n                <div class=\"meta-item\">\n                  <i class=\"fas fa-eye\"></i>\n                  <span>{{ recommendation.views }}次浏览</span>\n                </div>\n              </div>\n            </div>\n            \n            <div class=\"recommendation-badge\" v-if=\"recommendation.badge\">\n              {{ recommendation.badge }}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 搜索历史 -->\n      <div class=\"search-history\" v-if=\"showHistory && searchHistory.length > 0\">\n        <div class=\"history-header\">\n          <h3>最近搜索</h3>\n          <button @click=\"clearHistory\" class=\"clear-history-btn\">\n            <i class=\"fas fa-trash\"></i>\n            <span>清空</span>\n          </button>\n        </div>\n        \n        <div class=\"history-list\">\n          <div \n            v-for=\"item in searchHistory.slice(0, 5)\" \n            :key=\"item.id\"\n            @click=\"searchFromHistory(item)\"\n            class=\"history-item\"\n          >\n            <div class=\"history-icon\">\n              <i class=\"fas fa-history\"></i>\n            </div>\n            \n            <div class=\"history-content\">\n              <div class=\"history-question\">{{ item.question }}</div>\n              <div class=\"history-time\">{{ formatTime(item.timestamp) }}</div>\n            </div>\n            \n            <div class=\"history-actions\">\n              <button \n                @click.stop=\"removeFromHistory(item)\"\n                class=\"remove-btn\"\n              >\n                <i class=\"fas fa-times\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 学习统计 -->\n      <div class=\"learning-stats\" v-if=\"showStats\">\n        <div class=\"stats-header\">\n          <i class=\"fas fa-chart-line\"></i>\n          <span>学习统计</span>\n        </div>\n        \n        <div class=\"stats-grid\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ learningStats.questionsAsked }}</div>\n            <div class=\"stat-label\">今日提问</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ learningStats.knowledgeGained }}</div>\n            <div class=\"stat-label\">知识点</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ learningStats.readingTime }}</div>\n            <div class=\"stat-label\">阅读时长</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ learningStats.streak }}</div>\n            <div class=\"stat-label\">连续天数</div>\n          </div>\n        </div>\n        \n        <div class=\"achievement-section\" v-if=\"learningStats.achievements.length > 0\">\n          <div class=\"achievement-header\">最新成就</div>\n          <div class=\"achievement-list\">\n            <div \n              v-for=\"achievement in learningStats.achievements.slice(0, 2)\" \n              :key=\"achievement.id\"\n              class=\"achievement-item\"\n            >\n              <div class=\"achievement-icon\">\n                <i :class=\"achievement.icon\"></i>\n              </div>\n              <div class=\"achievement-content\">\n                <div class=\"achievement-name\">{{ achievement.name }}</div>\n                <div class=\"achievement-description\">{{ achievement.description }}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 无内容状态 -->\n      <div v-if=\"!hasAnyContent\" class=\"no-content\">\n        <div class=\"no-content-icon\">\n          <i class=\"fas fa-brain\"></i>\n        </div>\n        <div class=\"no-content-text\">\n          <h3>AI百科助手</h3>\n          <p>问我任何问题，获取智能解答</p>\n        </div>\n        <div class=\"quick-start\">\n          <div class=\"quick-start-header\">试试这些问题</div>\n          <div class=\"quick-questions\">\n            <button\n              v-for=\"question in quickStartQuestions\"\n              :key=\"question.id\"\n              @click=\"askQuickQuestion(question)\"\n              class=\"quick-question-btn\"\n            >\n              {{ question.text }}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 加载状态 -->\n      <div v-if=\"isSearching\" class=\"loading-overlay\">\n        <div class=\"loading-spinner\"></div>\n        <div class=\"loading-text\">{{ loadingText }}</div>\n      </div>\n    </div>\n  </BaseCard>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'\nimport BaseCard from '@/components/cards/BaseCard.vue'\nimport mockDataService from '@/services/MockDataService.js'\n\nexport default {\n  name: 'AIPediaCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    size: {\n      type: String,\n      default: 'large',\n      validator: (value) => ['small', 'medium', 'large'].includes(value)\n    },\n    position: {\n      type: Object,\n      default: () => ({ x: 0, y: 0 })\n    },\n    theme: {\n      type: String,\n      default: 'glassmorphism'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({})\n    },\n    displayMode: {\n      type: String,\n      default: 'full',\n      validator: (value) => ['compact', 'standard', 'full'].includes(value)\n    },\n    showSearch: {\n      type: Boolean,\n      default: true\n    },\n    showCategories: {\n      type: Boolean,\n      default: true\n    },\n    showHistory: {\n      type: Boolean,\n      default: true\n    },\n    showStats: {\n      type: Boolean,\n      default: true\n    },\n    autoRefresh: {\n      type: Boolean,\n      default: true\n    },\n    refreshInterval: {\n      type: Number,\n      default: 300000 // 5分钟\n    }\n  },\n  emits: ['question-asked', 'answer-received', 'category-explored', 'knowledge-shared'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const isSearching = ref(false)\n    const loadingText = ref('正在思考中...')\n    const searchQuery = ref('')\n    \n    const currentAnswer = ref(null)\n    const searchSuggestions = ref([])\n    const knowledgeCategories = ref([])\n    const dailyRecommendations = ref([])\n    const searchHistory = ref([])\n    const learningStats = ref({\n      questionsAsked: 0,\n      knowledgeGained: 0,\n      readingTime: '0分钟',\n      streak: 0,\n      achievements: []\n    })\n    \n    const quickStartQuestions = ref([\n      { id: 1, text: '什么是人工智能？' },\n      { id: 2, text: '如何保持健康的生活方式？' },\n      { id: 3, text: '太阳系有多少颗行星？' },\n      { id: 4, text: '如何学习编程？' }\n    ])\n\n    // 计算属性\n    const cardTitle = computed(() => {\n      if (currentAnswer.value) {\n        return 'AI解答'\n      }\n      return 'AI百科'\n    })\n    \n    const hasAnyContent = computed(() => {\n      return currentAnswer.value || \n             searchSuggestions.value.length > 0 || \n             knowledgeCategories.value.length > 0 || \n             dailyRecommendations.value.length > 0 || \n             searchHistory.value.length > 0\n    })\n\n    // 方法\n    const loadPediaData = async () => {\n      try {\n        const pediaData = await mockDataService.getPediaData()\n        \n        searchSuggestions.value = pediaData.searchSuggestions || []\n        knowledgeCategories.value = pediaData.knowledgeCategories || []\n        dailyRecommendations.value = pediaData.dailyRecommendations || []\n        searchHistory.value = pediaData.searchHistory || []\n        learningStats.value = { ...learningStats.value, ...pediaData.learningStats }\n        \n      } catch (error) {\n        console.error('Failed to load pedia data:', error)\n      }\n    }\n    \n    const performSearch = async () => {\n      if (!searchQuery.value.trim() || isSearching.value) return\n      \n      try {\n        isSearching.value = true\n        loadingText.value = '正在搜索知识库...'\n        \n        const question = searchQuery.value.trim()\n        \n        emit('question-asked', question)\n        \n        const answer = await mockDataService.searchKnowledge(question)\n        \n        currentAnswer.value = {\n          question,\n          answer: answer.content,\n          confidence: answer.confidence,\n          images: answer.images || [],\n          links: answer.links || [],\n          liked: false,\n          disliked: false,\n          timestamp: new Date()\n        }\n        \n        // 添加到搜索历史\n        searchHistory.value.unshift({\n          id: Date.now(),\n          question,\n          timestamp: new Date()\n        })\n        \n        // 限制历史记录数量\n        if (searchHistory.value.length > 20) {\n          searchHistory.value = searchHistory.value.slice(0, 20)\n        }\n        \n        emit('answer-received', currentAnswer.value)\n        \n        // 清空搜索框\n        searchQuery.value = ''\n        \n      } catch (error) {\n        console.error('Failed to search knowledge:', error)\n      } finally {\n        isSearching.value = false\n      }\n    }\n    \n    const onSearchInput = () => {\n      // 实时搜索建议可以在这里实现\n    }\n    \n    const clearSearch = () => {\n      searchQuery.value = ''\n      currentAnswer.value = null\n    }\n    \n    const selectSuggestion = (suggestion) => {\n      searchQuery.value = suggestion.text\n      performSearch()\n    }\n    \n    const exploreCategory = (category) => {\n      console.log('Exploring category:', category)\n      emit('category-explored', category)\n      // 实际应用中会显示该分类下的知识内容\n    }\n    \n    const viewRecommendation = (recommendation) => {\n      console.log('Viewing recommendation:', recommendation)\n      // 实际应用中会显示推荐内容的详情\n    }\n    \n    const searchFromHistory = (historyItem) => {\n      searchQuery.value = historyItem.question\n      performSearch()\n    }\n    \n    const removeFromHistory = (historyItem) => {\n      const index = searchHistory.value.findIndex(item => item.id === historyItem.id)\n      if (index > -1) {\n        searchHistory.value.splice(index, 1)\n      }\n    }\n    \n    const clearHistory = () => {\n      searchHistory.value = []\n    }\n    \n    const askQuickQuestion = (question) => {\n      searchQuery.value = question.text\n      performSearch()\n    }\n    \n    const formatAnswer = (answer) => {\n      // 简单的文本格式化\n      return answer\n        .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\n        .replace(/\\*(.*?)\\*/g, '<em>$1</em>')\n        .replace(/\\n/g, '<br>')\n    }\n    \n    const formatDate = (date) => {\n      return date.toLocaleDateString('zh-CN', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      })\n    }\n    \n    const formatTime = (timestamp) => {\n      const date = new Date(timestamp)\n      const now = new Date()\n      const diffTime = Math.abs(now - date)\n      const diffMinutes = Math.ceil(diffTime / (1000 * 60))\n      \n      if (diffMinutes < 60) {\n        return `${diffMinutes}分钟前`\n      } else if (diffMinutes < 1440) {\n        return `${Math.ceil(diffMinutes / 60)}小时前`\n      } else {\n        return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })\n      }\n    }\n    \n    const likeAnswer = () => {\n      if (currentAnswer.value) {\n        currentAnswer.value.liked = !currentAnswer.value.liked\n        if (currentAnswer.value.liked) {\n          currentAnswer.value.disliked = false\n        }\n      }\n    }\n    \n    const dislikeAnswer = () => {\n      if (currentAnswer.value) {\n        currentAnswer.value.disliked = !currentAnswer.value.disliked\n        if (currentAnswer.value.disliked) {\n          currentAnswer.value.liked = false\n        }\n      }\n    }\n    \n    const shareAnswer = () => {\n      if (currentAnswer.value) {\n        console.log('Sharing answer:', currentAnswer.value)\n        emit('knowledge-shared', currentAnswer.value)\n        // 实际应用中会打开分享功能\n      }\n    }\n    \n    const saveAnswer = () => {\n      if (currentAnswer.value) {\n        console.log('Saving answer:', currentAnswer.value)\n        // 实际应用中会保存到收藏夹\n      }\n    }\n    \n    const viewImage = (image) => {\n      console.log('Viewing image:', image)\n      // 实际应用中会打开图片查看器\n    }\n\n    // 生命周期\n    let refreshTimer = null\n    \n    onMounted(async () => {\n      await mockDataService.initialize()\n      await loadPediaData()\n      \n      // 设置自动刷新\n      if (props.autoRefresh) {\n        refreshTimer = setInterval(() => {\n          loadPediaData()\n        }, props.refreshInterval)\n      }\n    })\n\n    onUnmounted(() => {\n      if (refreshTimer) {\n        clearInterval(refreshTimer)\n      }\n    })\n\n    return {\n      // 响应式数据\n      isSearching,\n      loadingText,\n      searchQuery,\n      currentAnswer,\n      searchSuggestions,\n      knowledgeCategories,\n      dailyRecommendations,\n      searchHistory,\n      learningStats,\n      quickStartQuestions,\n      \n      // 计算属性\n      cardTitle,\n      hasAnyContent,\n      \n      // 方法\n      performSearch,\n      onSearchInput,\n      clearSearch,\n      selectSuggestion,\n      exploreCategory,\n      viewRecommendation,\n      searchFromHistory,\n      removeFromHistory,\n      clearHistory,\n      askQuickQuestion,\n      formatAnswer,\n      formatDate,\n      formatTime,\n      likeAnswer,\n      dislikeAnswer,\n      shareAnswer,\n      saveAnswer,\n      viewImage\n    }\n  }\n}\n</script>\n\n<style scoped>\n.ai-pedia-card {\n  height: 100%;\n}\n\n.pedia-container {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  padding: 16px;\n  position: relative;\n  overflow-y: auto;\n}\n\n/* 搜索部分 */\n.search-section {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.search-input-container {\n  display: flex;\n  gap: 8px;\n  margin-bottom: 12px;\n}\n\n.search-input {\n  flex: 1;\n  padding: 12px 16px;\n  border: none;\n  border-radius: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  color: rgba(255, 255, 255, 0.9);\n  font-size: 14px;\n  outline: none;\n  transition: all 0.3s ease;\n}\n\n.search-input::placeholder {\n  color: rgba(255, 255, 255, 0.5);\n}\n\n.search-input:focus {\n  background: rgba(255, 255, 255, 0.15);\n  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.3);\n}\n\n.search-input:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.search-btn,\n.clear-btn {\n  width: 40px;\n  height: 40px;\n  border: none;\n  border-radius: 8px;\n  background: rgba(74, 144, 226, 0.3);\n  color: #4a90e2;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.search-btn:hover:not(:disabled) {\n  background: rgba(74, 144, 226, 0.5);\n  transform: scale(1.05);\n}\n\n.search-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.clear-btn {\n  background: rgba(239, 68, 68, 0.3);\n  color: #ef4444;\n}\n\n.clear-btn:hover {\n  background: rgba(239, 68, 68, 0.5);\n}\n\n/* 搜索建议 */\n.search-suggestions {\n  margin-top: 12px;\n}\n\n.suggestions-header {\n  font-size: 12px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 8px;\n}\n\n.suggestions-list {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.suggestion-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border: none;\n  border-radius: 6px;\n  color: rgba(255, 255, 255, 0.8);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-align: left;\n  width: 100%;\n  font-size: 12px;\n}\n\n.suggestion-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateX(2px);\n}\n\n/* 当前问答 */\n.current-qa {\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 12px;\n  padding: 16px;\n  flex: 1;\n  min-height: 0;\n}\n\n.question-section {\n  margin-bottom: 16px;\n}\n\n.question-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  color: #4a90e2;\n  margin-bottom: 8px;\n}\n\n.question-text {\n  font-size: 16px;\n  color: rgba(255, 255, 255, 0.9);\n  line-height: 1.5;\n}\n\n.answer-section {\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n  padding-top: 16px;\n}\n\n.answer-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 12px;\n}\n\n.answer-header > div:first-child {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  color: #7ed321;\n}\n\n.confidence-indicator {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 11px;\n}\n\n.confidence-text {\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.confidence-bar {\n  width: 40px;\n  height: 4px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 2px;\n  overflow: hidden;\n}\n\n.confidence-fill {\n  height: 100%;\n  background: linear-gradient(90deg, #ef4444 0%, #f59e0b 50%, #10b981 100%);\n  transition: width 0.3s ease;\n}\n\n.confidence-value {\n  color: rgba(255, 255, 255, 0.8);\n  font-weight: 500;\n}\n\n.answer-content {\n  margin-bottom: 16px;\n}\n\n.answer-text {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.9);\n  line-height: 1.6;\n  margin-bottom: 16px;\n}\n\n/* 相关图片 */\n.answer-images {\n  margin-bottom: 16px;\n}\n\n.images-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));\n  gap: 8px;\n}\n\n.image-item {\n  cursor: pointer;\n  border-radius: 8px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n.image-item:hover {\n  transform: scale(1.02);\n}\n\n.image-item img {\n  width: 100%;\n  height: 80px;\n  object-fit: cover;\n}\n\n.image-caption {\n  padding: 4px 8px;\n  background: rgba(0, 0, 0, 0.7);\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.8);\n  text-align: center;\n}\n\n/* 相关链接 */\n.related-links {\n  margin-bottom: 16px;\n}\n\n.links-header {\n  font-size: 12px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 8px;\n}\n\n.links-list {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.link-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 6px 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 6px;\n  color: #4a90e2;\n  text-decoration: none;\n  font-size: 12px;\n  transition: all 0.3s ease;\n}\n\n.link-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateX(2px);\n}\n\n/* 答案操作 */\n.answer-actions {\n  display: flex;\n  gap: 8px;\n  padding-top: 12px;\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.action-btn {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  padding: 6px 12px;\n  border: none;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0.1);\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 11px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.action-btn:hover {\n  background: rgba(255, 255, 255, 0.15);\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.action-btn.active {\n  background: rgba(74, 144, 226, 0.3);\n  color: #4a90e2;\n}\n\n/* 知识分类 */\n.knowledge-categories {\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.categories-header h3 {\n  margin: 0 0 4px 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.categories-subtitle {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n  margin-bottom: 16px;\n}\n\n.categories-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 8px;\n}\n\n.category-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.category-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateX(2px);\n}\n\n.category-icon {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: rgba(74, 144, 226, 0.3);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  color: #4a90e2;\n  flex-shrink: 0;\n}\n\n.category-content {\n  flex: 1;\n}\n\n.category-name {\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n}\n\n.category-description {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n  margin-bottom: 2px;\n}\n\n.category-count {\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.5);\n}\n\n.category-arrow {\n  color: rgba(255, 255, 255, 0.4);\n  font-size: 12px;\n}\n\n/* 今日推荐 */\n.daily-recommendations {\n  background: rgba(126, 211, 33, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.recommendations-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.header-content {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  color: #7ed321;\n}\n\n.date-info {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.recommendations-list {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.recommendation-item {\n  display: flex;\n  gap: 12px;\n  padding: 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n}\n\n.recommendation-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateY(-1px);\n}\n\n.recommendation-icon {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: rgba(126, 211, 33, 0.3);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  color: #7ed321;\n  flex-shrink: 0;\n}\n\n.recommendation-content {\n  flex: 1;\n}\n\n.recommendation-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 4px;\n}\n\n.recommendation-summary {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n  margin-bottom: 8px;\n  line-height: 1.4;\n}\n\n.recommendation-meta {\n  display: flex;\n  gap: 12px;\n}\n\n.meta-item {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.5);\n}\n\n.recommendation-badge {\n  position: absolute;\n  top: 8px;\n  right: 8px;\n  background: #ef4444;\n  color: white;\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 8px;\n}\n\n/* 搜索历史 */\n.search-history {\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.history-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.history-header h3 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.clear-history-btn {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  padding: 4px 8px;\n  border: none;\n  border-radius: 4px;\n  background: rgba(239, 68, 68, 0.2);\n  color: #ef4444;\n  font-size: 11px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.clear-history-btn:hover {\n  background: rgba(239, 68, 68, 0.3);\n}\n\n.history-list {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n}\n\n.history-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.history-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n}\n\n.history-icon {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.5);\n  flex-shrink: 0;\n}\n\n.history-content {\n  flex: 1;\n}\n\n.history-question {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n}\n\n.history-time {\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.5);\n}\n\n.history-actions {\n  display: flex;\n}\n\n.remove-btn {\n  width: 20px;\n  height: 20px;\n  border: none;\n  border-radius: 4px;\n  background: rgba(239, 68, 68, 0.2);\n  color: #ef4444;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n}\n\n.remove-btn:hover {\n  background: rgba(239, 68, 68, 0.3);\n}\n\n/* 学习统计 */\n.learning-stats {\n  background: rgba(139, 92, 246, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.stats-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  color: #8b5cf6;\n  margin-bottom: 16px;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 12px;\n  margin-bottom: 16px;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n}\n\n.stat-value {\n  font-size: 18px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 4px;\n}\n\n.stat-label {\n  font-size: 11px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.achievement-section {\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n  padding-top: 12px;\n}\n\n.achievement-header {\n  font-size: 12px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 8px;\n}\n\n.achievement-list {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n}\n\n.achievement-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 6px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 6px;\n}\n\n.achievement-icon {\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  background: rgba(245, 158, 11, 0.3);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n  color: #f59e0b;\n  flex-shrink: 0;\n}\n\n.achievement-content {\n  flex: 1;\n}\n\n.achievement-name {\n  font-size: 11px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 1px;\n}\n\n.achievement-description {\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n/* 无内容状态 */\n.no-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  padding: 32px 16px;\n  flex: 1;\n}\n\n.no-content-icon {\n  font-size: 48px;\n  color: rgba(255, 255, 255, 0.3);\n  margin-bottom: 16px;\n}\n\n.no-content-text h3 {\n  margin: 0 0 8px 0;\n  font-size: 18px;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.no-content-text p {\n  margin: 0 0 24px 0;\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.quick-start {\n  width: 100%;\n  max-width: 300px;\n}\n\n.quick-start-header {\n  font-size: 12px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 12px;\n}\n\n.quick-questions {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n}\n\n.quick-question-btn {\n  padding: 8px 12px;\n  border: none;\n  border-radius: 6px;\n  background: rgba(74, 144, 226, 0.2);\n  color: #4a90e2;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-align: left;\n}\n\n.quick-question-btn:hover {\n  background: rgba(74, 144, 226, 0.3);\n  transform: translateX(2px);\n}\n\n/* 加载状态 */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.3);\n  backdrop-filter: blur(5px);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 12px;\n  border-radius: 12px;\n}\n\n.loading-spinner {\n  width: 32px;\n  height: 32px;\n  border: 3px solid rgba(255, 255, 255, 0.2);\n  border-top: 3px solid #4a90e2;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.loading-text {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* 动画 */\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* 响应式设计 */\n.size-small .pedia-container {\n  padding: 12px;\n  gap: 12px;\n}\n\n.size-small .stats-grid {\n  grid-template-columns: 1fr;\n}\n\n.mode-compact .daily-recommendations,\n.mode-compact .search-history,\n.mode-compact .learning-stats {\n  display: none;\n}\n\n.mode-compact .categories-grid {\n  grid-template-columns: 1fr;\n}\n</style>"], "mappings": "AAgVA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,KAAI,QAAS,KAAI;AAC3E,OAAOC,QAAO,MAAO,iCAAgC;AACrD,OAAOC,eAAc,MAAO,+BAA8B;AAE1D,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE;IACVH;EACF,CAAC;EACDI,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAGC,KAAK,IAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAACC,QAAQ,CAACD,KAAK;IACnE,CAAC;IACDE,QAAQ,EAAE;MACRN,IAAI,EAAEO,MAAM;MACZL,OAAO,EAAEA,CAAA,MAAO;QAAEM,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;IAChC,CAAC;IACDC,KAAK,EAAE;MACLV,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDS,WAAW,EAAE;MACXX,IAAI,EAAEO,MAAM;MACZL,OAAO,EAAEA,CAAA,MAAO,CAAC,CAAC;IACpB,CAAC;IACDU,WAAW,EAAE;MACXZ,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAGC,KAAK,IAAK,CAAC,SAAS,EAAE,UAAU,EAAE,MAAM,CAAC,CAACC,QAAQ,CAACD,KAAK;IACtE,CAAC;IACDS,UAAU,EAAE;MACVb,IAAI,EAAEc,OAAO;MACbZ,OAAO,EAAE;IACX,CAAC;IACDa,cAAc,EAAE;MACdf,IAAI,EAAEc,OAAO;MACbZ,OAAO,EAAE;IACX,CAAC;IACDc,WAAW,EAAE;MACXhB,IAAI,EAAEc,OAAO;MACbZ,OAAO,EAAE;IACX,CAAC;IACDe,SAAS,EAAE;MACTjB,IAAI,EAAEc,OAAO;MACbZ,OAAO,EAAE;IACX,CAAC;IACDgB,WAAW,EAAE;MACXlB,IAAI,EAAEc,OAAO;MACbZ,OAAO,EAAE;IACX,CAAC;IACDiB,eAAe,EAAE;MACfnB,IAAI,EAAEoB,MAAM;MACZlB,OAAO,EAAE,MAAK,CAAE;IAClB;EACF,CAAC;EACDmB,KAAK,EAAE,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,kBAAkB,CAAC;EACrFC,KAAKA,CAACxB,KAAK,EAAE;IAAEyB;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,WAAU,GAAIpC,GAAG,CAAC,KAAK;IAC7B,MAAMqC,WAAU,GAAIrC,GAAG,CAAC,UAAU;IAClC,MAAMsC,WAAU,GAAItC,GAAG,CAAC,EAAE;IAE1B,MAAMuC,aAAY,GAAIvC,GAAG,CAAC,IAAI;IAC9B,MAAMwC,iBAAgB,GAAIxC,GAAG,CAAC,EAAE;IAChC,MAAMyC,mBAAkB,GAAIzC,GAAG,CAAC,EAAE;IAClC,MAAM0C,oBAAmB,GAAI1C,GAAG,CAAC,EAAE;IACnC,MAAM2C,aAAY,GAAI3C,GAAG,CAAC,EAAE;IAC5B,MAAM4C,aAAY,GAAI5C,GAAG,CAAC;MACxB6C,cAAc,EAAE,CAAC;MACjBC,eAAe,EAAE,CAAC;MAClBC,WAAW,EAAE,KAAK;MAClBC,MAAM,EAAE,CAAC;MACTC,YAAY,EAAE;IAChB,CAAC;IAED,MAAMC,mBAAkB,GAAIlD,GAAG,CAAC,CAC9B;MAAEmD,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAW,CAAC,EAC3B;MAAED,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAe,CAAC,EAC/B;MAAED,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAa,CAAC,EAC7B;MAAED,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAU,EAC1B;;IAED;IACA,MAAMC,SAAQ,GAAInD,QAAQ,CAAC,MAAM;MAC/B,IAAIqC,aAAa,CAACvB,KAAK,EAAE;QACvB,OAAO,MAAK;MACd;MACA,OAAO,MAAK;IACd,CAAC;IAED,MAAMsC,aAAY,GAAIpD,QAAQ,CAAC,MAAM;MACnC,OAAOqC,aAAa,CAACvB,KAAI,IAClBwB,iBAAiB,CAACxB,KAAK,CAACuC,MAAK,GAAI,KACjCd,mBAAmB,CAACzB,KAAK,CAACuC,MAAK,GAAI,KACnCb,oBAAoB,CAAC1B,KAAK,CAACuC,MAAK,GAAI,KACpCZ,aAAa,CAAC3B,KAAK,CAACuC,MAAK,GAAI;IACtC,CAAC;;IAED;IACA,MAAMC,aAAY,GAAI,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF,MAAMC,SAAQ,GAAI,MAAMlD,eAAe,CAACmD,YAAY,CAAC;QAErDlB,iBAAiB,CAACxB,KAAI,GAAIyC,SAAS,CAACjB,iBAAgB,IAAK,EAAC;QAC1DC,mBAAmB,CAACzB,KAAI,GAAIyC,SAAS,CAAChB,mBAAkB,IAAK,EAAC;QAC9DC,oBAAoB,CAAC1B,KAAI,GAAIyC,SAAS,CAACf,oBAAmB,IAAK,EAAC;QAChEC,aAAa,CAAC3B,KAAI,GAAIyC,SAAS,CAACd,aAAY,IAAK,EAAC;QAClDC,aAAa,CAAC5B,KAAI,GAAI;UAAE,GAAG4B,aAAa,CAAC5B,KAAK;UAAE,GAAGyC,SAAS,CAACb;QAAc;MAE7E,EAAE,OAAOe,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK;MACnD;IACF;IAEA,MAAME,aAAY,GAAI,MAAAA,CAAA,KAAY;MAChC,IAAI,CAACvB,WAAW,CAACtB,KAAK,CAAC8C,IAAI,CAAC,KAAK1B,WAAW,CAACpB,KAAK,EAAE;MAEpD,IAAI;QACFoB,WAAW,CAACpB,KAAI,GAAI,IAAG;QACvBqB,WAAW,CAACrB,KAAI,GAAI,YAAW;QAE/B,MAAM+C,QAAO,GAAIzB,WAAW,CAACtB,KAAK,CAAC8C,IAAI,CAAC;QAExC3B,IAAI,CAAC,gBAAgB,EAAE4B,QAAQ;QAE/B,MAAMC,MAAK,GAAI,MAAMzD,eAAe,CAAC0D,eAAe,CAACF,QAAQ;QAE7DxB,aAAa,CAACvB,KAAI,GAAI;UACpB+C,QAAQ;UACRC,MAAM,EAAEA,MAAM,CAACE,OAAO;UACtBC,UAAU,EAAEH,MAAM,CAACG,UAAU;UAC7BC,MAAM,EAAEJ,MAAM,CAACI,MAAK,IAAK,EAAE;UAC3BC,KAAK,EAAEL,MAAM,CAACK,KAAI,IAAK,EAAE;UACzBC,KAAK,EAAE,KAAK;UACZC,QAAQ,EAAE,KAAK;UACfC,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB;;QAEA;QACA9B,aAAa,CAAC3B,KAAK,CAAC0D,OAAO,CAAC;UAC1BvB,EAAE,EAAEsB,IAAI,CAACE,GAAG,CAAC,CAAC;UACdZ,QAAQ;UACRS,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;;QAED;QACA,IAAI9B,aAAa,CAAC3B,KAAK,CAACuC,MAAK,GAAI,EAAE,EAAE;UACnCZ,aAAa,CAAC3B,KAAI,GAAI2B,aAAa,CAAC3B,KAAK,CAAC4D,KAAK,CAAC,CAAC,EAAE,EAAE;QACvD;QAEAzC,IAAI,CAAC,iBAAiB,EAAEI,aAAa,CAACvB,KAAK;;QAE3C;QACAsB,WAAW,CAACtB,KAAI,GAAI,EAAC;MAEvB,EAAE,OAAO2C,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK;MACpD,UAAU;QACRvB,WAAW,CAACpB,KAAI,GAAI,KAAI;MAC1B;IACF;IAEA,MAAM6D,aAAY,GAAIA,CAAA,KAAM;MAC1B;IAAA,CACF;IAEA,MAAMC,WAAU,GAAIA,CAAA,KAAM;MACxBxC,WAAW,CAACtB,KAAI,GAAI,EAAC;MACrBuB,aAAa,CAACvB,KAAI,GAAI,IAAG;IAC3B;IAEA,MAAM+D,gBAAe,GAAKC,UAAU,IAAK;MACvC1C,WAAW,CAACtB,KAAI,GAAIgE,UAAU,CAAC5B,IAAG;MAClCS,aAAa,CAAC;IAChB;IAEA,MAAMoB,eAAc,GAAKC,QAAQ,IAAK;MACpCtB,OAAO,CAACuB,GAAG,CAAC,qBAAqB,EAAED,QAAQ;MAC3C/C,IAAI,CAAC,mBAAmB,EAAE+C,QAAQ;MAClC;IACF;IAEA,MAAME,kBAAiB,GAAKC,cAAc,IAAK;MAC7CzB,OAAO,CAACuB,GAAG,CAAC,yBAAyB,EAAEE,cAAc;MACrD;IACF;IAEA,MAAMC,iBAAgB,GAAKC,WAAW,IAAK;MACzCjD,WAAW,CAACtB,KAAI,GAAIuE,WAAW,CAACxB,QAAO;MACvCF,aAAa,CAAC;IAChB;IAEA,MAAM2B,iBAAgB,GAAKD,WAAW,IAAK;MACzC,MAAME,KAAI,GAAI9C,aAAa,CAAC3B,KAAK,CAAC0E,SAAS,CAACC,IAAG,IAAKA,IAAI,CAACxC,EAAC,KAAMoC,WAAW,CAACpC,EAAE;MAC9E,IAAIsC,KAAI,GAAI,CAAC,CAAC,EAAE;QACd9C,aAAa,CAAC3B,KAAK,CAAC4E,MAAM,CAACH,KAAK,EAAE,CAAC;MACrC;IACF;IAEA,MAAMI,YAAW,GAAIA,CAAA,KAAM;MACzBlD,aAAa,CAAC3B,KAAI,GAAI,EAAC;IACzB;IAEA,MAAM8E,gBAAe,GAAK/B,QAAQ,IAAK;MACrCzB,WAAW,CAACtB,KAAI,GAAI+C,QAAQ,CAACX,IAAG;MAChCS,aAAa,CAAC;IAChB;IAEA,MAAMkC,YAAW,GAAK/B,MAAM,IAAK;MAC/B;MACA,OAAOA,MAAK,CACTgC,OAAO,CAAC,gBAAgB,EAAE,qBAAqB,EAC/CA,OAAO,CAAC,YAAY,EAAE,aAAa,EACnCA,OAAO,CAAC,KAAK,EAAE,MAAM;IAC1B;IAEA,MAAMC,UAAS,GAAKC,IAAI,IAAK;MAC3B,OAAOA,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE;MACP,CAAC;IACH;IAEA,MAAMC,UAAS,GAAK/B,SAAS,IAAK;MAChC,MAAM0B,IAAG,GAAI,IAAIzB,IAAI,CAACD,SAAS;MAC/B,MAAMG,GAAE,GAAI,IAAIF,IAAI,CAAC;MACrB,MAAM+B,QAAO,GAAIC,IAAI,CAACC,GAAG,CAAC/B,GAAE,GAAIuB,IAAI;MACpC,MAAMS,WAAU,GAAIF,IAAI,CAACG,IAAI,CAACJ,QAAO,IAAK,IAAG,GAAI,EAAE,CAAC;MAEpD,IAAIG,WAAU,GAAI,EAAE,EAAE;QACpB,OAAO,GAAGA,WAAW,KAAI;MAC3B,OAAO,IAAIA,WAAU,GAAI,IAAI,EAAE;QAC7B,OAAO,GAAGF,IAAI,CAACG,IAAI,CAACD,WAAU,GAAI,EAAE,CAAC,KAAI;MAC3C,OAAO;QACL,OAAOT,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;UAAEE,KAAK,EAAE,OAAO;UAAEC,GAAG,EAAE;QAAU,CAAC;MAC5E;IACF;IAEA,MAAMO,UAAS,GAAIA,CAAA,KAAM;MACvB,IAAItE,aAAa,CAACvB,KAAK,EAAE;QACvBuB,aAAa,CAACvB,KAAK,CAACsD,KAAI,GAAI,CAAC/B,aAAa,CAACvB,KAAK,CAACsD,KAAI;QACrD,IAAI/B,aAAa,CAACvB,KAAK,CAACsD,KAAK,EAAE;UAC7B/B,aAAa,CAACvB,KAAK,CAACuD,QAAO,GAAI,KAAI;QACrC;MACF;IACF;IAEA,MAAMuC,aAAY,GAAIA,CAAA,KAAM;MAC1B,IAAIvE,aAAa,CAACvB,KAAK,EAAE;QACvBuB,aAAa,CAACvB,KAAK,CAACuD,QAAO,GAAI,CAAChC,aAAa,CAACvB,KAAK,CAACuD,QAAO;QAC3D,IAAIhC,aAAa,CAACvB,KAAK,CAACuD,QAAQ,EAAE;UAChChC,aAAa,CAACvB,KAAK,CAACsD,KAAI,GAAI,KAAI;QAClC;MACF;IACF;IAEA,MAAMyC,WAAU,GAAIA,CAAA,KAAM;MACxB,IAAIxE,aAAa,CAACvB,KAAK,EAAE;QACvB4C,OAAO,CAACuB,GAAG,CAAC,iBAAiB,EAAE5C,aAAa,CAACvB,KAAK;QAClDmB,IAAI,CAAC,kBAAkB,EAAEI,aAAa,CAACvB,KAAK;QAC5C;MACF;IACF;IAEA,MAAMgG,UAAS,GAAIA,CAAA,KAAM;MACvB,IAAIzE,aAAa,CAACvB,KAAK,EAAE;QACvB4C,OAAO,CAACuB,GAAG,CAAC,gBAAgB,EAAE5C,aAAa,CAACvB,KAAK;QACjD;MACF;IACF;IAEA,MAAMiG,SAAQ,GAAKC,KAAK,IAAK;MAC3BtD,OAAO,CAACuB,GAAG,CAAC,gBAAgB,EAAE+B,KAAK;MACnC;IACF;;IAEA;IACA,IAAIC,YAAW,GAAI,IAAG;IAEtBhH,SAAS,CAAC,YAAY;MACpB,MAAMI,eAAe,CAAC6G,UAAU,CAAC;MACjC,MAAM5D,aAAa,CAAC;;MAEpB;MACA,IAAI9C,KAAK,CAACoB,WAAW,EAAE;QACrBqF,YAAW,GAAIE,WAAW,CAAC,MAAM;UAC/B7D,aAAa,CAAC;QAChB,CAAC,EAAE9C,KAAK,CAACqB,eAAe;MAC1B;IACF,CAAC;IAED3B,WAAW,CAAC,MAAM;MAChB,IAAI+G,YAAY,EAAE;QAChBG,aAAa,CAACH,YAAY;MAC5B;IACF,CAAC;IAED,OAAO;MACL;MACA/E,WAAW;MACXC,WAAW;MACXC,WAAW;MACXC,aAAa;MACbC,iBAAiB;MACjBC,mBAAmB;MACnBC,oBAAoB;MACpBC,aAAa;MACbC,aAAa;MACbM,mBAAmB;MAEnB;MACAG,SAAS;MACTC,aAAa;MAEb;MACAO,aAAa;MACbgB,aAAa;MACbC,WAAW;MACXC,gBAAgB;MAChBE,eAAe;MACfG,kBAAkB;MAClBE,iBAAiB;MACjBE,iBAAiB;MACjBK,YAAY;MACZC,gBAAgB;MAChBC,YAAY;MACZE,UAAU;MACVM,UAAU;MACVM,UAAU;MACVC,aAAa;MACbC,WAAW;MACXC,UAAU;MACVC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}