{"ast": null, "code": "import { ref, onMounted } from 'vue';\nimport SceneManager from './components/SceneManager.vue';\nimport TestSceneGeneration from './components/TestSceneGeneration.vue';\nimport DynamicWallpaperManager from './components/DynamicWallpaperManager.vue';\nimport VPATestPage from './components/VPATestPage.vue';\nexport default {\n  name: 'App',\n  components: {\n    SceneManager,\n    TestSceneGeneration,\n    DynamicWallpaperManager,\n    VPATestPage\n  },\n  setup() {\n    const currentMode = ref('normal'); // 'normal', 'test', 'vpa'\n    const initialScene = ref('default');\n    const autoSwitchEnabled = ref(false); // 禁用自动切换，避免意外的场景切换\n    const currentWallpaperPrompt = ref('动漫卡通风格的温馨小屋，柔和的阳光，可爱的卡通元素，温馨治愈的氛围');\n    const themeColors = ref(null);\n    const toggleMode = () => {\n      if (currentMode.value === 'normal') {\n        currentMode.value = 'test';\n      } else if (currentMode.value === 'test') {\n        currentMode.value = 'vpa';\n      } else {\n        currentMode.value = 'normal';\n      }\n    };\n    const handleWallpaperPrompt = promptData => {\n      console.log('收到壁纸提示词:', promptData);\n\n      // 处理不同格式的提示词数据\n      if (typeof promptData === 'string') {\n        currentWallpaperPrompt.value = promptData;\n      } else if (promptData && promptData.prompt) {\n        // 使用生成的情感化提示词\n        currentWallpaperPrompt.value = promptData.prompt;\n\n        // 记录详细的生成信息\n        console.log('🎨 情感化提示词生成详情:', {\n          prompt: promptData.prompt,\n          scene: promptData.scene?.name,\n          context: promptData.context,\n          originalPrompt: promptData.originalPrompt\n        });\n      }\n    };\n    const handleSceneChanged = event => {\n      console.log('场景切换:', event);\n\n      // 场景切换时不再直接设置提示词\n      // 等待SceneManager生成情感化提示词并通过wallpaper-prompt-ready事件传递\n      console.log('等待情感化提示词生成...');\n    };\n    const handleWallpaperChanged = wallpaper => {\n      console.log('壁纸已更换:', wallpaper);\n    };\n    const handleColorsExtracted = colors => {\n      console.log('颜色已提取:', colors);\n      themeColors.value = colors;\n    };\n\n    // 页面加载时的欢迎语音\n    const welcomeUser = async () => {\n      console.log('欢迎使用AI-HMI智能场景系统');\n\n      // 根据时间设置初始场景\n      const now = new Date();\n      const hour = now.getHours();\n      if (hour >= 7 && hour <= 9) {\n        initialScene.value = 'morningCommuteFamily';\n      } else if (hour >= 17 && hour <= 19) {\n        initialScene.value = 'eveningCommute';\n      } else if (hour >= 20 || hour <= 6) {\n        initialScene.value = 'rainyNight';\n      }\n    };\n    onMounted(() => {\n      welcomeUser();\n    });\n    return {\n      isTestMode,\n      toggleMode,\n      initialScene,\n      autoSwitchEnabled,\n      currentWallpaperPrompt,\n      themeColors,\n      handleWallpaperPrompt,\n      handleSceneChanged,\n      handleWallpaperChanged,\n      handleColorsExtracted\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "onMounted", "SceneManager", "TestSceneGeneration", "DynamicWallpaperManager", "VPATestPage", "name", "components", "setup", "currentMode", "initialScene", "autoSwitchEnabled", "currentWallpaperPrompt", "themeColors", "toggleMode", "value", "handleWallpaperPrompt", "promptData", "console", "log", "prompt", "scene", "context", "originalPrompt", "handleSceneChanged", "event", "handleWallpaperChanged", "wallpaper", "handleColorsExtracted", "colors", "welcomeUser", "now", "Date", "hour", "getHours", "isTestMode"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\App.vue"], "sourcesContent": ["<template>\r\n  <div id=\"app\">\r\n    <!-- 动态壁纸管理器 - 作为最底层 -->\r\n    <DynamicWallpaperManager\r\n      :scene-prompt=\"currentWallpaperPrompt\"\r\n      :auto-generate=\"true\"\r\n      :enable-config=\"true\"\r\n      @wallpaper-changed=\"handleWallpaperChanged\"\r\n      @colors-extracted=\"handleColorsExtracted\"\r\n    >\r\n      <!-- 模式切换 - 移到右下角 -->\r\n      <div class=\"mode-toggle\">\r\n        <button @click=\"toggleMode\" class=\"toggle-btn\">\r\n          {{ currentMode === 'normal' ? '🧪 测试模式' : currentMode === 'test' ? '👤 VPA测试' : '🚗 正常模式' }}\r\n        </button>\r\n      </div>\r\n\r\n      <!-- 测试模式 -->\r\n      <TestSceneGeneration\r\n        v-if=\"currentMode === 'test'\"\r\n        :theme-colors=\"themeColors\"\r\n        @colors-extracted=\"handleColorsExtracted\"\r\n      />\r\n\r\n      <!-- VPA测试模式 -->\r\n      <VPATestPage\r\n        v-else-if=\"currentMode === 'vpa'\"\r\n        :theme-colors=\"themeColors\"\r\n      />\r\n\r\n      <!-- 正常模式 -->\r\n      <div v-else class=\"normal-mode\">\r\n        <SceneManager\r\n          :initial-scene=\"initialScene\"\r\n          :show-indicator=\"true\"\r\n          :auto-switch=\"autoSwitchEnabled\"\r\n          :theme-colors=\"themeColors\"\r\n          @scene-changed=\"handleSceneChanged\"\r\n          @wallpaper-prompt-ready=\"handleWallpaperPrompt\"\r\n        />\r\n      </div>\r\n    </DynamicWallpaperManager>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, onMounted } from 'vue'\r\nimport SceneManager from './components/SceneManager.vue'\r\nimport TestSceneGeneration from './components/TestSceneGeneration.vue'\r\nimport DynamicWallpaperManager from './components/DynamicWallpaperManager.vue'\r\nimport VPATestPage from './components/VPATestPage.vue'\r\n\r\nexport default {\r\n  name: 'App',\r\n  components: {\r\n    SceneManager,\r\n    TestSceneGeneration,\r\n    DynamicWallpaperManager,\r\n    VPATestPage\r\n  },\r\n\r\n  setup() {\r\n    const currentMode = ref('normal') // 'normal', 'test', 'vpa'\r\n    const initialScene = ref('default')\r\n    const autoSwitchEnabled = ref(false) // 禁用自动切换，避免意外的场景切换\r\n    const currentWallpaperPrompt = ref('动漫卡通风格的温馨小屋，柔和的阳光，可爱的卡通元素，温馨治愈的氛围')\r\n    const themeColors = ref(null)\r\n\r\n    const toggleMode = () => {\r\n      if (currentMode.value === 'normal') {\r\n        currentMode.value = 'test'\r\n      } else if (currentMode.value === 'test') {\r\n        currentMode.value = 'vpa'\r\n      } else {\r\n        currentMode.value = 'normal'\r\n      }\r\n    }\r\n\r\n    const handleWallpaperPrompt = (promptData) => {\r\n      console.log('收到壁纸提示词:', promptData)\r\n      \r\n      // 处理不同格式的提示词数据\r\n      if (typeof promptData === 'string') {\r\n        currentWallpaperPrompt.value = promptData\r\n      } else if (promptData && promptData.prompt) {\r\n        // 使用生成的情感化提示词\r\n        currentWallpaperPrompt.value = promptData.prompt\r\n        \r\n        // 记录详细的生成信息\r\n        console.log('🎨 情感化提示词生成详情:', {\r\n          prompt: promptData.prompt,\r\n          scene: promptData.scene?.name,\r\n          context: promptData.context,\r\n          originalPrompt: promptData.originalPrompt\r\n        })\r\n      }\r\n    }\r\n\r\n    const handleSceneChanged = (event) => {\r\n      console.log('场景切换:', event)\r\n\r\n      // 场景切换时不再直接设置提示词\r\n      // 等待SceneManager生成情感化提示词并通过wallpaper-prompt-ready事件传递\r\n      console.log('等待情感化提示词生成...')\r\n    }\r\n\r\n    const handleWallpaperChanged = (wallpaper) => {\r\n      console.log('壁纸已更换:', wallpaper)\r\n    }\r\n\r\n    const handleColorsExtracted = (colors) => {\r\n      console.log('颜色已提取:', colors)\r\n      themeColors.value = colors\r\n    }\r\n\r\n    // 页面加载时的欢迎语音\r\n    const welcomeUser = async () => {\r\n      console.log('欢迎使用AI-HMI智能场景系统')\r\n      \r\n      // 根据时间设置初始场景\r\n      const now = new Date()\r\n      const hour = now.getHours()\r\n      \r\n      if (hour >= 7 && hour <= 9) {\r\n        initialScene.value = 'morningCommuteFamily'\r\n      } else if (hour >= 17 && hour <= 19) {\r\n        initialScene.value = 'eveningCommute'\r\n      } else if (hour >= 20 || hour <= 6) {\r\n        initialScene.value = 'rainyNight'\r\n      }\r\n    }\r\n\r\n    onMounted(() => {\r\n      welcomeUser()\r\n    })\r\n\r\n    return {\r\n      isTestMode,\r\n      toggleMode,\r\n      initialScene,\r\n      autoSwitchEnabled,\r\n      currentWallpaperPrompt,\r\n      themeColors,\r\n      handleWallpaperPrompt,\r\n      handleSceneChanged,\r\n      handleWallpaperChanged,\r\n      handleColorsExtracted\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n#app {\r\n  width: 100%;\r\n  height: 100vh;\r\n  margin: 0;\r\n  padding: 0;\r\n  font-family: 'Noto Sans', sans-serif;\r\n  overflow: hidden;\r\n  /* 移除默认背景，由DynamicWallpaperManager管理 */\r\n}\r\n\r\n.mode-toggle {\r\n  position: fixed;\r\n  bottom: 20px;\r\n  left: 20px;\r\n  z-index: 9999;\r\n}\r\n\r\n.toggle-btn {\r\n  padding: 8px 16px;\r\n  border: none;\r\n  border-radius: 20px;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  color: #333;\r\n  font-weight: 600;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.toggle-btn:hover {\r\n  background: white;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.normal-mode {\r\n  width: 100%;\r\n  height: 100vh;\r\n}\r\n\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n\r\nbody {\r\n  margin: 0;\r\n  padding: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 全局字体引入 */\r\n@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');\r\n\r\n/* 图标库引入 */\r\n@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');\r\n</style>\r\n"], "mappings": "AA8CA,SAASA,GAAG,EAAEC,SAAQ,QAAS,KAAI;AACnC,OAAOC,YAAW,MAAO,+BAA8B;AACvD,OAAOC,mBAAkB,MAAO,sCAAqC;AACrE,OAAOC,uBAAsB,MAAO,0CAAyC;AAC7E,OAAOC,WAAU,MAAO,8BAA6B;AAErD,eAAe;EACbC,IAAI,EAAE,KAAK;EACXC,UAAU,EAAE;IACVL,YAAY;IACZC,mBAAmB;IACnBC,uBAAuB;IACvBC;EACF,CAAC;EAEDG,KAAKA,CAAA,EAAG;IACN,MAAMC,WAAU,GAAIT,GAAG,CAAC,QAAQ,GAAE;IAClC,MAAMU,YAAW,GAAIV,GAAG,CAAC,SAAS;IAClC,MAAMW,iBAAgB,GAAIX,GAAG,CAAC,KAAK,GAAE;IACrC,MAAMY,sBAAqB,GAAIZ,GAAG,CAAC,mCAAmC;IACtE,MAAMa,WAAU,GAAIb,GAAG,CAAC,IAAI;IAE5B,MAAMc,UAAS,GAAIA,CAAA,KAAM;MACvB,IAAIL,WAAW,CAACM,KAAI,KAAM,QAAQ,EAAE;QAClCN,WAAW,CAACM,KAAI,GAAI,MAAK;MAC3B,OAAO,IAAIN,WAAW,CAACM,KAAI,KAAM,MAAM,EAAE;QACvCN,WAAW,CAACM,KAAI,GAAI,KAAI;MAC1B,OAAO;QACLN,WAAW,CAACM,KAAI,GAAI,QAAO;MAC7B;IACF;IAEA,MAAMC,qBAAoB,GAAKC,UAAU,IAAK;MAC5CC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEF,UAAU;;MAElC;MACA,IAAI,OAAOA,UAAS,KAAM,QAAQ,EAAE;QAClCL,sBAAsB,CAACG,KAAI,GAAIE,UAAS;MAC1C,OAAO,IAAIA,UAAS,IAAKA,UAAU,CAACG,MAAM,EAAE;QAC1C;QACAR,sBAAsB,CAACG,KAAI,GAAIE,UAAU,CAACG,MAAK;;QAE/C;QACAF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;UAC5BC,MAAM,EAAEH,UAAU,CAACG,MAAM;UACzBC,KAAK,EAAEJ,UAAU,CAACI,KAAK,EAAEf,IAAI;UAC7BgB,OAAO,EAAEL,UAAU,CAACK,OAAO;UAC3BC,cAAc,EAAEN,UAAU,CAACM;QAC7B,CAAC;MACH;IACF;IAEA,MAAMC,kBAAiB,GAAKC,KAAK,IAAK;MACpCP,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEM,KAAK;;MAE1B;MACA;MACAP,OAAO,CAACC,GAAG,CAAC,eAAe;IAC7B;IAEA,MAAMO,sBAAqB,GAAKC,SAAS,IAAK;MAC5CT,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEQ,SAAS;IACjC;IAEA,MAAMC,qBAAoB,GAAKC,MAAM,IAAK;MACxCX,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEU,MAAM;MAC5BhB,WAAW,CAACE,KAAI,GAAIc,MAAK;IAC3B;;IAEA;IACA,MAAMC,WAAU,GAAI,MAAAA,CAAA,KAAY;MAC9BZ,OAAO,CAACC,GAAG,CAAC,kBAAkB;;MAE9B;MACA,MAAMY,GAAE,GAAI,IAAIC,IAAI,CAAC;MACrB,MAAMC,IAAG,GAAIF,GAAG,CAACG,QAAQ,CAAC;MAE1B,IAAID,IAAG,IAAK,KAAKA,IAAG,IAAK,CAAC,EAAE;QAC1BvB,YAAY,CAACK,KAAI,GAAI,sBAAqB;MAC5C,OAAO,IAAIkB,IAAG,IAAK,EAAC,IAAKA,IAAG,IAAK,EAAE,EAAE;QACnCvB,YAAY,CAACK,KAAI,GAAI,gBAAe;MACtC,OAAO,IAAIkB,IAAG,IAAK,EAAC,IAAKA,IAAG,IAAK,CAAC,EAAE;QAClCvB,YAAY,CAACK,KAAI,GAAI,YAAW;MAClC;IACF;IAEAd,SAAS,CAAC,MAAM;MACd6B,WAAW,CAAC;IACd,CAAC;IAED,OAAO;MACLK,UAAU;MACVrB,UAAU;MACVJ,YAAY;MACZC,iBAAiB;MACjBC,sBAAsB;MACtBC,WAAW;MACXG,qBAAqB;MACrBQ,kBAAkB;MAClBE,sBAAsB;MACtBE;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}