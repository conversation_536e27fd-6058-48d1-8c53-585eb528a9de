# 动态壁纸开发设计文档（修改版）

## 项目概述

动态壁纸功能是AI-HMI系统的核心特性之一，通过图生视频技术将静态壁纸转换为动态视频壁纸（MP4格式），提升用户的视觉体验和沉浸感。

## 修改后的前端流程

### 核心流程变更

**原方案**: 前端获取壁纸URL → 调用URL接口生成动态壁纸
**新方案**: 前端获取壁纸 → 保存到images目录 → 调用文件上传接口生成动态壁纸

### 详细流程

```
1. 用户生成静态壁纸
        ↓
2. 前端下载壁纸图片到 images/ 目录
        ↓
3. 保存壁纸信息到本地存储
        ↓
4. 用户点击"生成动态壁纸"
        ↓
5. 前端读取 images/ 目录中的壁纸文件
        ↓
6. 调用文件上传接口生成动态壁纸
        ↓
7. 显示生成进度和预览
        ↓
8. 用户确认应用动态壁纸
```

## 技术实现方案

### 1. 前端文件管理服务

**位置**: `ai-hmi/src/services/WallpaperFileManager.js` (新建)

```javascript
class WallpaperFileManager {
  constructor() {
    this.imagesDir = '/images/'
    this.currentWallpaperFile = null
  }

  /**
   * 下载并保存壁纸到本地
   * @param {string} imageUrl - 壁纸图片URL
   * @param {string} taskId - 任务ID
   * @returns {Promise<Object>} 保存结果
   */
  async saveWallpaperToLocal(imageUrl, taskId) {
    try {
      // 生成文件名
      const fileName = `wallpaper_${taskId}.jpg`
      const filePath = `${this.imagesDir}${fileName}`
      
      // 下载图片
      const response = await fetch(imageUrl)
      if (!response.ok) {
        throw new Error(`下载失败: ${response.status}`)
      }
      
      const blob = await response.blob()
      
      // 保存到本地存储（模拟文件系统）
      const fileData = {
        name: fileName,
        path: filePath,
        size: blob.size,
        type: blob.type,
        lastModified: Date.now(),
        taskId: taskId,
        originalUrl: imageUrl
      }
      
      // 保存文件信息到localStorage
      this.saveFileInfo(fileData)
      
      // 保存文件内容到IndexedDB（用于后续上传）
      await this.saveFileToIndexedDB(fileName, blob)
      
      this.currentWallpaperFile = fileData
      
      return {
        success: true,
        file: fileData,
        message: '壁纸已保存到本地'
      }
      
    } catch (error) {
      console.error('保存壁纸失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 获取当前壁纸文件
   * @returns {Object|null} 当前壁纸文件信息
   */
  getCurrentWallpaperFile() {
    if (!this.currentWallpaperFile) {
      const savedFile = localStorage.getItem('currentWallpaperFile')
      if (savedFile) {
        this.currentWallpaperFile = JSON.parse(savedFile)
      }
    }
    return this.currentWallpaperFile
  }

  /**
   * 从IndexedDB获取文件Blob
   * @param {string} fileName - 文件名
   * @returns {Promise<Blob>} 文件Blob
   */
  async getFileFromIndexedDB(fileName) {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('WallpaperFiles', 1)
      
      request.onerror = () => reject(new Error('无法访问IndexedDB'))
      
      request.onsuccess = (event) => {
        const db = event.target.result
        const transaction = db.transaction(['files'], 'readonly')
        const store = transaction.objectStore('files')
        const getRequest = store.get(fileName)
        
        getRequest.onsuccess = () => {
          if (getRequest.result) {
            resolve(getRequest.result.blob)
          } else {
            reject(new Error('文件不存在'))
          }
        }
        
        getRequest.onerror = () => reject(new Error('读取文件失败'))
      }
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result
        if (!db.objectStoreNames.contains('files')) {
          db.createObjectStore('files', { keyPath: 'name' })
        }
      }
    })
  }

  /**
   * 保存文件到IndexedDB
   * @param {string} fileName - 文件名
   * @param {Blob} blob - 文件内容
   */
  async saveFileToIndexedDB(fileName, blob) {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('WallpaperFiles', 1)
      
      request.onerror = () => reject(new Error('无法访问IndexedDB'))
      
      request.onsuccess = (event) => {
        const db = event.target.result
        const transaction = db.transaction(['files'], 'readwrite')
        const store = transaction.objectStore('files')
        
        const putRequest = store.put({
          name: fileName,
          blob: blob,
          timestamp: Date.now()
        })
        
        putRequest.onsuccess = () => resolve()
        putRequest.onerror = () => reject(new Error('保存文件失败'))
      }
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result
        if (!db.objectStoreNames.contains('files')) {
          db.createObjectStore('files', { keyPath: 'name' })
        }
      }
    })
  }

  /**
   * 保存文件信息到localStorage
   * @param {Object} fileInfo - 文件信息
   */
  saveFileInfo(fileInfo) {
    localStorage.setItem('currentWallpaperFile', JSON.stringify(fileInfo))
  }

  /**
   * 清理旧文件
   * @param {number} maxAge - 最大保留时间（毫秒）
   */
  async cleanupOldFiles(maxAge = 24 * 60 * 60 * 1000) { // 默认24小时
    try {
      const request = indexedDB.open('WallpaperFiles', 1)
      
      request.onsuccess = (event) => {
        const db = event.target.result
        const transaction = db.transaction(['files'], 'readwrite')
        const store = transaction.objectStore('files')
        const getAllRequest = store.getAll()
        
        getAllRequest.onsuccess = () => {
          const files = getAllRequest.result
          const now = Date.now()
          
          files.forEach(file => {
            if (now - file.timestamp > maxAge) {
              store.delete(file.name)
            }
          })
        }
      }
    } catch (error) {
      console.warn('清理旧文件失败:', error)
    }
  }

  /**
   * 检查是否有可用的壁纸文件
   * @returns {boolean} 是否有可用文件
   */
  hasAvailableWallpaper() {
    const file = this.getCurrentWallpaperFile()
    return file && file.path && file.taskId
  }
}

export default WallpaperFileManager
```

### 2. 修改ImageGenerationService

**位置**: `ai-hmi/src/services/ImageGenerationService.js`

```javascript
import LlmService from './LlmService'
import WallpaperFileManager from './WallpaperFileManager'

class ImageGenerationService {
  constructor() {
    this.apiBaseUrl = process.env.VUE_APP_API_BASE_URL || 'http://localhost:8000'
    this.fileManager = new WallpaperFileManager()
  }

  async generateWallpaper(prompt, taskId = null) {
    if (!taskId) {
      taskId = `wallpaper_${Date.now()}`
    }

    try {
      // 使用LLM增强提示词
      const llmService = new LlmService()
      const enhancedPrompt = await llmService.generateResponse(prompt)
      
      // 调用简化版的文生图接口（用于AI-HMI测试）
      const response = await fetch(`${this.apiBaseUrl}/api/v1/kolors-simple/text-to-image`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          prompt: enhancedPrompt, 
          task_id: taskId
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      
      // 保存壁纸到本地
      const saveResult = await this.fileManager.saveWallpaperToLocal(data.image_url, taskId)
      
      if (!saveResult.success) {
        console.warn('保存壁纸到本地失败:', saveResult.error)
      }
      
      return {
        imageUrl: data.image_url,
        taskId: taskId,
        prompt: prompt,
        localFile: saveResult.file
      }
    } catch (error) {
      console.error('壁纸生成失败:', error)
      return this.getFallbackWallpaper()
    }
  }

  // ... 其他现有方法保持不变 ...

  /**
   * 获取当前壁纸文件信息
   * @returns {Object|null} 当前壁纸文件
   */
  getCurrentWallpaperFile() {
    return this.fileManager.getCurrentWallpaperFile()
  }

  /**
   * 检查是否有可用的壁纸文件用于生成动态壁纸
   * @returns {boolean} 是否有可用文件
   */
  hasAvailableWallpaperForDynamic() {
    return this.fileManager.hasAvailableWallpaper()
  }
}

export default ImageGenerationService
```

### 3. 创建DynamicWallpaperService

**位置**: `ai-hmi/src/services/DynamicWallpaperService.js` (新建)

```javascript
import WallpaperFileManager from './WallpaperFileManager'

class DynamicWallpaperService {
  constructor() {
    this.apiBaseUrl = process.env.VUE_APP_API_BASE_URL || 'http://localhost:8000'
    this.fileManager = new WallpaperFileManager()
  }

  /**
   * 生成动态壁纸
   * @param {Object} options - 生成选项
   * @param {Function} options.onProgress - 进度回调
   * @returns {Promise<Object>} 生成结果
   */
  async generateDynamicWallpaper(options = {}) {
    const { onProgress } = options
    
    try {
      // 检查是否有可用的壁纸文件
      const wallpaperFile = this.fileManager.getCurrentWallpaperFile()
      if (!wallpaperFile) {
        throw new Error('没有可用的壁纸文件，请先生成静态壁纸')
      }

      // 从IndexedDB获取文件Blob
      const fileBlob = await this.fileManager.getFileFromIndexedDB(wallpaperFile.name)
      
      // 创建FormData
      const formData = new FormData()
      formData.append('file', fileBlob, wallpaperFile.name)
      formData.append('task_id', `dynamic_${wallpaperFile.taskId}`)

      // 调用文件上传接口
      const response = await fetch(`${this.apiBaseUrl}/api/v1/dynamic-wallpaper/dynamic-wallpaper`, {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      
      // 更新进度
      if (onProgress) {
        onProgress({
          percentage: 100,
          message: '生成完成！',
          status: 'success'
        })
      }

      return {
        promptId: result.prompt_id,
        videoUrl: result.video_url,
        taskId: result.task_id,
        sourceFile: wallpaperFile
      }

    } catch (error) {
      console.error('动态壁纸生成失败:', error)
      
      if (onProgress) {
        onProgress({
          percentage: 0,
          message: `生成失败: ${error.message}`,
          status: 'error'
        })
      }
      
      throw error
    }
  }

  /**
   * 检查生成状态
   * @param {string} promptId - ComfyUI任务ID
   * @returns {Promise<Object>} 状态信息
   */
  async checkGenerationStatus(promptId) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/v1/dynamic-wallpaper/status/${promptId}`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('检查状态失败:', error)
      throw error
    }
  }

  /**
   * 取消生成任务
   * @param {string} promptId - ComfyUI任务ID
   * @returns {Promise<Object>} 取消结果
   */
  async cancelGeneration(promptId) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/v1/dynamic-wallpaper/cancel/${promptId}`, {
        method: 'POST'
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('取消任务失败:', error)
      throw error
    }
  }

  /**
   * 获取生成历史
   * @returns {Array} 生成历史
   */
  getGenerationHistory() {
    const history = localStorage.getItem('dynamic_wallpaper_history')
    return history ? JSON.parse(history) : []
  }

  /**
   * 保存到生成历史
   * @param {Object} result - 生成结果
   */
  saveToHistory(result) {
    const history = this.getGenerationHistory()
    history.unshift({
      ...result,
      timestamp: new Date().toISOString()
    })
    
    // 只保留最近20条记录
    const trimmedHistory = history.slice(0, 20)
    localStorage.setItem('dynamic_wallpaper_history', JSON.stringify(trimmedHistory))
  }
}

export default DynamicWallpaperService
```

### 4. 修改DynamicWallpaperManager组件

**位置**: `ai-hmi/src/components/DynamicWallpaperManager.vue`

```vue
<template>
  <div class="dynamic-wallpaper-manager">
    <!-- 生成控制区域 -->
    <div class="generation-controls">
      <el-button 
        type="primary" 
        :loading="isGenerating"
        :disabled="!canGenerate"
        @click="generateDynamicWallpaper"
        size="large"
        class="generate-btn"
      >
        <i class="el-icon-video-camera"></i>
        {{ getGenerateButtonText() }}
      </el-button>
      
      <!-- 文件状态显示 -->
      <div v-if="currentWallpaperFile" class="file-status">
        <i class="el-icon-document"></i>
        <span>已保存: {{ currentWallpaperFile.name }}</span>
      </div>
      
      <div v-else class="file-status warning">
        <i class="el-icon-warning"></i>
        <span>请先生成静态壁纸</span>
      </div>
    </div>

    <!-- 生成状态显示 -->
    <div v-if="isGenerating" class="generation-status">
      <el-progress 
        :percentage="generationProgress" 
        :status="progressStatus"
        :stroke-width="8"
      ></el-progress>
      <p class="status-text">{{ generationStatusText }}</p>
      
      <!-- 取消按钮 -->
      <el-button 
        type="danger" 
        size="small" 
        @click="cancelGeneration"
        plain
      >
        取消生成
      </el-button>
    </div>

    <!-- 结果预览 -->
    <div v-if="generatedVideo" class="result-preview">
      <video 
        :src="generatedVideo.videoUrl" 
        controls 
        autoplay 
        loop 
        muted
        class="preview-video"
      ></video>
      
      <div class="result-actions">
        <el-button @click="applyWallpaper" type="success">
          应用为壁纸
        </el-button>
        <el-button @click="downloadVideo" type="default">
          下载视频
        </el-button>
        <el-button @click="clearPreview" type="info" plain>
          清除预览
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import DynamicWallpaperService from '@/services/DynamicWallpaperService'
import ImageGenerationService from '@/services/ImageGenerationService'

export default {
  name: 'DynamicWallpaperManager',
  data() {
    return {
      isGenerating: false,
      generationProgress: 0,
      generationStatusText: '',
      progressStatus: '',
      currentPromptId: null,
      generatedVideo: null,
      currentWallpaperFile: null
    }
  },
  computed: {
    canGenerate() {
      return this.currentWallpaperFile && !this.isGenerating
    }
  },
  mounted() {
    this.loadCurrentWallpaperFile()
    this.setupEventListeners()
  },
  methods: {
    loadCurrentWallpaperFile() {
      const imageService = new ImageGenerationService()
      this.currentWallpaperFile = imageService.getCurrentWallpaperFile()
    },
    
    getGenerateButtonText() {
      if (this.isGenerating) return '生成中...'
      if (this.currentWallpaperFile) return '生成动态壁纸'
      return '请先生成静态壁纸'
    },
    
    async generateDynamicWallpaper() {
      try {
        this.isGenerating = true
        this.generationProgress = 0
        this.generationStatusText = '准备生成动态壁纸...'
        
        const service = new DynamicWallpaperService()
        
        const result = await service.generateDynamicWallpaper({
          onProgress: this.handleProgress
        })
        
        this.generatedVideo = result
        this.generationStatusText = '生成完成！'
        this.generationProgress = 100
        this.progressStatus = 'success'
        
        // 保存到历史记录
        service.saveToHistory(result)
        
        this.$message.success('动态壁纸生成成功！')
        
      } catch (error) {
        this.handleGenerationError(error)
      } finally {
        this.isGenerating = false
      }
    },
    
    handleProgress(progress) {
      this.generationProgress = progress.percentage
      this.generationStatusText = progress.message
      
      if (progress.status === 'error') {
        this.progressStatus = 'exception'
      } else if (progress.percentage === 100) {
        this.progressStatus = 'success'
      } else {
        this.progressStatus = ''
      }
    },
    
    async cancelGeneration() {
      if (this.currentPromptId) {
        const service = new DynamicWallpaperService()
        await service.cancelGeneration(this.currentPromptId)
      }
      
      this.isGenerating = false
      this.generationProgress = 0
      this.generationStatusText = '已取消生成'
      this.currentPromptId = null
    },
    
    handleGenerationError(error) {
      console.error('动态壁纸生成失败:', error)
      this.progressStatus = 'exception'
      this.generationStatusText = `生成失败: ${error.message}`
      
      this.$message.error({
        message: `动态壁纸生成失败: ${error.message}`,
        duration: 5000
      })
    },
    
    async applyWallpaper() {
      try {
        // 应用动态壁纸到全局状态
        this.$store.dispatch('wallpaper/setDynamicWallpaper', {
          videoUrl: this.generatedVideo.videoUrl,
          sourceFile: this.generatedVideo.sourceFile
        })
        
        // 保存到本地存储
        localStorage.setItem('currentDynamicWallpaper', JSON.stringify(this.generatedVideo))
        
        this.$message.success('动态壁纸已应用！')
        
      } catch (error) {
        this.$message.error('应用壁纸失败: ' + error.message)
      }
    },
    
    downloadVideo() {
      const link = document.createElement('a')
      link.href = this.generatedVideo.videoUrl
      link.download = `dynamic_wallpaper_${this.generatedVideo.taskId}.mp4`
      link.click()
    },
    
    clearPreview() {
      this.generatedVideo = null
      this.generationProgress = 0
      this.generationStatusText = ''
      this.progressStatus = ''
    },
    
    setupEventListeners() {
      // 监听静态壁纸更新事件
      this.$eventBus.$on('wallpaper:generated', (data) => {
        this.loadCurrentWallpaperFile()
      })
    }
  },
  
  beforeDestroy() {
    this.$eventBus.$off('wallpaper:generated')
  }
}
</script>

<style scoped>
.dynamic-wallpaper-manager {
  padding: 20px;
}

.generation-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.generate-btn {
  min-width: 160px;
}

.file-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  font-size: 14px;
}

.file-status.warning {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.generation-status {
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.status-text {
  margin: 10px 0;
  color: #666;
}

.result-preview {
  background: rgba(255, 255, 255, 0.05);
  padding: 20px;
  border-radius: 8px;
}

.preview-video {
  width: 100%;
  max-width: 600px;
  border-radius: 8px;
  margin-bottom: 15px;
}

.result-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}
</style>
```

## 用户体验优化

### 1. 文件状态显示

- 显示当前保存的壁纸文件名
- 文件大小和保存时间
- 文件可用性检查

### 2. 智能提示

- 无壁纸文件时显示"请先生成静态壁纸"
- 有文件时显示"已保存: wallpaper_xxx.jpg"
- 生成过程中显示详细进度

### 3. 错误处理

- 文件不存在时的友好提示
- 网络错误的重试机制
- 文件格式验证

## 开发优先级

### 第一阶段：核心功能
1. **WallpaperFileManager.js** - 文件管理服务
2. **ImageGenerationService.js** - 集成文件保存功能
3. **DynamicWallpaperService.js** - 文件上传接口调用
4. **DynamicWallpaperManager.vue** - 基础UI和逻辑

### 第二阶段：用户体验
1. 文件状态显示和验证
2. 进度反馈和错误处理
3. 预览和应用功能

### 第三阶段：优化
1. 文件清理机制
2. 性能优化
3. 扩展功能

## 测试重点

1. **文件保存和读取** - IndexedDB操作
2. **文件上传** - FormData和API调用
3. **错误处理** - 各种异常情况
4. **用户体验** - 状态显示和交互

这个修改方案的优势：
- ✅ 简化了前端流程，无需URL处理
- ✅ 直接使用文件上传接口，更可靠
- ✅ 本地文件管理，便于调试和测试
- ✅ 保持了良好的用户体验 