# 产品需求文档 (Product Requirements Document)

- **文档版本:** 9.0 (Gemini Refactored)
- **状态:** 正式版

---

## 1. 项目愿景与核心哲学

### 1.1. 愿景

打造一个主动式、情景感知、高度个性化的智能座舱体验。我们的目标是让车载系统从一个被动的工具，进化为一个能理解用户意图、预测用户需求、并主动提供服务的**"数字出行伴侣" (Virtual Personal Assistant - VPA)**。

### 1.2. 核心哲学：AI作为"座舱设计师"

本项目的核心是**"生成式UI引擎"**。AI的角色不再是执行预设脚本，而是像一个经验丰富的设计师，根据实时情景，从一个预设的、丰富的"素材库"中，为用户**决策、组合和渲染**出最佳的座舱体验，确保在任何时刻，都为用户呈现最合理、最高效、最美观的界面。

### 1.3. 核心机制：基于桌面画布的动态组合

AI的"设计"工作并非凭空创造，而是基于一个**统一的、结构化的桌面画布**。这个画布由以下两个核心元素定义：

1. **网格系统 (The Grid)**: 一个覆盖全屏的、高精度的 16列 x 9行 网格系统，为AI提供了更精细的布局能力和更大的设计自由度。
2. **组件库 (The Component Library)**: 一套预先定义好的、具有固定尺寸和功能的标准化UI组件（如天气卡片、音乐卡片等）。

**AI HMI的全部动态表现，其本质都是AI在这个统一的桌面画布上，对组件库中的组件进行智能化的"选择"、"放置"和"更新"的排列组合过程。**

`2_Design_System.md` 中定义的**"默认桌面布局"**，可以被视为这个排列组合过程的一个**最基础、最高频的"预设方案"**。它是系统的初始状态、简单场景下的首选方案，以及所有复杂布局变化的起点和回退终点。

---

## 2. 核心概念

- **VPA 数字人 (Virtual Personal Assistant)**: 系统的智能核心与视觉化身。
- **灵动岛 (Dynamic Island)**: 屏幕顶部的持久化状态栏。
- **动态卡片 (Dynamic Cards)**: 由AI根据场景动态选择、布局的信息载体。
- **临时交互面板 (Temporary Dialog Panel)**: 用于AI发起高优先级主动服务的模态对话框。
- **样式主题 (Style Themes)**: 一套完整的视觉语言（如“宁静”、“赛博朋克”），由AI选择应用。
- **过渡效果 (Transition Effects)**: 用于场景切换时的酷炫动画（如“深度模糊”、“蒙版擦除”）。

---

## 3. LLM驱动机制

AI(LLM)完成情景分析和决策后，**不会生成任何UI代码**。相反，它会输出一个标准化的**JSON对象**，我们称之为“桌面渲染计划”。前端系统的唯一职责就是解析这份计划，并将其渲染为用户看到的界面。这种机制将AI的“创造性思维”与前端的“工程实现”完全解耦，是整个系统的基石。

*(详细数据结构定义见 `4_API_Specification.md`)*

---

## 4. 高层级场景剧本：用户早高峰通勤 (旗舰版)

本剧本旨在说明系统的核心业务逻辑，而非具体的UI布局。核心用户画像为“有孩子的上班族，习惯在通勤路上处理事务”。

1.  **启动与环境感知**: 用户（主人）和孩子（毛毛）进入车辆。AI通过车内多模态识别系统识别到乘客“毛毛”，并主动问候：“Hello，主人和毛毛你好。今天是周一，按往常一样先送毛毛上学，再出发前往公司吧。” 同时，播报车辆状态和天气。

2.  **情景化内容服务 (送学途中)**: 导航自动设定至学校。途中，毛毛提问：“地球为什么是圆的？” AI调用百科功能进行科普解答。接着，AI无缝切换到幼儿教育功能，为毛毛播放上次未看完的教育视频。

3.  **场景切换 (抵达学校)**: AI提示：“学校到了，毛毛再见。” 导航自动更新，最终目的地为公司。

4.  **主动情感关怀 (上班途中)**: AI分析用户日程，提示“您一会开会有点紧张”，并主动推荐放松音乐，同时将画布层切换为匹配的动态壁纸。

5.  **主动服务推荐 (路径规划与点单)**: 用户提出需求：“帮我规划一个途径的麦当劳，我要买一份早餐，再帮我点一杯咖啡。” AI调用路线规划与第三方服务，推荐最优路线并执行点单，反馈订单状态。

6.  **日程提醒与抵达**: AI在合适的时间发出会议提醒。抵达公司后，AI总结并结束任务：“已到达公司，祝您工作愉快。记得取咖啡哦。” 界面恢复到简洁的待机状态。

---

## 5. AI决策原则与约束

- **布局稳定性原则**: 优先使用核心场景的“默认布局模板”，避免界面频繁剧烈变化。
- **信息过载保护原则**: 任何时候卡片总数不超过3个，保证界面简洁。
- **用户干预与学习原则**: 允许用户“不喜欢”某个布局，AI会记录该偏好并进行学习。

---

## 6. 新增功能需求

### 6.1. 智能语音助手增强

- **多语言支持**: 支持中英文混合识别和响应，适应用户的语言习惯
- **情感语音合成**: VPA能够根据情境调整语音的情感色彩（温暖、专业、活泼等）
- **噪音抑制**: 在高速行驶等嘈杂环境下保持高精度语音识别
- **离线语音**: 核心语音功能支持离线模式，确保网络不稳定时的基本交互
- **声纹识别**: 支持多用户声纹识别，自动切换个人配置

### 6.2. 高级驾驶辅助集成

- **ADAS状态可视化**: 实时显示自动驾驶、车道保持、自适应巡航等功能状态
- **安全预警系统**: 智能识别潜在危险并提供视觉和语音预警
- **驾驶行为分析**: 分析驾驶习惯并提供个性化的安全建议
- **疲劳检测**: 通过摄像头和方向盘传感器检测驾驶员疲劳状态
- **盲区监测**: 集成盲区监测信息到HMI界面中

### 6.3. 车联网服务扩展

- **实时交通优化**: 结合实时路况和个人偏好优化路线
- **智能充电管理**: 电动车充电站推荐、预约和支付集成
- **远程车辆控制**: 通过手机App远程控制空调、车窗、车门等
- **OTA升级管理**: 智能化的系统更新和功能升级管理
- **车队管理**: 支持家庭多车辆的统一管理

### 6.4. 个性化体验深化

- **用户画像精准化**: 基于驾驶行为、音乐偏好、路线选择等构建精准用户画像
- **场景记忆功能**: 记住用户在特定场景下的偏好设置
- **家庭成员识别**: 支持多用户配置文件，自动识别并切换个人设置
- **情绪感知优化**: 通过语音、面部表情、驾驶行为综合判断用户情绪状态
- **学习型推荐**: 基于用户行为持续优化推荐算法

### 6.5. 安全与隐私保护

- **数据加密**: 所有用户数据采用端到端加密
- **隐私模式**: 支持访客模式，隐藏个人信息
- **数据本地化**: 敏感数据优先本地存储和处理
- **权限管理**: 细粒度的功能权限控制
- **安全审计**: 定期安全检查和漏洞修复

### 6.6. 新增功能需求 (基于旗舰场景)

- **车内成员识别**: 在`高级驾驶辅助集成`中，增加对特定家庭成员（尤其是儿童）的识别能力。
- **第三方服务集成**: 在`车联网服务扩展`中，明确支持第三方餐饮点单与支付接口 (Food & Beverage Ordering API)。
- **知识与内容服务**: 在`个性化体验深化`中，增加对外部知识库/百科（Pedia/Knowledge Base API）和儿童内容服务（Kids' Content API）的集成能力。

---

## 6. 核心界面布局原型

### 6.1. 默认桌面布局 (16x9网格)

```
+--------------------------------------------------------------------------+
| [灵动岛: 时间 14:30 | 天气 22°C | 电量 85%]                               |
+--------------------------------------------------------------------------+
|                                                                          |
|                          主背景画布区域                                  |
|                     (动态壁纸/地图/氛围背景)                            |
|                                                                          |
|                                                                          |
|  [VPA角色]                                                              |
|   (o.o)                                                                  |
|                                                                          |
| +------------------+ +-------------------+ +----------------------------+ |
| | 天气卡片         | | 音乐控制卡片      | | 快捷操作卡片                | |
| | 22°C 晴天        | | ♪ 正在播放...     | | [导航] [电话] [设置]        | |
| | [详细天气]       | | [上一首|暂停|下一首] | | [空调] [车窗] [灯光]        | |
| +------------------+ +-------------------+ +----------------------------+ |
+--------------------------------------------------------------------------+
```

### 6.2. 导航专注模式布局

```
+--------------------------------------------------------------------------+
| [灵动岛: 导航至公司 - 剩余15分钟 | 下一步: 右转500米]                      |
+--------------------------------------------------------------------------+
|                                                                          |
|                            导航地图背景                                  |
|                        (路线高亮显示)                                   |
|                                                                          |
|                                                                          |
|                                                                          |
|                                                                          |
|                                                                          |
| [VPA角色]                                                             |
| +------------------+ +-------------------+ +----------------------------+ |
| | 音乐控制         | | 路况信息          | | 紧急联系人                  | |
| | ♪ 播放中...      | | 前方拥堵 +3分钟   | | [家人] [同事] [救援]        | |
| | [||] [>] [>>]    | | [避开拥堵]        | |                            | |
| +------------------+ +-------------------+ +----------------------------+ |
+--------------------------------------------------------------------------+
```

### 6.3. VPA主动服务对话模式

```
+--------------------------------------------------------------------------+
| [灵动岛: 正常状态显示]                                                   |
+--------------------------------------------------------------------------+
|                                                                          |
|                          背景画布                                        |
|                                                                          |
|    +--------------------------------------------------+                  |
|    |                                                  |                  |
|    |  [VPA头像] 检测到您今天心情不错！                |                  |
|    |   (^.^)     要不要播放一些轻松的音乐？          |                  |
|    |                                                  |                  |
|    |             [好的] [不用了] [稍后提醒]           |                  |
|    |                                                  |                  |
|    +--------------------------------------------------+                  |
|                                                                          |
| [背景中的其他卡片保持半透明状态]                                         |
+--------------------------------------------------------------------------+
```

### 6.4. 多用户识别界面

```
+--------------------------------------------------------------------------+
| [灵动岛: 检测到新用户]                                                   |
+--------------------------------------------------------------------------+
|                                                                          |
|                          欢迎界面背景                                    |
|                                                                          |
|              +----------------------------------------+                    |
|              |                                        |                    |
|              |  欢迎使用智能座舱系统                  |                    |
|              |                                        |                    |
|              |  [用户1头像] [用户2头像] [访客模式]    |                    |
|              |     张三        李四        Guest     |                    |
|              |                                        |                    |
|              |  或者说出您的姓名进行语音识别...        |                    |
|              |                                        |                    |
|              +----------------------------------------+                    |
|                                                                          |
+--------------------------------------------------------------------------+
```
