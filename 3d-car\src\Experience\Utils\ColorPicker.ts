import { colorPresets, useCarColorStore } from './Store';

export class ColorPicker {
  container: HTMLDivElement;
  activeIndex: number;
  isVisible: boolean;
  containerStyle: Record<string, string>;
  pickerStyle: Record<string, string>;
  colorItemStyle: Record<string, string>;
  activeItemStyle: Record<string, string>;
  toggleButton: HTMLButtonElement;
  
  constructor() {
    this.container = document.createElement('div');
    this.activeIndex = 0;
    this.isVisible = false;
    this.toggleButton = document.createElement('button');
    
    // 容器样式
    this.containerStyle = {
      position: 'fixed',
      bottom: '80px',
      left: '50%',
      transform: 'translateX(-50%)',
      zIndex: '10000',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: '10px',
      opacity: '0',
      transition: 'opacity 0.3s ease, transform 0.3s ease',
      pointerEvents: 'none',
    };
    
    // 颜色选择器容器样式 - 毛玻璃效果
    this.pickerStyle = {
      display: 'flex',
      flexWrap: 'wrap',
      justifyContent: 'center',
      gap: '12px',
      padding: '20px',
      backgroundColor: 'rgba(20, 20, 20, 0.5)', // 更透明的背景色
      backdropFilter: 'blur(10px)', // 毛玻璃效果
      WebkitBackdropFilter: 'blur(10px)', // Safari 支持
      borderRadius: '15px',
      maxWidth: '320px',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
      border: '1px solid rgba(255, 255, 255, 0.1)', // 微妙的边框增强毛玻璃感
    };
    
    // 颜色项样式
    this.colorItemStyle = {
      width: '36px',
      height: '36px',
      borderRadius: '50%',
      cursor: 'pointer',
      transition: 'transform 0.2s ease, box-shadow 0.2s ease',
      boxSizing: 'border-box',
      border: '2px solid transparent',
    };
    
    // 激活状态的颜色项样式
    this.activeItemStyle = {
      transform: 'scale(1.2)',
      border: '2px solid white',
      boxShadow: '0 0 15px rgba(255, 255, 255, 0.8)',
    };
    
    this.init();
  }
  
  init() {
    // 应用容器样式
    Object.assign(this.container.style, this.containerStyle);
    
    // 创建标题
    const title = document.createElement('div');
    title.textContent = '选择车身颜色';
    title.style.color = 'white';
    title.style.fontSize = '16px';
    title.style.fontWeight = 'bold';
    title.style.marginBottom = '10px';
    title.style.textShadow = '0 2px 4px rgba(0, 0, 0, 0.5)';
    this.container.appendChild(title);
    
    // 创建颜色选择器容器
    const pickerContainer = document.createElement('div');
    Object.assign(pickerContainer.style, this.pickerStyle);
    
    // 添加webkit前缀
    (pickerContainer.style as any)['-webkit-backdrop-filter'] = 'blur(10px)';
    
    // 创建颜色选项
    colorPresets.forEach((preset, index) => {
      const colorItem = document.createElement('div');
      Object.assign(colorItem.style, this.colorItemStyle);
      colorItem.style.backgroundColor = preset.color;
      
      // 设置数据属性
      colorItem.dataset.color = preset.color;
      colorItem.dataset.index = String(index);
      colorItem.title = preset.name;
      
      // 激活默认颜色
      if (index === this.activeIndex) {
        Object.assign(colorItem.style, this.activeItemStyle);
      }
      
      // 悬停效果
      colorItem.addEventListener('mouseenter', () => {
        if (index !== this.activeIndex) {
          colorItem.style.transform = 'scale(1.1)';
          colorItem.style.boxShadow = '0 0 10px rgba(255, 255, 255, 0.3)';
        }
      });
      
      colorItem.addEventListener('mouseleave', () => {
        if (index !== this.activeIndex) {
          colorItem.style.transform = 'scale(1)';
          colorItem.style.boxShadow = 'none';
        }
      });
      
      // 点击事件
      colorItem.addEventListener('click', () => {
        this.selectColor(index);
      });
      
      pickerContainer.appendChild(colorItem);
    });
    
    this.container.appendChild(pickerContainer);
    
    // 添加到文档
    document.body.appendChild(this.container);
    
    // 添加切换按钮
    this.addToggleButton();
    
    // 确保面板初始状态为隐藏
    this.container.style.opacity = '0';
    this.container.style.pointerEvents = 'none';
    this.container.style.transform = 'translateX(-50%) translateY(20px)';
    
    // 确保颜色选择器初始状态正确
    const currentColor = useCarColorStore.getState().bodyColor;
    const initialIndex = colorPresets.findIndex(preset => preset.color === currentColor);
    if (initialIndex !== -1) {
      this.selectColor(initialIndex);
    }
    
    // 添加到控制台，方便调试
    console.log('Color Picker initialized');
    (window as any).colorPicker = this;
  }
  
  selectColor(index: number) {
    // 更新激活状态
    const colorItems = this.container.querySelectorAll('[data-color]');
    colorItems.forEach((item, i) => {
      const colorItem = item as HTMLDivElement;
      if (i === index) {
        Object.assign(colorItem.style, {...this.colorItemStyle, ...this.activeItemStyle});
      } else {
        Object.assign(colorItem.style, this.colorItemStyle);
      }
    });
    
    this.activeIndex = index;
    
    // 更新全局状态
    const selectedColor = colorPresets[index].color;
    const { setBodyColor } = useCarColorStore.getState();
    setBodyColor(selectedColor);
    
    console.log(`Color changed to: ${selectedColor}`);
  }
  
  addToggleButton() {
    const button = this.toggleButton;
    button.textContent = '车身颜色';
    
    const buttonStyles = {
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      padding: '12px 20px',
      backgroundColor: 'rgba(20, 20, 20, 0.7)',
      backdropFilter: 'blur(5px)',
      color: 'white',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      borderRadius: '30px',
      cursor: 'pointer',
      fontSize: '14px',
      fontWeight: 'bold',
      zIndex: '10000',
      boxShadow: '0 4px 10px rgba(0, 0, 0, 0.3)',
      transition: 'transform 0.2s ease, background-color 0.2s ease',
    };
    
    Object.assign(button.style, buttonStyles);
    // 添加webkit前缀
    (button.style as any)['-webkit-backdrop-filter'] = 'blur(5px)';
    
    // 添加悬停效果
    button.addEventListener('mouseenter', () => {
      button.style.backgroundColor = 'rgba(30, 30, 30, 0.8)';
      button.style.transform = 'translateY(-2px)';
    });
    
    button.addEventListener('mouseleave', () => {
      button.style.backgroundColor = 'rgba(20, 20, 20, 0.7)';
      button.style.transform = 'translateY(0)';
    });
    
    button.addEventListener('click', () => {
      this.toggleVisibility();
    });
    
    document.body.appendChild(button);
    
    // 确保按钮可见
    console.log('Toggle button added to the DOM');
  }
  
  toggleVisibility() {
    this.isVisible = !this.isVisible;
    
    if (this.isVisible) {
      this.container.style.opacity = '1';
      this.container.style.pointerEvents = 'all';
      this.container.style.transform = 'translateX(-50%) translateY(0)';
      this.toggleButton.style.backgroundColor = 'rgba(38, 214, 233, 0.7)';
      this.toggleButton.style.backdropFilter = 'blur(5px)';
      (this.toggleButton.style as any)['-webkit-backdrop-filter'] = 'blur(5px)';
      this.toggleButton.style.border = '1px solid rgba(255, 255, 255, 0.2)';
    } else {
      this.container.style.opacity = '0';
      this.container.style.pointerEvents = 'none';
      this.container.style.transform = 'translateX(-50%) translateY(20px)';
      this.toggleButton.style.backgroundColor = 'rgba(20, 20, 20, 0.7)';
      this.toggleButton.style.backdropFilter = 'blur(5px)';
      (this.toggleButton.style as any)['-webkit-backdrop-filter'] = 'blur(5px)';
      this.toggleButton.style.border = '1px solid rgba(255, 255, 255, 0.1)';
    }
    
    console.log(`Color picker visibility: ${this.isVisible}`);
  }
  
  // 销毁函数
  dispose() {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
    if (this.toggleButton && this.toggleButton.parentNode) {
      this.toggleButton.parentNode.removeChild(this.toggleButton);
    }
  }
} 