[{"id": "two-column-container", "name": "双栏容器", "type": "container", "description": "一个通用的两栏布局容器，默认1:1，可通过style覆盖比例。", "styles": {"display": "grid", "gap": "16px", "grid-template-columns": "1fr 1fr"}, "slots": [{"id": "left-panel", "description": "左侧栏"}, {"id": "main-content", "description": "主内容区"}]}, {"id": "vertical-stack-container", "name": "垂直堆叠容器", "type": "container", "description": "将子元素从上到下垂直排列，适用于动态数量的子项。", "styles": {"display": "flex", "flex-direction": "column", "gap": "12px"}, "slots": [{"id": "slot-1", "description": "插槽1"}, {"id": "slot-2", "description": "插槽2"}, {"id": "slot-3", "description": "插槽3"}]}, {"id": "three-column-grid-container", "name": "三栏网格容器", "type": "container", "description": "一个等宽的三栏网格布局。", "styles": {"display": "grid", "gap": "16px", "grid-template-columns": "repeat(3, 1fr)"}, "slots": [{"id": "col-1", "description": "第一列"}, {"id": "col-2", "description": "第二列"}, {"id": "col-3", "description": "第三列"}]}]