{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeStyle as _normalizeStyle, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"home-status-overview\"\n};\nconst _hoisted_2 = {\n  class: \"status-header\"\n};\nconst _hoisted_3 = {\n  class: \"home-info\"\n};\nconst _hoisted_4 = {\n  class: \"home-name\"\n};\nconst _hoisted_5 = {\n  class: \"home-address\"\n};\nconst _hoisted_6 = {\n  class: \"quick-stats\"\n};\nconst _hoisted_7 = {\n  class: \"stat-item\"\n};\nconst _hoisted_8 = {\n  class: \"stat-value\"\n};\nconst _hoisted_9 = {\n  class: \"stat-item\"\n};\nconst _hoisted_10 = {\n  class: \"stat-value\"\n};\nconst _hoisted_11 = {\n  class: \"stat-item\"\n};\nconst _hoisted_12 = {\n  class: \"stat-value\"\n};\nconst _hoisted_13 = {\n  class: \"stat-item\"\n};\nconst _hoisted_14 = {\n  class: \"stat-value\"\n};\nconst _hoisted_15 = {\n  key: 0,\n  class: \"scene-modes\"\n};\nconst _hoisted_16 = {\n  class: \"modes-grid\"\n};\nconst _hoisted_17 = [\"onClick\", \"disabled\"];\nconst _hoisted_18 = {\n  class: \"scene-icon\"\n};\nconst _hoisted_19 = {\n  class: \"scene-info\"\n};\nconst _hoisted_20 = {\n  class: \"scene-name\"\n};\nconst _hoisted_21 = {\n  class: \"scene-desc\"\n};\nconst _hoisted_22 = {\n  class: \"device-controls\"\n};\nconst _hoisted_23 = {\n  class: \"controls-header\"\n};\nconst _hoisted_24 = {\n  class: \"view-toggle\"\n};\nconst _hoisted_25 = [\"onClick\"];\nconst _hoisted_26 = {\n  class: \"device-header\"\n};\nconst _hoisted_27 = {\n  class: \"device-icon\"\n};\nconst _hoisted_28 = {\n  class: \"device-info\"\n};\nconst _hoisted_29 = {\n  class: \"device-name\"\n};\nconst _hoisted_30 = {\n  class: \"device-room\"\n};\nconst _hoisted_31 = {\n  class: \"device-controls-area\"\n};\nconst _hoisted_32 = {\n  key: 0,\n  class: \"switch-control\"\n};\nconst _hoisted_33 = {\n  class: \"switch\"\n};\nconst _hoisted_34 = [\"checked\", \"onChange\"];\nconst _hoisted_35 = {\n  class: \"light-control\"\n};\nconst _hoisted_36 = {\n  class: \"brightness-control\"\n};\nconst _hoisted_37 = [\"value\", \"onInput\"];\nconst _hoisted_38 = {\n  class: \"brightness-value\"\n};\nconst _hoisted_39 = {\n  key: 0,\n  class: \"color-control\"\n};\nconst _hoisted_40 = {\n  class: \"color-presets\"\n};\nconst _hoisted_41 = [\"onClick\"];\nconst _hoisted_42 = {\n  class: \"thermostat-control\"\n};\nconst _hoisted_43 = {\n  class: \"temperature-display\"\n};\nconst _hoisted_44 = {\n  class: \"current-temp\"\n};\nconst _hoisted_45 = {\n  class: \"target-temp\"\n};\nconst _hoisted_46 = {\n  class: \"temp-controls\"\n};\nconst _hoisted_47 = [\"onClick\"];\nconst _hoisted_48 = [\"onClick\"];\nconst _hoisted_49 = {\n  class: \"security-control\"\n};\nconst _hoisted_50 = {\n  class: \"security-status\"\n};\nconst _hoisted_51 = [\"onClick\"];\nconst _hoisted_52 = {\n  class: \"generic-status\"\n};\nconst _hoisted_53 = {\n  class: \"status-text\"\n};\nconst _hoisted_54 = {\n  key: 0,\n  class: \"device-details\"\n};\nconst _hoisted_55 = {\n  class: \"detail-label\"\n};\nconst _hoisted_56 = {\n  class: \"detail-value\"\n};\nconst _hoisted_57 = {\n  key: 1,\n  class: \"ai-suggestions\"\n};\nconst _hoisted_58 = {\n  class: \"suggestions-list\"\n};\nconst _hoisted_59 = {\n  class: \"suggestion-content\"\n};\nconst _hoisted_60 = {\n  class: \"suggestion-text\"\n};\nconst _hoisted_61 = {\n  class: \"suggestion-meta\"\n};\nconst _hoisted_62 = {\n  class: \"energy-saving\"\n};\nconst _hoisted_63 = {\n  class: \"confidence\"\n};\nconst _hoisted_64 = [\"onClick\", \"disabled\"];\nconst _hoisted_65 = {\n  key: 2,\n  class: \"loading-overlay\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_BaseCard = _resolveComponent(\"BaseCard\");\n  return _openBlock(), _createBlock(_component_BaseCard, {\n    \"card-type\": 'smart-home',\n    size: $props.size,\n    position: $props.position,\n    theme: $props.theme,\n    \"theme-colors\": $props.themeColors,\n    \"show-header\": true,\n    \"show-footer\": false,\n    title: $setup.cardTitle,\n    icon: 'fas fa-home',\n    class: \"smart-home-control-card\"\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"smart-home-container\", [`size-${$props.size}`, `mode-${$props.displayMode}`]])\n    }, [_createCommentVNode(\" 家庭状态概览 \"), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"h3\", _hoisted_4, _toDisplayString($setup.homeInfo.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_5, _toDisplayString($setup.homeInfo.address), 1 /* TEXT */)]), _createElementVNode(\"div\", {\n      class: _normalizeClass([\"overall-status\", $setup.overallStatus.type])\n    }, [_createElementVNode(\"i\", {\n      class: _normalizeClass($setup.overallStatus.icon)\n    }, null, 2 /* CLASS */), _createElementVNode(\"span\", null, _toDisplayString($setup.overallStatus.text), 1 /* TEXT */)], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, _toDisplayString($setup.homeStats.temperature) + \"°C\", 1 /* TEXT */), _cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n      class: \"stat-label\"\n    }, \"室内温度\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, _toDisplayString($setup.homeStats.humidity) + \"%\", 1 /* TEXT */), _cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n      class: \"stat-label\"\n    }, \"湿度\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, _toDisplayString($setup.homeStats.activeDevices), 1 /* TEXT */), _cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n      class: \"stat-label\"\n    }, \"活跃设备\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, _toDisplayString($setup.homeStats.energyUsage) + \"W\", 1 /* TEXT */), _cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n      class: \"stat-label\"\n    }, \"功耗\", -1 /* CACHED */))])])]), _createCommentVNode(\" 场景模式快捷控制 \"), $props.showSceneModes ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n      class: \"modes-header\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-magic\"\n    }), _createElementVNode(\"span\", null, \"场景模式\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_16, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.sceneModes, scene => {\n      return _openBlock(), _createElementBlock(\"button\", {\n        key: scene.id,\n        onClick: $event => $setup.activateScene(scene),\n        class: _normalizeClass(['scene-btn', {\n          active: scene.id === $setup.activeSceneId\n        }]),\n        disabled: $setup.isProcessing\n      }, [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"i\", {\n        class: _normalizeClass(scene.icon)\n      }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, _toDisplayString(scene.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_21, _toDisplayString(scene.description), 1 /* TEXT */)])], 10 /* CLASS, PROPS */, _hoisted_17);\n    }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 设备控制区域 \"), _createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, [_cache[9] || (_cache[9] = _createElementVNode(\"span\", null, \"设备控制\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"button\", {\n      onClick: _cache[0] || (_cache[0] = $event => $setup.viewMode = 'grid'),\n      class: _normalizeClass(['toggle-btn', {\n        active: $setup.viewMode === 'grid'\n      }])\n    }, _cache[7] || (_cache[7] = [_createElementVNode(\"i\", {\n      class: \"fas fa-th\"\n    }, null, -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"button\", {\n      onClick: _cache[1] || (_cache[1] = $event => $setup.viewMode = 'list'),\n      class: _normalizeClass(['toggle-btn', {\n        active: $setup.viewMode === 'list'\n      }])\n    }, _cache[8] || (_cache[8] = [_createElementVNode(\"i\", {\n      class: \"fas fa-list\"\n    }, null, -1 /* CACHED */)]), 2 /* CLASS */)])]), _createElementVNode(\"div\", {\n      class: _normalizeClass(['devices-container', `view-${$setup.viewMode}`])\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.filteredDevices, device => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: device.id,\n        class: _normalizeClass(['device-item', `type-${device.type}`, `status-${device.status}`]),\n        onClick: $event => $setup.handleDeviceClick(device)\n      }, [_createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"i\", {\n        class: _normalizeClass(device.icon)\n      }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"div\", _hoisted_29, _toDisplayString(device.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_30, _toDisplayString(device.room), 1 /* TEXT */)]), _createElementVNode(\"div\", {\n        class: _normalizeClass([\"device-status-indicator\", device.status])\n      }, [...(_cache[10] || (_cache[10] = [_createElementVNode(\"div\", {\n        class: \"status-dot\"\n      }, null, -1 /* CACHED */)]))], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_31, [_createCommentVNode(\" 开关控制 \"), device.type === 'switch' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_32, [_createElementVNode(\"label\", _hoisted_33, [_createElementVNode(\"input\", {\n        type: \"checkbox\",\n        checked: device.state.on,\n        onChange: $event => $setup.toggleDevice(device)\n      }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_34), _cache[11] || (_cache[11] = _createElementVNode(\"span\", {\n        class: \"slider\"\n      }, null, -1 /* CACHED */))])])) : device.type === 'light' ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 1\n      }, [_createCommentVNode(\" 调光控制 \"), _createElementVNode(\"div\", _hoisted_35, [_createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"input\", {\n        type: \"range\",\n        value: device.state.brightness,\n        onInput: $event => $setup.adjustBrightness(device, $event.target.value),\n        min: \"0\",\n        max: \"100\",\n        class: \"brightness-slider\"\n      }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_37), _createElementVNode(\"span\", _hoisted_38, _toDisplayString(device.state.brightness) + \"%\", 1 /* TEXT */)]), device.state.colorSupport ? (_openBlock(), _createElementBlock(\"div\", _hoisted_39, [_createElementVNode(\"div\", _hoisted_40, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.colorPresets, color => {\n        return _openBlock(), _createElementBlock(\"button\", {\n          key: color.name,\n          onClick: $event => $setup.setLightColor(device, color),\n          class: _normalizeClass(['color-preset', {\n            active: device.state.color === color.value\n          }]),\n          style: _normalizeStyle({\n            backgroundColor: color.value\n          })\n        }, null, 14 /* CLASS, STYLE, PROPS */, _hoisted_41);\n      }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true)])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : device.type === 'thermostat' ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 2\n      }, [_createCommentVNode(\" 温控器控制 \"), _createElementVNode(\"div\", _hoisted_42, [_createElementVNode(\"div\", _hoisted_43, [_createElementVNode(\"span\", _hoisted_44, _toDisplayString(device.state.currentTemp) + \"°C\", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_45, \"目标: \" + _toDisplayString(device.state.targetTemp) + \"°C\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_46, [_createElementVNode(\"button\", {\n        onClick: $event => $setup.adjustTemperature(device, -1),\n        class: \"temp-btn\"\n      }, [...(_cache[12] || (_cache[12] = [_createElementVNode(\"i\", {\n        class: \"fas fa-minus\"\n      }, null, -1 /* CACHED */)]))], 8 /* PROPS */, _hoisted_47), _createElementVNode(\"button\", {\n        onClick: $event => $setup.adjustTemperature(device, 1),\n        class: \"temp-btn\"\n      }, [...(_cache[13] || (_cache[13] = [_createElementVNode(\"i\", {\n        class: \"fas fa-plus\"\n      }, null, -1 /* CACHED */)]))], 8 /* PROPS */, _hoisted_48)])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : device.type === 'security' ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 3\n      }, [_createCommentVNode(\" 安防设备控制 \"), _createElementVNode(\"div\", _hoisted_49, [_createElementVNode(\"div\", _hoisted_50, [_createElementVNode(\"span\", {\n        class: _normalizeClass(['status-text', device.state.armed ? 'armed' : 'disarmed'])\n      }, _toDisplayString(device.state.armed ? '已布防' : '已撤防'), 3 /* TEXT, CLASS */)]), _createElementVNode(\"button\", {\n        onClick: $event => $setup.toggleSecurity(device),\n        class: _normalizeClass(['security-toggle', device.state.armed ? 'disarm' : 'arm'])\n      }, _toDisplayString(device.state.armed ? '撤防' : '布防'), 11 /* TEXT, CLASS, PROPS */, _hoisted_51)])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n        key: 4\n      }, [_createCommentVNode(\" 通用状态显示 \"), _createElementVNode(\"div\", _hoisted_52, [_createElementVNode(\"span\", _hoisted_53, _toDisplayString($setup.getDeviceStatusText(device)), 1 /* TEXT */)])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))]), _createCommentVNode(\" 设备详细信息 \"), device.showDetails ? (_openBlock(), _createElementBlock(\"div\", _hoisted_54, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(device.details, (value, key) => {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"detail-item\",\n          key: key\n        }, [_createElementVNode(\"span\", _hoisted_55, _toDisplayString(key) + \":\", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_56, _toDisplayString(value), 1 /* TEXT */)]);\n      }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)], 10 /* CLASS, PROPS */, _hoisted_25);\n    }), 128 /* KEYED_FRAGMENT */))], 2 /* CLASS */)]), _createCommentVNode(\" AI智能建议 \"), $setup.aiSuggestions.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_57, [_cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n      class: \"suggestions-header\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-lightbulb\"\n    }), _createElementVNode(\"span\", null, \"AI智能建议\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_58, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.aiSuggestions, suggestion => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: suggestion.id,\n        class: \"suggestion-item\"\n      }, [_createElementVNode(\"div\", _hoisted_59, [_createElementVNode(\"div\", _hoisted_60, _toDisplayString(suggestion.text), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_61, [_createElementVNode(\"span\", _hoisted_62, \"节能 \" + _toDisplayString(suggestion.energySaving), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_63, \"可信度 \" + _toDisplayString(Math.round(suggestion.confidence * 100)) + \"%\", 1 /* TEXT */)])]), _createElementVNode(\"button\", {\n        onClick: $event => $setup.applySuggestion(suggestion),\n        class: \"apply-btn\",\n        disabled: $setup.isProcessing\n      }, [...(_cache[14] || (_cache[14] = [_createElementVNode(\"i\", {\n        class: \"fas fa-check\"\n      }, null, -1 /* CACHED */)]))], 8 /* PROPS */, _hoisted_64)]);\n    }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 加载状态 \"), $setup.isLoading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_65, _cache[16] || (_cache[16] = [_createElementVNode(\"div\", {\n      class: \"loading-spinner\"\n    }, null, -1 /* CACHED */), _createElementVNode(\"div\", {\n      class: \"loading-text\"\n    }, \"正在连接智能设备...\", -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"size\", \"position\", \"theme\", \"theme-colors\", \"title\"]);\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_component_BaseCard", "size", "$props", "position", "theme", "themeColors", "title", "$setup", "cardTitle", "icon", "_createElementVNode", "_normalizeClass", "displayMode", "_createCommentVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_toDisplayString", "homeInfo", "name", "_hoisted_5", "address", "overallStatus", "type", "text", "_hoisted_6", "_hoisted_7", "_hoisted_8", "homeStats", "temperature", "_hoisted_9", "_hoisted_10", "humidity", "_hoisted_11", "_hoisted_12", "activeDevices", "_hoisted_13", "_hoisted_14", "energyUsage", "showSceneModes", "_createElementBlock", "_hoisted_15", "_hoisted_16", "_Fragment", "_renderList", "sceneModes", "scene", "key", "id", "onClick", "$event", "activateScene", "active", "activeSceneId", "disabled", "isProcessing", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "description", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_cache", "viewMode", "filteredDevices", "device", "status", "handleDeviceClick", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "_hoisted_30", "room", "_hoisted_31", "_hoisted_32", "_hoisted_33", "checked", "state", "on", "onChange", "toggleDevice", "_hoisted_35", "_hoisted_36", "value", "brightness", "onInput", "adjustBrightness", "target", "min", "max", "_hoisted_38", "colorSupport", "_hoisted_39", "_hoisted_40", "colorPresets", "color", "setLightColor", "style", "_normalizeStyle", "backgroundColor", "_hoisted_42", "_hoisted_43", "_hoisted_44", "currentTemp", "_hoisted_45", "targetTemp", "_hoisted_46", "adjustTemperature", "_hoisted_49", "_hoisted_50", "armed", "toggleSecurity", "_hoisted_51", "_hoisted_52", "_hoisted_53", "getDeviceStatusText", "showDetails", "_hoisted_54", "details", "_hoisted_55", "_hoisted_56", "aiSuggestions", "length", "_hoisted_57", "_hoisted_58", "suggestion", "_hoisted_59", "_hoisted_60", "_hoisted_61", "_hoisted_62", "energySaving", "_hoisted_63", "Math", "round", "confidence", "applySuggestion", "isLoading", "_hoisted_65"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\SmartHomeControlCard.vue"], "sourcesContent": ["<template>\n  <BaseCard\n    :card-type=\"'smart-home'\"\n    :size=\"size\"\n    :position=\"position\"\n    :theme=\"theme\"\n    :theme-colors=\"themeColors\"\n    :show-header=\"true\"\n    :show-footer=\"false\"\n    :title=\"cardTitle\"\n    :icon=\"'fas fa-home'\"\n    class=\"smart-home-control-card\"\n  >\n    <div class=\"smart-home-container\" :class=\"[`size-${size}`, `mode-${displayMode}`]\">\n      <!-- 家庭状态概览 -->\n      <div class=\"home-status-overview\">\n        <div class=\"status-header\">\n          <div class=\"home-info\">\n            <h3 class=\"home-name\">{{ homeInfo.name }}</h3>\n            <div class=\"home-address\">{{ homeInfo.address }}</div>\n          </div>\n          <div class=\"overall-status\" :class=\"overallStatus.type\">\n            <i :class=\"overallStatus.icon\"></i>\n            <span>{{ overallStatus.text }}</span>\n          </div>\n        </div>\n        \n        <div class=\"quick-stats\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ homeStats.temperature }}°C</div>\n            <div class=\"stat-label\">室内温度</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ homeStats.humidity }}%</div>\n            <div class=\"stat-label\">湿度</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ homeStats.activeDevices }}</div>\n            <div class=\"stat-label\">活跃设备</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ homeStats.energyUsage }}W</div>\n            <div class=\"stat-label\">功耗</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 场景模式快捷控制 -->\n      <div class=\"scene-modes\" v-if=\"showSceneModes\">\n        <div class=\"modes-header\">\n          <i class=\"fas fa-magic\"></i>\n          <span>场景模式</span>\n        </div>\n        <div class=\"modes-grid\">\n          <button\n            v-for=\"scene in sceneModes\"\n            :key=\"scene.id\"\n            @click=\"activateScene(scene)\"\n            :class=\"['scene-btn', { active: scene.id === activeSceneId }]\"\n            :disabled=\"isProcessing\"\n          >\n            <div class=\"scene-icon\">\n              <i :class=\"scene.icon\"></i>\n            </div>\n            <div class=\"scene-info\">\n              <div class=\"scene-name\">{{ scene.name }}</div>\n              <div class=\"scene-desc\">{{ scene.description }}</div>\n            </div>\n          </button>\n        </div>\n      </div>\n\n      <!-- 设备控制区域 -->\n      <div class=\"device-controls\">\n        <div class=\"controls-header\">\n          <span>设备控制</span>\n          <div class=\"view-toggle\">\n            <button \n              @click=\"viewMode = 'grid'\"\n              :class=\"['toggle-btn', { active: viewMode === 'grid' }]\"\n            >\n              <i class=\"fas fa-th\"></i>\n            </button>\n            <button \n              @click=\"viewMode = 'list'\"\n              :class=\"['toggle-btn', { active: viewMode === 'list' }]\"\n            >\n              <i class=\"fas fa-list\"></i>\n            </button>\n          </div>\n        </div>\n        \n        <div :class=\"['devices-container', `view-${viewMode}`]\">\n          <div\n            v-for=\"device in filteredDevices\"\n            :key=\"device.id\"\n            :class=\"['device-item', `type-${device.type}`, `status-${device.status}`]\"\n            @click=\"handleDeviceClick(device)\"\n          >\n            <div class=\"device-header\">\n              <div class=\"device-icon\">\n                <i :class=\"device.icon\"></i>\n              </div>\n              <div class=\"device-info\">\n                <div class=\"device-name\">{{ device.name }}</div>\n                <div class=\"device-room\">{{ device.room }}</div>\n              </div>\n              <div class=\"device-status-indicator\" :class=\"device.status\">\n                <div class=\"status-dot\"></div>\n              </div>\n            </div>\n            \n            <div class=\"device-controls-area\">\n              <!-- 开关控制 -->\n              <div v-if=\"device.type === 'switch'\" class=\"switch-control\">\n                <label class=\"switch\">\n                  <input \n                    type=\"checkbox\" \n                    :checked=\"device.state.on\"\n                    @change=\"toggleDevice(device)\"\n                  >\n                  <span class=\"slider\"></span>\n                </label>\n              </div>\n              \n              <!-- 调光控制 -->\n              <div v-else-if=\"device.type === 'light'\" class=\"light-control\">\n                <div class=\"brightness-control\">\n                  <input\n                    type=\"range\"\n                    :value=\"device.state.brightness\"\n                    @input=\"adjustBrightness(device, $event.target.value)\"\n                    min=\"0\"\n                    max=\"100\"\n                    class=\"brightness-slider\"\n                  >\n                  <span class=\"brightness-value\">{{ device.state.brightness }}%</span>\n                </div>\n                <div class=\"color-control\" v-if=\"device.state.colorSupport\">\n                  <div class=\"color-presets\">\n                    <button\n                      v-for=\"color in colorPresets\"\n                      :key=\"color.name\"\n                      @click=\"setLightColor(device, color)\"\n                      :class=\"['color-preset', { active: device.state.color === color.value }]\"\n                      :style=\"{ backgroundColor: color.value }\"\n                    ></button>\n                  </div>\n                </div>\n              </div>\n              \n              <!-- 温控器控制 -->\n              <div v-else-if=\"device.type === 'thermostat'\" class=\"thermostat-control\">\n                <div class=\"temperature-display\">\n                  <span class=\"current-temp\">{{ device.state.currentTemp }}°C</span>\n                  <span class=\"target-temp\">目标: {{ device.state.targetTemp }}°C</span>\n                </div>\n                <div class=\"temp-controls\">\n                  <button @click=\"adjustTemperature(device, -1)\" class=\"temp-btn\">\n                    <i class=\"fas fa-minus\"></i>\n                  </button>\n                  <button @click=\"adjustTemperature(device, 1)\" class=\"temp-btn\">\n                    <i class=\"fas fa-plus\"></i>\n                  </button>\n                </div>\n              </div>\n              \n              <!-- 安防设备控制 -->\n              <div v-else-if=\"device.type === 'security'\" class=\"security-control\">\n                <div class=\"security-status\">\n                  <span :class=\"['status-text', device.state.armed ? 'armed' : 'disarmed']\">\n                    {{ device.state.armed ? '已布防' : '已撤防' }}\n                  </span>\n                </div>\n                <button \n                  @click=\"toggleSecurity(device)\"\n                  :class=\"['security-toggle', device.state.armed ? 'disarm' : 'arm']\"\n                >\n                  {{ device.state.armed ? '撤防' : '布防' }}\n                </button>\n              </div>\n              \n              <!-- 通用状态显示 -->\n              <div v-else class=\"generic-status\">\n                <span class=\"status-text\">{{ getDeviceStatusText(device) }}</span>\n              </div>\n            </div>\n            \n            <!-- 设备详细信息 -->\n            <div v-if=\"device.showDetails\" class=\"device-details\">\n              <div class=\"detail-item\" v-for=\"(value, key) in device.details\" :key=\"key\">\n                <span class=\"detail-label\">{{ key }}:</span>\n                <span class=\"detail-value\">{{ value }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- AI智能建议 -->\n      <div class=\"ai-suggestions\" v-if=\"aiSuggestions.length > 0\">\n        <div class=\"suggestions-header\">\n          <i class=\"fas fa-lightbulb\"></i>\n          <span>AI智能建议</span>\n        </div>\n        <div class=\"suggestions-list\">\n          <div\n            v-for=\"suggestion in aiSuggestions\"\n            :key=\"suggestion.id\"\n            class=\"suggestion-item\"\n          >\n            <div class=\"suggestion-content\">\n              <div class=\"suggestion-text\">{{ suggestion.text }}</div>\n              <div class=\"suggestion-meta\">\n                <span class=\"energy-saving\">节能 {{ suggestion.energySaving }}</span>\n                <span class=\"confidence\">可信度 {{ Math.round(suggestion.confidence * 100) }}%</span>\n              </div>\n            </div>\n            <button \n              @click=\"applySuggestion(suggestion)\"\n              class=\"apply-btn\"\n              :disabled=\"isProcessing\"\n            >\n              <i class=\"fas fa-check\"></i>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 加载状态 -->\n      <div v-if=\"isLoading\" class=\"loading-overlay\">\n        <div class=\"loading-spinner\"></div>\n        <div class=\"loading-text\">正在连接智能设备...</div>\n      </div>\n    </div>\n  </BaseCard>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, onUnmounted } from 'vue'\nimport BaseCard from '@/components/cards/BaseCard.vue'\nimport mockDataService from '@/services/MockDataService.js'\n\nexport default {\n  name: 'SmartHomeControlCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    size: {\n      type: String,\n      default: 'large',\n      validator: (value) => ['small', 'medium', 'large'].includes(value)\n    },\n    position: {\n      type: Object,\n      default: () => ({ x: 0, y: 0 })\n    },\n    theme: {\n      type: String,\n      default: 'glassmorphism'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({})\n    },\n    displayMode: {\n      type: String,\n      default: 'full',\n      validator: (value) => ['compact', 'standard', 'full'].includes(value)\n    },\n    showSceneModes: {\n      type: Boolean,\n      default: true\n    },\n    deviceFilter: {\n      type: String,\n      default: 'all',\n      validator: (value) => ['all', 'lights', 'security', 'climate', 'entertainment'].includes(value)\n    },\n    autoRefresh: {\n      type: Boolean,\n      default: true\n    },\n    refreshInterval: {\n      type: Number,\n      default: 30000 // 30秒\n    }\n  },\n  emits: ['device-control', 'scene-activated', 'suggestion-applied'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const isLoading = ref(false)\n    const isProcessing = ref(false)\n    const viewMode = ref('grid')\n    const activeSceneId = ref(null)\n    \n    const homeInfo = ref({\n      name: '我的家',\n      address: '北京市朝阳区'\n    })\n    \n    const homeStats = ref({\n      temperature: 22,\n      humidity: 45,\n      activeDevices: 12,\n      energyUsage: 1250\n    })\n    \n    const devices = ref([])\n    const sceneModes = ref([])\n    const aiSuggestions = ref([])\n    \n    const colorPresets = ref([\n      { name: '暖白', value: '#FFF8DC' },\n      { name: '冷白', value: '#F0F8FF' },\n      { name: '红色', value: '#FF6B6B' },\n      { name: '绿色', value: '#4ECDC4' },\n      { name: '蓝色', value: '#45B7D1' },\n      { name: '紫色', value: '#96CEB4' }\n    ])\n\n    // 计算属性\n    const cardTitle = computed(() => {\n      const onlineDevices = devices.value.filter(d => d.status === 'online').length\n      return `智能家居 (${onlineDevices}/${devices.value.length})`\n    })\n    \n    const overallStatus = computed(() => {\n      const onlineDevices = devices.value.filter(d => d.status === 'online').length\n      const totalDevices = devices.value.length\n      \n      if (totalDevices === 0) {\n        return { type: 'unknown', icon: 'fas fa-question', text: '未知' }\n      }\n      \n      const onlinePercentage = (onlineDevices / totalDevices) * 100\n      \n      if (onlinePercentage >= 90) {\n        return { type: 'excellent', icon: 'fas fa-check-circle', text: '运行良好' }\n      } else if (onlinePercentage >= 70) {\n        return { type: 'good', icon: 'fas fa-exclamation-triangle', text: '基本正常' }\n      } else {\n        return { type: 'warning', icon: 'fas fa-exclamation-circle', text: '需要关注' }\n      }\n    })\n    \n    const filteredDevices = computed(() => {\n      if (props.deviceFilter === 'all') {\n        return devices.value\n      }\n      return devices.value.filter(device => device.category === props.deviceFilter)\n    })\n\n    // 方法\n    const loadSmartHomeData = async () => {\n      try {\n        isLoading.value = true\n        const smartHomeData = await mockDataService.getSmartHomeData()\n        \n        devices.value = smartHomeData.devices || []\n        sceneModes.value = smartHomeData.scenes || []\n        aiSuggestions.value = smartHomeData.aiSuggestions || []\n        activeSceneId.value = smartHomeData.activeScene || null\n        \n        // 更新家庭统计信息\n        updateHomeStats()\n        \n      } catch (error) {\n        console.error('Failed to load smart home data:', error)\n      } finally {\n        isLoading.value = false\n      }\n    }\n    \n    const updateHomeStats = () => {\n      const thermostat = devices.value.find(d => d.type === 'thermostat')\n      if (thermostat) {\n        homeStats.value.temperature = thermostat.state.currentTemp\n        homeStats.value.humidity = thermostat.state.humidity || 45\n      }\n      \n      homeStats.value.activeDevices = devices.value.filter(d => d.status === 'online').length\n      \n      // 计算总功耗\n      const totalPower = devices.value.reduce((sum, device) => {\n        return sum + (device.state.powerUsage || 0)\n      }, 0)\n      homeStats.value.energyUsage = totalPower\n    }\n    \n    const handleDeviceClick = (device) => {\n      device.showDetails = !device.showDetails\n    }\n    \n    const toggleDevice = async (device) => {\n      try {\n        isProcessing.value = true\n        \n        const newState = !device.state.on\n        await mockDataService.controlDevice(device.id, { on: newState })\n        \n        device.state.on = newState\n        device.status = newState ? 'online' : 'offline'\n        \n        emit('device-control', {\n          deviceId: device.id,\n          action: 'toggle',\n          state: { on: newState }\n        })\n        \n        updateHomeStats()\n        \n      } catch (error) {\n        console.error('Failed to toggle device:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n    \n    const adjustBrightness = async (device, brightness) => {\n      try {\n        const brightnessValue = parseInt(brightness)\n        await mockDataService.controlDevice(device.id, { brightness: brightnessValue })\n        \n        device.state.brightness = brightnessValue\n        \n        emit('device-control', {\n          deviceId: device.id,\n          action: 'brightness',\n          state: { brightness: brightnessValue }\n        })\n        \n      } catch (error) {\n        console.error('Failed to adjust brightness:', error)\n      }\n    }\n    \n    const setLightColor = async (device, color) => {\n      try {\n        await mockDataService.controlDevice(device.id, { color: color.value })\n        \n        device.state.color = color.value\n        \n        emit('device-control', {\n          deviceId: device.id,\n          action: 'color',\n          state: { color: color.value }\n        })\n        \n      } catch (error) {\n        console.error('Failed to set light color:', error)\n      }\n    }\n    \n    const adjustTemperature = async (device, delta) => {\n      try {\n        const newTemp = device.state.targetTemp + delta\n        if (newTemp < 16 || newTemp > 30) return\n        \n        await mockDataService.controlDevice(device.id, { targetTemp: newTemp })\n        \n        device.state.targetTemp = newTemp\n        \n        emit('device-control', {\n          deviceId: device.id,\n          action: 'temperature',\n          state: { targetTemp: newTemp }\n        })\n        \n      } catch (error) {\n        console.error('Failed to adjust temperature:', error)\n      }\n    }\n    \n    const toggleSecurity = async (device) => {\n      try {\n        isProcessing.value = true\n        \n        const newArmedState = !device.state.armed\n        await mockDataService.controlDevice(device.id, { armed: newArmedState })\n        \n        device.state.armed = newArmedState\n        \n        emit('device-control', {\n          deviceId: device.id,\n          action: 'security',\n          state: { armed: newArmedState }\n        })\n        \n      } catch (error) {\n        console.error('Failed to toggle security:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n    \n    const activateScene = async (scene) => {\n      try {\n        isProcessing.value = true\n        \n        await mockDataService.activateScene(scene.id)\n        \n        activeSceneId.value = scene.id\n        \n        // 应用场景设置到设备\n        scene.deviceSettings.forEach(setting => {\n          const device = devices.value.find(d => d.id === setting.deviceId)\n          if (device) {\n            Object.assign(device.state, setting.state)\n          }\n        })\n        \n        emit('scene-activated', scene)\n        updateHomeStats()\n        \n      } catch (error) {\n        console.error('Failed to activate scene:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n    \n    const applySuggestion = async (suggestion) => {\n      try {\n        isProcessing.value = true\n        \n        // 应用AI建议\n        await mockDataService.applySuggestion(suggestion.id)\n        \n        // 从建议列表中移除\n        const index = aiSuggestions.value.findIndex(s => s.id === suggestion.id)\n        if (index > -1) {\n          aiSuggestions.value.splice(index, 1)\n        }\n        \n        emit('suggestion-applied', suggestion)\n        \n        // 重新加载数据以反映变化\n        await loadSmartHomeData()\n        \n      } catch (error) {\n        console.error('Failed to apply suggestion:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n    \n    const getDeviceStatusText = (device) => {\n      switch (device.status) {\n        case 'online':\n          return '在线'\n        case 'offline':\n          return '离线'\n        case 'error':\n          return '故障'\n        default:\n          return '未知'\n      }\n    }\n\n    // 生命周期\n    let refreshTimer = null\n    \n    onMounted(async () => {\n      await mockDataService.initialize()\n      await loadSmartHomeData()\n      \n      // 设置自动刷新\n      if (props.autoRefresh) {\n        refreshTimer = setInterval(() => {\n          loadSmartHomeData()\n        }, props.refreshInterval)\n      }\n    })\n\n    onUnmounted(() => {\n      if (refreshTimer) {\n        clearInterval(refreshTimer)\n      }\n    })\n\n    return {\n      // 响应式数据\n      isLoading,\n      isProcessing,\n      viewMode,\n      activeSceneId,\n      homeInfo,\n      homeStats,\n      devices,\n      sceneModes,\n      aiSuggestions,\n      colorPresets,\n      \n      // 计算属性\n      cardTitle,\n      overallStatus,\n      filteredDevices,\n      \n      // 方法\n      handleDeviceClick,\n      toggleDevice,\n      adjustBrightness,\n      setLightColor,\n      adjustTemperature,\n      toggleSecurity,\n      activateScene,\n      applySuggestion,\n      getDeviceStatusText\n    }\n  }\n}\n</script>\n\n<style scoped>\n.smart-home-control-card {\n  height: 100%;\n}\n\n.smart-home-container {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  padding: 16px;\n  position: relative;\n}\n\n/* 家庭状态概览 */\n.home-status-overview {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.status-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 16px;\n}\n\n.home-info h3 {\n  margin: 0 0 4px 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.home-address {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.overall-status {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 6px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.overall-status.excellent {\n  background: rgba(16, 185, 129, 0.2);\n  color: #10b981;\n}\n\n.overall-status.good {\n  background: rgba(245, 158, 11, 0.2);\n  color: #f59e0b;\n}\n\n.overall-status.warning {\n  background: rgba(239, 68, 68, 0.2);\n  color: #ef4444;\n}\n\n.quick-stats {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 12px;\n}\n\n.stat-item {\n  text-align: center;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 4px;\n}\n\n.stat-label {\n  font-size: 11px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n/* 场景模式 */\n.scene-modes {\n  background: rgba(0, 0, 0, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.modes-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 12px;\n}\n\n.modes-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n  gap: 8px;\n}\n\n.scene-btn {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border: none;\n  border-radius: 8px;\n  color: rgba(255, 255, 255, 0.8);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-align: left;\n}\n\n.scene-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n  transform: translateY(-1px);\n}\n\n.scene-btn.active {\n  background: rgba(74, 144, 226, 0.3);\n  color: #4a90e2;\n}\n\n.scene-icon {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n}\n\n.scene-info {\n  flex: 1;\n}\n\n.scene-name {\n  font-size: 12px;\n  font-weight: 500;\n  margin-bottom: 2px;\n}\n\n.scene-desc {\n  font-size: 10px;\n  opacity: 0.7;\n}\n\n/* 设备控制 */\n.device-controls {\n  flex: 1;\n  min-height: 0;\n}\n\n.controls-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.controls-header span {\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.view-toggle {\n  display: flex;\n  gap: 4px;\n}\n\n.toggle-btn {\n  width: 28px;\n  height: 28px;\n  border: none;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0.1);\n  color: rgba(255, 255, 255, 0.6);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n}\n\n.toggle-btn:hover,\n.toggle-btn.active {\n  background: rgba(255, 255, 255, 0.2);\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.devices-container {\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.devices-container.view-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 12px;\n}\n\n.devices-container.view-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.device-item {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 12px;\n  padding: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border-left: 4px solid transparent;\n}\n\n.device-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateY(-1px);\n}\n\n.device-item.status-online {\n  border-left-color: #10b981;\n}\n\n.device-item.status-offline {\n  border-left-color: #6b7280;\n}\n\n.device-item.status-error {\n  border-left-color: #ef4444;\n}\n\n.device-header {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  margin-bottom: 12px;\n}\n\n.device-icon {\n  width: 32px;\n  height: 32px;\n  border-radius: 8px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.device-info {\n  flex: 1;\n}\n\n.device-name {\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n}\n\n.device-room {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.device-status-indicator {\n  width: 12px;\n  height: 12px;\n  position: relative;\n}\n\n.status-dot {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  background: #6b7280;\n}\n\n.device-status-indicator.online .status-dot {\n  background: #10b981;\n  animation: pulse 2s infinite;\n}\n\n.device-status-indicator.error .status-dot {\n  background: #ef4444;\n}\n\n/* 设备控制区域 */\n.device-controls-area {\n  margin-bottom: 8px;\n}\n\n/* 开关控制 */\n.switch-control {\n  display: flex;\n  justify-content: center;\n}\n\n.switch {\n  position: relative;\n  display: inline-block;\n  width: 44px;\n  height: 24px;\n}\n\n.switch input {\n  opacity: 0;\n  width: 0;\n  height: 0;\n}\n\n.slider {\n  position: absolute;\n  cursor: pointer;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(255, 255, 255, 0.2);\n  transition: 0.3s;\n  border-radius: 24px;\n}\n\n.slider:before {\n  position: absolute;\n  content: \"\";\n  height: 18px;\n  width: 18px;\n  left: 3px;\n  bottom: 3px;\n  background-color: white;\n  transition: 0.3s;\n  border-radius: 50%;\n}\n\ninput:checked + .slider {\n  background-color: #4a90e2;\n}\n\ninput:checked + .slider:before {\n  transform: translateX(20px);\n}\n\n/* 调光控制 */\n.light-control {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.brightness-control {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.brightness-slider {\n  flex: 1;\n  height: 4px;\n  border-radius: 2px;\n  background: rgba(255, 255, 255, 0.2);\n  outline: none;\n  -webkit-appearance: none;\n}\n\n.brightness-slider::-webkit-slider-thumb {\n  -webkit-appearance: none;\n  appearance: none;\n  width: 16px;\n  height: 16px;\n  border-radius: 50%;\n  background: #4a90e2;\n  cursor: pointer;\n}\n\n.brightness-value {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.8);\n  min-width: 30px;\n  text-align: right;\n}\n\n.color-presets {\n  display: flex;\n  gap: 4px;\n  justify-content: center;\n}\n\n.color-preset {\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  border: 2px solid transparent;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.color-preset:hover,\n.color-preset.active {\n  border-color: rgba(255, 255, 255, 0.8);\n  transform: scale(1.1);\n}\n\n/* 温控器控制 */\n.thermostat-control {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  align-items: center;\n}\n\n.temperature-display {\n  text-align: center;\n}\n\n.current-temp {\n  font-size: 18px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n  display: block;\n}\n\n.target-temp {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.temp-controls {\n  display: flex;\n  gap: 8px;\n}\n\n.temp-btn {\n  width: 32px;\n  height: 32px;\n  border: none;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.2);\n  color: rgba(255, 255, 255, 0.8);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.temp-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: scale(1.05);\n}\n\n/* 安防控制 */\n.security-control {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  align-items: center;\n}\n\n.security-status .status-text.armed {\n  color: #ef4444;\n}\n\n.security-status .status-text.disarmed {\n  color: #10b981;\n}\n\n.security-toggle {\n  padding: 6px 12px;\n  border: none;\n  border-radius: 6px;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.security-toggle.arm {\n  background: rgba(239, 68, 68, 0.2);\n  color: #ef4444;\n}\n\n.security-toggle.disarm {\n  background: rgba(16, 185, 129, 0.2);\n  color: #10b981;\n}\n\n/* 通用状态 */\n.generic-status {\n  text-align: center;\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.7);\n}\n\n/* 设备详细信息 */\n.device-details {\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n  padding-top: 8px;\n  margin-top: 8px;\n}\n\n.detail-item {\n  display: flex;\n  justify-content: space-between;\n  font-size: 11px;\n  margin-bottom: 4px;\n}\n\n.detail-label {\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.detail-value {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* AI建议 */\n.ai-suggestions {\n  background: rgba(126, 211, 33, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.suggestions-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  color: #7ed321;\n  margin-bottom: 12px;\n}\n\n.suggestions-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.suggestion-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n}\n\n.suggestion-content {\n  flex: 1;\n}\n\n.suggestion-text {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 4px;\n}\n\n.suggestion-meta {\n  display: flex;\n  gap: 12px;\n  font-size: 10px;\n}\n\n.energy-saving {\n  color: #7ed321;\n}\n\n.confidence {\n  color: #4a90e2;\n}\n\n.apply-btn {\n  width: 28px;\n  height: 28px;\n  border: none;\n  border-radius: 6px;\n  background: rgba(126, 211, 33, 0.3);\n  color: #7ed321;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.apply-btn:hover {\n  background: rgba(126, 211, 33, 0.5);\n  transform: scale(1.05);\n}\n\n/* 加载状态 */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.3);\n  backdrop-filter: blur(5px);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 12px;\n  border-radius: 12px;\n}\n\n.loading-spinner {\n  width: 32px;\n  height: 32px;\n  border: 3px solid rgba(255, 255, 255, 0.2);\n  border-top: 3px solid #4a90e2;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.loading-text {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* 动画 */\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n}\n\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* 响应式设计 */\n.size-small .smart-home-container {\n  padding: 12px;\n  gap: 12px;\n}\n\n.size-small .quick-stats {\n  grid-template-columns: repeat(2, 1fr);\n}\n\n.size-small .modes-grid {\n  grid-template-columns: repeat(2, 1fr);\n}\n\n.mode-compact .scene-modes,\n.mode-compact .ai-suggestions {\n  display: none;\n}\n\n.mode-compact .devices-container {\n  max-height: 250px;\n}\n</style>"], "mappings": ";;EAeWA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAW;;EAChBA,KAAK,EAAC;AAAW;;EAChBA,KAAK,EAAC;AAAc;;EAQxBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAGpBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAGpBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAGpBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;;EAOxBA,KAAK,EAAC;;;EAKJA,KAAK,EAAC;AAAY;;;EAQdA,KAAK,EAAC;AAAY;;EAGlBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAY;;EAClBA,KAAK,EAAC;AAAY;;EAO1BA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAa;;;EAuBjBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAa;;EAGnBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAa;;EAOvBA,KAAK,EAAC;AAAsB;;;EAEMA,KAAK,EAAC;;;EAClCA,KAAK,EAAC;AAAQ;;;EAWkBA,KAAK,EAAC;AAAe;;EACvDA,KAAK,EAAC;AAAoB;;;EASvBA,KAAK,EAAC;AAAkB;;;EAE3BA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAe;;;EAagBA,KAAK,EAAC;AAAoB;;EACjEA,KAAK,EAAC;AAAqB;;EACxBA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAa;;EAEtBA,KAAK,EAAC;AAAe;;;;EAWgBA,KAAK,EAAC;AAAkB;;EAC7DA,KAAK,EAAC;AAAiB;;;EAclBA,KAAK,EAAC;AAAgB;;EAC1BA,KAAK,EAAC;AAAa;;;EAKEA,KAAK,EAAC;;;EAE3BA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAc;;;EAQ/BA,KAAK,EAAC;;;EAKJA,KAAK,EAAC;AAAkB;;EAMpBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAiB;;EACvBA,KAAK,EAAC;AAAiB;;EACpBA,KAAK,EAAC;AAAe;;EACrBA,KAAK,EAAC;AAAY;;;;EAeZA,KAAK,EAAC;;;;uBArOhCC,YAAA,CA0OWC,mBAAA;IAzOR,WAAS,EAAE,YAAY;IACvBC,IAAI,EAAEC,MAAA,CAAAD,IAAI;IACVE,QAAQ,EAAED,MAAA,CAAAC,QAAQ;IAClBC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACZ,cAAY,EAAEF,MAAA,CAAAG,WAAW;IACzB,aAAW,EAAE,IAAI;IACjB,aAAW,EAAE,KAAK;IAClBC,KAAK,EAAEC,MAAA,CAAAC,SAAS;IAChBC,IAAI,EAAE,aAAa;IACpBX,KAAK,EAAC;;sBAEN,MA6NM,CA7NNY,mBAAA,CA6NM;MA7NDZ,KAAK,EAAAa,eAAA,EAAC,sBAAsB,WAAkBT,MAAA,CAAAD,IAAI,YAAYC,MAAA,CAAAU,WAAW;QAC5EC,mBAAA,YAAe,EACfH,mBAAA,CA8BM,OA9BNI,UA8BM,GA7BJJ,mBAAA,CASM,OATNK,UASM,GARJL,mBAAA,CAGM,OAHNM,UAGM,GAFJN,mBAAA,CAA8C,MAA9CO,UAA8C,EAAAC,gBAAA,CAArBX,MAAA,CAAAY,QAAQ,CAACC,IAAI,kBACtCV,mBAAA,CAAsD,OAAtDW,UAAsD,EAAAH,gBAAA,CAAzBX,MAAA,CAAAY,QAAQ,CAACG,OAAO,iB,GAE/CZ,mBAAA,CAGM;MAHDZ,KAAK,EAAAa,eAAA,EAAC,gBAAgB,EAASJ,MAAA,CAAAgB,aAAa,CAACC,IAAI;QACpDd,mBAAA,CAAmC;MAA/BZ,KAAK,EAAAa,eAAA,CAAEJ,MAAA,CAAAgB,aAAa,CAACd,IAAI;6BAC7BC,mBAAA,CAAqC,cAAAQ,gBAAA,CAA5BX,MAAA,CAAAgB,aAAa,CAACE,IAAI,iB,oBAI/Bf,mBAAA,CAiBM,OAjBNgB,UAiBM,GAhBJhB,mBAAA,CAGM,OAHNiB,UAGM,GAFJjB,mBAAA,CAA2D,OAA3DkB,UAA2D,EAAAV,gBAAA,CAAhCX,MAAA,CAAAsB,SAAS,CAACC,WAAW,IAAG,IAAE,iB,0BACrDpB,mBAAA,CAAkC;MAA7BZ,KAAK,EAAC;IAAY,GAAC,MAAI,oB,GAE9BY,mBAAA,CAGM,OAHNqB,UAGM,GAFJrB,mBAAA,CAAuD,OAAvDsB,WAAuD,EAAAd,gBAAA,CAA5BX,MAAA,CAAAsB,SAAS,CAACI,QAAQ,IAAG,GAAC,iB,0BACjDvB,mBAAA,CAAgC;MAA3BZ,KAAK,EAAC;IAAY,GAAC,IAAE,oB,GAE5BY,mBAAA,CAGM,OAHNwB,WAGM,GAFJxB,mBAAA,CAA2D,OAA3DyB,WAA2D,EAAAjB,gBAAA,CAAhCX,MAAA,CAAAsB,SAAS,CAACO,aAAa,kB,0BAClD1B,mBAAA,CAAkC;MAA7BZ,KAAK,EAAC;IAAY,GAAC,MAAI,oB,GAE9BY,mBAAA,CAGM,OAHN2B,WAGM,GAFJ3B,mBAAA,CAA0D,OAA1D4B,WAA0D,EAAApB,gBAAA,CAA/BX,MAAA,CAAAsB,SAAS,CAACU,WAAW,IAAG,GAAC,iB,0BACpD7B,mBAAA,CAAgC;MAA3BZ,KAAK,EAAC;IAAY,GAAC,IAAE,oB,OAKhCe,mBAAA,cAAiB,EACcX,MAAA,CAAAsC,cAAc,I,cAA7CC,mBAAA,CAsBM,OAtBNC,WAsBM,G,0BArBJhC,mBAAA,CAGM;MAHDZ,KAAK,EAAC;IAAc,IACvBY,mBAAA,CAA4B;MAAzBZ,KAAK,EAAC;IAAc,IACvBY,mBAAA,CAAiB,cAAX,MAAI,E,qBAEZA,mBAAA,CAgBM,OAhBNiC,WAgBM,I,kBAfJF,mBAAA,CAcSG,SAAA,QAAAC,WAAA,CAbStC,MAAA,CAAAuC,UAAU,EAAnBC,KAAK;2BADdN,mBAAA,CAcS;QAZNO,GAAG,EAAED,KAAK,CAACE,EAAE;QACbC,OAAK,EAAAC,MAAA,IAAE5C,MAAA,CAAA6C,aAAa,CAACL,KAAK;QAC1BjD,KAAK,EAAAa,eAAA;UAAA0C,MAAA,EAA0BN,KAAK,CAACE,EAAE,KAAK1C,MAAA,CAAA+C;QAAa;QACzDC,QAAQ,EAAEhD,MAAA,CAAAiD;UAEX9C,mBAAA,CAEM,OAFN+C,WAEM,GADJ/C,mBAAA,CAA2B;QAAvBZ,KAAK,EAAAa,eAAA,CAAEoC,KAAK,CAACtC,IAAI;iCAEvBC,mBAAA,CAGM,OAHNgD,WAGM,GAFJhD,mBAAA,CAA8C,OAA9CiD,WAA8C,EAAAzC,gBAAA,CAAnB6B,KAAK,CAAC3B,IAAI,kBACrCV,mBAAA,CAAqD,OAArDkD,WAAqD,EAAA1C,gBAAA,CAA1B6B,KAAK,CAACc,WAAW,iB;6EAMpDhD,mBAAA,YAAe,EACfH,mBAAA,CA4HM,OA5HNoD,WA4HM,GA3HJpD,mBAAA,CAgBM,OAhBNqD,WAgBM,G,0BAfJrD,mBAAA,CAAiB,cAAX,MAAI,qBACVA,mBAAA,CAaM,OAbNsD,WAaM,GAZJtD,mBAAA,CAKS;MAJNwC,OAAK,EAAAe,MAAA,QAAAA,MAAA,MAAAd,MAAA,IAAE5C,MAAA,CAAA2D,QAAQ;MACfpE,KAAK,EAAAa,eAAA;QAAA0C,MAAA,EAA2B9C,MAAA,CAAA2D,QAAQ;MAAA;kCAEzCxD,mBAAA,CAAyB;MAAtBZ,KAAK,EAAC;IAAW,0B,mBAEtBY,mBAAA,CAKS;MAJNwC,OAAK,EAAAe,MAAA,QAAAA,MAAA,MAAAd,MAAA,IAAE5C,MAAA,CAAA2D,QAAQ;MACfpE,KAAK,EAAAa,eAAA;QAAA0C,MAAA,EAA2B9C,MAAA,CAAA2D,QAAQ;MAAA;kCAEzCxD,mBAAA,CAA2B;MAAxBZ,KAAK,EAAC;IAAa,0B,uBAK5BY,mBAAA,CAwGM;MAxGAZ,KAAK,EAAAa,eAAA,+BAAgCJ,MAAA,CAAA2D,QAAQ;2BACjDzB,mBAAA,CAsGMG,SAAA,QAAAC,WAAA,CArGatC,MAAA,CAAA4D,eAAe,EAAzBC,MAAM;2BADf3B,mBAAA,CAsGM;QApGHO,GAAG,EAAEoB,MAAM,CAACnB,EAAE;QACdnD,KAAK,EAAAa,eAAA,yBAA0ByD,MAAM,CAAC5C,IAAI,cAAc4C,MAAM,CAACC,MAAM;QACrEnB,OAAK,EAAAC,MAAA,IAAE5C,MAAA,CAAA+D,iBAAiB,CAACF,MAAM;UAEhC1D,mBAAA,CAWM,OAXN6D,WAWM,GAVJ7D,mBAAA,CAEM,OAFN8D,WAEM,GADJ9D,mBAAA,CAA4B;QAAxBZ,KAAK,EAAAa,eAAA,CAAEyD,MAAM,CAAC3D,IAAI;iCAExBC,mBAAA,CAGM,OAHN+D,WAGM,GAFJ/D,mBAAA,CAAgD,OAAhDgE,WAAgD,EAAAxD,gBAAA,CAApBkD,MAAM,CAAChD,IAAI,kBACvCV,mBAAA,CAAgD,OAAhDiE,WAAgD,EAAAzD,gBAAA,CAApBkD,MAAM,CAACQ,IAAI,iB,GAEzClE,mBAAA,CAEM;QAFDZ,KAAK,EAAAa,eAAA,EAAC,yBAAyB,EAASyD,MAAM,CAACC,MAAM;2CACxD3D,mBAAA,CAA8B;QAAzBZ,KAAK,EAAC;MAAY,0B,uBAI3BY,mBAAA,CA0EM,OA1ENmE,WA0EM,GAzEJhE,mBAAA,UAAa,EACFuD,MAAM,CAAC5C,IAAI,iB,cAAtBiB,mBAAA,CASM,OATNqC,WASM,GARJpE,mBAAA,CAOQ,SAPRqE,WAOQ,GANNrE,mBAAA,CAIC;QAHCc,IAAI,EAAC,UAAU;QACdwD,OAAO,EAAEZ,MAAM,CAACa,KAAK,CAACC,EAAE;QACxBC,QAAM,EAAAhC,MAAA,IAAE5C,MAAA,CAAA6E,YAAY,CAAChB,MAAM;yFAE9B1D,mBAAA,CAA4B;QAAtBZ,KAAK,EAAC;MAAQ,2B,OAKRsE,MAAM,CAAC5C,IAAI,gB,cAA3BiB,mBAAA,CAuBMG,SAAA;QAAAI,GAAA;MAAA,IAxBNnC,mBAAA,UAAa,EACbH,mBAAA,CAuBM,OAvBN2E,WAuBM,GAtBJ3E,mBAAA,CAUM,OAVN4E,WAUM,GATJ5E,mBAAA,CAOC;QANCc,IAAI,EAAC,OAAO;QACX+D,KAAK,EAAEnB,MAAM,CAACa,KAAK,CAACO,UAAU;QAC9BC,OAAK,EAAAtC,MAAA,IAAE5C,MAAA,CAAAmF,gBAAgB,CAACtB,MAAM,EAAEjB,MAAM,CAACwC,MAAM,CAACJ,KAAK;QACpDK,GAAG,EAAC,GAAG;QACPC,GAAG,EAAC,KAAK;QACT/F,KAAK,EAAC;6DAERY,mBAAA,CAAoE,QAApEoF,WAAoE,EAAA5E,gBAAA,CAAlCkD,MAAM,CAACa,KAAK,CAACO,UAAU,IAAG,GAAC,gB,GAE9BpB,MAAM,CAACa,KAAK,CAACc,YAAY,I,cAA1DtD,mBAAA,CAUM,OAVNuD,WAUM,GATJtF,mBAAA,CAQM,OARNuF,WAQM,I,kBAPJxD,mBAAA,CAMUG,SAAA,QAAAC,WAAA,CALQtC,MAAA,CAAA2F,YAAY,EAArBC,KAAK;6BADd1D,mBAAA,CAMU;UAJPO,GAAG,EAAEmD,KAAK,CAAC/E,IAAI;UACf8B,OAAK,EAAAC,MAAA,IAAE5C,MAAA,CAAA6F,aAAa,CAAChC,MAAM,EAAE+B,KAAK;UAClCrG,KAAK,EAAAa,eAAA;YAAA0C,MAAA,EAA6Be,MAAM,CAACa,KAAK,CAACkB,KAAK,KAAKA,KAAK,CAACZ;UAAK;UACpEc,KAAK,EAAAC,eAAA;YAAAC,eAAA,EAAqBJ,KAAK,CAACZ;UAAK;;oIAO9BnB,MAAM,CAAC5C,IAAI,qB,cAA3BiB,mBAAA,CAaMG,SAAA;QAAAI,GAAA;MAAA,IAdNnC,mBAAA,WAAc,EACdH,mBAAA,CAaM,OAbN8F,WAaM,GAZJ9F,mBAAA,CAGM,OAHN+F,WAGM,GAFJ/F,mBAAA,CAAkE,QAAlEgG,WAAkE,EAAAxF,gBAAA,CAApCkD,MAAM,CAACa,KAAK,CAAC0B,WAAW,IAAG,IAAE,iBAC3DjG,mBAAA,CAAoE,QAApEkG,WAAoE,EAA1C,MAAI,GAAA1F,gBAAA,CAAGkD,MAAM,CAACa,KAAK,CAAC4B,UAAU,IAAG,IAAE,gB,GAE/DnG,mBAAA,CAOM,OAPNoG,WAOM,GANJpG,mBAAA,CAES;QAFAwC,OAAK,EAAAC,MAAA,IAAE5C,MAAA,CAAAwG,iBAAiB,CAAC3C,MAAM;QAAOtE,KAAK,EAAC;2CACnDY,mBAAA,CAA4B;QAAzBZ,KAAK,EAAC;MAAc,0B,kCAEzBY,mBAAA,CAES;QAFAwC,OAAK,EAAAC,MAAA,IAAE5C,MAAA,CAAAwG,iBAAiB,CAAC3C,MAAM;QAAMtE,KAAK,EAAC;2CAClDY,mBAAA,CAA2B;QAAxBZ,KAAK,EAAC;MAAa,0B,yFAMZsE,MAAM,CAAC5C,IAAI,mB,cAA3BiB,mBAAA,CAYMG,SAAA;QAAAI,GAAA;MAAA,IAbNnC,mBAAA,YAAe,EACfH,mBAAA,CAYM,OAZNsG,WAYM,GAXJtG,mBAAA,CAIM,OAJNuG,WAIM,GAHJvG,mBAAA,CAEO;QAFAZ,KAAK,EAAAa,eAAA,iBAAkByD,MAAM,CAACa,KAAK,CAACiC,KAAK;0BAC3C9C,MAAM,CAACa,KAAK,CAACiC,KAAK,wC,GAGzBxG,mBAAA,CAKS;QAJNwC,OAAK,EAAAC,MAAA,IAAE5C,MAAA,CAAA4G,cAAc,CAAC/C,MAAM;QAC5BtE,KAAK,EAAAa,eAAA,qBAAsByD,MAAM,CAACa,KAAK,CAACiC,KAAK;0BAE3C9C,MAAM,CAACa,KAAK,CAACiC,KAAK,8CAAAE,WAAA,E,qEAKzB3E,mBAAA,CAEMG,SAAA;QAAAI,GAAA;MAAA,IAHNnC,mBAAA,YAAe,EACfH,mBAAA,CAEM,OAFN2G,WAEM,GADJ3G,mBAAA,CAAkE,QAAlE4G,WAAkE,EAAApG,gBAAA,CAArCX,MAAA,CAAAgH,mBAAmB,CAACnD,MAAM,kB,uDAI3DvD,mBAAA,YAAe,EACJuD,MAAM,CAACoD,WAAW,I,cAA7B/E,mBAAA,CAKM,OALNgF,WAKM,I,kBAJJhF,mBAAA,CAGMG,SAAA,QAAAC,WAAA,CAH0CuB,MAAM,CAACsD,OAAO,GAA7BnC,KAAK,EAAEvC,GAAG;6BAA3CP,mBAAA,CAGM;UAHD3C,KAAK,EAAC,aAAa;UAAyCkD,GAAG,EAAEA;YACpEtC,mBAAA,CAA4C,QAA5CiH,WAA4C,EAAAzG,gBAAA,CAAd8B,GAAG,IAAG,GAAC,iBACrCtC,mBAAA,CAA6C,QAA7CkH,WAA6C,EAAA1G,gBAAA,CAAfqE,KAAK,iB;;uDAO7C1E,mBAAA,YAAe,EACmBN,MAAA,CAAAsH,aAAa,CAACC,MAAM,Q,cAAtDrF,mBAAA,CA2BM,OA3BNsF,WA2BM,G,4BA1BJrH,mBAAA,CAGM;MAHDZ,KAAK,EAAC;IAAoB,IAC7BY,mBAAA,CAAgC;MAA7BZ,KAAK,EAAC;IAAkB,IAC3BY,mBAAA,CAAmB,cAAb,QAAM,E,qBAEdA,mBAAA,CAqBM,OArBNsH,WAqBM,I,kBApBJvF,mBAAA,CAmBMG,SAAA,QAAAC,WAAA,CAlBiBtC,MAAA,CAAAsH,aAAa,EAA3BI,UAAU;2BADnBxF,mBAAA,CAmBM;QAjBHO,GAAG,EAAEiF,UAAU,CAAChF,EAAE;QACnBnD,KAAK,EAAC;UAENY,mBAAA,CAMM,OANNwH,WAMM,GALJxH,mBAAA,CAAwD,OAAxDyH,WAAwD,EAAAjH,gBAAA,CAAxB+G,UAAU,CAACxG,IAAI,kBAC/Cf,mBAAA,CAGM,OAHN0H,WAGM,GAFJ1H,mBAAA,CAAmE,QAAnE2H,WAAmE,EAAvC,KAAG,GAAAnH,gBAAA,CAAG+G,UAAU,CAACK,YAAY,kBACzD5H,mBAAA,CAAkF,QAAlF6H,WAAkF,EAAzD,MAAI,GAAArH,gBAAA,CAAGsH,IAAI,CAACC,KAAK,CAACR,UAAU,CAACS,UAAU,WAAU,GAAC,gB,KAG/EhI,mBAAA,CAMS;QALNwC,OAAK,EAAAC,MAAA,IAAE5C,MAAA,CAAAoI,eAAe,CAACV,UAAU;QAClCnI,KAAK,EAAC,WAAW;QAChByD,QAAQ,EAAEhD,MAAA,CAAAiD;2CAEX9C,mBAAA,CAA4B;QAAzBZ,KAAK,EAAC;MAAc,0B;6EAM/Be,mBAAA,UAAa,EACFN,MAAA,CAAAqI,SAAS,I,cAApBnG,mBAAA,CAGM,OAHNoG,WAGM,EAAA5E,MAAA,SAAAA,MAAA,QAFJvD,mBAAA,CAAmC;MAA9BZ,KAAK,EAAC;IAAiB,2BAC5BY,mBAAA,CAA2C;MAAtCZ,KAAK,EAAC;IAAc,GAAC,aAAW,mB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}