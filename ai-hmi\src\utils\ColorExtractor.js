class ColorExtractor {
  static async extractColors(imageUrl, fallbackPrompt = '') {
    return new Promise((resolve) => {
      const img = new Image()
      img.crossOrigin = 'anonymous'

      img.onload = () => {
        try {
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')

          canvas.width = img.width
          canvas.height = img.height
          ctx.drawImage(img, 0, 0)

          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
          const colors = this.analyzeColors(imageData)

          console.log('颜色提取成功:', colors)
          resolve(colors)
        } catch (error) {
          console.error('颜色分析失败:', error)
          resolve(this.getSceneBasedColors(fallbackPrompt))
        }
      }

      img.onerror = (error) => {
        console.error('图片加载失败:', error)
        // 根据场景提示词生成智能默认颜色
        resolve(this.getSceneBasedColors(fallbackPrompt))
      }

      img.src = imageUrl
    })
  }

  static analyzeColors(imageData) {
    const data = imageData.data
    const colorMap = {}
    
    // 采样像素点
    for (let i = 0; i < data.length; i += 16) {
      const r = data[i]
      const g = data[i + 1]
      const b = data[i + 2]
      const a = data[i + 3]
      
      if (a < 128) continue
      
      const color = this.quantizeColor(r, g, b)
      const key = `${color.r},${color.g},${color.b}`
      
      colorMap[key] = (colorMap[key] || 0) + 1
    }
    
    const sortedColors = Object.entries(colorMap)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([key]) => {
        const [r, g, b] = key.split(',').map(Number)
        return { r, g, b }
      })
    
    return this.generateGlassmorphismPalette(sortedColors)
  }

  static generateGlassmorphismPalette(colors) {
    if (colors.length === 0) return this.getDefaultGlassmorphismColors()

    const primaryColor = colors[0]
    const secondaryColor = colors[1] || colors[0]

    // 计算背景亮度
    const backgroundBrightness = (primaryColor.r * 299 + primaryColor.g * 587 + primaryColor.b * 114) / 1000

    // 生成高对比度的文字颜色
    const textColor = this.getHighContrastTextColor(primaryColor)

    // 生成卡片背景颜色（基于主色调整）
    const cardBackground = this.getGlassBackground(primaryColor)

    // 生成卡片边框颜色
    const cardBorder = this.getGlassBorder(primaryColor)

    // 生成按钮颜色（基于主色但确保对比度）
    const buttonColors = this.generateButtonColors(primaryColor)

    // 生成标题颜色（确保在卡片背景上可见）
    const titleColor = this.getTitleColor(primaryColor)

    // 生成内容文字颜色（确保在卡片背景上可见）
    const contentTextColor = this.getContentTextColor(primaryColor)

    return {
      primary: this.rgbToHex(primaryColor),
      secondary: this.rgbToHex(secondaryColor),
      glassBackground: cardBackground,
      glassBorder: cardBorder,
      text: textColor,
      // 卡片专用颜色
      cardTitleColor: titleColor,
      cardContentColor: contentTextColor,
      // 按钮相关颜色
      buttonBackground: buttonColors.background,
      buttonColor: buttonColors.color,
      buttonBorder: buttonColors.border,
      buttonHoverBackground: buttonColors.hoverBackground,
      buttonTextShadow: buttonColors.textShadow,
      // 背景信息
      backgroundBrightness: backgroundBrightness,
      contrastRatio: this.getContrastRatio(primaryColor, textColor === '#FFFFFF' ? { r: 255, g: 255, b: 255 } : { r: 0, g: 0, b: 0 })
    }
  }

  static getGlassBackground(color) {
    const brightness = (color.r * 299 + color.g * 587 + color.b * 114) / 1000

    // 根据壁纸亮度动态调整卡片背景，确保与壁纸有足够对比度
    if (brightness > 180) {
      // 很亮的背景：使用深色半透明卡片，增强对比度
      return `rgba(${Math.max(0, color.r - 80)}, ${Math.max(0, color.g - 80)}, ${Math.max(0, color.b - 80)}, 0.75)`
    } else if (brightness > 128) {
      // 中等亮度：使用稍深的半透明卡片
      return `rgba(${Math.max(0, color.r - 40)}, ${Math.max(0, color.g - 40)}, ${Math.max(0, color.b - 40)}, 0.65)`
    } else if (brightness > 80) {
      // 较暗：使用稍亮的半透明卡片
      return `rgba(${Math.min(255, color.r + 40)}, ${Math.min(255, color.g + 40)}, ${Math.min(255, color.b + 40)}, 0.55)`
    } else {
      // 很暗：使用明亮的半透明卡片
      return `rgba(${Math.min(255, color.r + 80)}, ${Math.min(255, color.g + 80)}, ${Math.min(255, color.b + 80)}, 0.65)`
    }
  }

  static getGlassBorder(color) {
    const brightness = (color.r * 299 + color.g * 587 + color.b * 114) / 1000

    // 根据背景亮度动态调整边框颜色和透明度
    if (brightness > 180) {
      // 很亮的背景：使用深色边框
      return `rgba(${Math.max(0, color.r - 100)}, ${Math.max(0, color.g - 100)}, ${Math.max(0, color.b - 100)}, 0.6)`
    } else if (brightness > 128) {
      // 中等亮度：使用中性边框
      return `rgba(${color.r}, ${color.g}, ${color.b}, 0.4)`
    } else {
      // 较暗：使用亮色边框
      return `rgba(${Math.min(255, color.r + 100)}, ${Math.min(255, color.g + 100)}, ${Math.min(255, color.b + 100)}, 0.5)`
    }
  }

  // 根据场景提示词生成智能颜色
  static getSceneBasedColors(prompt = '') {
    const lowerPrompt = prompt.toLowerCase()

    // 定义场景颜色映射
    const sceneColorMap = {
      // 蓝色系 - 商务、科技、现代
      blue: { r: 52, g: 152, b: 219 }, // #3498db
      // 绿色系 - 自然、放松、春天
      green: { r: 46, g: 204, b: 113 }, // #2ecc71
      // 橙色系 - 温暖、日落、秋天
      orange: { r: 230, g: 126, b: 34 }, // #e67e22
      // 紫色系 - 夜晚、神秘、优雅
      purple: { r: 155, g: 89, b: 182 }, // #9b59b6
      // 红色系 - 激情、紧急、运动
      red: { r: 231, g: 76, b: 60 }, // #e74c3c
      // 青色系 - 清新、海洋、冰雪
      cyan: { r: 26, g: 188, b: 156 }, // #1abc9c
      // 黄色系 - 阳光、活力、明亮
      yellow: { r: 241, g: 196, b: 15 } // #f1c40f
    }

    let primaryColor = sceneColorMap.blue // 默认蓝色

    // 根据关键词匹配颜色
    if (lowerPrompt.includes('sunset') || lowerPrompt.includes('warm') || lowerPrompt.includes('orange') || lowerPrompt.includes('日落') || lowerPrompt.includes('温暖')) {
      primaryColor = sceneColorMap.orange
    } else if (lowerPrompt.includes('night') || lowerPrompt.includes('evening') || lowerPrompt.includes('purple') || lowerPrompt.includes('夜晚') || lowerPrompt.includes('晚上')) {
      primaryColor = sceneColorMap.purple
    } else if (lowerPrompt.includes('nature') || lowerPrompt.includes('green') || lowerPrompt.includes('forest') || lowerPrompt.includes('spring') || lowerPrompt.includes('自然') || lowerPrompt.includes('绿色') || lowerPrompt.includes('春天')) {
      primaryColor = sceneColorMap.green
    } else if (lowerPrompt.includes('ocean') || lowerPrompt.includes('sea') || lowerPrompt.includes('cyan') || lowerPrompt.includes('fresh') || lowerPrompt.includes('海洋') || lowerPrompt.includes('清新')) {
      primaryColor = sceneColorMap.cyan
    } else if (lowerPrompt.includes('emergency') || lowerPrompt.includes('red') || lowerPrompt.includes('urgent') || lowerPrompt.includes('紧急') || lowerPrompt.includes('红色')) {
      primaryColor = sceneColorMap.red
    } else if (lowerPrompt.includes('bright') || lowerPrompt.includes('sunny') || lowerPrompt.includes('yellow') || lowerPrompt.includes('明亮') || lowerPrompt.includes('阳光')) {
      primaryColor = sceneColorMap.yellow
    } else if (lowerPrompt.includes('glass') || lowerPrompt.includes('modern') || lowerPrompt.includes('business') || lowerPrompt.includes('玻璃') || lowerPrompt.includes('现代') || lowerPrompt.includes('商务')) {
      primaryColor = sceneColorMap.blue
    }

    const buttonColors = this.generateButtonColors(primaryColor)
    const cardBackground = this.getGlassBackground(primaryColor)
    const titleColor = this.getTitleColor(primaryColor)
    const contentTextColor = this.getContentTextColor(primaryColor)
    const backgroundBrightness = (primaryColor.r * 299 + primaryColor.g * 587 + primaryColor.b * 114) / 1000

    return {
      primary: this.rgbToHex(primaryColor),
      secondary: this.rgbToHex(sceneColorMap.cyan), // 辅助色使用青色
      glassBackground: cardBackground,
      glassBorder: this.getGlassBorder(primaryColor),
      text: this.getHighContrastTextColor(primaryColor),
      // 卡片专用颜色
      cardTitleColor: titleColor,
      cardContentColor: contentTextColor,
      // 按钮相关颜色
      buttonBackground: buttonColors.background,
      buttonColor: buttonColors.color,
      buttonBorder: buttonColors.border,
      buttonHoverBackground: buttonColors.hoverBackground,
      buttonTextShadow: buttonColors.textShadow,
      // 背景信息
      backgroundBrightness: backgroundBrightness,
      contrastRatio: this.getContrastRatio(primaryColor, buttonColors.color === '#FFFFFF' ? { r: 255, g: 255, b: 255 } : { r: 0, g: 0, b: 0 }),
      // 标记这是基于场景生成的颜色
      isSceneBased: true,
      scenePrompt: prompt
    }
  }

  static getDefaultGlassmorphismColors() {
    // 使用场景生成方法，传入默认场景
    return this.getSceneBasedColors('modern glass building, business style')
  }

  static quantizeColor(r, g, b) {
    // 颜色量化，减少颜色数量
    const factor = 32
    return {
      r: Math.round(r / factor) * factor,
      g: Math.round(g / factor) * factor,
      b: Math.round(b / factor) * factor
    }
  }

  static rgbToHex(color) {
    const toHex = (c) => {
      const hex = c.toString(16)
      return hex.length === 1 ? '0' + hex : hex
    }
    return `#${toHex(color.r)}${toHex(color.g)}${toHex(color.b)}`
  }

  static getContrastColor(color) {
    // 使用WCAG 2.1标准计算对比色
    const brightness = (color.r * 299 + color.g * 587 + color.b * 114) / 1000
    return brightness > 128 ? '#000000' : '#FFFFFF'
  }

  // 将十六进制颜色转换为RGB对象
  static hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null
  }

  // 计算两个颜色之间的对比度比值
  static getContrastRatio(color1, color2) {
    const getLuminance = (color) => {
      const rgb = typeof color === 'string' ? this.hexToRgb(color) : color
      const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
        c = c / 255
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
      })
      return 0.2126 * r + 0.7152 * g + 0.0722 * b
    }

    const lum1 = getLuminance(color1)
    const lum2 = getLuminance(color2)
    const brightest = Math.max(lum1, lum2)
    const darkest = Math.min(lum1, lum2)

    return (brightest + 0.05) / (darkest + 0.05)
  }

  // 获取高对比度的文字颜色
  static getHighContrastTextColor(backgroundColor) {
    const bgColor = typeof backgroundColor === 'string' ? this.hexToRgb(backgroundColor) : backgroundColor

    // 测试白色和黑色的对比度
    const whiteContrast = this.getContrastRatio(bgColor, { r: 255, g: 255, b: 255 })
    const blackContrast = this.getContrastRatio(bgColor, { r: 0, g: 0, b: 0 })

    // WCAG AA标准要求对比度至少为4.5:1，AAA标准要求7:1
    if (whiteContrast >= 4.5) {
      return '#FFFFFF'
    } else if (blackContrast >= 4.5) {
      return '#000000'
    } else {
      // 如果都不满足，选择对比度更高的
      return whiteContrast > blackContrast ? '#FFFFFF' : '#000000'
    }
  }

  // 生成适合按钮的高对比度颜色
  static generateButtonColors(backgroundColor) {
    const bgColor = typeof backgroundColor === 'string' ? this.hexToRgb(backgroundColor) : backgroundColor
    const brightness = (bgColor.r * 299 + bgColor.g * 587 + bgColor.b * 114) / 1000

    // 根据背景亮度选择按钮策略
    let buttonBg, textColor, borderColor, hoverBg

    if (brightness > 150) {
      // 亮背景：使用深色按钮
      buttonBg = `rgba(${Math.max(0, bgColor.r - 60)}, ${Math.max(0, bgColor.g - 60)}, ${Math.max(0, bgColor.b - 60)}, 0.85)`
      textColor = '#FFFFFF'
      borderColor = 'rgba(255, 255, 255, 0.7)'
      hoverBg = `rgba(${Math.max(0, bgColor.r - 80)}, ${Math.max(0, bgColor.g - 80)}, ${Math.max(0, bgColor.b - 80)}, 0.95)`
    } else if (brightness > 100) {
      // 中等亮度：使用对比色按钮
      buttonBg = `rgba(${bgColor.r}, ${bgColor.g}, ${bgColor.b}, 0.8)`
      textColor = brightness > 128 ? '#000000' : '#FFFFFF'
      borderColor = textColor === '#FFFFFF' ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)'
      hoverBg = `rgba(${bgColor.r}, ${bgColor.g}, ${bgColor.b}, 0.9)`
    } else {
      // 暗背景：使用亮色按钮
      buttonBg = `rgba(${Math.min(255, bgColor.r + 60)}, ${Math.min(255, bgColor.g + 60)}, ${Math.min(255, bgColor.b + 60)}, 0.85)`
      textColor = '#000000'
      borderColor = 'rgba(0, 0, 0, 0.7)'
      hoverBg = `rgba(${Math.min(255, bgColor.r + 80)}, ${Math.min(255, bgColor.g + 80)}, ${Math.min(255, bgColor.b + 80)}, 0.95)`
    }

    return {
      background: buttonBg,
      color: textColor,
      border: borderColor,
      hoverBackground: hoverBg,
      textShadow: textColor === '#FFFFFF'
        ? '0 1px 2px rgba(0, 0, 0, 0.8)'
        : '0 1px 2px rgba(255, 255, 255, 0.8)'
    }
  }

  // 生成卡片标题颜色
  static getTitleColor(backgroundColor) {
    const bgColor = typeof backgroundColor === 'string' ? this.hexToRgb(backgroundColor) : backgroundColor
    const brightness = (bgColor.r * 299 + bgColor.g * 587 + bgColor.b * 114) / 1000

    if (brightness > 150) {
      return '#1a1a1a' // 深色标题
    } else if (brightness > 100) {
      return brightness > 128 ? '#2c3e50' : '#ecf0f1'
    } else {
      return '#ffffff' // 白色标题
    }
  }

  // 生成卡片内容文字颜色
  static getContentTextColor(backgroundColor) {
    const bgColor = typeof backgroundColor === 'string' ? this.hexToRgb(backgroundColor) : backgroundColor
    const brightness = (bgColor.r * 299 + bgColor.g * 587 + bgColor.b * 114) / 1000

    if (brightness > 150) {
      return '#34495e' // 深灰色内容
    } else if (brightness > 100) {
      return brightness > 128 ? '#34495e' : '#bdc3c7'
    } else {
      return '#ecf0f1' // 浅色内容
    }
  }
}

export default ColorExtractor