{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, renderSlot as _renderSlot, resolveDynamicComponent as _resolveDynamicComponent, openBlock as _openBlock, createBlock as _createBlock, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createElementBlock as _createElementBlock, mergeProps as _mergeProps, normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle } from \"vue\";\nconst _hoisted_1 = {\n  key: 0,\n  class: \"card-header\"\n};\nconst _hoisted_2 = {\n  class: \"card-title\"\n};\nconst _hoisted_3 = {\n  key: 0,\n  class: \"card-actions\"\n};\nconst _hoisted_4 = {\n  class: \"card-content\"\n};\nconst _hoisted_5 = {\n  key: 1,\n  class: \"card-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: _normalizeClass($options.cardClasses),\n    style: _normalizeStyle($options.cardStyles),\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.handleClick && $options.handleClick(...args))\n  }, [_createCommentVNode(\" 卡片头部 \"), $props.showHeader ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [_renderSlot(_ctx.$slots, \"header\", {}, () => [_createElementVNode(\"div\", _hoisted_2, [$props.titleIcon ? (_openBlock(), _createBlock(_resolveDynamicComponent($props.titleIcon), {\n    key: 0,\n    class: \"title-icon\"\n  })) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"span\", null, _toDisplayString($props.title), 1 /* TEXT */)]), $props.showActions ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_renderSlot(_ctx.$slots, \"actions\", {}, undefined, true)])) : _createCommentVNode(\"v-if\", true)], true)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 卡片内容 \"), _createElementVNode(\"div\", _hoisted_4, [_renderSlot(_ctx.$slots, \"content\", {}, () => [(_openBlock(), _createBlock(_resolveDynamicComponent($props.contentComponent), _mergeProps($props.contentProps, {\n    onUpdate: $options.handleContentUpdate\n  }), null, 16 /* FULL_PROPS */, [\"onUpdate\"]))], true)]), _createCommentVNode(\" 卡片底部 \"), $props.showFooter ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_renderSlot(_ctx.$slots, \"footer\", {}, undefined, true)])) : _createCommentVNode(\"v-if\", true)], 6 /* CLASS, STYLE */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_normalizeClass", "$options", "cardClasses", "style", "_normalizeStyle", "cardStyles", "onClick", "_cache", "args", "handleClick", "_createCommentVNode", "$props", "showHeader", "_hoisted_1", "_renderSlot", "_ctx", "$slots", "_createElementVNode", "_hoisted_2", "titleIcon", "_createBlock", "_resolveDynamicComponent", "_toDisplayString", "title", "showActions", "_hoisted_3", "undefined", "_hoisted_4", "contentComponent", "_mergeProps", "contentProps", "onUpdate", "handleContentUpdate", "showFooter", "_hoisted_5"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\BaseCard.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"cardClasses\"\n    :style=\"cardStyles\"\n    @click=\"handleClick\"\n  >\n    <!-- 卡片头部 -->\n    <div v-if=\"showHeader\" class=\"card-header\">\n      <slot name=\"header\">\n        <div class=\"card-title\">\n          <component :is=\"titleIcon\" v-if=\"titleIcon\" class=\"title-icon\" />\n          <span>{{ title }}</span>\n        </div>\n        <div v-if=\"showActions\" class=\"card-actions\">\n          <slot name=\"actions\" />\n        </div>\n      </slot>\n    </div>\n\n    <!-- 卡片内容 -->\n    <div class=\"card-content\">\n      <slot name=\"content\">\n        <component\n          :is=\"contentComponent\"\n          v-bind=\"contentProps\"\n          @update=\"handleContentUpdate\"\n        />\n      </slot>\n    </div>\n\n    <!-- 卡片底部 -->\n    <div v-if=\"showFooter\" class=\"card-footer\">\n      <slot name=\"footer\" />\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'BaseCard',\n  props: {\n    // 尺寸配置\n    gridSize: {\n      type: String,\n      default: '4x2',\n      validator: (value) => /^\\d+x\\d+$/.test(value)\n    },\n\n    // 内容配置\n    title: {\n      type: String,\n      default: ''\n    },\n    titleIcon: {\n      type: [String, Object],\n      default: null\n    },\n    contentComponent: {\n      type: [String, Object],\n      default: null\n    },\n    contentProps: {\n      type: Object,\n      default: () => ({})\n    },\n\n    // 主题配置\n    theme: {\n      type: String,\n      default: 'glassmorphism',\n      validator: (value) => ['glassmorphism', 'minimal', 'dark', 'light'].includes(value)\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({})\n    },\n\n    // 布局配置\n    position: {\n      type: Object,\n      default: () => ({ x: 0, y: 0 })\n    },\n    size: {\n      type: String,\n      default: 'medium',\n      validator: (value) => ['small', 'medium', 'large'].includes(value)\n    },\n\n    // 显示控制\n    showHeader: {\n      type: Boolean,\n      default: true\n    },\n    showFooter: {\n      type: Boolean,\n      default: false\n    },\n    showActions: {\n      type: Boolean,\n      default: false\n    },\n\n    // 交互配置\n    clickable: {\n      type: Boolean,\n      default: false\n    },\n    disabled: {\n      type: Boolean,\n      default: false\n    },\n\n    // 状态\n    loading: {\n      type: Boolean,\n      default: false\n    }\n  },\n\n  emits: ['click', 'content-update'],\n\n  computed: {\n    cardClasses() {\n      return [\n        'base-card',\n        `card-${this.theme}`,\n        `card-size-${this.size}`,\n        `grid-${this.gridSize}`,\n        {\n          'card-clickable': this.clickable,\n          'card-disabled': this.disabled,\n          'card-loading': this.loading\n        }\n      ]\n    },\n\n    cardStyles() {\n      const styles = {\n        '--card-x': this.position.x,\n        '--card-y': this.position.y\n      }\n\n      // 应用主题颜色\n      if (this.themeColors) {\n        Object.keys(this.themeColors).forEach(key => {\n          styles[`--theme-${key}`] = this.themeColors[key]\n        })\n      }\n\n      return styles\n    }\n  },\n\n  methods: {\n    handleClick(event) {\n      if (!this.disabled && this.clickable) {\n        this.$emit('click', event)\n      }\n    },\n\n    handleContentUpdate(data) {\n      this.$emit('content-update', data)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.base-card {\n  position: relative;\n  border-radius: 16px;\n  overflow: hidden;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n/* 主题样式 */\n.card-glassmorphism {\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.card-minimal {\n  background: rgba(255, 255, 255, 0.95);\n  border: 1px solid rgba(0, 0, 0, 0.1);\n  backdrop-filter: none;\n}\n\n.card-dark {\n  background: rgba(0, 0, 0, 0.8);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  color: white;\n}\n\n.card-light {\n  background: rgba(255, 255, 255, 0.9);\n  border: 1px solid rgba(0, 0, 0, 0.1);\n  color: #333;\n}\n\n/* 尺寸样式 */\n.card-size-small {\n  min-height: 120px;\n  padding: 12px;\n}\n\n.card-size-medium {\n  min-height: 180px;\n  padding: 16px;\n}\n\n.card-size-large {\n  min-height: 240px;\n  padding: 20px;\n}\n\n/* 网格尺寸 */\n.grid-2x2 {\n  grid-column: span 2;\n  grid-row: span 2;\n}\n\n.grid-4x2 {\n  grid-column: span 4;\n  grid-row: span 2;\n}\n\n.grid-4x4 {\n  grid-column: span 4;\n  grid-row: span 4;\n}\n\n.grid-8x4 {\n  grid-column: span 8;\n  grid-row: span 4;\n}\n\n.grid-8x9 {\n  grid-column: span 8;\n  grid-row: span 9;\n}\n\n.grid-16x1 {\n  grid-column: span 16;\n  grid-row: span 1;\n}\n\n/* 交互状态 */\n.card-clickable {\n  cursor: pointer;\n}\n\n.card-clickable:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n}\n\n.card-disabled {\n  opacity: 0.6;\n  pointer-events: none;\n}\n\n.card-loading {\n  position: relative;\n}\n\n.card-loading::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(2px);\n}\n\n/* 卡片结构 */\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.card-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  color: var(--theme-text, rgba(255, 255, 255, 0.9));\n}\n\n.title-icon {\n  width: 20px;\n  height: 20px;\n  opacity: 0.8;\n}\n\n.card-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.card-content {\n  flex: 1;\n  min-height: 0;\n}\n\n.card-footer {\n  margin-top: 16px;\n  padding-top: 16px;\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .card-size-small {\n    min-height: 100px;\n    padding: 10px;\n  }\n\n  .card-size-medium {\n    min-height: 150px;\n    padding: 14px;\n  }\n\n  .card-size-large {\n    min-height: 200px;\n    padding: 16px;\n  }\n\n  .card-title {\n    font-size: 14px;\n  }\n}\n</style>"], "mappings": ";;;EAO2BA,KAAK,EAAC;;;EAEpBA,KAAK,EAAC;AAAY;;;EAICA,KAAK,EAAC;;;EAO7BA,KAAK,EAAC;AAAc;;;EAWFA,KAAK,EAAC;;;uBA9B/BC,mBAAA,CAiCM;IAhCHD,KAAK,EAAAE,eAAA,CAAEC,QAAA,CAAAC,WAAW;IAClBC,KAAK,EAAAC,eAAA,CAAEH,QAAA,CAAAI,UAAU;IACjBC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEP,QAAA,CAAAQ,WAAA,IAAAR,QAAA,CAAAQ,WAAA,IAAAD,IAAA,CAAW;MAEnBE,mBAAA,UAAa,EACFC,MAAA,CAAAC,UAAU,I,cAArBb,mBAAA,CAUM,OAVNc,UAUM,GATJC,WAAA,CAQOC,IAAA,CAAAC,MAAA,gBARP,MAQO,CAPLC,mBAAA,CAGM,OAHNC,UAGM,GAF6BP,MAAA,CAAAQ,SAAS,I,cAA1CC,YAAA,CAAiEC,wBAAA,CAAjDV,MAAA,CAAAQ,SAAS;;IAAmBrB,KAAK,EAAC;2CAClDmB,mBAAA,CAAwB,cAAAK,gBAAA,CAAfX,MAAA,CAAAY,KAAK,iB,GAELZ,MAAA,CAAAa,WAAW,I,cAAtBzB,mBAAA,CAEM,OAFN0B,UAEM,GADJX,WAAA,CAAuBC,IAAA,CAAAC,MAAA,iBAAAU,SAAA,Q,uFAK7BhB,mBAAA,UAAa,EACbO,mBAAA,CAQM,OARNU,UAQM,GAPJb,WAAA,CAMOC,IAAA,CAAAC,MAAA,iBANP,MAMO,E,cALLI,YAAA,CAIEC,wBAAA,CAHKV,MAAA,CAAAiB,gBAAgB,GADvBC,WAAA,CAIElB,MAFQ,CAAAmB,YAAY;IACnBC,QAAM,EAAE9B,QAAA,CAAA+B;EAAmB,8C,WAKlCtB,mBAAA,UAAa,EACFC,MAAA,CAAAsB,UAAU,I,cAArBlC,mBAAA,CAEM,OAFNmC,UAEM,GADJpB,WAAA,CAAsBC,IAAA,CAAAC,MAAA,gBAAAU,SAAA,Q", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}