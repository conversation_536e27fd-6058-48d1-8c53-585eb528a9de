class GlassStyleUtils {
  static calculateGlassStyles(colors) {
    const brightness = this.getColorBrightness(colors.primary)
    
    return {
      borderRadius: this.calculateBorderRadius(brightness),
      backdropFilter: 'blur(12px)',
      background: colors.glassBackground,
      border: `1px solid ${colors.glassBorder}`,
      boxShadow: this.calculateBoxShadow(brightness),
      textShadow: this.calculateTextShadow(brightness)
    }
  }

  static calculateBorderRadius(brightness) {
    // 根据颜色亮度计算圆角大小
    if (brightness > 200) return '8px'      // 高亮度 - 小圆角
    if (brightness > 150) return '12px'     // 中高亮度 - 中圆角
    if (brightness > 100) return '16px'     // 中亮度 - 大圆角
    return '20px'                           // 低亮度 - 最大圆角
  }

  static calculateBoxShadow(brightness) {
    const opacity = brightness > 128 ? 0.1 : 0.2
    return `0 8px 32px rgba(0, 0, 0, ${opacity})`
  }

  static calculateTextShadow(brightness) {
    if (brightness > 128) {
      return '0 1px 2px rgba(0, 0, 0, 0.5)'
    }
    return '0 1px 2px rgba(255, 255, 255, 0.3)'
  }

  static getColorBrightness(hexColor) {
    const rgb = this.hexToRgb(hexColor)
    return (rgb.r + rgb.g + rgb.b) / 3
  }

  static hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null
  }

  static rgbToHsl(r, g, b) {
    r /= 255
    g /= 255
    b /= 255

    const max = Math.max(r, g, b)
    const min = Math.min(r, g, b)
    let h, s, l = (max + min) / 2

    if (max === min) {
      h = s = 0
    } else {
      const d = max - min
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
      
      switch (max) {
        case r: h = ((g - b) / d + (g < b ? 6 : 0)) / 6; break
        case g: h = ((b - r) / d + 2) / 6; break
        case b: h = ((r - g) / d + 4) / 6; break
      }
    }

    return {
      h: Math.round(h * 360),
      s: Math.round(s * 100),
      l: Math.round(l * 100)
    }
  }

  static getComplementaryColor(hexColor) {
    const rgb = this.hexToRgb(hexColor)
    const hsl = this.rgbToHsl(rgb.r, rgb.g, rgb.b)
    
    // 色相偏移180度
    hsl.h = (hsl.h + 180) % 360
    
    return this.hslToHex(hsl.h, hsl.s, hsl.l)
  }

  static hslToHex(h, s, l) {
    h /= 360
    s /= 100
    l /= 100

    const hue2rgb = (p, q, t) => {
      if (t < 0) t += 1
      if (t > 1) t -= 1
      if (t < 1/6) return p + (q - p) * 6 * t
      if (t < 1/2) return q
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6
      return p
    }

    let r, g, b

    if (s === 0) {
      r = g = b = l
    } else {
      const q = l < 0.5 ? l * (1 + s) : l + s - l * s
      const p = 2 * l - q
      r = hue2rgb(p, q, h + 1/3)
      g = hue2rgb(p, q, h)
      b = hue2rgb(p, q, h - 1/3)
    }

    const toHex = (c) => {
      const hex = Math.round(c * 255).toString(16)
      return hex.length === 1 ? '0' + hex : hex
    }

    return `#${toHex(r)}${toHex(g)}${toHex(b)}`
  }

  static generateGlassmorphismVariations(baseColor) {
    const rgb = this.hexToRgb(baseColor)
    // eslint-disable-next-line no-unused-vars
    const brightness = (rgb.r + rgb.g + rgb.b) / 3
    
    return {
      light: {
        background: `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.1)`,
        border: `rgba(255, 255, 255, 0.3)`,
        shadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
      },
      medium: {
        background: `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.2)`,
        border: `rgba(255, 255, 255, 0.25)`,
        shadow: '0 8px 32px rgba(0, 0, 0, 0.15)'
      },
      dark: {
        background: `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.3)`,
        border: `rgba(255, 255, 255, 0.2)`,
        shadow: '0 8px 32px rgba(0, 0, 0, 0.2)'
      }
    }
  }
}

export default GlassStyleUtils