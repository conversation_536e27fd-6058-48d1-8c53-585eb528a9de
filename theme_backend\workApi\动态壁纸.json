{"4": {"inputs": {"clip_name": "clip_vision_h.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "加载CLIP视觉"}}, "5": {"inputs": {"strength_1": 1, "strength_2": 1, "crop": "center", "combine_embeds": "average", "force_offload": true, "tiles": 0, "ratio": 0.20000000000000004, "clip_vision": ["4", 0], "image_1": ["11", 0]}, "class_type": "WanVideoClipVisionEncode", "_meta": {"title": "WanVideo ClipVision Encode"}}, "6": {"inputs": {"weight": 2, "start_percent": 0, "end_percent": 1}, "class_type": "WanVideoEnhanceAVideo", "_meta": {"title": "WanVideo Enhance-A-Video"}}, "7": {"inputs": {"lora": "wan/lightx2v_I2V_14B_480p_cfg_step_distill_rank128_bf16.safetensors", "strength": 1.0000000000000002, "low_mem_load": false, "merge_loras": true}, "class_type": "WanVideoLoraSelect", "_meta": {"title": "WanVideo Lora Select"}}, "9": {"inputs": {"model_name": "umt5-xxl-enc-bf16.safetensors", "precision": "bf16", "load_device": "offload_device", "quantization": "fp8_e4m3fn"}, "class_type": "LoadWanVideoT5TextEncoder", "_meta": {"title": "WanVideo T5 Text Encoder Loader"}}, "11": {"inputs": {"image": "ComfyUI_kolors_lora_00001_.png"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "12": {"inputs": {"model": "wan/aniWan2114BFp8E4m3fn_i2v480pNew.safetensors", "base_precision": "bf16", "quantization": "fp8_e4m3fn", "load_device": "offload_device", "attention_mode": "radial_sage_attention", "lora": ["7", 0]}, "class_type": "WanVideoModelLoader", "_meta": {"title": "WanVideo Model Loader"}}, "13": {"inputs": {"offload_percent": 1}, "class_type": "WanVideoVRAMManagement", "_meta": {"title": "WanVideo VRAM Management"}}, "14": {"inputs": {"model_name": "wan_2.1_vae.safetensors", "precision": "bf16"}, "class_type": "WanVideoVAELoader", "_meta": {"title": "WanVideo VAE Loader"}}, "15": {"inputs": {"value": 768}, "class_type": "JWInteger", "_meta": {"title": "Integer"}}, "16": {"inputs": {"width": ["21", 3], "height": ["21", 4], "num_frames": ["23", 0], "noise_aug_strength": 0.030000000000000006, "start_latent_strength": 1, "end_latent_strength": 1, "force_offload": true, "fun_or_fl2v_model": false, "tiled_vae": false, "vae": ["14", 0], "clip_embeds": ["5", 0], "start_image": ["21", 0]}, "class_type": "WanVideoImageToVideoEncode", "_meta": {"title": "WanVideo ImageToVideo Encode"}}, "17": {"inputs": {"enable_vae_tiling": false, "tile_x": 272, "tile_y": 272, "tile_stride_x": 144, "tile_stride_y": 128, "normalization": "default", "vae": ["14", 0], "samples": ["19", 0]}, "class_type": "WanVideoDecode", "_meta": {"title": "WanVideo Decode"}}, "18": {"inputs": {"positive_prompt": ["32", 0], "negative_prompt": "Overexposure, static, blurred details, subtitles, paintings, pictures, still, overall gray, worst quality, low quality, JPEG compression residue, ugly, mutilated, redundant fingers, poorly painted hands, poorly painted faces, deformed, disfigured, deformed limbs, fused fingers, cluttered background, three legs, a lot of people in the background, upside down", "force_offload": false, "use_disk_cache": {"__value__": [false, true]}, "device": "gpu", "speak_and_recognation": {"__value__": [false, true]}, "t5": ["9", 0]}, "class_type": "WanVideoTextEncode", "_meta": {"title": "WanVideo TextEncode"}}, "19": {"inputs": {"steps": 4, "cfg": 1.0000000000000002, "shift": 5.000000000000001, "seed": 326925991348817, "force_offload": false, "scheduler": "flowmatch_distill", "riflex_freq_index": 0, "denoise_strength": 1, "batched_cfg": "", "rope_function": "comfy", "start_step": 0, "end_step": -1, "model": ["28", 0], "image_embeds": ["16", 0], "text_embeds": ["18", 0], "feta_args": ["6", 0]}, "class_type": "WanVideoSampler", "_meta": {"title": "WanVide<PERSON>"}}, "20": {"inputs": {"frame_rate": 16, "loop_count": 1, "filename_prefix": "WanVideoWrapper_I2V", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "images": ["33", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "21": {"inputs": {"aspect_ratio": "original", "proportional_width": 1, "proportional_height": 1, "fit": "letterbox", "method": "lanc<PERSON>s", "round_to_multiple": "128", "scale_to_side": "longest", "scale_to_length": ["15", 0], "background_color": "#000000", "image": ["34", 0]}, "class_type": "LayerUtility: ImageScaleByAspectRatio V2", "_meta": {"title": "LayerUtility: ImageScaleByAspectRatio V2"}}, "23": {"inputs": {"value": 81}, "class_type": "JWInteger", "_meta": {"title": "Integer"}}, "27": {"inputs": {"model": "glm-4v-flash", "user_prompt": "【任务总目标】\n接收用户提供的任意一张图片，输出一段高质量、单一完整的、用于生成循环动态壁纸的描述性中文提示词。\n【核心原则与自我约束】\n绝对禁止技术词汇： 最终输出的文本中，严禁出现任何形式的技术参数。包括但不限于：“5秒”、“循环”、“无缝”、“运镜”、“8K”等。\n强制使用描述性替代： 必须用文学性、描述性的语言来暗示循环和动态效果。例如，用“光影不知疲倦地流转”来暗示循环。\n严格控制字数： 最终生成的提示词总长度控制在80至150字之间，确保其简洁、有力。\n输出为单一文本块： 最终交付给您的必须是一个完整、连贯的段落，没有任何标题、列表或符号。\n【工作流程与生成步骤】\n第一步：阅读图片，深度理解图像内容，提炼核心意象（一句话概括）\n目标： 用一句话抓住画面的灵魂。\n操作：\n观察：[核心景物] + [环境元素] + [光影] + [整体氛围]\n组合成句： (例：一座静谧的雪山，在星空下静默矗立，月光洒在积雪上，泛着清冷的光辉。)\n第二步：捕捉场景的律动感（营造循环动态）\n目标： 选择场景中最具代表性的元素，赋予其周而复始的、永不枯竭的微小动态，营造出整个画面的生机和无缝循环感。\n操作：\n主要动态（选择1-2项）： (自然元素的持续变化) -> (例：天空中的云朵缓缓舒展又聚拢，仿佛在呼吸。远处的瀑布奔流不息，水汽蒸腾而上，又凝结成露珠滴落。)\n光影动态（营造氛围）： (光线的周期性变化) -> (例：阳光透过树叶的缝隙，在地面上投下的光斑不知疲倦地摇曳、变幻着形状。烛火规律地跳动，光晕随之柔和地收缩与扩张。)\n细节动态（增添生机）： (小元素的点缀性动作) -> (例：草叶上的露珠悄然滑落，又在叶尖重新凝结。花瓣随风轻颤，仿佛在低语。水中的涟漪一圈圈荡漾开去，又归于平静，然后再次泛起。)\n第三步：强调画面的静止与永恒感\n目标： 通过文字描述一个完全静止的“镜头”，让AI理解画面不需要任何移动，只关注内部元素的动态，从而加强循环效果。\n操作： 从以下意境中选择一种，并用自己的语言重新描述：\n[凝视意境] -> (描述：整个画面宛如一幅被施了魔法的油画，视角始终凝固定格，邀请观众静静欣赏其中永恒上演的微妙变化。)\n[时间静止意境] -> (描述：时间在此刻仿佛失去了意义，万物都在这个固定的画框内，进行着一场无声而永恒的循环展演。)\n[沉浸意境] -> (描述：视角沉浸其中，一动不动，所有的注意力都被画面中周而复始的律动所吸引，仿佛可以永远这样看下去。)\n第四步：整合与润色（生成最终Prompt）\n目标： 将以上三步提炼的句子无缝融合成一个完整、连贯的段落，并进行文学性润色，确保符合所有约束条件。\n【模板应用示例】\n假设用户提供了一张“夜晚森林中的发光蘑菇”图片。\n第一步（核心意象）： 静谧的深夜森林中，一片发光的蘑菇散发着柔和的荧光。\n第二步（场景律动）：\n主要动态：蘑菇的光芒有节奏地明暗闪烁，如同森林的心跳。\n细节动态：空气中漂浮着点点光尘，它们缓缓升腾，又悄然落下，周而复始。\n第三步（静止永恒）： 视角始终固定，静静地凝视着这片奇妙的景象。\n第四步（最终Prompt）：\n在一片静谧的深夜森林里，发光的蘑菇群散发着柔和而神秘的荧光，它们的光芒如同森林沉睡时的心跳，进行着有节奏的明暗闪烁。空气中，无数微小的光尘缓缓升腾，盘旋后又悄然落下，周而复始，永不停歇。整个画面宛如一幅被施了魔法的油画，视角始终凝固定格，邀请观众静静欣赏其中永恒上演的微妙变化。", "speak_and_recognation": {"__value__": [false, true]}, "image": ["11", 0]}, "class_type": "LayerUtility: ZhipuGLM4V", "_meta": {"title": "图层工具：ZhipuGLM4V（高级）"}}, "28": {"inputs": {"dense_attention_mode": "sparse_sage_attention", "dense_blocks": 1, "dense_vace_blocks": 1, "dense_timesteps": 10, "decay_factor": 0.2, "block_size": 128, "model": ["12", 0]}, "class_type": "WanVideoSetRadialAttention", "_meta": {"title": "WanVideo Set Radial Attention"}}, "32": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": "镜头固定，镜头固定，没有运镜。场景没有移动，", "text_b": ["27", 0], "text_c": "", "speak_and_recognation": {"__value__": [false, true]}, "result": "镜头固定, 在这片宁静的竹林深处，一只憨态可掬的大熊猫正悠然自得地享用着一根竹子。阳光透过翠绿的竹叶洒在大熊猫身上，形成斑驳的光影。微风拂过，竹叶轻轻摇曳，仿佛在为这位美食家伴奏。大熊猫的眼睛明亮而有神，嘴角微微上扬，露出满足的笑容。它似乎完全沉浸在眼前的美味之中，忘记了时间的流逝。在这个充满生机的世界里，大熊猫与大自然和谐共处，构成了一幅美丽的画卷。"}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "33": {"inputs": {"upscale_method": "lanc<PERSON>s", "width": 1920, "height": 1080, "crop": "center", "image": ["17", 0]}, "class_type": "ImageScale", "_meta": {"title": "缩放图像"}}, "34": {"inputs": {"size": 1280, "mode": true, "images": ["11", 0]}, "class_type": "easy imageScaleDownToSize", "_meta": {"title": "图像缩小（按边）"}}}