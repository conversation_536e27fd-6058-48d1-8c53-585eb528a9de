{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, vModelText as _vModelText, withKeys as _withKeys, withDirectives as _withDirectives, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  key: 0,\n  class: \"navigation-status\"\n};\nconst _hoisted_2 = {\n  class: \"status-header\"\n};\nconst _hoisted_3 = {\n  class: \"destination-info\"\n};\nconst _hoisted_4 = {\n  class: \"destination-name\"\n};\nconst _hoisted_5 = {\n  class: \"destination-address\"\n};\nconst _hoisted_6 = {\n  class: \"route-summary\"\n};\nconst _hoisted_7 = {\n  class: \"summary-item\"\n};\nconst _hoisted_8 = {\n  class: \"summary-value\"\n};\nconst _hoisted_9 = {\n  class: \"summary-item\"\n};\nconst _hoisted_10 = {\n  class: \"summary-value\"\n};\nconst _hoisted_11 = {\n  class: \"summary-item\"\n};\nconst _hoisted_12 = {\n  class: \"summary-value\"\n};\nconst _hoisted_13 = {\n  class: \"summary-item\"\n};\nconst _hoisted_14 = {\n  class: \"summary-value\"\n};\nconst _hoisted_15 = {\n  key: 1,\n  class: \"route-preview\"\n};\nconst _hoisted_16 = {\n  class: \"preview-header\"\n};\nconst _hoisted_17 = {\n  key: 0,\n  class: \"map-view\"\n};\nconst _hoisted_18 = {\n  class: \"map-container\"\n};\nconst _hoisted_19 = {\n  class: \"map-placeholder\"\n};\nconst _hoisted_20 = {\n  class: \"end-point\"\n};\nconst _hoisted_21 = {\n  class: \"directions-list\"\n};\nconst _hoisted_22 = {\n  class: \"step-icon\"\n};\nconst _hoisted_23 = {\n  class: \"step-content\"\n};\nconst _hoisted_24 = {\n  class: \"step-instruction\"\n};\nconst _hoisted_25 = {\n  class: \"step-distance\"\n};\nconst _hoisted_26 = {\n  key: 0,\n  class: \"step-status\"\n};\nconst _hoisted_27 = {\n  key: 2,\n  class: \"traffic-info\"\n};\nconst _hoisted_28 = {\n  class: \"traffic-alerts\"\n};\nconst _hoisted_29 = {\n  class: \"alert-icon\"\n};\nconst _hoisted_30 = {\n  class: \"alert-content\"\n};\nconst _hoisted_31 = {\n  class: \"alert-title\"\n};\nconst _hoisted_32 = {\n  class: \"alert-description\"\n};\nconst _hoisted_33 = {\n  class: \"alert-impact\"\n};\nconst _hoisted_34 = {\n  key: 0,\n  class: \"alternative-routes\"\n};\nconst _hoisted_35 = [\"onClick\"];\nconst _hoisted_36 = {\n  class: \"route-info\"\n};\nconst _hoisted_37 = {\n  class: \"route-name\"\n};\nconst _hoisted_38 = {\n  class: \"route-details\"\n};\nconst _hoisted_39 = {\n  class: \"route-time\"\n};\nconst _hoisted_40 = {\n  class: \"route-distance\"\n};\nconst _hoisted_41 = {\n  key: 0,\n  class: \"route-savings\"\n};\nconst _hoisted_42 = {\n  class: \"savings-text\"\n};\nconst _hoisted_43 = {\n  key: 3,\n  class: \"quick-actions\"\n};\nconst _hoisted_44 = [\"onClick\", \"disabled\"];\nconst _hoisted_45 = {\n  key: 4,\n  class: \"ai-navigation-suggestions\"\n};\nconst _hoisted_46 = {\n  class: \"suggestions-list\"\n};\nconst _hoisted_47 = {\n  class: \"suggestion-content\"\n};\nconst _hoisted_48 = {\n  class: \"suggestion-text\"\n};\nconst _hoisted_49 = {\n  class: \"suggestion-meta\"\n};\nconst _hoisted_50 = {\n  class: \"time-saved\"\n};\nconst _hoisted_51 = {\n  class: \"confidence\"\n};\nconst _hoisted_52 = [\"onClick\", \"disabled\"];\nconst _hoisted_53 = {\n  key: 5,\n  class: \"no-navigation\"\n};\nconst _hoisted_54 = {\n  class: \"destination-input\"\n};\nconst _hoisted_55 = [\"disabled\"];\nconst _hoisted_56 = {\n  key: 0,\n  class: \"recent-destinations\"\n};\nconst _hoisted_57 = {\n  class: \"recent-list\"\n};\nconst _hoisted_58 = [\"onClick\"];\nconst _hoisted_59 = {\n  class: \"recent-icon\"\n};\nconst _hoisted_60 = {\n  class: \"recent-info\"\n};\nconst _hoisted_61 = {\n  class: \"recent-name\"\n};\nconst _hoisted_62 = {\n  class: \"recent-address\"\n};\nconst _hoisted_63 = {\n  key: 6,\n  class: \"loading-overlay\"\n};\nconst _hoisted_64 = {\n  class: \"loading-text\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_BaseCard = _resolveComponent(\"BaseCard\");\n  return _openBlock(), _createBlock(_component_BaseCard, {\n    \"card-type\": 'navigation',\n    size: $props.size,\n    position: $props.position,\n    theme: $props.theme,\n    \"theme-colors\": $props.themeColors,\n    \"show-header\": true,\n    \"show-footer\": false,\n    title: $setup.cardTitle,\n    icon: 'fas fa-route',\n    class: \"navigation-card\"\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"navigation-container\", [`size-${$props.size}`, `mode-${$props.displayMode}`]])\n    }, [_createCommentVNode(\" 当前导航状态 \"), $setup.currentNavigation ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"h3\", _hoisted_4, _toDisplayString($setup.currentNavigation.destination.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_5, _toDisplayString($setup.currentNavigation.destination.address), 1 /* TEXT */)]), _createElementVNode(\"div\", {\n      class: _normalizeClass([\"navigation-mode\", $setup.currentNavigation.mode])\n    }, [_createElementVNode(\"i\", {\n      class: _normalizeClass($setup.getModeIcon($setup.currentNavigation.mode))\n    }, null, 2 /* CLASS */), _createElementVNode(\"span\", null, _toDisplayString($setup.getModeText($setup.currentNavigation.mode)), 1 /* TEXT */)], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, _toDisplayString($setup.currentNavigation.eta), 1 /* TEXT */), _cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n      class: \"summary-label\"\n    }, \"预计到达\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, _toDisplayString($setup.currentNavigation.distance), 1 /* TEXT */), _cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n      class: \"summary-label\"\n    }, \"剩余距离\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, _toDisplayString($setup.currentNavigation.duration), 1 /* TEXT */), _cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n      class: \"summary-label\"\n    }, \"剩余时间\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, _toDisplayString($setup.currentNavigation.traffic), 1 /* TEXT */), _cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n      class: \"summary-label\"\n    }, \"路况\", -1 /* CACHED */))])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 路线预览 \"), $props.showRoutePreview && $setup.currentNavigation ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, [_cache[8] || (_cache[8] = _createElementVNode(\"span\", null, \"路线预览\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n      onClick: _cache[0] || (_cache[0] = (...args) => $setup.toggleRouteView && $setup.toggleRouteView(...args)),\n      class: \"view-toggle-btn\"\n    }, [_createElementVNode(\"i\", {\n      class: _normalizeClass($setup.routeViewMode === 'map' ? 'fas fa-list' : 'fas fa-map')\n    }, null, 2 /* CLASS */)])]), _createCommentVNode(\" 地图视图 \"), $setup.routeViewMode === 'map' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createCommentVNode(\" 模拟地图显示 \"), _createElementVNode(\"div\", _hoisted_19, [_cache[10] || (_cache[10] = _createElementVNode(\"div\", {\n      class: \"route-line\"\n    }, null, -1 /* CACHED */)), _cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n      class: \"start-point\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-circle\"\n    }), _createElementVNode(\"span\", null, \"起点\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_20, [_cache[9] || (_cache[9] = _createElementVNode(\"i\", {\n      class: \"fas fa-map-marker-alt\"\n    }, null, -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($setup.currentNavigation.destination.name), 1 /* TEXT */)]), _cache[12] || (_cache[12] = _createElementVNode(\"div\", {\n      class: \"current-position\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-location-arrow\"\n    })], -1 /* CACHED */))])])])) : (_openBlock(), _createElementBlock(_Fragment, {\n      key: 1\n    }, [_createCommentVNode(\" 列表视图 \"), _createElementVNode(\"div\", _hoisted_21, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.currentNavigation.directions, (step, index) => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: index,\n        class: _normalizeClass(['direction-step', {\n          current: index === $setup.currentStepIndex\n        }])\n      }, [_createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"i\", {\n        class: _normalizeClass($setup.getDirectionIcon(step.action))\n      }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, _toDisplayString(step.instruction), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_25, _toDisplayString(step.distance), 1 /* TEXT */)]), index === $setup.currentStepIndex ? (_openBlock(), _createElementBlock(\"div\", _hoisted_26, [...(_cache[13] || (_cache[13] = [_createElementVNode(\"div\", {\n        class: \"status-indicator\"\n      }, null, -1 /* CACHED */)]))])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 交通信息 \"), $props.showTrafficInfo ? (_openBlock(), _createElementBlock(\"div\", _hoisted_27, [_cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n      class: \"traffic-header\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-traffic-light\"\n    }), _createElementVNode(\"span\", null, \"实时路况\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_28, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.trafficAlerts, alert => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: alert.id,\n        class: _normalizeClass(['traffic-alert', `severity-${alert.severity}`])\n      }, [_createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"i\", {\n        class: _normalizeClass($setup.getAlertIcon(alert.type))\n      }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_30, [_createElementVNode(\"div\", _hoisted_31, _toDisplayString(alert.title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_32, _toDisplayString(alert.description), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_33, \"影响时间: \" + _toDisplayString(alert.impact), 1 /* TEXT */)])], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))]), $setup.alternativeRoutes.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_34, [_cache[14] || (_cache[14] = _createElementVNode(\"div\", {\n      class: \"alternatives-header\"\n    }, \"备选路线\", -1 /* CACHED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.alternativeRoutes, route => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: route.id,\n        onClick: $event => $setup.selectAlternativeRoute(route),\n        class: \"alternative-route\"\n      }, [_createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"div\", _hoisted_37, _toDisplayString(route.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"span\", _hoisted_39, _toDisplayString(route.duration), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_40, _toDisplayString(route.distance), 1 /* TEXT */), _createElementVNode(\"span\", {\n        class: _normalizeClass(['route-traffic', route.trafficLevel])\n      }, _toDisplayString($setup.getTrafficText(route.trafficLevel)), 3 /* TEXT, CLASS */)])]), route.timeSaved ? (_openBlock(), _createElementBlock(\"div\", _hoisted_41, [_createElementVNode(\"span\", _hoisted_42, \"节省 \" + _toDisplayString(route.timeSaved), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)], 8 /* PROPS */, _hoisted_35);\n    }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 快速操作 \"), $props.showQuickActions ? (_openBlock(), _createElementBlock(\"div\", _hoisted_43, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.quickActions, action => {\n      return _openBlock(), _createElementBlock(\"button\", {\n        key: action.id,\n        onClick: $event => $setup.handleQuickAction(action),\n        class: _normalizeClass(['quick-action-btn', action.type]),\n        disabled: $setup.isProcessing\n      }, [_createElementVNode(\"i\", {\n        class: _normalizeClass(action.icon)\n      }, null, 2 /* CLASS */), _createElementVNode(\"span\", null, _toDisplayString(action.label), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_44);\n    }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" AI导航建议 \"), $setup.aiSuggestions.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_45, [_cache[17] || (_cache[17] = _createElementVNode(\"div\", {\n      class: \"suggestions-header\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-magic\"\n    }), _createElementVNode(\"span\", null, \"AI导航建议\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_46, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.aiSuggestions, suggestion => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: suggestion.id,\n        class: \"suggestion-item\"\n      }, [_createElementVNode(\"div\", _hoisted_47, [_createElementVNode(\"div\", _hoisted_48, _toDisplayString(suggestion.text), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_49, [_createElementVNode(\"span\", _hoisted_50, \"节省 \" + _toDisplayString(suggestion.timeSaved), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_51, \"可信度 \" + _toDisplayString(Math.round(suggestion.confidence * 100)) + \"%\", 1 /* TEXT */)])]), _createElementVNode(\"button\", {\n        onClick: $event => $setup.applySuggestion(suggestion),\n        class: \"apply-suggestion-btn\",\n        disabled: $setup.isProcessing\n      }, [...(_cache[16] || (_cache[16] = [_createElementVNode(\"i\", {\n        class: \"fas fa-check\"\n      }, null, -1 /* CACHED */)]))], 8 /* PROPS */, _hoisted_52)]);\n    }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 无导航状态 \"), !$setup.currentNavigation ? (_openBlock(), _createElementBlock(\"div\", _hoisted_53, [_cache[20] || (_cache[20] = _createElementVNode(\"div\", {\n      class: \"no-nav-icon\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-map-marked-alt\"\n    })], -1 /* CACHED */)), _cache[21] || (_cache[21] = _createElementVNode(\"div\", {\n      class: \"no-nav-text\"\n    }, [_createElementVNode(\"h3\", null, \"开始导航\"), _createElementVNode(\"p\", null, \"输入目的地开始您的旅程\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_54, [_withDirectives(_createElementVNode(\"input\", {\n      \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.destinationInput = $event),\n      onKeyup: _cache[2] || (_cache[2] = _withKeys((...args) => $setup.startNavigation && $setup.startNavigation(...args), [\"enter\"])),\n      placeholder: \"输入目的地...\",\n      class: \"destination-field\"\n    }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $setup.destinationInput]]), _createElementVNode(\"button\", {\n      onClick: _cache[3] || (_cache[3] = (...args) => $setup.startNavigation && $setup.startNavigation(...args)),\n      class: \"start-nav-btn\",\n      disabled: !$setup.destinationInput.trim()\n    }, _cache[18] || (_cache[18] = [_createElementVNode(\"i\", {\n      class: \"fas fa-search\"\n    }, null, -1 /* CACHED */)]), 8 /* PROPS */, _hoisted_55)]), $setup.recentDestinations.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_56, [_cache[19] || (_cache[19] = _createElementVNode(\"div\", {\n      class: \"recent-header\"\n    }, \"最近目的地\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_57, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.recentDestinations, destination => {\n      return _openBlock(), _createElementBlock(\"button\", {\n        key: destination.id,\n        onClick: $event => $setup.selectDestination(destination),\n        class: \"recent-item\"\n      }, [_createElementVNode(\"div\", _hoisted_59, [_createElementVNode(\"i\", {\n        class: _normalizeClass(destination.icon)\n      }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_60, [_createElementVNode(\"div\", _hoisted_61, _toDisplayString(destination.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_62, _toDisplayString(destination.address), 1 /* TEXT */)])], 8 /* PROPS */, _hoisted_58);\n    }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 加载状态 \"), $setup.isLoading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_63, [_cache[22] || (_cache[22] = _createElementVNode(\"div\", {\n      class: \"loading-spinner\"\n    }, null, -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_64, _toDisplayString($setup.loadingText), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"size\", \"position\", \"theme\", \"theme-colors\", \"title\"]);\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_component_BaseCard", "size", "$props", "position", "theme", "themeColors", "title", "$setup", "cardTitle", "icon", "_createElementVNode", "_normalizeClass", "displayMode", "_createCommentVNode", "currentNavigation", "_createElementBlock", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_toDisplayString", "destination", "name", "_hoisted_5", "address", "mode", "getModeIcon", "getModeText", "_hoisted_6", "_hoisted_7", "_hoisted_8", "eta", "_hoisted_9", "_hoisted_10", "distance", "_hoisted_11", "_hoisted_12", "duration", "_hoisted_13", "_hoisted_14", "traffic", "showRoutePreview", "_hoisted_15", "_hoisted_16", "onClick", "_cache", "args", "toggleRouteView", "routeViewMode", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_Fragment", "key", "_hoisted_21", "_renderList", "directions", "step", "index", "current", "currentStepIndex", "_hoisted_22", "getDirectionIcon", "action", "_hoisted_23", "_hoisted_24", "instruction", "_hoisted_25", "_hoisted_26", "showTrafficInfo", "_hoisted_27", "_hoisted_28", "trafficAlerts", "alert", "id", "severity", "_hoisted_29", "getAlertIcon", "type", "_hoisted_30", "_hoisted_31", "_hoisted_32", "description", "_hoisted_33", "impact", "alternativeRoutes", "length", "_hoisted_34", "route", "$event", "selectAlternativeRoute", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "trafficLevel", "getTrafficText", "timeSaved", "_hoisted_41", "_hoisted_42", "showQuickActions", "_hoisted_43", "quickActions", "handleQuickAction", "disabled", "isProcessing", "label", "aiSuggestions", "_hoisted_45", "_hoisted_46", "suggestion", "_hoisted_47", "_hoisted_48", "text", "_hoisted_49", "_hoisted_50", "_hoisted_51", "Math", "round", "confidence", "applySuggestion", "_hoisted_53", "_hoisted_54", "destinationInput", "onKeyup", "_with<PERSON><PERSON><PERSON>", "startNavigation", "placeholder", "trim", "recentDestinations", "_hoisted_56", "_hoisted_57", "selectDestination", "_hoisted_59", "_hoisted_60", "_hoisted_61", "_hoisted_62", "isLoading", "_hoisted_63", "_hoisted_64", "loadingText"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\NavigationCard.vue"], "sourcesContent": ["<template>\n  <BaseCard\n    :card-type=\"'navigation'\"\n    :size=\"size\"\n    :position=\"position\"\n    :theme=\"theme\"\n    :theme-colors=\"themeColors\"\n    :show-header=\"true\"\n    :show-footer=\"false\"\n    :title=\"cardTitle\"\n    :icon=\"'fas fa-route'\"\n    class=\"navigation-card\"\n  >\n    <div class=\"navigation-container\" :class=\"[`size-${size}`, `mode-${displayMode}`]\">\n      <!-- 当前导航状态 -->\n      <div class=\"navigation-status\" v-if=\"currentNavigation\">\n        <div class=\"status-header\">\n          <div class=\"destination-info\">\n            <h3 class=\"destination-name\">{{ currentNavigation.destination.name }}</h3>\n            <div class=\"destination-address\">{{ currentNavigation.destination.address }}</div>\n          </div>\n          <div class=\"navigation-mode\" :class=\"currentNavigation.mode\">\n            <i :class=\"getModeIcon(currentNavigation.mode)\"></i>\n            <span>{{ getModeText(currentNavigation.mode) }}</span>\n          </div>\n        </div>\n        \n        <div class=\"route-summary\">\n          <div class=\"summary-item\">\n            <div class=\"summary-value\">{{ currentNavigation.eta }}</div>\n            <div class=\"summary-label\">预计到达</div>\n          </div>\n          <div class=\"summary-item\">\n            <div class=\"summary-value\">{{ currentNavigation.distance }}</div>\n            <div class=\"summary-label\">剩余距离</div>\n          </div>\n          <div class=\"summary-item\">\n            <div class=\"summary-value\">{{ currentNavigation.duration }}</div>\n            <div class=\"summary-label\">剩余时间</div>\n          </div>\n          <div class=\"summary-item\">\n            <div class=\"summary-value\">{{ currentNavigation.traffic }}</div>\n            <div class=\"summary-label\">路况</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 路线预览 -->\n      <div class=\"route-preview\" v-if=\"showRoutePreview && currentNavigation\">\n        <div class=\"preview-header\">\n          <span>路线预览</span>\n          <button @click=\"toggleRouteView\" class=\"view-toggle-btn\">\n            <i :class=\"routeViewMode === 'map' ? 'fas fa-list' : 'fas fa-map'\"></i>\n          </button>\n        </div>\n        \n        <!-- 地图视图 -->\n        <div v-if=\"routeViewMode === 'map'\" class=\"map-view\">\n          <div class=\"map-container\">\n            <!-- 模拟地图显示 -->\n            <div class=\"map-placeholder\">\n              <div class=\"route-line\"></div>\n              <div class=\"start-point\">\n                <i class=\"fas fa-circle\"></i>\n                <span>起点</span>\n              </div>\n              <div class=\"end-point\">\n                <i class=\"fas fa-map-marker-alt\"></i>\n                <span>{{ currentNavigation.destination.name }}</span>\n              </div>\n              <div class=\"current-position\">\n                <i class=\"fas fa-location-arrow\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 列表视图 -->\n        <div v-else class=\"directions-list\">\n          <div \n            v-for=\"(step, index) in currentNavigation.directions\" \n            :key=\"index\"\n            :class=\"['direction-step', { current: index === currentStepIndex }]\"\n          >\n            <div class=\"step-icon\">\n              <i :class=\"getDirectionIcon(step.action)\"></i>\n            </div>\n            <div class=\"step-content\">\n              <div class=\"step-instruction\">{{ step.instruction }}</div>\n              <div class=\"step-distance\">{{ step.distance }}</div>\n            </div>\n            <div class=\"step-status\" v-if=\"index === currentStepIndex\">\n              <div class=\"status-indicator\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 交通信息 -->\n      <div class=\"traffic-info\" v-if=\"showTrafficInfo\">\n        <div class=\"traffic-header\">\n          <i class=\"fas fa-traffic-light\"></i>\n          <span>实时路况</span>\n        </div>\n        \n        <div class=\"traffic-alerts\">\n          <div \n            v-for=\"alert in trafficAlerts\" \n            :key=\"alert.id\"\n            :class=\"['traffic-alert', `severity-${alert.severity}`]\"\n          >\n            <div class=\"alert-icon\">\n              <i :class=\"getAlertIcon(alert.type)\"></i>\n            </div>\n            <div class=\"alert-content\">\n              <div class=\"alert-title\">{{ alert.title }}</div>\n              <div class=\"alert-description\">{{ alert.description }}</div>\n              <div class=\"alert-impact\">影响时间: {{ alert.impact }}</div>\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"alternative-routes\" v-if=\"alternativeRoutes.length > 0\">\n          <div class=\"alternatives-header\">备选路线</div>\n          <div \n            v-for=\"route in alternativeRoutes\" \n            :key=\"route.id\"\n            @click=\"selectAlternativeRoute(route)\"\n            class=\"alternative-route\"\n          >\n            <div class=\"route-info\">\n              <div class=\"route-name\">{{ route.name }}</div>\n              <div class=\"route-details\">\n                <span class=\"route-time\">{{ route.duration }}</span>\n                <span class=\"route-distance\">{{ route.distance }}</span>\n                <span :class=\"['route-traffic', route.trafficLevel]\">\n                  {{ getTrafficText(route.trafficLevel) }}\n                </span>\n              </div>\n            </div>\n            <div class=\"route-savings\" v-if=\"route.timeSaved\">\n              <span class=\"savings-text\">节省 {{ route.timeSaved }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 快速操作 -->\n      <div class=\"quick-actions\" v-if=\"showQuickActions\">\n        <button \n          v-for=\"action in quickActions\" \n          :key=\"action.id\"\n          @click=\"handleQuickAction(action)\"\n          :class=\"['quick-action-btn', action.type]\"\n          :disabled=\"isProcessing\"\n        >\n          <i :class=\"action.icon\"></i>\n          <span>{{ action.label }}</span>\n        </button>\n      </div>\n\n      <!-- AI导航建议 -->\n      <div class=\"ai-navigation-suggestions\" v-if=\"aiSuggestions.length > 0\">\n        <div class=\"suggestions-header\">\n          <i class=\"fas fa-magic\"></i>\n          <span>AI导航建议</span>\n        </div>\n        <div class=\"suggestions-list\">\n          <div\n            v-for=\"suggestion in aiSuggestions\"\n            :key=\"suggestion.id\"\n            class=\"suggestion-item\"\n          >\n            <div class=\"suggestion-content\">\n              <div class=\"suggestion-text\">{{ suggestion.text }}</div>\n              <div class=\"suggestion-meta\">\n                <span class=\"time-saved\">节省 {{ suggestion.timeSaved }}</span>\n                <span class=\"confidence\">可信度 {{ Math.round(suggestion.confidence * 100) }}%</span>\n              </div>\n            </div>\n            <button \n              @click=\"applySuggestion(suggestion)\"\n              class=\"apply-suggestion-btn\"\n              :disabled=\"isProcessing\"\n            >\n              <i class=\"fas fa-check\"></i>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 无导航状态 -->\n      <div v-if=\"!currentNavigation\" class=\"no-navigation\">\n        <div class=\"no-nav-icon\">\n          <i class=\"fas fa-map-marked-alt\"></i>\n        </div>\n        <div class=\"no-nav-text\">\n          <h3>开始导航</h3>\n          <p>输入目的地开始您的旅程</p>\n        </div>\n        <div class=\"destination-input\">\n          <input \n            v-model=\"destinationInput\"\n            @keyup.enter=\"startNavigation\"\n            placeholder=\"输入目的地...\"\n            class=\"destination-field\"\n          >\n          <button @click=\"startNavigation\" class=\"start-nav-btn\" :disabled=\"!destinationInput.trim()\">\n            <i class=\"fas fa-search\"></i>\n          </button>\n        </div>\n        \n        <div class=\"recent-destinations\" v-if=\"recentDestinations.length > 0\">\n          <div class=\"recent-header\">最近目的地</div>\n          <div class=\"recent-list\">\n            <button\n              v-for=\"destination in recentDestinations\"\n              :key=\"destination.id\"\n              @click=\"selectDestination(destination)\"\n              class=\"recent-item\"\n            >\n              <div class=\"recent-icon\">\n                <i :class=\"destination.icon\"></i>\n              </div>\n              <div class=\"recent-info\">\n                <div class=\"recent-name\">{{ destination.name }}</div>\n                <div class=\"recent-address\">{{ destination.address }}</div>\n              </div>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 加载状态 -->\n      <div v-if=\"isLoading\" class=\"loading-overlay\">\n        <div class=\"loading-spinner\"></div>\n        <div class=\"loading-text\">{{ loadingText }}</div>\n      </div>\n    </div>\n  </BaseCard>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, onUnmounted } from 'vue'\nimport BaseCard from '@/components/cards/BaseCard.vue'\nimport mockDataService from '@/services/MockDataService.js'\n\nexport default {\n  name: 'NavigationCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    size: {\n      type: String,\n      default: 'large',\n      validator: (value) => ['small', 'medium', 'large'].includes(value)\n    },\n    position: {\n      type: Object,\n      default: () => ({ x: 0, y: 0 })\n    },\n    theme: {\n      type: String,\n      default: 'glassmorphism'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({})\n    },\n    displayMode: {\n      type: String,\n      default: 'full',\n      validator: (value) => ['compact', 'standard', 'full'].includes(value)\n    },\n    showRoutePreview: {\n      type: Boolean,\n      default: true\n    },\n    showTrafficInfo: {\n      type: Boolean,\n      default: true\n    },\n    showQuickActions: {\n      type: Boolean,\n      default: true\n    },\n    autoRefresh: {\n      type: Boolean,\n      default: true\n    },\n    refreshInterval: {\n      type: Number,\n      default: 30000 // 30秒\n    }\n  },\n  emits: ['navigation-start', 'navigation-end', 'route-changed', 'suggestion-applied'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const isLoading = ref(false)\n    const isProcessing = ref(false)\n    const loadingText = ref('正在规划路线...')\n    const routeViewMode = ref('map')\n    const currentStepIndex = ref(0)\n    const destinationInput = ref('')\n    \n    const currentNavigation = ref(null)\n    const trafficAlerts = ref([])\n    const alternativeRoutes = ref([])\n    const aiSuggestions = ref([])\n    const recentDestinations = ref([])\n    \n    const quickActions = ref([\n      {\n        id: 1,\n        label: '重新规划',\n        icon: 'fas fa-redo',\n        type: 'primary',\n        action: 'replan'\n      },\n      {\n        id: 2,\n        label: '语音导航',\n        icon: 'fas fa-volume-up',\n        type: 'secondary',\n        action: 'voice'\n      },\n      {\n        id: 3,\n        label: '分享位置',\n        icon: 'fas fa-share',\n        type: 'secondary',\n        action: 'share'\n      },\n      {\n        id: 4,\n        label: '结束导航',\n        icon: 'fas fa-stop',\n        type: 'danger',\n        action: 'stop'\n      }\n    ])\n\n    // 计算属性\n    const cardTitle = computed(() => {\n      if (currentNavigation.value) {\n        return `导航中 - ${currentNavigation.value.destination.name}`\n      }\n      return '导航'\n    })\n\n    // 方法\n    const loadNavigationData = async () => {\n      try {\n        isLoading.value = true\n        loadingText.value = '正在获取导航信息...'\n        \n        const navigationData = await mockDataService.getNavigationData()\n        \n        currentNavigation.value = navigationData.currentNavigation || null\n        trafficAlerts.value = navigationData.trafficAlerts || []\n        alternativeRoutes.value = navigationData.alternativeRoutes || []\n        aiSuggestions.value = navigationData.aiSuggestions || []\n        recentDestinations.value = navigationData.recentDestinations || []\n        \n      } catch (error) {\n        console.error('Failed to load navigation data:', error)\n      } finally {\n        isLoading.value = false\n      }\n    }\n    \n    const getModeIcon = (mode) => {\n      const icons = {\n        driving: 'fas fa-car',\n        walking: 'fas fa-walking',\n        transit: 'fas fa-bus',\n        cycling: 'fas fa-bicycle'\n      }\n      return icons[mode] || 'fas fa-route'\n    }\n    \n    const getModeText = (mode) => {\n      const texts = {\n        driving: '驾车',\n        walking: '步行',\n        transit: '公交',\n        cycling: '骑行'\n      }\n      return texts[mode] || '导航'\n    }\n    \n    const getDirectionIcon = (action) => {\n      const icons = {\n        straight: 'fas fa-arrow-up',\n        left: 'fas fa-arrow-left',\n        right: 'fas fa-arrow-right',\n        uturn: 'fas fa-undo',\n        merge: 'fas fa-code-branch',\n        exit: 'fas fa-sign-out-alt'\n      }\n      return icons[action] || 'fas fa-arrow-up'\n    }\n    \n    const getAlertIcon = (type) => {\n      const icons = {\n        accident: 'fas fa-car-crash',\n        construction: 'fas fa-hard-hat',\n        traffic: 'fas fa-traffic-light',\n        weather: 'fas fa-cloud-rain',\n        closure: 'fas fa-ban'\n      }\n      return icons[type] || 'fas fa-exclamation-triangle'\n    }\n    \n    const getTrafficText = (level) => {\n      const texts = {\n        light: '畅通',\n        moderate: '缓慢',\n        heavy: '拥堵',\n        severe: '严重拥堵'\n      }\n      return texts[level] || '未知'\n    }\n    \n    const toggleRouteView = () => {\n      routeViewMode.value = routeViewMode.value === 'map' ? 'list' : 'map'\n    }\n    \n    const startNavigation = async () => {\n      if (!destinationInput.value.trim()) return\n      \n      try {\n        isLoading.value = true\n        loadingText.value = '正在规划路线...'\n        \n        const destination = {\n          name: destinationInput.value.trim(),\n          address: destinationInput.value.trim()\n        }\n        \n        const navigationResult = await mockDataService.startNavigation(destination)\n        \n        currentNavigation.value = navigationResult\n        destinationInput.value = ''\n        \n        emit('navigation-start', navigationResult)\n        \n      } catch (error) {\n        console.error('Failed to start navigation:', error)\n      } finally {\n        isLoading.value = false\n      }\n    }\n    \n    const selectDestination = async (destination) => {\n      try {\n        isLoading.value = true\n        loadingText.value = '正在规划路线...'\n        \n        const navigationResult = await mockDataService.startNavigation(destination)\n        \n        currentNavigation.value = navigationResult\n        \n        emit('navigation-start', navigationResult)\n        \n      } catch (error) {\n        console.error('Failed to start navigation:', error)\n      } finally {\n        isLoading.value = false\n      }\n    }\n    \n    const selectAlternativeRoute = async (route) => {\n      try {\n        isProcessing.value = true\n        \n        const updatedNavigation = await mockDataService.selectRoute(route.id)\n        \n        currentNavigation.value = updatedNavigation\n        \n        emit('route-changed', route)\n        \n        // 重新加载数据以获取新的备选路线\n        await loadNavigationData()\n        \n      } catch (error) {\n        console.error('Failed to select alternative route:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n    \n    const handleQuickAction = async (action) => {\n      switch (action.action) {\n        case 'replan':\n          await replanRoute()\n          break\n        case 'voice':\n          toggleVoiceNavigation()\n          break\n        case 'share':\n          shareLocation()\n          break\n        case 'stop':\n          await stopNavigation()\n          break\n      }\n    }\n    \n    const replanRoute = async () => {\n      if (!currentNavigation.value) return\n      \n      try {\n        isProcessing.value = true\n        loadingText.value = '正在重新规划路线...'\n        \n        const updatedNavigation = await mockDataService.replanRoute()\n        \n        currentNavigation.value = updatedNavigation\n        \n        await loadNavigationData()\n        \n      } catch (error) {\n        console.error('Failed to replan route:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n    \n    const toggleVoiceNavigation = () => {\n      if (currentNavigation.value) {\n        currentNavigation.value.voiceEnabled = !currentNavigation.value.voiceEnabled\n      }\n    }\n    \n    const shareLocation = () => {\n      // 模拟分享位置功能\n      console.log('Sharing current location...')\n    }\n    \n    const stopNavigation = async () => {\n      try {\n        isProcessing.value = true\n        \n        await mockDataService.stopNavigation()\n        \n        const stoppedNavigation = currentNavigation.value\n        currentNavigation.value = null\n        currentStepIndex.value = 0\n        \n        emit('navigation-end', stoppedNavigation)\n        \n      } catch (error) {\n        console.error('Failed to stop navigation:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n    \n    const applySuggestion = async (suggestion) => {\n      try {\n        isProcessing.value = true\n        \n        await mockDataService.applySuggestion(suggestion.id)\n        \n        // 从建议列表中移除\n        const index = aiSuggestions.value.findIndex(s => s.id === suggestion.id)\n        if (index > -1) {\n          aiSuggestions.value.splice(index, 1)\n        }\n        \n        emit('suggestion-applied', suggestion)\n        \n        // 重新加载导航数据\n        await loadNavigationData()\n        \n      } catch (error) {\n        console.error('Failed to apply suggestion:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n    \n    const updateNavigationProgress = () => {\n      if (currentNavigation.value && currentNavigation.value.directions) {\n        // 模拟导航进度更新\n        const totalSteps = currentNavigation.value.directions.length\n        if (currentStepIndex.value < totalSteps - 1) {\n          // 随机推进到下一步（实际应用中基于GPS位置）\n          if (Math.random() < 0.1) { // 10%概率推进\n            currentStepIndex.value++\n          }\n        }\n      }\n    }\n\n    // 生命周期\n    let refreshTimer = null\n    let progressTimer = null\n    \n    onMounted(async () => {\n      await mockDataService.initialize()\n      await loadNavigationData()\n      \n      // 设置自动刷新\n      if (props.autoRefresh) {\n        refreshTimer = setInterval(() => {\n          loadNavigationData()\n        }, props.refreshInterval)\n        \n        // 设置导航进度更新\n        progressTimer = setInterval(() => {\n          updateNavigationProgress()\n        }, 5000) // 每5秒检查一次进度\n      }\n    })\n\n    onUnmounted(() => {\n      if (refreshTimer) {\n        clearInterval(refreshTimer)\n      }\n      if (progressTimer) {\n        clearInterval(progressTimer)\n      }\n    })\n\n    return {\n      // 响应式数据\n      isLoading,\n      isProcessing,\n      loadingText,\n      routeViewMode,\n      currentStepIndex,\n      destinationInput,\n      currentNavigation,\n      trafficAlerts,\n      alternativeRoutes,\n      aiSuggestions,\n      recentDestinations,\n      quickActions,\n      \n      // 计算属性\n      cardTitle,\n      \n      // 方法\n      getModeIcon,\n      getModeText,\n      getDirectionIcon,\n      getAlertIcon,\n      getTrafficText,\n      toggleRouteView,\n      startNavigation,\n      selectDestination,\n      selectAlternativeRoute,\n      handleQuickAction,\n      applySuggestion\n    }\n  }\n}\n</script>\n\n<style scoped>\n.navigation-card {\n  height: 100%;\n}\n\n.navigation-container {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  padding: 16px;\n  position: relative;\n}\n\n/* 导航状态 */\n.navigation-status {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.status-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 16px;\n}\n\n.destination-info h3 {\n  margin: 0 0 4px 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.destination-address {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.navigation-mode {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 6px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 500;\n  background: rgba(74, 144, 226, 0.2);\n  color: #4a90e2;\n}\n\n.route-summary {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 12px;\n}\n\n.summary-item {\n  text-align: center;\n}\n\n.summary-value {\n  font-size: 16px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 4px;\n}\n\n.summary-label {\n  font-size: 11px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n/* 路线预览 */\n.route-preview {\n  background: rgba(0, 0, 0, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n  flex: 1;\n  min-height: 0;\n}\n\n.preview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.view-toggle-btn {\n  width: 28px;\n  height: 28px;\n  border: none;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0.2);\n  color: rgba(255, 255, 255, 0.7);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n}\n\n.view-toggle-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  color: rgba(255, 255, 255, 0.9);\n}\n\n/* 地图视图 */\n.map-view {\n  height: 200px;\n}\n\n.map-container {\n  width: 100%;\n  height: 100%;\n  border-radius: 8px;\n  overflow: hidden;\n  position: relative;\n}\n\n.map-placeholder {\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.route-line {\n  position: absolute;\n  top: 30%;\n  left: 20%;\n  right: 20%;\n  height: 3px;\n  background: linear-gradient(90deg, #4a90e2 0%, #7ed321 100%);\n  border-radius: 2px;\n  transform: rotate(-15deg);\n}\n\n.start-point,\n.end-point {\n  position: absolute;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 10px;\n  color: white;\n  background: rgba(0, 0, 0, 0.5);\n  padding: 4px 8px;\n  border-radius: 12px;\n}\n\n.start-point {\n  top: 20%;\n  left: 15%;\n  color: #4a90e2;\n}\n\n.end-point {\n  bottom: 20%;\n  right: 15%;\n  color: #7ed321;\n}\n\n.current-position {\n  position: absolute;\n  top: 50%;\n  left: 40%;\n  color: #ff6b6b;\n  font-size: 16px;\n  animation: pulse 2s infinite;\n}\n\n/* 方向列表 */\n.directions-list {\n  max-height: 200px;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.direction-step {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n  transition: all 0.3s ease;\n}\n\n.direction-step.current {\n  background: rgba(74, 144, 226, 0.2);\n  border-left: 3px solid #4a90e2;\n}\n\n.step-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.8);\n  flex-shrink: 0;\n}\n\n.direction-step.current .step-icon {\n  background: rgba(74, 144, 226, 0.3);\n  color: #4a90e2;\n}\n\n.step-content {\n  flex: 1;\n}\n\n.step-instruction {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n}\n\n.step-distance {\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.step-status {\n  width: 8px;\n  height: 8px;\n}\n\n.status-indicator {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  background: #4a90e2;\n  animation: pulse 1.5s infinite;\n}\n\n/* 交通信息 */\n.traffic-info {\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.traffic-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 12px;\n}\n\n.traffic-alerts {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  margin-bottom: 16px;\n}\n\n.traffic-alert {\n  display: flex;\n  align-items: flex-start;\n  gap: 8px;\n  padding: 8px;\n  border-radius: 8px;\n  border-left: 3px solid transparent;\n}\n\n.traffic-alert.severity-low {\n  background: rgba(16, 185, 129, 0.1);\n  border-left-color: #10b981;\n}\n\n.traffic-alert.severity-medium {\n  background: rgba(245, 158, 11, 0.1);\n  border-left-color: #f59e0b;\n}\n\n.traffic-alert.severity-high {\n  background: rgba(239, 68, 68, 0.1);\n  border-left-color: #ef4444;\n}\n\n.alert-icon {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  flex-shrink: 0;\n}\n\n.alert-content {\n  flex: 1;\n}\n\n.alert-title {\n  font-size: 12px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n}\n\n.alert-description {\n  font-size: 11px;\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 2px;\n}\n\n.alert-impact {\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.5);\n}\n\n/* 备选路线 */\n.alternative-routes {\n  margin-top: 12px;\n}\n\n.alternatives-header {\n  font-size: 12px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.8);\n  margin-bottom: 8px;\n}\n\n.alternative-route {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  margin-bottom: 4px;\n}\n\n.alternative-route:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateX(2px);\n}\n\n.route-info {\n  flex: 1;\n}\n\n.route-name {\n  font-size: 12px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n}\n\n.route-details {\n  display: flex;\n  gap: 8px;\n  font-size: 10px;\n}\n\n.route-time {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.route-distance {\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.route-traffic.light {\n  color: #10b981;\n}\n\n.route-traffic.moderate {\n  color: #f59e0b;\n}\n\n.route-traffic.heavy {\n  color: #ef4444;\n}\n\n.route-savings {\n  font-size: 10px;\n  color: #7ed321;\n}\n\n/* 快速操作 */\n.quick-actions {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 8px;\n}\n\n.quick-action-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 6px;\n  padding: 8px 12px;\n  border: none;\n  border-radius: 8px;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.quick-action-btn.primary {\n  background: rgba(74, 144, 226, 0.2);\n  color: #4a90e2;\n}\n\n.quick-action-btn.secondary {\n  background: rgba(255, 255, 255, 0.1);\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.quick-action-btn.danger {\n  background: rgba(239, 68, 68, 0.2);\n  color: #ef4444;\n}\n\n.quick-action-btn:hover {\n  transform: translateY(-1px);\n}\n\n.quick-action-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* AI建议 */\n.ai-navigation-suggestions {\n  background: rgba(126, 211, 33, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.suggestions-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  color: #7ed321;\n  margin-bottom: 12px;\n}\n\n.suggestions-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.suggestion-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n}\n\n.suggestion-content {\n  flex: 1;\n}\n\n.suggestion-text {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 4px;\n}\n\n.suggestion-meta {\n  display: flex;\n  gap: 12px;\n  font-size: 10px;\n}\n\n.time-saved {\n  color: #7ed321;\n}\n\n.confidence {\n  color: #4a90e2;\n}\n\n.apply-suggestion-btn {\n  width: 28px;\n  height: 28px;\n  border: none;\n  border-radius: 6px;\n  background: rgba(126, 211, 33, 0.3);\n  color: #7ed321;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.apply-suggestion-btn:hover {\n  background: rgba(126, 211, 33, 0.5);\n  transform: scale(1.05);\n}\n\n/* 无导航状态 */\n.no-navigation {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  padding: 32px 16px;\n  flex: 1;\n}\n\n.no-nav-icon {\n  font-size: 48px;\n  color: rgba(255, 255, 255, 0.3);\n  margin-bottom: 16px;\n}\n\n.no-nav-text h3 {\n  margin: 0 0 8px 0;\n  font-size: 18px;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.no-nav-text p {\n  margin: 0 0 24px 0;\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.destination-input {\n  display: flex;\n  gap: 8px;\n  width: 100%;\n  max-width: 300px;\n  margin-bottom: 24px;\n}\n\n.destination-field {\n  flex: 1;\n  padding: 12px 16px;\n  border: none;\n  border-radius: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  color: rgba(255, 255, 255, 0.9);\n  font-size: 14px;\n  outline: none;\n  transition: all 0.3s ease;\n}\n\n.destination-field::placeholder {\n  color: rgba(255, 255, 255, 0.5);\n}\n\n.destination-field:focus {\n  background: rgba(255, 255, 255, 0.15);\n  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.3);\n}\n\n.start-nav-btn {\n  padding: 12px;\n  border: none;\n  border-radius: 8px;\n  background: rgba(74, 144, 226, 0.3);\n  color: #4a90e2;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.start-nav-btn:hover:not(:disabled) {\n  background: rgba(74, 144, 226, 0.5);\n  transform: scale(1.05);\n}\n\n.start-nav-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* 最近目的地 */\n.recent-destinations {\n  width: 100%;\n  max-width: 300px;\n}\n\n.recent-header {\n  font-size: 12px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 8px;\n  text-align: left;\n}\n\n.recent-list {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.recent-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border: none;\n  border-radius: 8px;\n  color: rgba(255, 255, 255, 0.8);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-align: left;\n  width: 100%;\n}\n\n.recent-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateX(2px);\n}\n\n.recent-icon {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.recent-info {\n  flex: 1;\n}\n\n.recent-name {\n  font-size: 12px;\n  font-weight: 500;\n  margin-bottom: 2px;\n}\n\n.recent-address {\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.5);\n}\n\n/* 加载状态 */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.3);\n  backdrop-filter: blur(5px);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 12px;\n  border-radius: 12px;\n}\n\n.loading-spinner {\n  width: 32px;\n  height: 32px;\n  border: 3px solid rgba(255, 255, 255, 0.2);\n  border-top: 3px solid #4a90e2;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.loading-text {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* 动画 */\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n}\n\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* 响应式设计 */\n.size-small .navigation-container {\n  padding: 12px;\n  gap: 12px;\n}\n\n.size-small .route-summary {\n  grid-template-columns: repeat(2, 1fr);\n}\n\n.size-small .quick-actions {\n  grid-template-columns: 1fr;\n}\n\n.mode-compact .route-preview,\n.mode-compact .traffic-info,\n.mode-compact .ai-navigation-suggestions {\n  display: none;\n}\n\n.mode-compact .quick-actions {\n  grid-template-columns: repeat(4, 1fr);\n}\n</style>"], "mappings": ";;;EAeWA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAkB;;EACvBA,KAAK,EAAC;AAAkB;;EACvBA,KAAK,EAAC;AAAqB;;EAQ/BA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAe;;EAGvBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAe;;EAGvBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAe;;EAGvBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAe;;;EAO3BA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAgB;;;EAQSA,KAAK,EAAC;;;EACnCA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAiB;;EAMrBA,KAAK,EAAC;AAAW;;EAYhBA,KAAK,EAAC;AAAiB;;EAM1BA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAkB;;EACxBA,KAAK,EAAC;AAAe;;;EAEvBA,KAAK,EAAC;;;;EAQZA,KAAK,EAAC;;;EAMJA,KAAK,EAAC;AAAgB;;EAMlBA,KAAK,EAAC;AAAY;;EAGlBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAmB;;EACzBA,KAAK,EAAC;AAAc;;;EAK1BA,KAAK,EAAC;;;;EAQFA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAY;;EAClBA,KAAK,EAAC;AAAe;;EAClBA,KAAK,EAAC;AAAY;;EAClBA,KAAK,EAAC;AAAgB;;;EAM3BA,KAAK,EAAC;;;EACHA,KAAK,EAAC;AAAc;;;EAO7BA,KAAK,EAAC;;;;;EAcNA,KAAK,EAAC;;;EAKJA,KAAK,EAAC;AAAkB;;EAMpBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAiB;;EACvBA,KAAK,EAAC;AAAiB;;EACpBA,KAAK,EAAC;AAAY;;EAClBA,KAAK,EAAC;AAAY;;;;EAeHA,KAAK,EAAC;;;EAQ9BA,KAAK,EAAC;AAAmB;;;;EAYzBA,KAAK,EAAC;;;EAEJA,KAAK,EAAC;AAAa;;;EAOfA,KAAK,EAAC;AAAa;;EAGnBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAgB;;;EAQfA,KAAK,EAAC;;;EAErBA,KAAK,EAAC;AAAc;;;uBA3O/BC,YAAA,CA8OWC,mBAAA;IA7OR,WAAS,EAAE,YAAY;IACvBC,IAAI,EAAEC,MAAA,CAAAD,IAAI;IACVE,QAAQ,EAAED,MAAA,CAAAC,QAAQ;IAClBC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACZ,cAAY,EAAEF,MAAA,CAAAG,WAAW;IACzB,aAAW,EAAE,IAAI;IACjB,aAAW,EAAE,KAAK;IAClBC,KAAK,EAAEC,MAAA,CAAAC,SAAS;IAChBC,IAAI,EAAE,cAAc;IACrBX,KAAK,EAAC;;sBAEN,MAiOM,CAjONY,mBAAA,CAiOM;MAjODZ,KAAK,EAAAa,eAAA,EAAC,sBAAsB,WAAkBT,MAAA,CAAAD,IAAI,YAAYC,MAAA,CAAAU,WAAW;QAC5EC,mBAAA,YAAe,EACsBN,MAAA,CAAAO,iBAAiB,I,cAAtDC,mBAAA,CA8BM,OA9BNC,UA8BM,GA7BJN,mBAAA,CASM,OATNO,UASM,GARJP,mBAAA,CAGM,OAHNQ,UAGM,GAFJR,mBAAA,CAA0E,MAA1ES,UAA0E,EAAAC,gBAAA,CAA1Cb,MAAA,CAAAO,iBAAiB,CAACO,WAAW,CAACC,IAAI,kBAClEZ,mBAAA,CAAkF,OAAlFa,UAAkF,EAAAH,gBAAA,CAA9Cb,MAAA,CAAAO,iBAAiB,CAACO,WAAW,CAACG,OAAO,iB,GAE3Ed,mBAAA,CAGM;MAHDZ,KAAK,EAAAa,eAAA,EAAC,iBAAiB,EAASJ,MAAA,CAAAO,iBAAiB,CAACW,IAAI;QACzDf,mBAAA,CAAoD;MAAhDZ,KAAK,EAAAa,eAAA,CAAEJ,MAAA,CAAAmB,WAAW,CAACnB,MAAA,CAAAO,iBAAiB,CAACW,IAAI;6BAC7Cf,mBAAA,CAAsD,cAAAU,gBAAA,CAA7Cb,MAAA,CAAAoB,WAAW,CAACpB,MAAA,CAAAO,iBAAiB,CAACW,IAAI,kB,oBAI/Cf,mBAAA,CAiBM,OAjBNkB,UAiBM,GAhBJlB,mBAAA,CAGM,OAHNmB,UAGM,GAFJnB,mBAAA,CAA4D,OAA5DoB,UAA4D,EAAAV,gBAAA,CAA9Bb,MAAA,CAAAO,iBAAiB,CAACiB,GAAG,kB,0BACnDrB,mBAAA,CAAqC;MAAhCZ,KAAK,EAAC;IAAe,GAAC,MAAI,oB,GAEjCY,mBAAA,CAGM,OAHNsB,UAGM,GAFJtB,mBAAA,CAAiE,OAAjEuB,WAAiE,EAAAb,gBAAA,CAAnCb,MAAA,CAAAO,iBAAiB,CAACoB,QAAQ,kB,0BACxDxB,mBAAA,CAAqC;MAAhCZ,KAAK,EAAC;IAAe,GAAC,MAAI,oB,GAEjCY,mBAAA,CAGM,OAHNyB,WAGM,GAFJzB,mBAAA,CAAiE,OAAjE0B,WAAiE,EAAAhB,gBAAA,CAAnCb,MAAA,CAAAO,iBAAiB,CAACuB,QAAQ,kB,0BACxD3B,mBAAA,CAAqC;MAAhCZ,KAAK,EAAC;IAAe,GAAC,MAAI,oB,GAEjCY,mBAAA,CAGM,OAHN4B,WAGM,GAFJ5B,mBAAA,CAAgE,OAAhE6B,WAAgE,EAAAnB,gBAAA,CAAlCb,MAAA,CAAAO,iBAAiB,CAAC0B,OAAO,kB,0BACvD9B,mBAAA,CAAmC;MAA9BZ,KAAK,EAAC;IAAe,GAAC,IAAE,oB,4CAKnCe,mBAAA,UAAa,EACoBX,MAAA,CAAAuC,gBAAgB,IAAIlC,MAAA,CAAAO,iBAAiB,I,cAAtEC,mBAAA,CAgDM,OAhDN2B,WAgDM,GA/CJhC,mBAAA,CAKM,OALNiC,WAKM,G,0BAJJjC,mBAAA,CAAiB,cAAX,MAAI,qBACVA,mBAAA,CAES;MAFAkC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEvC,MAAA,CAAAwC,eAAA,IAAAxC,MAAA,CAAAwC,eAAA,IAAAD,IAAA,CAAe;MAAEhD,KAAK,EAAC;QACrCY,mBAAA,CAAuE;MAAnEZ,KAAK,EAAAa,eAAA,CAAEJ,MAAA,CAAAyC,aAAa;iCAI5BnC,mBAAA,UAAa,EACFN,MAAA,CAAAyC,aAAa,c,cAAxBjC,mBAAA,CAkBM,OAlBNkC,WAkBM,GAjBJvC,mBAAA,CAgBM,OAhBNwC,WAgBM,GAfJrC,mBAAA,YAAe,EACfH,mBAAA,CAaM,OAbNyC,WAaM,G,4BAZJzC,mBAAA,CAA8B;MAAzBZ,KAAK,EAAC;IAAY,4B,4BACvBY,mBAAA,CAGM;MAHDZ,KAAK,EAAC;IAAa,IACtBY,mBAAA,CAA6B;MAA1BZ,KAAK,EAAC;IAAe,IACxBY,mBAAA,CAAe,cAAT,IAAE,E,qBAEVA,mBAAA,CAGM,OAHN0C,WAGM,G,0BAFJ1C,mBAAA,CAAqC;MAAlCZ,KAAK,EAAC;IAAuB,4BAChCY,mBAAA,CAAqD,cAAAU,gBAAA,CAA5Cb,MAAA,CAAAO,iBAAiB,CAACO,WAAW,CAACC,IAAI,iB,+BAE7CZ,mBAAA,CAEM;MAFDZ,KAAK,EAAC;IAAkB,IAC3BY,mBAAA,CAAqC;MAAlCZ,KAAK,EAAC;IAAuB,G,4CAOxCiB,mBAAA,CAiBMsC,SAAA;MAAAC,GAAA;IAAA,IAlBNzC,mBAAA,UAAa,EACbH,mBAAA,CAiBM,OAjBN6C,WAiBM,I,kBAhBJxC,mBAAA,CAeMsC,SAAA,QAAAG,WAAA,CAdoBjD,MAAA,CAAAO,iBAAiB,CAAC2C,UAAU,GAA5CC,IAAI,EAAEC,KAAK;2BADrB5C,mBAAA,CAeM;QAbHuC,GAAG,EAAEK,KAAK;QACV7D,KAAK,EAAAa,eAAA;UAAAiD,OAAA,EAAgCD,KAAK,KAAKpD,MAAA,CAAAsD;QAAgB;UAEhEnD,mBAAA,CAEM,OAFNoD,WAEM,GADJpD,mBAAA,CAA8C;QAA1CZ,KAAK,EAAAa,eAAA,CAAEJ,MAAA,CAAAwD,gBAAgB,CAACL,IAAI,CAACM,MAAM;iCAEzCtD,mBAAA,CAGM,OAHNuD,WAGM,GAFJvD,mBAAA,CAA0D,OAA1DwD,WAA0D,EAAA9C,gBAAA,CAAzBsC,IAAI,CAACS,WAAW,kBACjDzD,mBAAA,CAAoD,OAApD0D,WAAoD,EAAAhD,gBAAA,CAAtBsC,IAAI,CAACxB,QAAQ,iB,GAEdyB,KAAK,KAAKpD,MAAA,CAAAsD,gBAAgB,I,cAAzD9C,mBAAA,CAEM,OAFNsD,WAEM,OAAAxB,MAAA,SAAAA,MAAA,QADJnC,mBAAA,CAAoC;QAA/BZ,KAAK,EAAC;MAAkB,0B;+HAMrCe,mBAAA,UAAa,EACmBX,MAAA,CAAAoE,eAAe,I,cAA/CvD,mBAAA,CA8CM,OA9CNwD,WA8CM,G,4BA7CJ7D,mBAAA,CAGM;MAHDZ,KAAK,EAAC;IAAgB,IACzBY,mBAAA,CAAoC;MAAjCZ,KAAK,EAAC;IAAsB,IAC/BY,mBAAA,CAAiB,cAAX,MAAI,E,qBAGZA,mBAAA,CAeM,OAfN8D,WAeM,I,kBAdJzD,mBAAA,CAaMsC,SAAA,QAAAG,WAAA,CAZYjD,MAAA,CAAAkE,aAAa,EAAtBC,KAAK;2BADd3D,mBAAA,CAaM;QAXHuC,GAAG,EAAEoB,KAAK,CAACC,EAAE;QACb7E,KAAK,EAAAa,eAAA,+BAAgC+D,KAAK,CAACE,QAAQ;UAEpDlE,mBAAA,CAEM,OAFNmE,WAEM,GADJnE,mBAAA,CAAyC;QAArCZ,KAAK,EAAAa,eAAA,CAAEJ,MAAA,CAAAuE,YAAY,CAACJ,KAAK,CAACK,IAAI;iCAEpCrE,mBAAA,CAIM,OAJNsE,WAIM,GAHJtE,mBAAA,CAAgD,OAAhDuE,WAAgD,EAAA7D,gBAAA,CAApBsD,KAAK,CAACpE,KAAK,kBACvCI,mBAAA,CAA4D,OAA5DwE,WAA4D,EAAA9D,gBAAA,CAA1BsD,KAAK,CAACS,WAAW,kBACnDzE,mBAAA,CAAwD,OAAxD0E,WAAwD,EAA9B,QAAM,GAAAhE,gBAAA,CAAGsD,KAAK,CAACW,MAAM,iB;sCAKf9E,MAAA,CAAA+E,iBAAiB,CAACC,MAAM,Q,cAA9DxE,mBAAA,CAsBM,OAtBNyE,WAsBM,G,4BArBJ9E,mBAAA,CAA2C;MAAtCZ,KAAK,EAAC;IAAqB,GAAC,MAAI,sB,kBACrCiB,mBAAA,CAmBMsC,SAAA,QAAAG,WAAA,CAlBYjD,MAAA,CAAA+E,iBAAiB,EAA1BG,KAAK;2BADd1E,mBAAA,CAmBM;QAjBHuC,GAAG,EAAEmC,KAAK,CAACd,EAAE;QACb/B,OAAK,EAAA8C,MAAA,IAAEnF,MAAA,CAAAoF,sBAAsB,CAACF,KAAK;QACpC3F,KAAK,EAAC;UAENY,mBAAA,CASM,OATNkF,WASM,GARJlF,mBAAA,CAA8C,OAA9CmF,WAA8C,EAAAzE,gBAAA,CAAnBqE,KAAK,CAACnE,IAAI,kBACrCZ,mBAAA,CAMM,OANNoF,WAMM,GALJpF,mBAAA,CAAoD,QAApDqF,WAAoD,EAAA3E,gBAAA,CAAxBqE,KAAK,CAACpD,QAAQ,kBAC1C3B,mBAAA,CAAwD,QAAxDsF,WAAwD,EAAA5E,gBAAA,CAAxBqE,KAAK,CAACvD,QAAQ,kBAC9CxB,mBAAA,CAEO;QAFAZ,KAAK,EAAAa,eAAA,mBAAoB8E,KAAK,CAACQ,YAAY;0BAC7C1F,MAAA,CAAA2F,cAAc,CAACT,KAAK,CAACQ,YAAY,yB,KAITR,KAAK,CAACU,SAAS,I,cAAhDpF,mBAAA,CAEM,OAFNqF,WAEM,GADJ1F,mBAAA,CAA0D,QAA1D2F,WAA0D,EAA/B,KAAG,GAAAjF,gBAAA,CAAGqE,KAAK,CAACU,SAAS,iB;kHAMxDtF,mBAAA,UAAa,EACoBX,MAAA,CAAAoG,gBAAgB,I,cAAjDvF,mBAAA,CAWM,OAXNwF,WAWM,I,kBAVJxF,mBAAA,CASSsC,SAAA,QAAAG,WAAA,CARUjD,MAAA,CAAAiG,YAAY,EAAtBxC,MAAM;2BADfjD,mBAAA,CASS;QAPNuC,GAAG,EAAEU,MAAM,CAACW,EAAE;QACd/B,OAAK,EAAA8C,MAAA,IAAEnF,MAAA,CAAAkG,iBAAiB,CAACzC,MAAM;QAC/BlE,KAAK,EAAAa,eAAA,sBAAuBqD,MAAM,CAACe,IAAI;QACvC2B,QAAQ,EAAEnG,MAAA,CAAAoG;UAEXjG,mBAAA,CAA4B;QAAxBZ,KAAK,EAAAa,eAAA,CAAEqD,MAAM,CAACvD,IAAI;+BACtBC,mBAAA,CAA+B,cAAAU,gBAAA,CAAtB4C,MAAM,CAAC4C,KAAK,iB;2EAIzB/F,mBAAA,YAAe,EAC8BN,MAAA,CAAAsG,aAAa,CAACtB,MAAM,Q,cAAjExE,mBAAA,CA2BM,OA3BN+F,WA2BM,G,4BA1BJpG,mBAAA,CAGM;MAHDZ,KAAK,EAAC;IAAoB,IAC7BY,mBAAA,CAA4B;MAAzBZ,KAAK,EAAC;IAAc,IACvBY,mBAAA,CAAmB,cAAb,QAAM,E,qBAEdA,mBAAA,CAqBM,OArBNqG,WAqBM,I,kBApBJhG,mBAAA,CAmBMsC,SAAA,QAAAG,WAAA,CAlBiBjD,MAAA,CAAAsG,aAAa,EAA3BG,UAAU;2BADnBjG,mBAAA,CAmBM;QAjBHuC,GAAG,EAAE0D,UAAU,CAACrC,EAAE;QACnB7E,KAAK,EAAC;UAENY,mBAAA,CAMM,OANNuG,WAMM,GALJvG,mBAAA,CAAwD,OAAxDwG,WAAwD,EAAA9F,gBAAA,CAAxB4F,UAAU,CAACG,IAAI,kBAC/CzG,mBAAA,CAGM,OAHN0G,WAGM,GAFJ1G,mBAAA,CAA6D,QAA7D2G,WAA6D,EAApC,KAAG,GAAAjG,gBAAA,CAAG4F,UAAU,CAACb,SAAS,kBACnDzF,mBAAA,CAAkF,QAAlF4G,WAAkF,EAAzD,MAAI,GAAAlG,gBAAA,CAAGmG,IAAI,CAACC,KAAK,CAACR,UAAU,CAACS,UAAU,WAAU,GAAC,gB,KAG/E/G,mBAAA,CAMS;QALNkC,OAAK,EAAA8C,MAAA,IAAEnF,MAAA,CAAAmH,eAAe,CAACV,UAAU;QAClClH,KAAK,EAAC,sBAAsB;QAC3B4G,QAAQ,EAAEnG,MAAA,CAAAoG;2CAEXjG,mBAAA,CAA4B;QAAzBZ,KAAK,EAAC;MAAc,0B;6EAM/Be,mBAAA,WAAc,E,CACFN,MAAA,CAAAO,iBAAiB,I,cAA7BC,mBAAA,CAuCM,OAvCN4G,WAuCM,G,4BAtCJjH,mBAAA,CAEM;MAFDZ,KAAK,EAAC;IAAa,IACtBY,mBAAA,CAAqC;MAAlCZ,KAAK,EAAC;IAAuB,G,iDAElCY,mBAAA,CAGM;MAHDZ,KAAK,EAAC;IAAa,IACtBY,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAkB,WAAf,aAAW,E,qBAEhBA,mBAAA,CAUM,OAVNkH,WAUM,G,gBATJlH,mBAAA,CAKC;iEAJUH,MAAA,CAAAsH,gBAAgB,GAAAnC,MAAA;MACxBoC,OAAK,EAAAjF,MAAA,QAAAA,MAAA,MAAAkF,SAAA,KAAAjF,IAAA,KAAQvC,MAAA,CAAAyH,eAAA,IAAAzH,MAAA,CAAAyH,eAAA,IAAAlF,IAAA,CAAe;MAC7BmF,WAAW,EAAC,UAAU;MACtBnI,KAAK,EAAC;mEAHGS,MAAA,CAAAsH,gBAAgB,E,GAK3BnH,mBAAA,CAES;MAFAkC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEvC,MAAA,CAAAyH,eAAA,IAAAzH,MAAA,CAAAyH,eAAA,IAAAlF,IAAA,CAAe;MAAEhD,KAAK,EAAC,eAAe;MAAE4G,QAAQ,GAAGnG,MAAA,CAAAsH,gBAAgB,CAACK,IAAI;oCACtFxH,mBAAA,CAA6B;MAA1BZ,KAAK,EAAC;IAAe,0B,kCAIWS,MAAA,CAAA4H,kBAAkB,CAAC5C,MAAM,Q,cAAhExE,mBAAA,CAkBM,OAlBNqH,WAkBM,G,4BAjBJ1H,mBAAA,CAAsC;MAAjCZ,KAAK,EAAC;IAAe,GAAC,OAAK,qBAChCY,mBAAA,CAeM,OAfN2H,WAeM,I,kBAdJtH,mBAAA,CAaSsC,SAAA,QAAAG,WAAA,CAZejD,MAAA,CAAA4H,kBAAkB,EAAjC9G,WAAW;2BADpBN,mBAAA,CAaS;QAXNuC,GAAG,EAAEjC,WAAW,CAACsD,EAAE;QACnB/B,OAAK,EAAA8C,MAAA,IAAEnF,MAAA,CAAA+H,iBAAiB,CAACjH,WAAW;QACrCvB,KAAK,EAAC;UAENY,mBAAA,CAEM,OAFN6H,WAEM,GADJ7H,mBAAA,CAAiC;QAA7BZ,KAAK,EAAAa,eAAA,CAAEU,WAAW,CAACZ,IAAI;iCAE7BC,mBAAA,CAGM,OAHN8H,WAGM,GAFJ9H,mBAAA,CAAqD,OAArD+H,WAAqD,EAAArH,gBAAA,CAAzBC,WAAW,CAACC,IAAI,kBAC5CZ,mBAAA,CAA2D,OAA3DgI,WAA2D,EAAAtH,gBAAA,CAA5BC,WAAW,CAACG,OAAO,iB;oHAO5DX,mBAAA,UAAa,EACFN,MAAA,CAAAoI,SAAS,I,cAApB5H,mBAAA,CAGM,OAHN6H,WAGM,G,4BAFJlI,mBAAA,CAAmC;MAA9BZ,KAAK,EAAC;IAAiB,4BAC5BY,mBAAA,CAAiD,OAAjDmI,WAAiD,EAAAzH,gBAAA,CAApBb,MAAA,CAAAuI,WAAW,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}