<template>
  <div 
    :class="['main-layout-container', `theme-${currentTheme}`, { 'debug-mode': debugMode }]"
    :style="containerStyles"
  >
    <!-- 背景层 -->
    <div class="background-layer">
      <slot name="background">
        <!-- 动态壁纸管理器 -->
        <DynamicWallpaperManager 
          v-if="showWallpaper"
          @wallpaper-loaded="handleWallpaperLoaded"
        />
      </slot>
    </div>

    <!-- 主网格系统 -->
    <GridSystem16x9 
      :layout-mode="currentLayout"
      :debug-mode="debugMode"
      :responsive="responsive"
      :animated="animated"
      @layout-changed="handleLayoutChanged"
      @grid-ready="handleGridReady"
    >
      <slot :layout-config="layoutConfig" :theme-config="themeConfig">
        <!-- 默认布局内容 -->
        <div class="default-layout">
          <h2>AI-HMI 主界面</h2>
          <p>当前布局: {{ currentLayout }}</p>
          <p>当前主题: {{ currentTheme }}</p>
        </div>
      </slot>
    </GridSystem16x9>

    <!-- 覆盖层 -->
    <div class="overlay-layer">
      <slot name="overlay">
        <!-- 场景指示器等覆盖内容 -->
      </slot>
    </div>

    <!-- 调试信息面板 -->
    <div v-if="debugMode" class="debug-panel">
      <div class="debug-info">
        <h4>布局调试信息</h4>
        <div class="debug-item">
          <span>当前布局:</span>
          <span>{{ currentLayout }}</span>
        </div>
        <div class="debug-item">
          <span>屏幕尺寸:</span>
          <span>{{ screenSize }}</span>
        </div>
        <div class="debug-item">
          <span>网格配置:</span>
          <span>{{ gridConfig.columns }}x{{ gridConfig.rows }}</span>
        </div>
        <div class="debug-item">
          <span>响应式:</span>
          <span>{{ responsive ? '启用' : '禁用' }}</span>
        </div>
      </div>
      
      <div class="debug-controls">
        <button @click="toggleLayout" class="debug-btn">切换布局</button>
        <button @click="toggleTheme" class="debug-btn">切换主题</button>
        <button @click="toggleResponsive" class="debug-btn">响应式</button>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, ref, onMounted } from 'vue'
import { useLayoutStore } from '@/store'
import GridSystem16x9 from './GridSystem16x9.vue'
import DynamicWallpaperManager from '../DynamicWallpaperManager.vue'

export default {
  name: 'MainLayoutContainer',
  components: {
    GridSystem16x9,
    DynamicWallpaperManager
  },
  props: {
    // 初始布局
    initialLayout: {
      type: String,
      default: 'default'
    },
    
    // 初始主题
    initialTheme: {
      type: String,
      default: 'dark'
    },
    
    // 调试模式
    debugMode: {
      type: Boolean,
      default: false
    },
    
    // 响应式配置
    responsive: {
      type: Boolean,
      default: true
    },
    
    // 动画配置
    animated: {
      type: Boolean,
      default: true
    },
    
    // 显示壁纸
    showWallpaper: {
      type: Boolean,
      default: true
    }
  },
  
  emits: ['layout-ready', 'layout-changed', 'theme-changed'],
  
  setup(props, { emit }) {
    const layoutStore = useLayoutStore()
    
    // 响应式状态
    const currentLayout = ref(props.initialLayout)
    const currentTheme = ref(props.initialTheme)
    const gridConfig = ref({ columns: 16, rows: 9 })
    
    // 计算属性
    const containerStyles = computed(() => ({
      '--current-theme': currentTheme.value,
      '--layout-mode': currentLayout.value
    }))
    
    const layoutConfig = computed(() => ({
      layout: currentLayout.value,
      theme: currentTheme.value,
      responsive: props.responsive,
      animated: props.animated
    }))
    
    const themeConfig = computed(() => ({
      name: currentTheme.value,
      // 可以根据主题返回不同的配置
      colors: getThemeColors(currentTheme.value)
    }))
    
    const screenSize = computed(() => layoutStore.screenSize)
    
    // 主题颜色配置
    const getThemeColors = (theme) => {
      const themes = {
        light: {
          primary: '#4a90e2',
          secondary: '#7ed321',
          background: 'rgba(255, 255, 255, 0.9)',
          text: '#333333'
        },
        dark: {
          primary: '#2c3e50',
          secondary: '#3498db',
          background: 'rgba(0, 0, 0, 0.8)',
          text: '#ffffff'
        },
        glass: {
          primary: '#4a90e2',
          secondary: '#7ed321',
          background: 'rgba(255, 255, 255, 0.1)',
          text: '#ffffff'
        }
      }
      return themes[theme] || themes.dark
    }
    
    // 事件处理
    const handleLayoutChanged = (layoutData) => {
      emit('layout-changed', layoutData)
    }
    
    const handleGridReady = (config) => {
      gridConfig.value = config
      emit('layout-ready', {
        layout: currentLayout.value,
        theme: currentTheme.value,
        gridConfig: config
      })
    }
    
    const handleWallpaperLoaded = (wallpaperData) => {
      console.log('壁纸已加载:', wallpaperData)
    }
    
    // 调试功能
    const toggleLayout = () => {
      const layouts = ['default', 'family', 'focus', 'entertainment', 'minimal']
      const currentIndex = layouts.indexOf(currentLayout.value)
      const nextIndex = (currentIndex + 1) % layouts.length
      currentLayout.value = layouts[nextIndex]
      layoutStore.switchLayout(currentLayout.value)
    }
    
    const toggleTheme = () => {
      const themes = ['dark', 'light', 'glass']
      const currentIndex = themes.indexOf(currentTheme.value)
      const nextIndex = (currentIndex + 1) % themes.length
      currentTheme.value = themes[nextIndex]
      emit('theme-changed', currentTheme.value)
    }
    
    const toggleResponsive = () => {
      // 这里可以实现响应式切换逻辑
      console.log('切换响应式模式')
    }
    
    // 初始化
    onMounted(() => {
      // 设置初始布局
      layoutStore.switchLayout(currentLayout.value)
    })
    
    return {
      currentLayout,
      currentTheme,
      gridConfig,
      containerStyles,
      layoutConfig,
      themeConfig,
      screenSize,
      handleLayoutChanged,
      handleGridReady,
      handleWallpaperLoaded,
      toggleLayout,
      toggleTheme,
      toggleResponsive
    }
  }
}
</script>

<style scoped>
.main-layout-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* 背景层 */
.background-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

/* 覆盖层 */
.overlay-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  pointer-events: none;
}

.overlay-layer > * {
  pointer-events: auto;
}

/* 主题样式 */
.theme-dark {
  color: #ffffff;
  background: rgba(0, 0, 0, 0.8);
}

.theme-light {
  color: #333333;
  background: rgba(255, 255, 255, 0.9);
}

.theme-glass {
  color: #ffffff;
  background: transparent;
}

/* 默认布局内容 */
.default-layout {
  grid-column: 1 / -1;
  grid-row: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 20px;
}

.default-layout h2 {
  margin: 0;
  font-size: 32px;
  font-weight: 300;
}

.default-layout p {
  margin: 0;
  font-size: 16px;
  opacity: 0.8;
}

/* 调试面板 */
.debug-panel {
  position: fixed;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 20px;
  color: white;
  font-size: 14px;
  z-index: 9999;
  min-width: 250px;
}

.debug-info h4 {
  margin: 0 0 15px 0;
  color: #4a90e2;
}

.debug-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.debug-item span:first-child {
  opacity: 0.7;
}

.debug-controls {
  margin-top: 15px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.debug-btn {
  padding: 6px 12px;
  background: #4a90e2;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.debug-btn:hover {
  background: #357abd;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .debug-panel {
    top: 10px;
    left: 10px;
    right: 10px;
    min-width: auto;
  }
  
  .debug-controls {
    justify-content: center;
  }
}
</style>
