/**
 * 模拟数据服务 - 为AI-HMI项目提供仿真数据
 * 专为模拟座舱显示设计，无需真实API集成
 */

class MockDataService {
  constructor() {
    this.isInitialized = false;
    this.currentScene = 'commute';
    this.currentUser = null;
    this.mockData = {
      navigation: {
        currentRoute: {
          destination: '公司',
          estimatedTime: '25分钟',
          distance: '12.5公里',
          traffic: 'moderate',
          routePoints: [
            { lat: 39.9042, lng: 116.4074, name: '家' },
            { lat: 39.9142, lng: 116.4174, name: '幼儿园' },
            { lat: 39.9242, lng: 116.4274, name: '公司' }
          ]
        },
        trafficStatus: {
          level: 'moderate',
          description: '路况一般，比平时拥堵',
          delay: '10分钟'
        }
      },
      music: {
        currentSong: {
          title: '日落大道',
          artist: 'AI推荐',
          album: '通勤放松歌单',
          cover: '/assets/music/sunset-road.jpg',
          duration: 240,
          currentTime: 45
        },
        playlist: [
          { title: '日落大道', artist: 'AI推荐', mood: 'relaxing' },
          { title: '晨光序曲', artist: 'AI生成', mood: 'energetic' },
          { title: '城市节拍', artist: 'AI混音', mood: 'focused' }
        ],
        isPlaying: true,
        aiRecommendation: {
          reason: '基于您的通勤时间和当前情绪推荐',
          mood: 'focused',
          scene: 'commute'
        }
      },
      schedule: {
        todayEvents: [
          {
            id: 1,
            title: '重要会议',
            time: '10:00',
            location: '会议室A',
            priority: 'high',
            status: 'upcoming',
            aiSuggestion: '建议提前5分钟到达，已为您准备会议资料摘要'
          },
          {
            id: 2,
            title: '项目评审',
            time: '14:30',
            location: '会议室B',
            priority: 'medium',
            status: 'scheduled'
          },
          {
            id: 3,
            title: '接毛毛放学',
            time: '17:00',
            location: '幼儿园',
            priority: 'high',
            status: 'scheduled'
          }
        ],
        aiOptimization: {
          suggestion: '直达公司，咖啡已预订',
          timeSaved: '8分钟',
          confidence: 0.95
        }
      },
      smartHome: {
        devices: [
          {
            id: 1,
            name: '客厅灯',
            type: 'light',
            status: 'on',
            icon: 'fas fa-lightbulb',
            brightness: 80
          },
          {
            id: 2,
            name: '空调',
            type: 'climate',
            status: 'off',
            icon: 'fas fa-snowflake',
            temperature: 24
          },
          {
            id: 3,
            name: '安防系统',
            type: 'security',
            status: 'armed',
            icon: 'fas fa-shield-alt'
          },
          {
            id: 4,
            name: '扫地机器人',
            type: 'cleaning',
            status: 'cleaning',
            icon: 'fas fa-robot',
            progress: 65
          }
        ],
        homeStatus: {
          temperature: 22,
          humidity: 45,
          airQuality: 'good',
          energyUsage: 'normal'
        }
      },
      orders: [
        {
          id: 1,
          type: 'coffee',
          vendor: '瑞幸咖啡',
          item: '美式咖啡 + 三明治',
          status: 'confirmed',
          pickupTime: '09:35',
          location: '公司楼下',
          estimatedReady: '5分钟',
          aiOptimized: true
        }
      ],
      vpa: {
        currentState: 'companion',
        mood: 'helpful',
        lastInteraction: Date.now() - 30000,
        conversationHistory: [
          {
            id: 1,
            type: 'vpa',
            content: 'Hello，主人和毛毛你好。按往常一样先送毛毛上学吧？',
            timestamp: Date.now() - 300000
          },
          {
            id: 2,
            type: 'user',
            content: '是的，今天路况怎么样？',
            timestamp: Date.now() - 280000
          },
          {
            id: 3,
            type: 'vpa',
            content: '今天路况比平时拥堵一些，预计会多花10分钟。我已经为您优化了路线。',
            timestamp: Date.now() - 260000
          }
        ],
        quickActions: [
          { id: 1, label: '播放音乐', action: 'playMusic' },
          { id: 2, label: '查看日程', action: 'showSchedule' },
          { id: 3, label: '导航回家', action: 'navigateHome' },
          { id: 4, label: '控制家居', action: 'controlHome' }
        ]
      },
      kidEducation: {
        currentContent: {
          type: 'video',
          title: '小猪佩奇学数学',
          duration: '15分钟',
          progress: 35,
          ageAppropriate: true
        },
        aiQA: {
          lastQuestion: '地球为什么是圆的？',
          answer: '因为呀，有一个叫"万有引力"的大力士，它从地球的中心把所有东西都紧紧地抱住，抱得时间太久了，就把地球抱成了一个圆滚滚的胖子啦！',
          difficulty: 'child-friendly'
        }
      },
      dynamicIsland: {
        currentInfo: {
          primary: '前往: 公司',
          secondary: '预计: 25分钟',
          status: '智能方案: 直达+咖啡预订',
          icon: '🚀',
          priority: 'high'
        },
        notifications: [
          {
            id: 1,
            type: 'navigation',
            message: '路线已优化',
            priority: 'medium'
          },
          {
            id: 2,
            type: 'order',
            message: '咖啡预订成功',
            priority: 'low'
          }
        ]
      }
    };
  }

  // 初始化服务
  async initialize() {
    if (this.isInitialized) return;
    
    // 模拟初始化延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    this.isInitialized = true;
    console.log('MockDataService initialized');
  }

  // 获取导航数据
  getNavigationData() {
    return {
      ...this.mockData.navigation,
      timestamp: Date.now()
    };
  }

  // 获取音乐数据
  getMusicData() {
    return {
      ...this.mockData.music,
      timestamp: Date.now()
    };
  }

  // 获取日程数据
  getScheduleData() {
    return {
      ...this.mockData.schedule,
      timestamp: Date.now()
    };
  }

  // 获取智能家居数据
  getSmartHomeData() {
    return {
      ...this.mockData.smartHome,
      timestamp: Date.now()
    };
  }

  // 获取订单数据
  getOrdersData() {
    return {
      orders: this.mockData.orders,
      timestamp: Date.now()
    };
  }

  // 获取VPA数据
  getVPAData() {
    return {
      ...this.mockData.vpa,
      timestamp: Date.now()
    };
  }

  // 获取儿童教育数据
  getKidEducationData() {
    return {
      ...this.mockData.kidEducation,
      timestamp: Date.now()
    };
  }

  // 获取灵动岛数据
  getDynamicIslandData() {
    return {
      ...this.mockData.dynamicIsland,
      timestamp: Date.now()
    };
  }

  // AI百科数据
  getPediaData() {
    return {
      searchSuggestions: [
        { id: 1, text: '什么是人工智能？', icon: 'fas fa-robot' },
        { id: 2, text: '如何保持健康的生活方式？', icon: 'fas fa-heart' },
        { id: 3, text: '太阳系有几颗行星？', icon: 'fas fa-globe' },
        { id: 4, text: '如何学习编程？', icon: 'fas fa-code' },
        { id: 5, text: '气候变化的影响', icon: 'fas fa-leaf' }
      ],
      knowledgeCategories: [
        { id: 1, name: '科学技术', description: '探索科技前沿知识', count: 156, icon: 'fas fa-flask' },
        { id: 2, name: '健康生活', description: '健康养生知识分享', count: 89, icon: 'fas fa-heartbeat' },
        { id: 3, name: '历史文化', description: '人文历史知识宝库', count: 234, icon: 'fas fa-landmark' },
        { id: 4, name: '自然科学', description: '物理化学生物知识', count: 178, icon: 'fas fa-atom' },
        { id: 5, name: '艺术文学', description: '文学艺术欣赏', count: 92, icon: 'fas fa-palette' },
        { id: 6, name: '生活技能', description: '实用生活小技巧', count: 145, icon: 'fas fa-tools' }
      ],
      dailyRecommendations: [
        {
          id: 1,
          title: '人工智能的发展历程',
          summary: '从图灵测试到ChatGPT，探索AI技术的演进之路和未来发展趋势',
          category: '科技',
          readTime: 5,
          views: 1234,
          icon: 'fas fa-robot',
          badge: '热门'
        },
        {
          id: 2,
          title: '健康饮食的科学原理',
          summary: '了解营养学基础，掌握科学饮食搭配方法',
          category: '健康',
          readTime: 3,
          views: 856,
          icon: 'fas fa-apple-alt',
          badge: '推荐'
        },
        {
          id: 3,
          title: '量子计算的基本概念',
          summary: '深入浅出解释量子计算原理和应用前景',
          category: '科学',
          readTime: 8,
          views: 567,
          icon: 'fas fa-atom',
          badge: '新'
        }
      ],
      searchHistory: [],
      learningStats: {
        questionsAsked: 12,
        knowledgeGained: 45,
        readingTime: '2小时30分',
        streak: 7,
        achievements: [
          { id: 1, name: '好奇宝宝', description: '连续提问7天', icon: 'fas fa-star' },
          { id: 2, name: '知识达人', description: '学习45个知识点', icon: 'fas fa-graduation-cap' }
        ]
      },
      timestamp: Date.now()
    };
  }

  // 搜索知识库
  async searchKnowledge(query) {
    // 模拟搜索延迟
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    const knowledgeBase = {
      '人工智能': {
        content: '人工智能（Artificial Intelligence，AI）是指由人制造出来的机器所表现出来的智能。通常人工智能是指通过普通计算机程序来呈现人类智能的技术。\n\n**主要特征：**\n- 学习能力：能够从数据中学习和改进\n- 推理能力：能够进行逻辑推理和决策\n- 感知能力：能够理解和处理感官信息\n- 语言处理：能够理解和生成自然语言',
        confidence: 0.95,
        images: [
          { url: '/images/ai-brain.jpg', caption: 'AI神经网络示意图' },
          { url: '/images/ai-robot.jpg', caption: '智能机器人' }
        ],
        links: [
          { id: 1, title: 'AI发展史详解', url: 'https://example.com/ai-history' },
          { id: 2, title: '机器学习入门', url: 'https://example.com/ml-intro' }
        ]
      },
      '健康': {
        content: '健康的生活方式包括均衡饮食、规律运动、充足睡眠和良好的心理状态。\n\n**核心要素：**\n- **饮食**：多吃蔬菜水果，少吃加工食品\n- **运动**：每周至少150分钟中等强度运动\n- **睡眠**：成人每晚7-9小时优质睡眠\n- **心理**：保持积极心态，学会压力管理',
        confidence: 0.92,
        images: [
          { url: '/images/healthy-food.jpg', caption: '健康饮食搭配' },
          { url: '/images/exercise.jpg', caption: '规律运动' }
        ],
        links: [
          { id: 1, title: '营养学指南', url: 'https://example.com/nutrition' },
          { id: 2, title: '运动健身计划', url: 'https://example.com/fitness' }
        ]
      },
      '太阳系': {
        content: '太阳系包含8颗行星：水星、金星、地球、火星、木星、土星、天王星、海王星。\n\n**行星分类：**\n- **类地行星**：水星、金星、地球、火星\n- **气态巨行星**：木星、土星\n- **冰巨行星**：天王星、海王星\n\n冥王星在2006年被重新分类为矮行星。',
        confidence: 0.98,
        images: [
          { url: '/images/solar-system.jpg', caption: '太阳系示意图' },
          { url: '/images/planets.jpg', caption: '八大行星' }
        ],
        links: [
          { id: 1, title: 'NASA太阳系探索', url: 'https://example.com/nasa-solar' },
          { id: 2, title: '行星科学研究', url: 'https://example.com/planetary-science' }
        ]
      },
      '编程': {
        content: '学习编程需要循序渐进，从基础概念开始，逐步掌握编程思维和实践技能。\n\n**学习路径：**\n1. **选择语言**：Python适合初学者\n2. **基础语法**：变量、函数、控制结构\n3. **数据结构**：数组、链表、树等\n4. **算法思维**：解决问题的逻辑方法\n5. **项目实践**：通过实际项目巩固知识',
        confidence: 0.89,
        images: [
          { url: '/images/coding.jpg', caption: '编程学习' },
          { url: '/images/algorithms.jpg', caption: '算法可视化' }
        ],
        links: [
          { id: 1, title: 'Python入门教程', url: 'https://example.com/python-tutorial' },
          { id: 2, title: '算法学习指南', url: 'https://example.com/algorithms' }
        ]
      }
    };
    
    // 简单的关键词匹配
    for (const [key, value] of Object.entries(knowledgeBase)) {
      if (query.includes(key) || key.includes(query)) {
        return value;
      }
    }
    
    // 默认回答
    return {
      content: `关于"${query}"的问题很有趣！这是一个值得深入探讨的话题。\n\n虽然我目前没有详细的相关信息，但我建议您可以：\n- 查阅相关的专业资料\n- 咨询领域专家\n- 通过实践来加深理解\n\n如果您有更具体的问题，我很乐意为您提供更有针对性的帮助。`,
      confidence: 0.65,
      images: [],
      links: []
    };
  }

  // 模拟场景切换
  switchScene(sceneId) {
    this.currentScene = sceneId;
    
    // 根据场景更新数据
    switch (sceneId) {
      case 'family':
        this.mockData.navigation.currentRoute.destination = '幼儿园';
        this.mockData.dynamicIsland.currentInfo.primary = '前往: 幼儿园';
        break;
      case 'focus':
        this.mockData.navigation.currentRoute.destination = '公司';
        this.mockData.dynamicIsland.currentInfo.primary = '前往: 公司';
        break;
      case 'entertainment':
        this.mockData.navigation.currentRoute.destination = '电影院';
        this.mockData.dynamicIsland.currentInfo.primary = '前往: 电影院';
        break;
      default:
        break;
    }
    
    return this.getAllSceneData();
  }

  // 获取所有场景数据
  getAllSceneData() {
    return {
      navigation: this.getNavigationData(),
      music: this.getMusicData(),
      schedule: this.getScheduleData(),
      smartHome: this.getSmartHomeData(),
      orders: this.getOrdersData(),
      vpa: this.getVPAData(),
      kidEducation: this.getKidEducationData(),
      dynamicIsland: this.getDynamicIslandData(),
      pedia: this.getPediaData(),
      currentScene: this.currentScene,
      timestamp: Date.now()
    };
  }

  // 模拟设备控制
  async controlDevice(deviceId, action, value) {
    const device = this.mockData.smartHome.devices.find(d => d.id === deviceId);
    if (!device) return false;

    // 模拟控制延迟
    await new Promise(resolve => setTimeout(resolve, 200));

    switch (action) {
      case 'toggle':
        device.status = device.status === 'on' ? 'off' : 'on';
        break;
      case 'setBrightness':
        if (device.type === 'light') {
          device.brightness = value;
        }
        break;
      case 'setTemperature':
        if (device.type === 'climate') {
          device.temperature = value;
        }
        break;
    }

    return true;
  }

  // 模拟VPA对话
  async sendVPAMessage(message) {
    // 模拟AI处理时间
    await new Promise(resolve => setTimeout(resolve, 1000));

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: message,
      timestamp: Date.now()
    };

    // 简单的模拟回复逻辑
    let vpaResponse = '';
    if (message.includes('音乐')) {
      vpaResponse = '好的，我为您播放适合当前场景的音乐。';
    } else if (message.includes('导航')) {
      vpaResponse = '已为您规划最优路线，预计节省8分钟。';
    } else if (message.includes('家居')) {
      vpaResponse = '正在为您控制智能家居设备。';
    } else {
      vpaResponse = '我明白了，让我为您处理这个请求。';
    }

    const vpaMessage = {
      id: Date.now() + 1,
      type: 'vpa',
      content: vpaResponse,
      timestamp: Date.now()
    };

    this.mockData.vpa.conversationHistory.push(userMessage, vpaMessage);
    
    return vpaMessage;
  }
}

// 创建单例实例
const mockDataService = new MockDataService();

export default mockDataService;