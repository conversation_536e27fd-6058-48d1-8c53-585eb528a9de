<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>通勤界面</title>
<style>
  body {
    font-family: 'Noto Sans', sans-serif;
    color: #7D8A6E;
    background-color: #F0F2EB;
    margin: 0;
    padding: 0;
  }
  .container {
    display: flex;
    flex-wrap: wrap;
  }
  .left-panel {
    width: 30%;
    padding: 20px;
  }
  .main-content {
    width: 70%;
    padding: 20px;
  }
  .card {
    background-color: #FFFFFF;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
  }
  .card h2 {
    margin: 0 0 10px 0;
  }
  .card p {
    margin: 0;
  }
</style>
</head>
<body>
<div class="container">
  <div class="left-panel">
    <div class="card">
      <h2>导航详情</h2>
      <p>即将到达下一个路口</p>
    </div>
    <div class="card">
      <h2>待办事项</h2>
      <p>送孩子上学</p>
    </div>
  </div>
  <div class="main-content">
    <div class="card">
      <h2>动态岛</h2>
      <p>实时通知和活动</p>
    </div>
    <div class="card">
      <h2>天气</h2>
      <p>晴朗，温度22°C</p>
    </div>
  </div>
</div>
</body>
</html>