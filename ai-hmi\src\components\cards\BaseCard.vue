<template>
  <div
    :class="cardClasses"
    :style="cardStyles"
    @click="handleClick"
  >
    <!-- 卡片头部 -->
    <div v-if="showHeader" class="card-header">
      <slot name="header">
        <div class="card-title">
          <component :is="titleIcon" v-if="titleIcon" class="title-icon" />
          <span>{{ title }}</span>
        </div>
        <div v-if="showActions" class="card-actions">
          <slot name="actions" />
        </div>
      </slot>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <slot name="content">
        <component
          :is="contentComponent"
          v-bind="contentProps"
          @update="handleContentUpdate"
        />
      </slot>
    </div>

    <!-- 卡片底部 -->
    <div v-if="showFooter" class="card-footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'BaseCard',
  props: {
    // 尺寸配置
    gridSize: {
      type: String,
      default: '4x2',
      validator: (value) => /^\d+x\d+$/.test(value)
    },

    // 内容配置
    title: {
      type: String,
      default: ''
    },
    titleIcon: {
      type: [String, Object],
      default: null
    },
    contentComponent: {
      type: [String, Object],
      default: null
    },
    contentProps: {
      type: Object,
      default: () => ({})
    },

    // 主题配置
    theme: {
      type: String,
      default: 'glassmorphism',
      validator: (value) => ['glassmorphism', 'minimal', 'dark', 'light'].includes(value)
    },
    themeColors: {
      type: Object,
      default: () => ({})
    },

    // 布局配置
    position: {
      type: Object,
      default: () => ({ x: 0, y: 0 })
    },
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },

    // 显示控制
    showHeader: {
      type: Boolean,
      default: true
    },
    showFooter: {
      type: Boolean,
      default: false
    },
    showActions: {
      type: Boolean,
      default: false
    },

    // 交互配置
    clickable: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },

    // 状态
    loading: {
      type: Boolean,
      default: false
    }
  },

  emits: ['click', 'content-update'],

  computed: {
    cardClasses() {
      return [
        'base-card',
        `card-${this.theme}`,
        `card-size-${this.size}`,
        `grid-${this.gridSize}`,
        {
          'card-clickable': this.clickable,
          'card-disabled': this.disabled,
          'card-loading': this.loading
        }
      ]
    },

    cardStyles() {
      const styles = {
        '--card-x': this.position.x,
        '--card-y': this.position.y
      }

      // 应用主题颜色
      if (this.themeColors) {
        Object.keys(this.themeColors).forEach(key => {
          styles[`--theme-${key}`] = this.themeColors[key]
        })
      }

      return styles
    }
  },

  methods: {
    handleClick(event) {
      if (!this.disabled && this.clickable) {
        this.$emit('click', event)
      }
    },

    handleContentUpdate(data) {
      this.$emit('content-update', data)
    }
  }
}
</script>

<style scoped>
.base-card {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 主题样式 */
.card-glassmorphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-minimal {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.1);
  backdrop-filter: none;
}

.card-dark {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
}

.card-light {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: #333;
}

/* 尺寸样式 */
.card-size-small {
  min-height: 120px;
  padding: 12px;
}

.card-size-medium {
  min-height: 180px;
  padding: 16px;
}

.card-size-large {
  min-height: 240px;
  padding: 20px;
}

/* 网格尺寸 */
.grid-2x2 {
  grid-column: span 2;
  grid-row: span 2;
}

.grid-4x2 {
  grid-column: span 4;
  grid-row: span 2;
}

.grid-4x4 {
  grid-column: span 4;
  grid-row: span 4;
}

.grid-8x4 {
  grid-column: span 8;
  grid-row: span 4;
}

.grid-8x9 {
  grid-column: span 8;
  grid-row: span 9;
}

.grid-16x1 {
  grid-column: span 16;
  grid-row: span 1;
}

/* 交互状态 */
.card-clickable {
  cursor: pointer;
}

.card-clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.card-disabled {
  opacity: 0.6;
  pointer-events: none;
}

.card-loading {
  position: relative;
}

.card-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(2px);
}

/* 卡片结构 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text, rgba(255, 255, 255, 0.9));
}

.title-icon {
  width: 20px;
  height: 20px;
  opacity: 0.8;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.card-content {
  flex: 1;
  min-height: 0;
}

.card-footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-size-small {
    min-height: 100px;
    padding: 10px;
  }

  .card-size-medium {
    min-height: 150px;
    padding: 14px;
  }

  .card-size-large {
    min-height: 200px;
    padding: 16px;
  }

  .card-title {
    font-size: 14px;
  }
}
</style>
