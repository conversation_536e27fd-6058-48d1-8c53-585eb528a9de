<template>
  <div class="voice-interaction-manager">
    <!-- 语音输入按钮 -->
    <button
      @click="toggleVoiceInteraction"
      :class="['voice-btn', {
        listening: isListening,
        processing: isProcessing,
        speaking: isSpeaking
      }]"
    >
      <i :class="getVoiceButtonIcon"></i>
    </button>

    <!-- 文本输入切换按钮 -->
    <button
      @click="toggleTextInput"
      class="text-toggle-btn"
      :title="showTextInput ? '隐藏文本输入' : '显示文本输入'"
    >
      <i :class="showTextInput ? 'fas fa-keyboard' : 'fas fa-edit'"></i>
    </button>

    <!-- 识别结果显示 -->
    <div v-if="recognitionResult" class="recognition-result">
      <div class="result-text">{{ recognitionResult }}</div>
      <div class="result-actions">
        <button @click="acceptResult" class="action-btn accept">
          <i class="fas fa-check"></i>
        </button>
        <button @click="rejectResult" class="action-btn reject">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- 处理状态显示 -->
    <div v-if="statusMessage" class="status-message">
      <i class="fas fa-info-circle"></i>
      {{ statusMessage }}
    </div>

    <!-- 文本输入备用方案 -->
    <div v-if="showTextInput" class="text-input-fallback">
      <input
        v-model="textInput"
        @keyup.enter="submitTextInput"
        placeholder="或直接输入描述..."
        class="text-input-field"
      />
      <button @click="submitTextInput" class="submit-btn">
        <i class="fas fa-paper-plane"></i>
      </button>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import AsrService from '@/services/AsrService'
import TtsService from '@/services/TtsService'
import LlmService from '@/services/LlmService'

export default {
  name: 'VoiceInteractionManager',
  emits: ['wallpaper-prompt-ready', 'scene-switch-requested'],

  setup(props, { emit }) {
    const asrService = new AsrService()
    const ttsService = new TtsService()
    const llmService = new LlmService()

    const isListening = ref(false)
    const isProcessing = ref(false)
    const isSpeaking = ref(false)
    const recognitionResult = ref('')
    const textInput = ref('')
    const statusMessage = ref('')
    const showTextInput = ref(false) // 默认隐藏文本输入框

    const voiceState = computed(() => {
      if (isSpeaking.value) return 'speaking'
      if (isProcessing.value) return 'processing'
      if (isListening.value) return 'listening'
      return 'idle'
    })

    const getVoiceButtonIcon = computed(() => {
      switch (voiceState.value) {
        case 'speaking':
          return 'fas fa-volume-up'
        case 'processing':
          return 'fas fa-spinner fa-spin'
        case 'listening':
          return 'fas fa-stop'
        default:
          return 'fas fa-microphone'
      }
    })

    const toggleVoiceInteraction = async () => {
      if (isListening.value) {
        stopVoiceInteraction()
      } else {
        startVoiceInteraction()
      }
    }

    const startVoiceInteraction = async () => {
      if (isListening.value || isProcessing.value) return

      try {
        isListening.value = true
        statusMessage.value = '正在聆听...'
        
        // 使用ASR服务进行语音识别
        const result = await asrService.startRecognition()
        recognitionResult.value = result
        
        // 自动接受结果并处理
        await processVoiceResult(result)
        
      } catch (error) {
        console.error('语音识别失败:', error)
        statusMessage.value = '语音识别失败，请重试'
        
        // 语音反馈
        await ttsService.speak('语音识别失败，请重试或使用文本输入')
      } finally {
        isListening.value = false
        statusMessage.value = ''
      }
    }

    const stopVoiceInteraction = () => {
      if (asrService) {
        asrService.stopRecognition()
      }
      if (ttsService) {
        ttsService.stop()
      }
      isListening.value = false
      statusMessage.value = ''
    }

    const processVoiceResult = async (userInput) => {
      isProcessing.value = true
      statusMessage.value = '正在处理...'

      try {
        // 首先尝试识别场景切换指令
        statusMessage.value = '正在理解您的需求...'
        const sceneResult = await llmService.detectSceneFromVoice(userInput)
        
        if (sceneResult.sceneId && sceneResult.confidence > 0.6) {
          // 场景切换逻辑
          statusMessage.value = `正在切换到${getSceneName(sceneResult.sceneId)}...`
          await ttsService.speak(`正在为您切换到${getSceneName(sceneResult.sceneId)}`)
          
          // 发送场景切换请求
          emit('scene-switch-requested', {
            sceneId: sceneResult.sceneId,
            confidence: sceneResult.confidence,
            reason: sceneResult.reason
          })
          
          // 完成反馈
          setTimeout(async () => {
            await ttsService.speak('场景切换完成')
            statusMessage.value = ''
          }, 2000)
          
          return
        }
        
        // 如果不是场景切换，则按原来的逻辑处理壁纸生成
        const enhancedPrompt = await llmService.generateResponse(userInput)
        
        // 语音反馈处理结果
        statusMessage.value = '正在生成壁纸...'
        await ttsService.speak('正在为您生成壁纸，请稍候')
        
        // 发送最终提示词给主题管理器
        emit('wallpaper-prompt-ready', enhancedPrompt)
        
        // 完成反馈
        setTimeout(async () => {
          await ttsService.speak('壁纸生成完成')
          statusMessage.value = ''
        }, 3000)
        
      } catch (error) {
        console.error('处理语音输入失败:', error)
        statusMessage.value = '处理失败，请重试'
        
        await ttsService.speak('处理失败，请重试')
        
        // 降级方案：直接使用原始输入
        emit('wallpaper-prompt-ready', userInput)
      } finally {
        isProcessing.value = false
        recognitionResult.value = ''
      }
    }

    const acceptResult = async () => {
      if (recognitionResult.value.trim()) {
        await processVoiceResult(recognitionResult.value)
      }
    }

    const rejectResult = () => {
      recognitionResult.value = ''
      statusMessage.value = ''
      ttsService.speak('请重新描述')
    }

    const submitTextInput = async () => {
      if (textInput.value.trim()) {
        await processVoiceResult(textInput.value)
        textInput.value = ''
      }
    }

    const toggleTextInput = () => {
      showTextInput.value = !showTextInput.value
    }

    // 获取场景的中文名称
    const getSceneName = (sceneId) => {
      const sceneNames = {
        morningCommuteFamily: '家庭出行模式',
        morningCommuteFocus: '专注通勤模式',
        eveningCommute: '下班通勤模式',
        waitingMode: '等待休息模式',
        rainyNight: '雨夜模式',
        familyTrip: '家庭出游模式',
        longDistance: '长途驾驶模式',
        guestMode: '访客模式',
        petMode: '宠物模式',
        carWashMode: '洗车模式',
        romanticMode: '浪漫模式',
        chargingMode: '充电模式',
        fatigueDetection: '疲劳检测模式',
        userSwitch: '用户切换模式',
        parkingMode: '泊车模式',
        emergencyMode: '紧急模式'
      }
      return sceneNames[sceneId] || sceneId
    }

    // 初始化TTS语音
    onMounted(() => {
      // 等待语音列表加载
      if (ttsService.isSupported) {
        setTimeout(() => {
          ttsService.getVoices()
        }, 100)
      }
    })

    return {
      isListening,
      isProcessing,
      isSpeaking,
      recognitionResult,
      textInput,
      statusMessage,
      showTextInput,
      voiceState,
      getVoiceButtonIcon,
      toggleVoiceInteraction,
      toggleTextInput,
      acceptResult,
      rejectResult,
      submitTextInput,
      getSceneName
    }
  }
}
</script>

<style scoped>
.voice-interaction-manager {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 10px;
  max-width: 420px;
}

.voice-btn {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.voice-btn:hover {
  transform: scale(1.1);
  background: rgba(255, 255, 255, 0.3);
}

.voice-btn.listening {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  animation: pulse 1.5s infinite;
}

.voice-btn.processing {
  background: linear-gradient(45deg, #4834d4, #686de0);
  animation: pulse 2s infinite;
}

.voice-btn.speaking {
  background: linear-gradient(45deg, #00d2d3, #54a0ff);
  animation: wave 2s infinite;
}

.text-toggle-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  font-size: 16px;
}

.text-toggle-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.recognition-result {
  padding: 15px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.result-text {
  font-size: 14px;
  color: #333;
  margin-bottom: 10px;
  line-height: 1.4;
}

.result-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.action-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn.accept {
  background: #2ed573;
  color: white;
}

.action-btn.reject {
  background: #ff4757;
  color: white;
}

.action-btn:hover {
  transform: scale(1.1);
}

.status-message {
  padding: 12px 15px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  font-size: 13px;
  color: #333;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 8px;
}

.text-input-fallback {
  display: flex;
  gap: 10px;
}

.text-input-field {
  flex: 1;
  padding: 12px 18px;
  border: none;
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  outline: none;
  backdrop-filter: blur(10px);
  color: #333;
}

.submit-btn {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.submit-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes wave {
  0%, 100% { transform: scale(1); }
  25% { transform: scale(1.02); }
  75% { transform: scale(1.02); }
}
</style>