# 2. 设计系统 (Design System)

- **文档版本:** 9.0 (Refactored)
- **状态:** 正式版

---

## 1. 视觉主题库 (Style Theme Library)

主题是一套完整的、预设的、具有独特风格的视觉语言。AI将根据场景和用户偏好，从库中选择一个最合适的主题应用到整个UI。

### 1.1. 主题：自然 (Serenity)
- 参考提示词：【设计一个亲生物登录页面界面，创造与自然的连接感，具有有机形状、自然纹理和唤起宁静和幸福感的生命元素。使用受自然环境启发的宁静色彩调色板 - 柔和的森林绿、温和的天空蓝、泥土棕和日落橙 - 创造一种平静、欢迎氛围。实现模仿自然运动的微妙动画 - 轻柔的叶子摇曳、云朵飘动或水波纹效果 - 作为背景元素和用户交互反馈。设计登录容器时采用柔软、有机的形式而非锐利的几何形状，边缘看起来自然而非完美直线，并融入创造自然深度的微妙阴影。包含呈现高质量自然图像的背景元素、有机模式的抽象表示（如叶脉、水波纹或木纹），或微妙的动画自然元素，如轻柔的降雨或斑驳的光影效果。创建感觉与自然融合的表单元素 - 边框灵感来自有机形态的输入字段、类似平滑石头或叶子等自然元素的按钮，以及绽放或生长而非使用标准高亮的焦点状态。使用平衡可读性和自然特性的排版，选择具有微妙有机质感而非刺硬几何形式的字体。设计使用自然隐喻的错误和成功状态 - 也许错误使用凋谢效果，成功使用绽放效果。如果情境适当，为交互包含微妙的自然音效（如按钮悬停时轻柔的树叶沙沙声或表单提交时柔和的水声）。整个登录体验应该感觉像是短暂地撤退到自然中，减轻压力，并在进入数字空间之前提供片刻宁静，即使在这种简短的交互中也创造亲生物恢复的感觉。】 


### 1.2. 主题：赛博朋克 (Cyberpunk)
- 参考提示词：【设计一个具有赛博朋克美学的AI图像编辑平台登陆页面，其特色是霓虹照明的首屏区域、未来主义功能展示和高科技号召性行动模块。使用以深黑色和丰富蓝色为主的暗色调配色方案，配以电光霓虹色调（亮粉色、酸性绿、电光蓝），模拟未来科技的光芒。实现戏剧性照明效果，模仿赛博朋克小说中霓虹浸染的城市环境，使用渐变和辉光暗示先进的数字能力。创建在悬停或滚动时触发的故障动画和数字失真效果，增强高科技、略显不稳定的赛博朋克感。设计具有锐角形状、不对称布局和暴露网格系统的UI组件，唤起赛博界面的美学。包含技术元素，如人工电路图案、数据可视化片段和数字噪点纹理作为背景和装饰元素。融入复古未来主义排版，为标题使用做旧数字字体，为正文使用清晰、高可读性的字体，确保在复杂的视觉环境中保持可读性。包含模拟全息显示或线框插图，展示AI图像编辑功能和转换效果，使用带有故障效果的3D风格模型。对于图像编辑工具预览，显示带有赛博朋克风格转换的前后对比（如添加霓虹效果、数字覆盖或未来城市背景）。整体布局应平衡沉浸式赛博朋克美学与关于AI图像编辑能力的清晰营销信息，创造一个感觉像是提供来自未来技术的登陆页面。】

### 1.3. 主题：玻璃拟态 (Glassmorphism) 默认主题
- 参考提示词：【使用玻璃态设计原则为AI新闻网站设计一个精致的着陆页。创建一个既未来主义又优雅的界面，具有磨砂玻璃效果、微妙的透明度、美丽的模糊效果和柔和的彩色背景。布局应包括一个突出展示最新AI突破的主要英雄部分，随后是新闻类别、热门话题和通讯订阅部分。为新闻项目实现带有微妙边框和阴影的玻璃卡片，每张卡片包含标题、简短描述、发布日期和相关图片。导航应该是流畅的，具有半透明玻璃效果，在滚动时保持可访问性。使用深蓝色、紫色和青色的色彩调色板，搭配暗示技术进步的渐变，并由鲜艳的强调色补充重要的交互元素。排版应简洁现代，优先考虑可读性，同时保持面向科技的美学。为卡片和按钮融入微妙的悬停动画，增强玻璃效果 - 也许略微增加透明度或发光效果。设计具有玻璃般特性的交互元素，如按钮和表单字段，交互时带有柔和、彩色的光芒。包含抽象、流动的背景形状或模式，暗示数据流或神经网络，巧妙地置于玻璃元素后面以创造深度。对于移动响应性，考虑玻璃元素如何适应和堆叠，同时保持优雅的美学。整体印象应该是一个尖端的数字出版物，感觉精致、值得信赖和身临其境 - 使用玻璃态设计创建一个既未来主义又易于接近的界面，适合寻求AI最新发展的用户。】

### 1.3. 主题：苹果 
- 参考提示词： 【设计一个遵循苹果人机界面指南的电子商务界面，同时融入有趣的素描元素。使用毛玻璃效果（SF Symbols背景模糊）实现苹果标志性的清晰、尊重和深度设计原则，用于导航栏和覆盖层。使用苹果的SF Pro字体系列进行排版，配以大胆的标题和简洁的正文文本。遵循苹果的间距和布局指南，提供宽敞的留白和精确的动态字体缩放。实现苹果基于弹簧的动画系统，用于流畅的过渡和微交互，使用自然的减速曲线。设计带有微妙阴影和圆角的产品卡片（SF圆角风格），融入苹果基于卡片的设计模式进行产品展示。使用苹果的标准系统颜色作为基础调色板，辅以符合可访问性标准的自定义强调色。包含苹果风格的手势交互，如产品视图之间的平滑滑动过渡和下拉刷新动画。对于趣味元素，整合与苹果极简美学相辅相成的手绘装饰和插图 - 例如自定义插图SF Symbols替代品，或符合苹果分层系统的装饰背景。导航应遵循苹果的标签栏模式，使用简洁的图标，而搜索和过滤功能则使用苹果的搜索栏和表单展示方式。购物车和结账流程应使用苹果的表单和模态展示风格，配以标准iOS/macOS表单元素。布局应使用苹果推荐的响应式模式在不同平台之间无缝适配，保持一致的信息层次结构。为触摸设备上的交互元素包含触觉反馈模式。最终设计应该感觉像一个高端苹果生态系统应用，其中经过深思熟虑地整合了有趣元素，这些元素增强但不会掩盖苹果精致、极简的美学。】

### 1.3.1 主题：可爱风格
- 参考提示词： 【设计一个具有日本可爱文化灵感的kawaii/可爱风格移动照片分享应用界面，包含照片流、图像捕捉、具有可爱滤镜的编辑工具和社交互动功能。使用柔和的粉色、薄荷绿、淡紫色和浅蓝色的柔和色彩调色板，搭配温暖的中性背景，唤起舒适和欢乐的感觉。实现有趣的动画，界面元素以卡通物理效果弹跳、摇晃和弹跳 - 变成可爱生物的加载指示器，按下时会挤压的点赞/评论按钮，以及感觉轻盈弹跳的屏幕之间过渡。创建一个可爱风格的照片流，帖子卡片具有圆角、类似花边或动物形状的装饰框架，以及样式为可爱表情符号而非普通图标的反应按钮。包含创新的相机界面，具有kawaii主题的拍摄按钮，以可爱角色主题（如猫耳朵、闪亮眼睛和粉红脸颊）为特色的滤镜选择，以及具有装饰贴纸和可爱文本选项的编辑工具。设计可自定义头像框架的个人资料页面，成就徽章显示为可爱的收藏角色，以及在有趣布局中组织的照片集合。包含社交功能，如带有动画表情反应的评论，样式为可爱邮箱并带有动画传递效果的通知中心，以及由可爱吉祥物角色代表热门类别的发现页面。使用底部标签栏构建导航，包含5个主要部分（Feed、发现、相机、通知、个人资料），由轻轻点击时会微妙动画的可爱角色图标表示。包含Android智能手机设备框架以提供逼真的上下文，确保所有UI元素相对于设备屏幕使用绝对定位，具有适当的自适应布局。整体体验应创造一个欢乐、引人入胜的照片分享环境，通过一致的kawaii美学鼓励创意表达，让用户在有效完成核心照片分享任务的同时微笑。请求设备框架以模拟设计在实际设备上的显示效果。
---



## 1.4. 壁纸整合设计原则 (Wallpaper Integration)

### **核心理念：组件与壁纸的和谐共生**

所有UI组件必须与动态壁纸形成视觉整体，而非简单的叠加关系。组件应该像"漂浮"在壁纸之上的智能元素，与背景环境产生自然的视觉交互。

### **1.4.1. 透明度层次系统**
- **完全透明**: VPA数字人背景 (opacity: 0) - 让数字人仿佛站在真实桌面中
- **高透明**: 临时交互组件 (opacity: 0.1-0.3) - 保持背景可见性
- **中透明**: 常驻卡片组件 (opacity: 0.4-0.7) - 平衡可读性与整合性
- **低透明**: 重要信息组件 (opacity: 0.8-0.9) - 确保信息清晰传达

### **1.4.2. 毛玻璃效果规范**
- **模糊强度**: backdrop-filter: blur(8px-20px) 根据组件重要性调整
- **饱和度增强**: backdrop-filter: saturate(1.2-1.8) 增强色彩层次
- **亮度调节**: backdrop-filter: brightness(1.1-1.3) 适应不同壁纸亮度
- **边框光晕**: box-shadow + border 营造悬浮感

### **1.4.3. 动态适应机制**
- **色彩提取**: 自动从壁纸提取主色调，调整组件配色
- **亮度检测**: 根据壁纸亮度自动切换浅色/深色组件主题
- **对比度优化**: 确保文字在任何壁纸背景下都具有足够对比度

---

## 1.5. SVG图标设计规范 (SVG Icon Standards)

### **统一性原则：所有图标均采用SVG矢量格式**

为确保在不同分辨率和主题下的完美呈现，系统中的所有图标都必须使用SVG格式，并遵循统一的设计规范。

### **1.5.1. 技术规范**
- **画布尺寸**: 24x24px 标准网格
- **描边宽度**: 1.5px-2px，确保清晰度
- **圆角半径**: 2px 统一圆角，与整体设计语言保持一致
- **颜色变量**: 使用CSS变量，支持主题动态切换

### **1.5.2. 分类体系**
- **系统图标**: 导航、设置、返回、菜单等基础操作
- **天气图标**: 晴天、多云、雨天、雪天等天气状态
- **媒体图标**: 播放、暂停、下一首、音量等媒体控制
- **汽车图标**: 速度、油量、电池、导航等车载专用
- **状态图标**: 连接、断开、加载、完成等状态指示

### **1.5.3. 动态效果**
- **悬停动画**: 轻微缩放 (scale: 1.1) + 颜色渐变
- **点击反馈**: 快速缩放 (scale: 0.95 → 1.0) 提供触觉反馈
- **状态切换**: 平滑的形状变换动画 (如播放→暂停)

---

## 2. 场景过渡效果 (Transition Effects)

### **2.1. 设计哲学：将切换本身变为一种体验**

在本系统中，“切换”（无论是主题切换还是场景布局切换）不应是生硬的、瞬时的。我们将其视为一次微型的**“视觉叙事”**，一次与用户情感和场景逻辑相呼应的动态表演。蒙版切换是实现这一目标的核心技术，它通过**“动态揭示”**而非“直接替换”的方式，为用户带来优雅、流畅且充满惊喜的过渡体验。

最终的呈现状态**永远是100%的全新界面**，切换特效只是连接两个完整界面的、短暂而华丽的“桥梁”。

### **2.2. 核心技术栈与实现原理**

#### **2.2.1. 核心技术**

为了实现高保真、高性能且高度可控的蒙版切换动画，我们的特效库将采用以下核心技术的组合：

*   **GSAP (GreenSock Animation Platform)**: 作为首选的JavaScript动画库，负责**精确控制动画的时间线、缓动函数（Easing）和执行顺序**，确保所有过渡效果都能达到60fps的流畅度。

*   **CSS `clip-path` 属性**: 这是实现“蒙版”效果的**核心CSS属性**。我们将通过GSAP来动态地、平滑地改变 `clip-path` 的值（例如，一个圆形的半径或一个多边形的顶点坐标），从而创造出各种各样的“揭示”动画。

#### **2.2.2. 实现原理：基于快照的无缝动画**

所有蒙版切换效果都遵循一个统一的技术原理，以保证性能和视觉效果的完美：

1.  **预渲染**: 在后台渲染出即将切换到的**完整目标界面**。
2.  **捕获快照**: 将当前可见的界面“截图”为一张静态图片（或Canvas纹理）。
3.  **动画执行**: 将“目标界面”置于“当前界面快照”之下。通过GSAP操作“目标界面”的 `clip-path` 属性，使其从不可见到完全可见，仿佛是在“擦除”上层的快照。
4.  **清理**: 动画结束后，“当前界面快照”被彻底移除，屏幕上只留下**100%的、可交互的新界面**。

### **2.3. 蒙版切换特效库 (Mask Wipe Effects Library)**

AI将根据场景切换的逻辑和“情绪”，从以下预设库中选择一个最合适的过渡效果。

#### **2.3.1. 范式一：面板式揭示 (Panel Reveal)**
*描述: 目标界面像一个独立的“面板”，以平移或缩放的方式进入画面，覆盖旧界面。*

*   **特效ID: `Sliding_Panel_Wipe`**
    *   **描述**: 目标界面从屏幕一侧平滑地进入，逐步覆盖旧界面的快照。
    *   **AI应用逻辑**: 从信息浏览切换到**导航模式**时，AI可选择此效果，寓意“旅程的开始”。
    *   **技术说明**: GSAP动画化一个矩形 `clip-path` 的位置。
    *   **ASCII原型 (三阶段)**:
        ```
        阶段1: 初始状态          阶段2: 过渡中              阶段3: 最终状态
        +--------------------+    +--------------------+    +--------------------+ 
        |                    |    | [新界] | 旧界面快照 |    |                    |
        |      旧界面        |    | [面]   |            |    |      新界面        |
        |                    |    | [  ]   |            |    |                    |
        +--------------------+    +--------------------+    +--------------------+
        ```

*   **特效ID: `Angled_Panel_Wipe`**
    *   **描述**: 带有倾斜角度的面板进入画面，更具速度感和设计感。
    *   **AI应用逻辑**: 切换到更具活力或科技感的主题（如“赛博朋克”）时，AI可选用此效果。
    *   **技术说明**: GSAP动画化一个 `polygon()` 定义的斜切多边形 `clip-path` 的位置。
    *   **ASCII原型 (三阶段)**:
        ```
        阶段1: 初始状态          阶段2: 过渡中              阶段3: 最终状态
        +--------------------+    +--------------------+    +--------------------+
        |                    |    | /新界面/ | 旧快照   |    |                    |
        |      旧界面        |    |/      / |          |    |      新界面        |
        |                    |   //       |          |    |                    |
        +--------------------+    +--------------------+    +--------------------+
        ```

#### **2.3.2. 范式二：扩展式揭示 (Expansion Reveal)**
*描述: 目标界面仿佛从一个点或一条线“生长”出来，直至铺满整个屏幕。*

*   **特效ID: `Circle_Expand_Reveal`**
    *   **描述**: 目标界面从屏幕中央的一个点，以圆形向外扩展，直至完全可见。
    *   **AI应用逻辑**: 当用户发出“生成XX桌面”指令后，AI可调用此效果来呈现最终生成的桌面，给予用户“魔法般创造出来”的感觉。
    *   **技术说明**: GSAP动画化一个 `circle()` 定义的圆形 `clip-path` 的半径值。
    *   **ASCII原型 (三阶段)**:
        ```
        阶段1: 初始状态          阶段2: 过渡中              阶段3: 最终状态
        +--------------------+    +--------------------+    +--------------------+
        |                    |    |   旧快照中的洞     |    |                    |
        |      旧界面        |    |      (○)         |    |      新界面        |
        |                    |    |     新界面可见     |    |                    |
        +--------------------+    +--------------------+    +--------------------+
        ```

#### **2.3.3. 范式三：开合式揭示 (Symmetrical Reveal)**
*描述: 目标界面由两个部分从中轴线向两边展开，或从两边向中轴线汇合。*

*   **特效ID: `Curtain_Open_Reveal`**
    *   **描述**: 目标界面像拉开剧院幕布一样，从屏幕中轴线向两侧平滑展开。
    *   **AI应用逻辑**: 当用户选择进入“影院模式”或“专注模式”时，AI可调用此效果，给予一种“好戏即将开始”的心理暗示。
    *   **技术说明**: GSAP同时动画化两个矩形 `clip-path`，让它们分别向屏幕的两侧移动。
    *   **ASCII原型 (三阶段)**:
        ```
        阶段1: 初始状态          阶段2: 过渡中              阶段3: 最终状态
        +--------------------+    +--------------------+    +--------------------+
        |                    |    |旧快照>|新界面|<旧快照|    |                    |
        |      旧界面        |    |      >|      |<      |    |      新界面        |
        |      |      |      |    |                    |
        +--------------------+    +--------------------+    +--------------------+
        ```

### **2.4. API集成与回退机制**

*   **API契约**: AI在返回的“桌面渲染计划”JSON中，通过 `transition_effect` 字段来指定使用哪个特效的ID。
    *   **示例**:
        ```json
        {
          "scene_id": "scene-1691-theme-cyberpunk",
          "transition_effect": "Angled_Panel_Wipe",
          "theme_name": "Cyberpunk",
          "...": "..."
        }
        ```

*   **回退机制 (Fallback)**: 如果AI提供的 `transition_effect` ID在前端的特效库中不存在，或因任何原因执行失败，系统将自动回退到一个最简单的**默认淡入淡出（Fade In/Out）效果**，以保证系统的稳定性和用户体验的连续性。

---



*   **画布层**: 切换为家庭友好的动态壁纸。
*   **网格层**:
    *   左侧加载 `儿童教育卡片 (8x9)`，播放视频。
    *   右上角加载 `百科问答卡片 (8x4)`，显示问题的答案。
    *   灵动岛显示第一段导航：“前往：XX幼儿园”。
    *   VPA陪伴小窗 (2x2) 持续存在。
*   **ASCII 原型图**:
    ```
    +--------------------------------------------------------------------------+
    | [灵动岛: 前往: XX幼儿园, 预计15分钟]                                     |
    | +----------------------+  +--------------------+                         |
    | |                      |  |   百科问答卡片(8x4)  |                         |
    | | 儿童教育卡片(8x9)    |  | "地球是圆的..."      |                         |
    | | (视频播放中)         |  +--------------------+                         |
    | |                      |                                                  |
    | |                      |                                     [o.o]      |
    | +----------------------+                                       VPA小窗   |
    +--------------------------------------------------------------------------+
    ```

### **阶段二：专注通勤模式 (独自上班)**

*   **画布层**: 切换为“放松的音乐动态壁纸”。
*   **网格层**:
    *   儿童教育和百科卡片消失。
    *   加载 `音乐控制大卡片 (8x9)`。
    *   加载 `订单状态卡片 (4x2)`。
    *   灵动岛更新导航：“前往：公司 | 途径：麦当劳”。
*   **ASCII 原型图**:
    ```
    +--------------------------------------------------------------------------+
    | [灵动岛: 前往: 公司 | 途径: 麦当劳]                                     |
    | +----------------------+  +--------------------+                         |
    | |                      |  | 订单状态卡片(4x2)  |                         |
    | | 音乐控制大卡片(8x9)  |  | "咖啡正在制作"     |                         |
    | | (放松歌单)           |  +--------------------+                         |
    | |                      |                                                  |
    | |                      |                                     [o.o]      |
    | +----------------------+                                       VPA小窗   |
    +--------------------------------------------------------------------------+
    ```

### **阶段三：抵达与提醒**

*   **画布层**: 切换为公司主题壁纸或默认壁纸。
*   **网格层**:
    *   所有卡片消失。
    *   加载 `日程提醒卡片 (8x4)`，显示“10:00 科室会议”。
    *   VPA交互面板弹出，进行最后的语音提醒：“记得取咖啡哦”。

---

## 3. 桌面画布与组件库 (Desktop Canvas & Component Library)

AI HMI的动态布局是基于“桌面画布”和“组件库”的智能化排列组合。桌面被划分为两个核心层次：

- **画布层 (Canvas Layer)**: 位于最底层，负责呈现沉浸式的视觉背景，通常是动态壁纸或实时地图。它为整个驾驶体验设定了基调。
```
- **网格层 (Grid Layer)**: 位于画布层之上，是一个不可见的、标准化的16x9高精度网格系统。所有UI组件都在这个网格上进行精确的、可预测的布局。AI的“生成式布局”本质上就是在这个网格上对组件进行逻辑化的“拼图游戏”。
```
这种分层架构确保了视觉美感（画布层）与功能布局（网格层）的完美分离与和谐统一。

### **3.1. 桌面画布规范 (Canvas Specification)**

- **屏幕比例**: 16:9 (标准车载宽屏)
- **网格系统**: 16x9 (16列9行), 这是所有组件布局的唯一、高精度参考系。所有历史版本中提及的 `8x4` 网格系统均已废弃，统一升级至 `16x9` 以获得更精细的布局控制能力。

### **3.2. 标准化组件库 (Finalized Component Library)**

这是AI进行“生成式布局”时可以使用的全部、固定尺寸的标准化组件。组件尺寸的固定化是保证UI稳定性和AI决策效率的黄金法则。

#### **VPA (Virtual Personal Assistant) 组件**

-   **VPA陪伴小窗 (VPA_Avatar_Widget)**
    -   **可用尺寸**: `2x2`, `2x4`, `3x3` (基于16x9网格)
    -   **用途**: 作为AI在非交互状态下的视觉化身和"陪伴者"。它提供了一个持续的、不打扰的存在感，通常在AI退居幕后执行任务时出现。
    -   **视觉设计**: 无边框、无背景的纯净形象展示，直接使用动态GIF资源（vpa2.gif）作为VPA形象，与桌面背景完美融合。

#### **新增场景化组件 (Based on Flagship Scene)**

-   **儿童教育卡片 (KidEducationCard)**
    -   **固定尺寸**: 8x9 (或更大，适用于视频)
    -   **用途**: 专门用于播放儿童视频或展示互动教育内容，是“家庭通勤”场景的核心组件。

-   **百科问答卡片 (PediaCard)**
    -   **固定尺寸**: 8x4
    -   **用途**: 以图文并茂或纯文本形式，展示对用户（尤其是儿童）问题的解答。

-   **订单状态卡片 (OrderStatusCard)**
    -   **固定尺寸**: 4x2
    -   **用途**: 显示第三方服务（如咖啡、外卖）的实时订单状态，增强用户对后台任务的感知。
    -   **内部布局**: [透明背景的动态VPA形象GIF]。

-   **VPA交互面板 (VPA_Interaction_Panel)**
    -   **可用尺寸**: `8x9`, `4x4` (基于16x9网格)
    -   **用途**: 这是VPA的"主工作台"。当需要与用户进行复杂对话、展示LLM的详细回答、或提供多步操作指引时，AI会加载此面板。它是系统启动时的主入口。
    -   **视觉设计**: VPA形象部分采用无边框设计，直接使用动态GIF资源（vpn1.gif）展示，与毛玻璃面板背景形成层次感。
    -   **内部布局 (ASCII)**:
        ```
        +----------------------+
        |   [VPA动态GIF-大]    |
        |  你好, 我是小绿。    |
        |  今天想去哪里?       |
        |                      |
        | [生成通勤桌面(按钮)] |
        | [播放音乐(按钮)]     |
        +----------------------+
        ```

#### **信息与控制卡片**

-   **灵动岛 (Dynamic_Island)**
    -   **可用尺寸**: `16x1`, `4x1` (基于16x9网格)
    -   **用途**: 持久化显示最高优先级的实时状态信息，如核心导航指令、重要提醒等。

-   **标准卡片 (Standard_Card)**
    -   **可用尺寸**: `8x4`, `4x2` (基于16x9网格)
    -   **用途**: 显示天气信息。
    -   **内部布局 (ASCII)**:
        ```
        +--------------------+
        | ☀️  28°C  深圳     |
        | 空气质量: 优       |
        |                    |
        +--------------------+
        ```

-   **大卡片 (Large_Card)**
    -   **可用尺寸**: `8x9`, `4x4` (基于16x9网格)
    -   **内部布局 (ASCII)**:
        ```
        +--------------------+
        | 正在播放: 星辰大海   |
        | 歌手: 黄霄雲         |
        | ≪   ▶   ≫      🔊  |
        +--------------------+
        ```


    -   **内部布局 (ASCII)**:
        ```
        +--------------------+
        | 今日待办 (3)       |
        | ☐ 09:00 参加晨会   |
        | ☑ 14:00 提交报告   |
        +--------------------+
        ```

-   **快捷指令卡片 (QuickActionCard)**
    -   **可用尺寸**: `4x2` (基于16x9网格)
    -   **内部布局 (ASCII)**:
        ```
        +--------------------+
        | 快捷指令           |
        | [导航回家] [开空调]  |
        | [播放音乐] [打电话]  |
        +--------------------+
        ```

#### **任务与内容面板**


    -   **用途**: 展示AI正在执行的后台任务进度，例如“正在为您规划通勤路线...”。
    -   **内部布局 (ASCII)**:
        ```
        +----------------------+
        | 任务引擎             |
        |                      |
        | 正在为您规划通勤路线...|
        | [▓▓▓▓▓▓▓░░░░░] 60%    |
        |                      |
        +----------------------+
        ```


    -   **内部布局 (ASCII)**:
        ```
        +----------------------+
        | 详细导航             |
        |                      |
        | --> 前方500米右转    |
        |      进入科技大道    |
        |                      |
        | 剩余里程: 5.2km      |
        +----------------------+
        ```


    -   **内部布局 (ASCII)**:
        ```
        +----------------------+
        | 今日新闻             |
        |                      |
        | - AI技术获得新突破   |
        | - 本地天气预警...    |
        | - 财经市场动态...    |
        |                      |
        +----------------------+
        ```

### **3.3. 组件动态行为与状态 (Component Dynamic Behavior & States)**

为了让设计系统与场景库的联系更紧密，本章节将根据 `场景.md` 中的具体用例，对核心组件的动态行为和不同状态进行补充说明。

#### **VPA (Virtual Personal Assistant) 组件**

-   **VPA陪伴小窗 (VPA_Avatar_Widget)**
    -   **状态: `受限模式 (Restricted Mode)`**
        -   **触发场景**: `访客/代驾模式`。
        -   **行为描述**: 在此模式下，VPA的形象保持待命，但所有涉及用户隐私的对话能力和数据访问都将被禁用。VPA的交互将仅限于基础的、非个性化的车辆控制指令。

-   **VPA交互面板 (VPA_Interaction_Panel)**
    -   **动态内容: `上下文感知 (Context-Aware)`**
        -   **触发场景**: `用户早高峰通勤` (及几乎所有需要用户输入的场景)。
        -   **行为描述**: 面板内的交互选项（如按钮和建议文本）并非固定不变，而是由AI根据当前时间、地点、用户习惯、车辆状态等多种因素动态生成。例如，在通勤场景中，AI会生成“生成通勤桌面”、“导航到公司”等高度相关的快捷指令。

#### **信息与控制卡片**

-   **灵动岛 (Dynamic_Island)**
    -   **内容多样性: `多模态状态显示 (Multi-modal Status Display)`**
        -   **行为描述**: 灵动岛是最高优先级的状态展示区域，其显示内容会根据核心任务动态变化，以最简洁的方式呈现关键信息。
        -   **内容示例 (来自场景库)**:
            -   **导航状态**: `[<- 8:00 前往: XX幼儿园, 预计: 30分到达]` (早高峰通勤)
            -   **车辆状态**: `[P] <--- 档位挂入P档, 驻车系统已激活]` (车内等待)
            -   **简洁导航**: `[灵动岛: 回家 - 剩余15分钟]` (雨夜的归途)
            -   **长途驾驶**: `[灵动岛: G2高速 - 距离下一出口 25km]` (长途高速)
            -   **模式状态**: `[访客模式已激活]` (访客/代驾模式)

-   **音乐控制卡片 (MusicControlCard)**
    -   **动态内容: `AI推荐 (AI Recommendation)`**
        -   **触发场景**: `下班通勤`。
        -   **行为描述**: 卡片内容不再是简单的上一首/下一首，而是由AI根据用户状态（如疲劳）和场景（如傍晚回家）主动推荐的特定歌单（如“日落大道”），将音乐播放与情感关怀相结合。

-   **智能家居卡片 (SmartHomeCard)**
    -   **状态同步: `远程控制与状态反馈 (Remote Control & Status Feedback)`**
        -   **触发场景**: `下班通勤`。
        -   **行为描述**: 该卡片能实时显示已连接的智能家居设备的状态（如客厅空调、空气净化器），并允许用户在返家途中进行远程预操作，实现车家互联的无缝体验。

-   **视频播放器 (VideoPlayerCard)**
    -   **模式锁定: `安全优先 (Safety First)`**
        -   **触发场景**: `车内等待/摸鱼`。
        -   **行为描述**: 仅当车辆完全停稳（挂入P档）后，该大尺寸视频卡片才会被AI允许加载到桌面上。一旦车辆开始行驶，该卡片会自动最小化或关闭，确保驾驶安全。

-   **多功能快捷卡片 (Multi-functional Quick Cards)**
    -   **行为描述**: 在特定场景下，AI会组合一系列小尺寸（2x2）或中尺寸（4x2）的卡片，以满足该场景下的高频需求。
    -   **组合示例 (来自场景库)**:
        -   **家庭出游**: `后座娱乐控制` + `查找最近的洗手间` + `零食提醒`。
        -   **长途驾驶**: `下一服务区信息` + `驾驶员状态监测` + `续航里程`。
        -   **访客模式**: `基础音乐控制` + `车辆基本设置`（功能受限）。

---

### 4. 未来迭代方向 (Roadmap)

*   **V2.0**: 实现完整的语音交互，用户可以通过自然语言与VPA对话，完成所有操作，包括指定卡片布局（“把新闻卡片放大”）。
*   **V2.1**: 引入更丰富的文生图主题，并允许用户自定义prompt。
*   **V2.5**: 开放卡片生态。允许第三方服务（如外卖APP、视频APP、社交软件）以标准化的卡片形式接入桌面，由VPA进行统一的智能调度。
*   **V3.0**: 与车辆控制深度集成，VPA可根据导航和路况信息，建议开启/关闭某些驾驶辅助功能。
*   **V3.1**: 与外部应用生态打通，如直接在车机上完成咖啡下单和支付。



## 新增组件库扩展

### 智能语音交互组件

#### 语音状态指示器
```
+------------------+
| 🎤 正在聆听...   |
| ●●●○○○○○○○      |
| [取消]           |
+------------------+
```

### ADAS状态可视化组件

#### 驾驶辅助状态面板
```
+--------------------------------+
| 🚗 驾驶辅助状态                |
| ✅ 自适应巡航 (120km/h)        |
| ✅ 车道保持                    |
| ⚠️  盲区监测 (左侧有车)        |
| ❌ 自动泊车 (未激活)           |
+--------------------------------+
```

#### 安全预警组件
```
+--------------------------------+
| ⚠️  安全提醒                   |
| 前方急刹车风险                 |
| 建议减速至80km/h               |
| [知道了] [忽略]                |
+--------------------------------+
```

### 车联网服务组件

#### 充电站推荐卡片
```
+--------------------------------+
| ⚡ 附近充电站                  |
| 📍 万达广场充电站 (2.3km)      |
| 💰 ¥1.2/度 | 🔋 快充可用        |
| ⏱️  预计充电时间: 45分钟        |
| [导航前往] [预约充电]          |
+--------------------------------+
```

#### 远程控制面板
```
+--------------------------------+
| 🏠 远程车辆控制                |
| 🌡️  空调: 22°C [调节]          |
| 🪟 车窗: 已关闭 [控制]         |
| 🔒 车门: 已锁定 [解锁]         |
| 💡 车灯: 关闭 [开启]           |
+--------------------------------+
```

### 个性化体验组件

#### 用户识别欢迎界面
```
+--------------------------------------------------+
| 👋 欢迎回来，张先生！                            |
| 🕐 上次使用: 昨天 18:30                         |
| 📊 今日推荐:                                    |
| • 🎵 根据心情播放轻松音乐                       |
| • 🗺️  避开拥堵路线 (节省15分钟)                |
| • ☕ 路过星巴克，要来杯咖啡吗？                 |
| [应用推荐] [自定义设置]                         |
+--------------------------------------------------+
```

#### 情绪感知反馈组件
```
+------------------+
| 😊 心情检测      |
| 当前状态: 愉悦   |
| 建议播放:        |
| 🎵 轻快音乐      |
| [接受] [更换]    |
+------------------+
```

### 安全隐私组件

#### 访客模式界面
```
+--------------------------------------------------+
| 🔒 访客模式已激活                               |
| • 个人数据已隐藏                                |
| • 仅提供基础功能                                |
| • 使用结束后将清除临时数据                      |
|                                                  |
| 可用功能:                                        |
| [🗺️ 导航] [🎵 音乐] [🌡️ 空调] [📞 紧急联系]    |
|                                                  |
| 退出访客模式请输入PIN: [____]                   |
+--------------------------------------------------+
```

#### 数据隐私设置
```
+--------------------------------+
| 🛡️  隐私设置                   |
| 📍 位置信息: 仅行程中使用      |
| 🎤 语音数据: 本地处理          |
| 📊 使用统计: 匿名收集          |
| 🔄 数据同步: 加密传输          |
| [详细设置] [重置隐私]          |
+--------------------------------+
```

---

## 新增场景布局原型

### 智能充电场景布局
```
+--------------------------------------------------------------------------+
| [灵动岛: 充电中 45% | 预计完成时间: 14:30]                              |
+--------------------------------------------------------------------------+
|                                                                          |
|                        充电站环境背景                                   |
|                                                                          |
|                                                                          |
|  [VPA头像]                                                              |
|   (^.^) "充电期间，要不要看个电影？"                                    |
|                                                                          |
| +------------------+ +-------------------+ +----------------------------+ |
| | ⚡ 充电状态       | | 🎬 娱乐推荐       | | 🛒 附近商店                 | |
| | 45% (23kWh)      | | [电影] [音乐]     | | [咖啡店] [超市] [餐厅]      | |
| | 剩余时间: 25分钟 | | [播客] [有声书]   | | 步行距离: 2-5分钟           | |
| +------------------+ +-------------------+ +----------------------------+ |
+--------------------------------------------------------------------------+
```

### 家庭出行模式布局
```
+--------------------------------------------------------------------------+
| [灵动岛: 家庭模式 | 目的地: 动物园 | 预计到达: 15:20]                     |
+--------------------------------------------------------------------------+
|                                                                          |
|                        家庭友好背景主题                                 |
|                                                                          |
|                                                                          |
|  [VPA头像]                                                              |
|   (o.o) "小朋友们，还有10分钟就到啦！"                                  |
|                                                                          |
| +------------------+ +-------------------+ +----------------------------+ |
| | 🎵 儿童音乐       | | 🎮 后座娱乐       | | 🚻 便民服务                 | |
| | ♪ 小星星         | | [动画片] [游戏]   | | [洗手间] [母婴室]           | |
| | [上一首|暂停|下一首] | | [故事] [儿歌]     | | [餐厅] [药店]               | |
| +------------------+ +-------------------+ +----------------------------+ |
+--------------------------------------------------------------------------+
```

### 疲劳检测预警布局
```
+--------------------------------------------------------------------------+
| [灵动岛: ⚠️ 疲劳驾驶预警 | 建议休息]                                    |
+--------------------------------------------------------------------------+
|                                                                          |
|                        警示色背景渐变                                   |
|                                                                          |
|    +--------------------------------------------------+                  |
|    | ⚠️  检测到疲劳驾驶                               |                  |
|    | 建议立即休息或更换驾驶员                         |                  |
|    |                                                  |                  |
|    | [查找休息区] [播放提神音乐] [联系家人代驾]       |                  |
|    +--------------------------------------------------+                  |
|                                                                          |
| +------------------+ +-------------------+ +----------------------------+ |
| | 🛣️  最近服务区    | | ☕ 提神建议        | | 📞 紧急联系人               | |
| | 距离: 5km        | | [咖啡] [洗脸]     | | [家人] [朋友] [代驾]        | |
| | 设施: 全套       | | [休息] [换人]     | | [一键呼叫]                  | |
| +------------------+ +-------------------+ +----------------------------+ |
+--------------------------------------------------------------------------+
```

### 多用户切换界面
```
+--------------------------------------------------------------------------+
| [灵动岛: 用户切换模式]                                                   |
+--------------------------------------------------------------------------+
|                                                                          |
|                        中性背景主题                                     |
|                                                                          |
|              +----------------------------------------+                    |
|              | 👥 选择用户配置文件                    |                    |
|              |                                        |                    |
|              | [👨 张先生]  [👩 李女士]  [👶 儿童模式] |                    |
|              |   主驾驶员    副驾驶员     后排乘客   |                    |
|              |                                        |                    |
|              | [🎤 语音识别] [👤 访客模式] [⚙️ 设置]  |                    |
|              |                                        |                    |
|              +----------------------------------------+                    |
|                                                                          |
+--------------------------------------------------------------------------+
```