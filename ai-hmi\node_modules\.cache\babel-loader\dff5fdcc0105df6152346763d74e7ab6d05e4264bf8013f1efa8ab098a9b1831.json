{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, normalizeClass as _normalizeClass, withModifiers as _withModifiers, normalizeStyle as _normalizeStyle, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  key: 0,\n  class: \"ai-optimization-banner\"\n};\nconst _hoisted_2 = {\n  class: \"optimization-content\"\n};\nconst _hoisted_3 = {\n  class: \"optimization-text\"\n};\nconst _hoisted_4 = {\n  class: \"optimization-meta\"\n};\nconst _hoisted_5 = {\n  class: \"time-saved\"\n};\nconst _hoisted_6 = {\n  class: \"confidence\"\n};\nconst _hoisted_7 = {\n  class: \"schedule-list\"\n};\nconst _hoisted_8 = {\n  class: \"schedule-header\"\n};\nconst _hoisted_9 = {\n  class: \"schedule-date\"\n};\nconst _hoisted_10 = {\n  class: \"events-container\"\n};\nconst _hoisted_11 = [\"onClick\"];\nconst _hoisted_12 = {\n  class: \"event-time\"\n};\nconst _hoisted_13 = {\n  class: \"time-text\"\n};\nconst _hoisted_14 = {\n  class: \"event-content\"\n};\nconst _hoisted_15 = {\n  class: \"event-title\"\n};\nconst _hoisted_16 = {\n  key: 0,\n  class: \"event-location\"\n};\nconst _hoisted_17 = {\n  key: 1,\n  class: \"event-ai-suggestion\"\n};\nconst _hoisted_18 = {\n  class: \"event-actions\"\n};\nconst _hoisted_19 = [\"onClick\", \"disabled\"];\nconst _hoisted_20 = [\"onClick\"];\nconst _hoisted_21 = {\n  key: 1,\n  class: \"time-management\"\n};\nconst _hoisted_22 = {\n  class: \"management-content\"\n};\nconst _hoisted_23 = {\n  class: \"travel-time-analysis\"\n};\nconst _hoisted_24 = {\n  class: \"analysis-item\"\n};\nconst _hoisted_25 = {\n  class: \"value\"\n};\nconst _hoisted_26 = {\n  class: \"analysis-item\"\n};\nconst _hoisted_27 = {\n  class: \"value highlight\"\n};\nconst _hoisted_28 = {\n  class: \"time-buffer\"\n};\nconst _hoisted_29 = {\n  class: \"buffer-info\"\n};\nconst _hoisted_30 = {\n  class: \"buffer-bar\"\n};\nconst _hoisted_31 = {\n  key: 2,\n  class: \"quick-actions\"\n};\nconst _hoisted_32 = [\"onClick\", \"disabled\"];\nconst _hoisted_33 = {\n  key: 3,\n  class: \"loading-overlay\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_BaseCard = _resolveComponent(\"BaseCard\");\n  return _openBlock(), _createBlock(_component_BaseCard, {\n    \"card-type\": 'ai-schedule',\n    size: $props.size,\n    position: $props.position,\n    theme: $props.theme,\n    \"theme-colors\": $props.themeColors,\n    \"show-header\": true,\n    \"show-footer\": false,\n    title: $setup.cardTitle,\n    icon: 'fas fa-calendar-alt',\n    class: \"ai-schedule-assistant-card\"\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"schedule-container\", [`size-${$props.size}`, `mode-${$props.displayMode}`]])\n    }, [_createCommentVNode(\" AI优化建议横幅 \"), $setup.aiOptimization && $setup.aiOptimization.suggestion ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n      class: \"optimization-icon\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-magic\"\n    })], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_2, [_cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n      class: \"optimization-title\"\n    }, \"AI智能建议\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, _toDisplayString($setup.aiOptimization.suggestion), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"span\", _hoisted_5, \"节省 \" + _toDisplayString($setup.aiOptimization.timeSaved), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_6, \"可信度 \" + _toDisplayString(Math.round($setup.aiOptimization.confidence * 100)) + \"%\", 1 /* TEXT */)])]), _createElementVNode(\"button\", {\n      class: \"apply-suggestion-btn\",\n      onClick: _cache[0] || (_cache[0] = (...args) => $setup.applySuggestion && $setup.applySuggestion(...args))\n    }, _cache[2] || (_cache[2] = [_createElementVNode(\"i\", {\n      class: \"fas fa-check\"\n    }, null, -1 /* CACHED */)]))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 今日日程列表 \"), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_cache[4] || (_cache[4] = _createElementVNode(\"h3\", {\n      class: \"schedule-title\"\n    }, \"今日日程\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_9, _toDisplayString($setup.currentDate), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_10, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.todayEvents, event => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: event.id,\n        class: _normalizeClass(['event-item', `priority-${event.priority}`, `status-${event.status}`]),\n        onClick: $event => $setup.handleEventClick(event)\n      }, [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, _toDisplayString(event.time), 1 /* TEXT */), _createElementVNode(\"div\", {\n        class: _normalizeClass([\"time-indicator\", $setup.getTimeStatus(event)])\n      }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, _toDisplayString(event.title), 1 /* TEXT */), event.location ? (_openBlock(), _createElementBlock(\"div\", _hoisted_16, [_cache[5] || (_cache[5] = _createElementVNode(\"i\", {\n        class: \"fas fa-map-marker-alt\"\n      }, null, -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString(event.location), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" AI建议 \"), event.aiSuggestion ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [_cache[6] || (_cache[6] = _createElementVNode(\"i\", {\n        class: \"fas fa-lightbulb\"\n      }, null, -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString(event.aiSuggestion), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_18, [event.status === 'upcoming' ? (_openBlock(), _createElementBlock(\"button\", {\n        key: 0,\n        onClick: _withModifiers($event => $setup.startNavigation(event), [\"stop\"]),\n        class: \"nav-btn\",\n        disabled: !event.location\n      }, [...(_cache[7] || (_cache[7] = [_createElementVNode(\"i\", {\n        class: \"fas fa-route\"\n      }, null, -1 /* CACHED */)]))], 8 /* PROPS */, _hoisted_19)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"button\", {\n        onClick: _withModifiers($event => $setup.toggleEventReminder(event), [\"stop\"]),\n        class: _normalizeClass(['reminder-btn', {\n          active: event.reminderEnabled\n        }])\n      }, [...(_cache[8] || (_cache[8] = [_createElementVNode(\"i\", {\n        class: \"fas fa-bell\"\n      }, null, -1 /* CACHED */)]))], 10 /* CLASS, PROPS */, _hoisted_20)])], 10 /* CLASS, PROPS */, _hoisted_11);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 智能时间管理 \"), $props.showTimeManagement ? (_openBlock(), _createElementBlock(\"div\", _hoisted_21, [_cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n      class: \"management-header\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-clock\"\n    }), _createElementVNode(\"span\", null, \"智能时间管理\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, [_cache[9] || (_cache[9] = _createElementVNode(\"span\", {\n      class: \"label\"\n    }, \"当前位置到下个会议\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_25, _toDisplayString($setup.travelTimeToNext), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_26, [_cache[10] || (_cache[10] = _createElementVNode(\"span\", {\n      class: \"label\"\n    }, \"建议出发时间\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_27, _toDisplayString($setup.suggestedDepartureTime), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"span\", null, \"缓冲时间: \" + _toDisplayString($setup.timeBuffer), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_30, [_createElementVNode(\"div\", {\n      class: \"buffer-fill\",\n      style: _normalizeStyle({\n        width: $setup.bufferPercentage + '%'\n      })\n    }, null, 4 /* STYLE */)])])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 快速操作 \"), $props.showQuickActions ? (_openBlock(), _createElementBlock(\"div\", _hoisted_31, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.quickActions, action => {\n      return _openBlock(), _createElementBlock(\"button\", {\n        key: action.id,\n        onClick: $event => $setup.handleQuickAction(action),\n        class: \"quick-action-btn\",\n        disabled: $setup.isProcessing\n      }, [_createElementVNode(\"i\", {\n        class: _normalizeClass(action.icon)\n      }, null, 2 /* CLASS */), _createElementVNode(\"span\", null, _toDisplayString(action.label), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_32);\n    }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 加载状态 \"), $setup.isLoading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_33, _cache[12] || (_cache[12] = [_createElementVNode(\"div\", {\n      class: \"loading-spinner\"\n    }, null, -1 /* CACHED */), _createElementVNode(\"div\", {\n      class: \"loading-text\"\n    }, \"正在优化您的日程...\", -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"size\", \"position\", \"theme\", \"theme-colors\", \"title\"]);\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_component_BaseCard", "size", "$props", "position", "theme", "themeColors", "title", "$setup", "cardTitle", "icon", "_createElementVNode", "_normalizeClass", "displayMode", "_createCommentVNode", "aiOptimization", "suggestion", "_createElementBlock", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_toDisplayString", "_hoisted_4", "_hoisted_5", "timeSaved", "_hoisted_6", "Math", "round", "confidence", "onClick", "_cache", "args", "applySuggestion", "_hoisted_7", "_hoisted_8", "_hoisted_9", "currentDate", "_hoisted_10", "_Fragment", "_renderList", "todayEvents", "event", "key", "id", "priority", "status", "$event", "handleEventClick", "_hoisted_12", "_hoisted_13", "time", "getTimeStatus", "_hoisted_14", "_hoisted_15", "location", "_hoisted_16", "aiSuggestion", "_hoisted_17", "_hoisted_18", "_withModifiers", "startNavigation", "disabled", "toggleEventReminder", "active", "reminderEnabled", "showTimeManagement", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "travelTimeToNext", "_hoisted_26", "_hoisted_27", "suggestedDepartureTime", "_hoisted_28", "_hoisted_29", "timeBuffer", "_hoisted_30", "style", "_normalizeStyle", "width", "bufferPercentage", "showQuickActions", "_hoisted_31", "quickActions", "action", "handleQuickAction", "isProcessing", "label", "isLoading", "_hoisted_33"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\AIScheduleAssistantCard.vue"], "sourcesContent": ["<template>\n  <BaseCard\n    :card-type=\"'ai-schedule'\"\n    :size=\"size\"\n    :position=\"position\"\n    :theme=\"theme\"\n    :theme-colors=\"themeColors\"\n    :show-header=\"true\"\n    :show-footer=\"false\"\n    :title=\"cardTitle\"\n    :icon=\"'fas fa-calendar-alt'\"\n    class=\"ai-schedule-assistant-card\"\n  >\n    <div class=\"schedule-container\" :class=\"[`size-${size}`, `mode-${displayMode}`]\">\n      <!-- AI优化建议横幅 -->\n      <div v-if=\"aiOptimization && aiOptimization.suggestion\" class=\"ai-optimization-banner\">\n        <div class=\"optimization-icon\">\n          <i class=\"fas fa-magic\"></i>\n        </div>\n        <div class=\"optimization-content\">\n          <div class=\"optimization-title\">AI智能建议</div>\n          <div class=\"optimization-text\">{{ aiOptimization.suggestion }}</div>\n          <div class=\"optimization-meta\">\n            <span class=\"time-saved\">节省 {{ aiOptimization.timeSaved }}</span>\n            <span class=\"confidence\">可信度 {{ Math.round(aiOptimization.confidence * 100) }}%</span>\n          </div>\n        </div>\n        <button class=\"apply-suggestion-btn\" @click=\"applySuggestion\">\n          <i class=\"fas fa-check\"></i>\n        </button>\n      </div>\n\n      <!-- 今日日程列表 -->\n      <div class=\"schedule-list\">\n        <div class=\"schedule-header\">\n          <h3 class=\"schedule-title\">今日日程</h3>\n          <div class=\"schedule-date\">{{ currentDate }}</div>\n        </div>\n        \n        <div class=\"events-container\">\n          <div \n            v-for=\"event in todayEvents\" \n            :key=\"event.id\"\n            :class=\"['event-item', `priority-${event.priority}`, `status-${event.status}`]\"\n            @click=\"handleEventClick(event)\"\n          >\n            <div class=\"event-time\">\n              <div class=\"time-text\">{{ event.time }}</div>\n              <div class=\"time-indicator\" :class=\"getTimeStatus(event)\"></div>\n            </div>\n            \n            <div class=\"event-content\">\n              <div class=\"event-title\">{{ event.title }}</div>\n              <div class=\"event-location\" v-if=\"event.location\">\n                <i class=\"fas fa-map-marker-alt\"></i>\n                <span>{{ event.location }}</span>\n              </div>\n              \n              <!-- AI建议 -->\n              <div v-if=\"event.aiSuggestion\" class=\"event-ai-suggestion\">\n                <i class=\"fas fa-lightbulb\"></i>\n                <span>{{ event.aiSuggestion }}</span>\n              </div>\n            </div>\n            \n            <div class=\"event-actions\">\n              <button \n                v-if=\"event.status === 'upcoming'\"\n                @click.stop=\"startNavigation(event)\"\n                class=\"nav-btn\"\n                :disabled=\"!event.location\"\n              >\n                <i class=\"fas fa-route\"></i>\n              </button>\n              \n              <button \n                @click.stop=\"toggleEventReminder(event)\"\n                :class=\"['reminder-btn', { active: event.reminderEnabled }]\"\n              >\n                <i class=\"fas fa-bell\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 智能时间管理 -->\n      <div class=\"time-management\" v-if=\"showTimeManagement\">\n        <div class=\"management-header\">\n          <i class=\"fas fa-clock\"></i>\n          <span>智能时间管理</span>\n        </div>\n        \n        <div class=\"management-content\">\n          <div class=\"travel-time-analysis\">\n            <div class=\"analysis-item\">\n              <span class=\"label\">当前位置到下个会议</span>\n              <span class=\"value\">{{ travelTimeToNext }}</span>\n            </div>\n            <div class=\"analysis-item\">\n              <span class=\"label\">建议出发时间</span>\n              <span class=\"value highlight\">{{ suggestedDepartureTime }}</span>\n            </div>\n          </div>\n          \n          <div class=\"time-buffer\">\n            <div class=\"buffer-info\">\n              <span>缓冲时间: {{ timeBuffer }}</span>\n              <div class=\"buffer-bar\">\n                <div class=\"buffer-fill\" :style=\"{ width: bufferPercentage + '%' }\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 快速操作 -->\n      <div class=\"quick-actions\" v-if=\"showQuickActions\">\n        <button \n          v-for=\"action in quickActions\" \n          :key=\"action.id\"\n          @click=\"handleQuickAction(action)\"\n          class=\"quick-action-btn\"\n          :disabled=\"isProcessing\"\n        >\n          <i :class=\"action.icon\"></i>\n          <span>{{ action.label }}</span>\n        </button>\n      </div>\n\n      <!-- 加载状态 -->\n      <div v-if=\"isLoading\" class=\"loading-overlay\">\n        <div class=\"loading-spinner\"></div>\n        <div class=\"loading-text\">正在优化您的日程...</div>\n      </div>\n    </div>\n  </BaseCard>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, onUnmounted } from 'vue'\nimport BaseCard from '@/components/cards/BaseCard.vue'\nimport mockDataService from '@/services/MockDataService.js'\n\nexport default {\n  name: 'AIScheduleAssistantCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    size: {\n      type: String,\n      default: 'medium',\n      validator: (value) => ['small', 'medium', 'large'].includes(value)\n    },\n    position: {\n      type: Object,\n      default: () => ({ x: 0, y: 0 })\n    },\n    theme: {\n      type: String,\n      default: 'glassmorphism'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({})\n    },\n    displayMode: {\n      type: String,\n      default: 'full',\n      validator: (value) => ['compact', 'standard', 'full'].includes(value)\n    },\n    showTimeManagement: {\n      type: Boolean,\n      default: true\n    },\n    showQuickActions: {\n      type: Boolean,\n      default: true\n    },\n    autoRefresh: {\n      type: Boolean,\n      default: true\n    },\n    refreshInterval: {\n      type: Number,\n      default: 60000 // 1分钟\n    }\n  },\n  emits: ['event-click', 'navigation-start', 'suggestion-applied', 'action-triggered'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const isLoading = ref(false)\n    const isProcessing = ref(false)\n    const todayEvents = ref([])\n    const aiOptimization = ref(null)\n    const currentDate = ref('')\n    const travelTimeToNext = ref('--')\n    const suggestedDepartureTime = ref('--')\n    const timeBuffer = ref('5分钟')\n    const bufferPercentage = ref(75)\n    \n    // 快速操作\n    const quickActions = ref([\n      {\n        id: 1,\n        label: '添加事件',\n        icon: 'fas fa-plus',\n        action: 'addEvent'\n      },\n      {\n        id: 2,\n        label: '查看周视图',\n        icon: 'fas fa-calendar-week',\n        action: 'weekView'\n      },\n      {\n        id: 3,\n        label: 'AI优化',\n        icon: 'fas fa-magic',\n        action: 'aiOptimize'\n      }\n    ])\n\n    // 计算属性\n    const cardTitle = computed(() => {\n      const upcomingCount = todayEvents.value.filter(e => e.status === 'upcoming').length\n      return upcomingCount > 0 ? `日程助手 (${upcomingCount}个待办)` : '日程助手'\n    })\n\n    // 方法\n    const updateCurrentDate = () => {\n      const now = new Date()\n      currentDate.value = now.toLocaleDateString('zh-CN', {\n        month: 'long',\n        day: 'numeric',\n        weekday: 'long'\n      })\n    }\n\n    const getTimeStatus = (event) => {\n      const now = new Date()\n      const eventTime = new Date()\n      const [hours, minutes] = event.time.split(':').map(Number)\n      eventTime.setHours(hours, minutes, 0, 0)\n      \n      const timeDiff = eventTime.getTime() - now.getTime()\n      const minutesDiff = Math.floor(timeDiff / (1000 * 60))\n      \n      if (minutesDiff < 0) return 'past'\n      if (minutesDiff <= 15) return 'imminent'\n      if (minutesDiff <= 60) return 'soon'\n      return 'future'\n    }\n\n    const loadScheduleData = async () => {\n      try {\n        isLoading.value = true\n        const scheduleData = await mockDataService.getScheduleData()\n        \n        todayEvents.value = scheduleData.todayEvents || []\n        aiOptimization.value = scheduleData.aiOptimization || null\n        \n        // 更新时间管理信息\n        updateTimeManagement()\n        \n      } catch (error) {\n        console.error('Failed to load schedule data:', error)\n      } finally {\n        isLoading.value = false\n      }\n    }\n\n    const updateTimeManagement = () => {\n      const upcomingEvents = todayEvents.value.filter(e => e.status === 'upcoming')\n      if (upcomingEvents.length > 0) {\n        const nextEvent = upcomingEvents[0]\n        \n        // 模拟计算行程时间\n        travelTimeToNext.value = '15分钟'\n        \n        // 计算建议出发时间\n        const eventTime = new Date()\n        const [hours, minutes] = nextEvent.time.split(':').map(Number)\n        eventTime.setHours(hours, minutes, 0, 0)\n        \n        const departureTime = new Date(eventTime.getTime() - 20 * 60 * 1000) // 提前20分钟\n        suggestedDepartureTime.value = departureTime.toLocaleTimeString('zh-CN', {\n          hour: '2-digit',\n          minute: '2-digit'\n        })\n      }\n    }\n\n    const handleEventClick = (event) => {\n      emit('event-click', event)\n    }\n\n    const startNavigation = (event) => {\n      if (!event.location) return\n      \n      emit('navigation-start', {\n        destination: event.location,\n        event: event\n      })\n    }\n\n    const toggleEventReminder = (event) => {\n      event.reminderEnabled = !event.reminderEnabled\n      // 这里可以调用API保存提醒设置\n    }\n\n    const applySuggestion = async () => {\n      if (!aiOptimization.value) return\n      \n      try {\n        isProcessing.value = true\n        \n        // 模拟应用建议的过程\n        await new Promise(resolve => setTimeout(resolve, 1000))\n        \n        emit('suggestion-applied', aiOptimization.value)\n        \n        // 重新加载数据\n        await loadScheduleData()\n        \n      } catch (error) {\n        console.error('Failed to apply suggestion:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n\n    const handleQuickAction = async (action) => {\n      emit('action-triggered', action)\n      \n      switch (action.action) {\n        case 'aiOptimize':\n          await performAIOptimization()\n          break\n        case 'addEvent':\n          // 触发添加事件对话框\n          break\n        case 'weekView':\n          // 切换到周视图\n          break\n      }\n    }\n\n    const performAIOptimization = async () => {\n      try {\n        isProcessing.value = true\n        \n        // 模拟AI优化过程\n        await new Promise(resolve => setTimeout(resolve, 2000))\n        \n        // 生成新的优化建议\n        aiOptimization.value = {\n          suggestion: '建议将下午会议提前30分钟，避开交通高峰',\n          timeSaved: '15分钟',\n          confidence: 0.88\n        }\n        \n      } catch (error) {\n        console.error('AI optimization failed:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n\n    // 生命周期\n    let refreshTimer = null\n    \n    onMounted(async () => {\n      await mockDataService.initialize()\n      updateCurrentDate()\n      await loadScheduleData()\n      \n      // 设置自动刷新\n      if (props.autoRefresh) {\n        refreshTimer = setInterval(() => {\n          updateCurrentDate()\n          updateTimeManagement()\n        }, props.refreshInterval)\n      }\n    })\n\n    onUnmounted(() => {\n      if (refreshTimer) {\n        clearInterval(refreshTimer)\n      }\n    })\n\n    return {\n      // 响应式数据\n      isLoading,\n      isProcessing,\n      todayEvents,\n      aiOptimization,\n      currentDate,\n      travelTimeToNext,\n      suggestedDepartureTime,\n      timeBuffer,\n      bufferPercentage,\n      quickActions,\n      \n      // 计算属性\n      cardTitle,\n      \n      // 方法\n      getTimeStatus,\n      handleEventClick,\n      startNavigation,\n      toggleEventReminder,\n      applySuggestion,\n      handleQuickAction\n    }\n  }\n}\n</script>\n\n<style scoped>\n.ai-schedule-assistant-card {\n  height: 100%;\n}\n\n.schedule-container {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  padding: 16px;\n  position: relative;\n}\n\n/* AI优化建议横幅 */\n.ai-optimization-banner {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background: linear-gradient(135deg, rgba(74, 144, 226, 0.2) 0%, rgba(126, 211, 33, 0.2) 100%);\n  border-radius: 12px;\n  border: 1px solid rgba(74, 144, 226, 0.3);\n  backdrop-filter: blur(10px);\n}\n\n.optimization-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #4a90e2 0%, #7ed321 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 16px;\n  flex-shrink: 0;\n}\n\n.optimization-content {\n  flex: 1;\n}\n\n.optimization-title {\n  font-size: 12px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 4px;\n}\n\n.optimization-text {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.8);\n  line-height: 1.4;\n  margin-bottom: 6px;\n}\n\n.optimization-meta {\n  display: flex;\n  gap: 12px;\n  font-size: 11px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.time-saved {\n  color: #7ed321;\n}\n\n.confidence {\n  color: #4a90e2;\n}\n\n.apply-suggestion-btn {\n  width: 36px;\n  height: 36px;\n  border: none;\n  border-radius: 8px;\n  background: rgba(126, 211, 33, 0.3);\n  color: #7ed321;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.apply-suggestion-btn:hover {\n  background: rgba(126, 211, 33, 0.5);\n  transform: scale(1.05);\n}\n\n/* 日程列表 */\n.schedule-list {\n  flex: 1;\n  min-height: 0;\n}\n\n.schedule-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.schedule-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n  margin: 0;\n}\n\n.schedule-date {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.events-container {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.event-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 12px;\n  border-left: 4px solid transparent;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.event-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateX(2px);\n}\n\n.event-item.priority-high {\n  border-left-color: #ef4444;\n}\n\n.event-item.priority-medium {\n  border-left-color: #f59e0b;\n}\n\n.event-item.priority-low {\n  border-left-color: #10b981;\n}\n\n.event-item.status-past {\n  opacity: 0.6;\n}\n\n.event-time {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 4px;\n  min-width: 60px;\n}\n\n.time-text {\n  font-size: 14px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.time-indicator {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.3);\n}\n\n.time-indicator.imminent {\n  background: #ef4444;\n  animation: pulse 1.5s infinite;\n}\n\n.time-indicator.soon {\n  background: #f59e0b;\n}\n\n.time-indicator.future {\n  background: #10b981;\n}\n\n.time-indicator.past {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.event-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.event-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.event-location {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.event-ai-suggestion {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 11px;\n  color: #7ed321;\n  background: rgba(126, 211, 33, 0.1);\n  padding: 4px 8px;\n  border-radius: 6px;\n  margin-top: 4px;\n}\n\n.event-actions {\n  display: flex;\n  gap: 4px;\n}\n\n.nav-btn,\n.reminder-btn {\n  width: 28px;\n  height: 28px;\n  border: none;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0.2);\n  color: rgba(255, 255, 255, 0.7);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n}\n\n.nav-btn:hover,\n.reminder-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.reminder-btn.active {\n  background: rgba(74, 144, 226, 0.3);\n  color: #4a90e2;\n}\n\n/* 智能时间管理 */\n.time-management {\n  padding: 12px;\n  background: rgba(0, 0, 0, 0.1);\n  border-radius: 12px;\n}\n\n.management-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 12px;\n}\n\n.management-content {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.travel-time-analysis {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n}\n\n.analysis-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 12px;\n}\n\n.analysis-item .label {\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.analysis-item .value {\n  color: rgba(255, 255, 255, 0.9);\n  font-weight: 500;\n}\n\n.analysis-item .value.highlight {\n  color: #7ed321;\n}\n\n.time-buffer {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n}\n\n.buffer-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.buffer-bar {\n  height: 4px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 2px;\n  overflow: hidden;\n}\n\n.buffer-fill {\n  height: 100%;\n  background: linear-gradient(90deg, #7ed321 0%, #4a90e2 100%);\n  transition: width 0.3s ease;\n}\n\n/* 快速操作 */\n.quick-actions {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.quick-action-btn {\n  flex: 1;\n  min-width: 80px;\n  padding: 8px 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border: none;\n  border-radius: 8px;\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 6px;\n}\n\n.quick-action-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n  transform: translateY(-1px);\n}\n\n.quick-action-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* 加载状态 */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.3);\n  backdrop-filter: blur(5px);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 12px;\n  border-radius: 12px;\n}\n\n.loading-spinner {\n  width: 32px;\n  height: 32px;\n  border: 3px solid rgba(255, 255, 255, 0.2);\n  border-top: 3px solid #4a90e2;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.loading-text {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* 动画 */\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n}\n\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* 响应式设计 */\n.size-small .schedule-container {\n  padding: 12px;\n  gap: 12px;\n}\n\n.size-small .event-item {\n  padding: 8px;\n}\n\n.size-small .optimization-banner {\n  padding: 8px;\n}\n\n.size-large .schedule-container {\n  padding: 20px;\n  gap: 20px;\n}\n\n.mode-compact .events-container {\n  max-height: 200px;\n}\n\n.mode-compact .time-management,\n.mode-compact .quick-actions {\n  display: none;\n}\n</style>"], "mappings": ";;;EAe8DA,KAAK,EAAC;;;EAIvDA,KAAK,EAAC;AAAsB;;EAE1BA,KAAK,EAAC;AAAmB;;EACzBA,KAAK,EAAC;AAAmB;;EACtBA,KAAK,EAAC;AAAY;;EAClBA,KAAK,EAAC;AAAY;;EASzBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAe;;EAGvBA,KAAK,EAAC;AAAkB;;;EAOpBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAW;;EAInBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAa;;;EACnBA,KAAK,EAAC;;;;EAMoBA,KAAK,EAAC;;;EAMlCA,KAAK,EAAC;AAAe;;;;;EAsB3BA,KAAK,EAAC;;;EAMJA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAe;;EAElBA,KAAK,EAAC;AAAO;;EAEhBA,KAAK,EAAC;AAAe;;EAElBA,KAAK,EAAC;AAAiB;;EAI5BA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAY;;;EAS1BA,KAAK,EAAC;;;;;EAcWA,KAAK,EAAC;;;;uBAlIhCC,YAAA,CAuIWC,mBAAA;IAtIR,WAAS,EAAE,aAAa;IACxBC,IAAI,EAAEC,MAAA,CAAAD,IAAI;IACVE,QAAQ,EAAED,MAAA,CAAAC,QAAQ;IAClBC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACZ,cAAY,EAAEF,MAAA,CAAAG,WAAW;IACzB,aAAW,EAAE,IAAI;IACjB,aAAW,EAAE,KAAK;IAClBC,KAAK,EAAEC,MAAA,CAAAC,SAAS;IAChBC,IAAI,EAAE,qBAAqB;IAC5BX,KAAK,EAAC;;sBAEN,MA0HM,CA1HNY,mBAAA,CA0HM;MA1HDZ,KAAK,EAAAa,eAAA,EAAC,oBAAoB,WAAkBT,MAAA,CAAAD,IAAI,YAAYC,MAAA,CAAAU,WAAW;QAC1EC,mBAAA,cAAiB,EACNN,MAAA,CAAAO,cAAc,IAAIP,MAAA,CAAAO,cAAc,CAACC,UAAU,I,cAAtDC,mBAAA,CAeM,OAfNC,UAeM,G,0BAdJP,mBAAA,CAEM;MAFDZ,KAAK,EAAC;IAAmB,IAC5BY,mBAAA,CAA4B;MAAzBZ,KAAK,EAAC;IAAc,G,qBAEzBY,mBAAA,CAOM,OAPNQ,UAOM,G,0BANJR,mBAAA,CAA4C;MAAvCZ,KAAK,EAAC;IAAoB,GAAC,QAAM,qBACtCY,mBAAA,CAAoE,OAApES,UAAoE,EAAAC,gBAAA,CAAlCb,MAAA,CAAAO,cAAc,CAACC,UAAU,kBAC3DL,mBAAA,CAGM,OAHNW,UAGM,GAFJX,mBAAA,CAAiE,QAAjEY,UAAiE,EAAxC,KAAG,GAAAF,gBAAA,CAAGb,MAAA,CAAAO,cAAc,CAACS,SAAS,kBACvDb,mBAAA,CAAsF,QAAtFc,UAAsF,EAA7D,MAAI,GAAAJ,gBAAA,CAAGK,IAAI,CAACC,KAAK,CAACnB,MAAA,CAAAO,cAAc,CAACa,UAAU,WAAU,GAAC,gB,KAGnFjB,mBAAA,CAES;MAFDZ,KAAK,EAAC,sBAAsB;MAAE8B,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEvB,MAAA,CAAAwB,eAAA,IAAAxB,MAAA,CAAAwB,eAAA,IAAAD,IAAA,CAAe;kCAC1DpB,mBAAA,CAA4B;MAAzBZ,KAAK,EAAC;IAAc,0B,2CAI3Be,mBAAA,YAAe,EACfH,mBAAA,CAmDM,OAnDNsB,UAmDM,GAlDJtB,mBAAA,CAGM,OAHNuB,UAGM,G,0BAFJvB,mBAAA,CAAoC;MAAhCZ,KAAK,EAAC;IAAgB,GAAC,MAAI,qBAC/BY,mBAAA,CAAkD,OAAlDwB,UAAkD,EAAAd,gBAAA,CAApBb,MAAA,CAAA4B,WAAW,iB,GAG3CzB,mBAAA,CA4CM,OA5CN0B,WA4CM,I,kBA3CJpB,mBAAA,CA0CMqB,SAAA,QAAAC,WAAA,CAzCY/B,MAAA,CAAAgC,WAAW,EAApBC,KAAK;2BADdxB,mBAAA,CA0CM;QAxCHyB,GAAG,EAAED,KAAK,CAACE,EAAE;QACb5C,KAAK,EAAAa,eAAA,4BAA6B6B,KAAK,CAACG,QAAQ,cAAcH,KAAK,CAACI,MAAM;QAC1EhB,OAAK,EAAAiB,MAAA,IAAEtC,MAAA,CAAAuC,gBAAgB,CAACN,KAAK;UAE9B9B,mBAAA,CAGM,OAHNqC,WAGM,GAFJrC,mBAAA,CAA6C,OAA7CsC,WAA6C,EAAA5B,gBAAA,CAAnBoB,KAAK,CAACS,IAAI,kBACpCvC,mBAAA,CAAgE;QAA3DZ,KAAK,EAAAa,eAAA,EAAC,gBAAgB,EAASJ,MAAA,CAAA2C,aAAa,CAACV,KAAK;iCAGzD9B,mBAAA,CAYM,OAZNyC,WAYM,GAXJzC,mBAAA,CAAgD,OAAhD0C,WAAgD,EAAAhC,gBAAA,CAApBoB,KAAK,CAAClC,KAAK,kBACLkC,KAAK,CAACa,QAAQ,I,cAAhDrC,mBAAA,CAGM,OAHNsC,WAGM,G,0BAFJ5C,mBAAA,CAAqC;QAAlCZ,KAAK,EAAC;MAAuB,4BAChCY,mBAAA,CAAiC,cAAAU,gBAAA,CAAxBoB,KAAK,CAACa,QAAQ,iB,wCAGzBxC,mBAAA,UAAa,EACF2B,KAAK,CAACe,YAAY,I,cAA7BvC,mBAAA,CAGM,OAHNwC,WAGM,G,0BAFJ9C,mBAAA,CAAgC;QAA7BZ,KAAK,EAAC;MAAkB,4BAC3BY,mBAAA,CAAqC,cAAAU,gBAAA,CAA5BoB,KAAK,CAACe,YAAY,iB,0CAI/B7C,mBAAA,CAgBM,OAhBN+C,WAgBM,GAdIjB,KAAK,CAACI,MAAM,mB,cADpB5B,mBAAA,CAOS;;QALNY,OAAK,EAAA8B,cAAA,CAAAb,MAAA,IAAOtC,MAAA,CAAAoD,eAAe,CAACnB,KAAK;QAClC1C,KAAK,EAAC,SAAS;QACd8D,QAAQ,GAAGpB,KAAK,CAACa;yCAElB3C,mBAAA,CAA4B;QAAzBZ,KAAK,EAAC;MAAc,0B,uEAGzBY,mBAAA,CAKS;QAJNkB,OAAK,EAAA8B,cAAA,CAAAb,MAAA,IAAOtC,MAAA,CAAAsD,mBAAmB,CAACrB,KAAK;QACrC1C,KAAK,EAAAa,eAAA;UAAAmD,MAAA,EAA6BtB,KAAK,CAACuB;QAAe;yCAExDrD,mBAAA,CAA2B;QAAxBZ,KAAK,EAAC;MAAa,0B;wCAOhCe,mBAAA,YAAe,EACoBX,MAAA,CAAA8D,kBAAkB,I,cAArDhD,mBAAA,CA2BM,OA3BNiD,WA2BM,G,4BA1BJvD,mBAAA,CAGM;MAHDZ,KAAK,EAAC;IAAmB,IAC5BY,mBAAA,CAA4B;MAAzBZ,KAAK,EAAC;IAAc,IACvBY,mBAAA,CAAmB,cAAb,QAAM,E,qBAGdA,mBAAA,CAoBM,OApBNwD,WAoBM,GAnBJxD,mBAAA,CASM,OATNyD,WASM,GARJzD,mBAAA,CAGM,OAHN0D,WAGM,G,0BAFJ1D,mBAAA,CAAoC;MAA9BZ,KAAK,EAAC;IAAO,GAAC,WAAS,qBAC7BY,mBAAA,CAAiD,QAAjD2D,WAAiD,EAAAjD,gBAAA,CAA1Bb,MAAA,CAAA+D,gBAAgB,iB,GAEzC5D,mBAAA,CAGM,OAHN6D,WAGM,G,4BAFJ7D,mBAAA,CAAiC;MAA3BZ,KAAK,EAAC;IAAO,GAAC,QAAM,qBAC1BY,mBAAA,CAAiE,QAAjE8D,WAAiE,EAAApD,gBAAA,CAAhCb,MAAA,CAAAkE,sBAAsB,iB,KAI3D/D,mBAAA,CAOM,OAPNgE,WAOM,GANJhE,mBAAA,CAKM,OALNiE,WAKM,GAJJjE,mBAAA,CAAmC,cAA7B,QAAM,GAAAU,gBAAA,CAAGb,MAAA,CAAAqE,UAAU,kBACzBlE,mBAAA,CAEM,OAFNmE,WAEM,GADJnE,mBAAA,CAA0E;MAArEZ,KAAK,EAAC,aAAa;MAAEgF,KAAK,EAAAC,eAAA;QAAAC,KAAA,EAAWzE,MAAA,CAAA0E,gBAAgB;MAAA;4EAOpEpE,mBAAA,UAAa,EACoBX,MAAA,CAAAgF,gBAAgB,I,cAAjDlE,mBAAA,CAWM,OAXNmE,WAWM,I,kBAVJnE,mBAAA,CASSqB,SAAA,QAAAC,WAAA,CARU/B,MAAA,CAAA6E,YAAY,EAAtBC,MAAM;2BADfrE,mBAAA,CASS;QAPNyB,GAAG,EAAE4C,MAAM,CAAC3C,EAAE;QACdd,OAAK,EAAAiB,MAAA,IAAEtC,MAAA,CAAA+E,iBAAiB,CAACD,MAAM;QAChCvF,KAAK,EAAC,kBAAkB;QACvB8D,QAAQ,EAAErD,MAAA,CAAAgF;UAEX7E,mBAAA,CAA4B;QAAxBZ,KAAK,EAAAa,eAAA,CAAE0E,MAAM,CAAC5E,IAAI;+BACtBC,mBAAA,CAA+B,cAAAU,gBAAA,CAAtBiE,MAAM,CAACG,KAAK,iB;2EAIzB3E,mBAAA,UAAa,EACFN,MAAA,CAAAkF,SAAS,I,cAApBzE,mBAAA,CAGM,OAHN0E,WAGM,EAAA7D,MAAA,SAAAA,MAAA,QAFJnB,mBAAA,CAAmC;MAA9BZ,KAAK,EAAC;IAAiB,2BAC5BY,mBAAA,CAA2C;MAAtCZ,KAAK,EAAC;IAAc,GAAC,aAAW,mB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}