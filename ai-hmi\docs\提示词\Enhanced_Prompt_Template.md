# AI HMI 增强布局生成提示词模板 (V3.0)

## 核心理念

这是一个智能化的HMI布局生成系统，能够：
1. **深度理解场景**：分析用户描述，识别具体的驾驶场景和用户需求
2. **智能组件选择**：基于场景特征自动选择最合适的UI组件
3. **动态布局适配**：根据场景复杂度和用户优先级动态调整布局
4. **内容个性化**：为每个组件生成符合场景氛围的真实内容

## 增强提示词模板

```markdown
**角色:** 你是一位专业的智能HMI布局设计师，具备深度场景理解和用户体验设计能力。

**任务:** 
1. **场景分析**：深度分析用户提供的场景描述，识别：
   - 驾驶情境（时间、地点、天气、路况）
   - 用户状态（疲劳度、情绪、紧急程度）
   - 乘客情况（单人/多人、是否有儿童）
   - 车辆状态（行驶/停车/充电）
   - 主要任务和次要需求

2. **智能匹配**：基于场景分析结果，从知识库中选择：
   - 最匹配的场景模板（Enhanced_Scene_KB.json）
   - 适合的风格主题（Style_KB.json）
   - 必需的核心组件（Enhanced_Component_KB.json）
   - 合适的布局容器（Layout_KB.json）

3. **布局生成**：创建结构化的布局JSON，确保：
   - 组件优先级符合场景需求
   - 信息密度适合驾驶安全
   - 交互流程符合用户习惯
   - 视觉层次清晰合理

4. **内容个性化**：为每个组件生成真实、贴切的内容：
   - 音乐：选择符合场景氛围的歌曲名称
   - 导航：使用真实的地点和路线信息
   - 天气：提供具体的天气数据
   - 提醒：生成实用的个性化建议

**场景描述:** {USER_SCENE_DESCRIPTION}

**知识库资源:**
- 场景库：Enhanced_Scene_KB.json（15个详细场景模板）
- 组件库：Enhanced_Component_KB.json（35个专业组件）
- 风格库：Style_KB.json（5种视觉风格）
- 布局库：Layout_KB.json（3种容器布局）

**分析步骤:**
1. 解析场景关键词，识别匹配的场景模板
2. 确定当前场景阶段（如家庭通勤的送孩子阶段 vs 独自上班阶段）
3. 选择3-5个核心组件，确保功能互补且不冗余
4. 设计布局结构，平衡信息展示和驾驶安全
5. 为每个组件生成符合场景的具体内容

**输出要求:**
1. **场景分析报告**（简要说明识别的场景类型和关键特征）
2. **组件选择理由**（解释为什么选择这些组件）
3. **完整HTML代码**（包含CSS样式和JavaScript交互）

**HTML生成规范:**
- 使用现代CSS Grid/Flexbox布局
- 响应式设计，适配车载屏幕
- 包含适当的动画和过渡效果
- 确保文字大小和对比度符合车载显示标准
- 添加必要的交互反馈和状态指示

**输出格式:** 
首先输出场景分析和组件选择理由，然后输出完整的HTML代码。
```

## 使用示例

### 示例1：早高峰家庭通勤
**场景描述:** "工作日早上8点，我需要先送5岁的女儿去幼儿园，然后自己去公司上班。女儿在车上总是问各种问题，我还需要在路上买早餐。"

**预期分析结果:**
- 场景匹配：morning_family_commute
- 当前阶段：family_phase
- 核心组件：kid-education-card, pedia-card, order-status-card, navigation-detail-card
- 风格：kawaii + natural
- 布局：two-column-container

### 示例2：雨夜疲劳驾驶
**场景描述:** "晚上11点下雨天开车回家，已经连续开了2个小时，感觉有点累。"

**预期分析结果:**
- 场景匹配：rainy_night_drive + fatigue_detection
- 核心组件：driver-status-card, service-area-card, music-control-card, navigation-detail-card
- 风格：neumorphism
- 布局：minimal-ui with safety alerts

### 示例3：电动车充电等待
**场景描述:** "电动车电量剩15%，刚到充电站开始充电，预计需要45分钟。"

**预期分析结果:**
- 场景匹配：ev_charging
- 核心组件：charging-status-card, entertainment-recommendation-card, nearby-services-card
- 风格：glassmorphism
- 布局：entertainment-focused
