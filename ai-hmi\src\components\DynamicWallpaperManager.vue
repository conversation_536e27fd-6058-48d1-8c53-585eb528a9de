<template>
  <div class="dynamic-wallpaper-manager">
    <!-- 壁纸容器 -->
    <div class="wallpaper-container" :class="{ 'loading': isLoading }">
      <!-- 图片壁纸 -->
      <div 
        v-if="currentWallpaper && currentWallpaper.type === 'image'"
        class="wallpaper-image"
        :style="{ backgroundImage: `url(${currentWallpaper.url})` }"
      ></div>
      
      <!-- 视频壁纸 -->
      <video 
        v-if="currentWallpaper && currentWallpaper.type === 'video'"
        class="wallpaper-video"
        :src="currentWallpaper.url"
        autoplay
        muted
        loop
        playsinline
      ></video>
      
      <!-- 默认渐变背景 -->
      <div 
        v-if="!currentWallpaper"
        class="wallpaper-default"
        :style="defaultGradient"
      ></div>
      
      <!-- 加载遮罩 -->
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-spinner">
          <i class="fas fa-spinner fa-spin"></i>
          <span>{{ loadingText }}</span>
        </div>
      </div>
    </div>
    
    <!-- 内容插槽 -->
    <div class="content-overlay" :style="overlayStyles">
      <slot></slot>
    </div>
    
    <!-- 配置面板 -->
    <div v-if="showConfig" class="config-panel">
      <div class="config-header">
        <h3>壁纸设置</h3>
        <button @click="showConfig = false" class="close-btn">×</button>
      </div>
      <div class="config-content">
        <div class="config-item">
          <label>
            <input 
              type="checkbox" 
              v-model="config.enabled"
              @change="onConfigChange"
            />
            启用动态壁纸
          </label>
        </div>
        <div class="config-item">
          <label>
            <input 
              type="checkbox" 
              v-model="config.autoGenerate"
              @change="onConfigChange"
            />
            自动生成壁纸
          </label>
        </div>
        <div class="config-item">
          <label>壁纸透明度:</label>
          <input 
            type="range" 
            min="0.1" 
            max="1" 
            step="0.1"
            v-model="config.opacity"
            @input="onConfigChange"
          />
          <span>{{ config.opacity }}</span>
        </div>
        <div class="config-item">
          <label>卡片透明度:</label>
          <input 
            type="range" 
            min="0.1" 
            max="0.8" 
            step="0.1"
            v-model="config.cardOpacity"
            @input="onConfigChange"
          />
          <span>{{ config.cardOpacity }}</span>
        </div>
        <div class="config-actions">
          <button @click="generateNewWallpaper" :disabled="isLoading">
            重新生成壁纸
          </button>
          <button @click="generateDynamicWallpaper" :disabled="isLoading || !currentWallpaper">
            生成动态壁纸
          </button>
          <button @click="resetToDefault">
            恢复默认
          </button>
        </div>
      </div>
    </div>
    
    <!-- 配置按钮 -->
    <button 
      class="config-toggle"
      @click="showConfig = !showConfig"
      :class="{ 'active': showConfig }"
    >
      <i class="fas fa-cog"></i>
    </button>
    
    <!-- 动态壁纸预览模态框 -->
    <div v-if="showDynamicPreview" class="preview-modal">
      <div class="preview-content">
        <div class="preview-header">
          <h3>动态壁纸预览</h3>
          <button @click="closeDynamicPreview" class="close-btn">×</button>
        </div>
        <div class="preview-body">
          <div class="preview-video-container">
            <video 
              v-if="previewVideoUrl"
              class="preview-video"
              :src="previewVideoUrl"
              autoplay
              muted
              loop
              playsinline
            ></video>
          </div>
          <div class="preview-info">
            <p>动态壁纸已生成完成！</p>
            <p>预览视频效果，决定是否应用为动态壁纸。</p>
          </div>
        </div>
        <div class="preview-actions">
          <button @click="applyDynamicWallpaper" class="apply-btn">
            <i class="fas fa-check"></i> 应用动态壁纸
          </button>
          <button @click="closeDynamicPreview" class="cancel-btn">
            <i class="fas fa-times"></i> 取消
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import ImageGenerationService from '@/services/ImageGenerationService'
import DynamicWallpaperService from '@/services/DynamicWallpaperService'
import ColorExtractor from '@/utils/ColorExtractor'
import AIColorAnalyzer from '@/utils/AIColorAnalyzer'

export default {
  name: 'DynamicWallpaperManager',
  
  props: {
    scenePrompt: {
      type: String,
      default: '现代简约风格，商务氛围'
    },
    autoGenerate: {
      type: Boolean,
      default: true
    },
    enableConfig: {
      type: Boolean,
      default: true
    }
  },
  
  emits: ['wallpaper-changed', 'colors-extracted'],
  
  setup(props, { emit }) {
    // 响应式数据
    const isLoading = ref(false)
    const showConfig = ref(false)
    const currentWallpaper = ref(null)
    const extractedColors = ref(null)
    const loadingText = ref('正在生成壁纸...')
    const showDynamicPreview = ref(false)
    const previewVideoUrl = ref('')
    const pendingDynamicWallpaper = ref(null)
    
    // 配置
    const config = reactive({
      enabled: true,
      autoGenerate: true,
      opacity: 0.8,
      cardOpacity: 0.3,
      contrastBoost: 1.2
    })
    
    // 服务实例
    const imageService = new ImageGenerationService()
    const dynamicWallpaperService = new DynamicWallpaperService()
    
    // 计算属性
    const defaultGradient = computed(() => ({
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }))
    
    const overlayStyles = computed(() => {
      if (!extractedColors.value) return {}
      
      return {
        '--glass-bg': extractedColors.value.glassBackground,
        '--glass-border': extractedColors.value.glassBorder,
        '--text-color': extractedColors.value.text,
        '--card-opacity': config.cardOpacity
      }
    })
    
    // 方法
    const generateWallpaper = async (prompt = props.scenePrompt) => {
      if (!config.enabled) return
      
      isLoading.value = true
      
      try {
        console.log('开始生成壁纸，提示词:', prompt)
        const result = await imageService.generateWallpaper(prompt)
        
        if (result && result.imageUrl) {
          currentWallpaper.value = {
            type: 'image',
            url: result.imageUrl,
            prompt: result.prompt,
            taskId: result.taskId
          }
          
          // 提取颜色，传递提示词用于智能降级
          await extractColors(result.imageUrl, prompt)
          
          // 保存到历史
          imageService.saveToHistory(result)
          
          emit('wallpaper-changed', currentWallpaper.value)
          console.log('壁纸生成成功:', result.imageUrl)
        }
      } catch (error) {
        console.error('壁纸生成失败:', error)
        // 使用默认壁纸
        resetToDefault()
      } finally {
        isLoading.value = false
      }
    }
    
    const extractColors = async (imageUrl, prompt = props.scenePrompt, optimizedPrompt = '') => {
      try {
        console.log('🎨 开始AI智能配色分析...')

        // 使用AI智能配色分析器
        const intelligentColors = await AIColorAnalyzer.analyzeWallpaperAndGenerateColors(
          imageUrl,
          prompt,
          optimizedPrompt
        )

        extractedColors.value = intelligentColors
        emit('colors-extracted', intelligentColors)
        console.log('🎨 AI智能配色完成:', intelligentColors)

        // 如果有AI分析结果，显示详细信息
        if (intelligentColors.aiAnalysis) {
          console.log('🧠 AI分析结果:', {
            情感氛围: intelligentColors.aiAnalysis.mood,
            整体亮度: intelligentColors.aiAnalysis.brightness,
            氛围描述: intelligentColors.aiAnalysis.atmosphere,
            主要颜色: intelligentColors.aiAnalysis.dominantColors
          })
        }

      } catch (error) {
        console.error('AI配色分析失败:', error)
        // 降级到传统颜色提取
        try {
          const colors = await ColorExtractor.extractColors(imageUrl, prompt)
          extractedColors.value = colors
          emit('colors-extracted', colors)
          console.log('降级到传统颜色提取:', colors)
        } catch (fallbackError) {
          console.error('传统颜色提取也失败:', fallbackError)
          // 最终降级到场景智能颜色
          const fallbackColors = ColorExtractor.getSceneBasedColors(prompt)
          extractedColors.value = fallbackColors
          emit('colors-extracted', fallbackColors)
          console.log('使用场景智能颜色:', fallbackColors)
        }
      }
    }
    
    const generateNewWallpaper = () => {
      generateWallpaper()
    }
    
    const generateDynamicWallpaper = async () => {
      if (!currentWallpaper.value || currentWallpaper.value.type !== 'image') {
        console.warn('当前没有可用的静态壁纸，无法生成动态壁纸')
        return
      }

      isLoading.value = true
      loadingText.value = '正在生成动态壁纸...这可能需要2-3分钟，请耐心等待...'

      try {
        console.log('开始智能生成动态壁纸')

        // 获取当前壁纸信息
        const wallpaperInfo = imageService.getCurrentWallpaperInfo()
        const taskId = dynamicWallpaperService.generateTaskId()

        let imageFile = null
        let imageUrl = null

        // 优先尝试从本地路径加载文件
        if (wallpaperInfo && wallpaperInfo.imagePath) {
          console.log('尝试从本地路径加载文件:', wallpaperInfo.imagePath)
          imageFile = await imageService.loadFileFromPath(wallpaperInfo.imagePath)
        }

        // 如果本地文件加载失败，使用URL模式
        if (!imageFile && wallpaperInfo && wallpaperInfo.imageUrl) {
          console.log('本地文件不可用，使用URL模式:', wallpaperInfo.imageUrl)
          imageUrl = wallpaperInfo.imageUrl
        }

        // 如果都没有，尝试从当前壁纸URL创建文件
        if (!imageFile && !imageUrl && currentWallpaper.value.url) {
          console.log('从当前壁纸URL创建文件:', currentWallpaper.value.url)
          try {
            const response = await fetch(currentWallpaper.value.url)
            const blob = await response.blob()
            imageFile = new File([blob], 'wallpaper.png', { type: 'image/png' })
          } catch (error) {
            console.error('从URL创建文件失败:', error)
            imageUrl = currentWallpaper.value.url
          }
        }

        // 调用智能生成服务
        const result = await dynamicWallpaperService.generateDynamicWallpaper({
          imageFile: imageFile,
          imageUrl: imageUrl,
          taskId: taskId,
          onProgress: (progress) => {
            loadingText.value = progress.message
            console.log(`生成进度: ${progress.percentage}% - ${progress.message}`)
          }
        })

        if (result && result.url) {
          // 保存生成的动态壁纸信息，等待用户确认
          pendingDynamicWallpaper.value = {
            type: 'video',
            url: result.url,
            prompt: currentWallpaper.value.prompt,
            taskId: result.taskId
          }

          // 显示预览模态框
          previewVideoUrl.value = result.url
          showDynamicPreview.value = true

          console.log('动态壁纸生成成功:', result.url)
        }
      } catch (error) {
        console.error('动态壁纸生成失败:', error)
        loadingText.value = `生成失败: ${error.message}`

        // 显示错误提示
        setTimeout(() => {
          loadingText.value = '正在生成壁纸...'
        }, 3000)
      } finally {
        isLoading.value = false
      }
    }
    
    const applyDynamicWallpaper = () => {
      if (pendingDynamicWallpaper.value) {
        // 应用动态壁纸
        currentWallpaper.value = { ...pendingDynamicWallpaper.value }
        emit('wallpaper-changed', currentWallpaper.value)
        console.log('动态壁纸已应用:', currentWallpaper.value.url)
        
        // 关闭预览模态框
        closeDynamicPreview()
      }
    }
    
    const closeDynamicPreview = () => {
      showDynamicPreview.value = false
      previewVideoUrl.value = ''
      pendingDynamicWallpaper.value = null
    }
    
    const resetToDefault = () => {
      currentWallpaper.value = null
      // 使用基于当前场景的智能颜色
      const smartColors = ColorExtractor.getSceneBasedColors(props.scenePrompt)
      extractedColors.value = smartColors
      emit('colors-extracted', smartColors)
      console.log('重置为智能默认颜色:', smartColors)
    }
    
    const onConfigChange = () => {
      // 保存配置到本地存储
      localStorage.setItem('wallpaper_config', JSON.stringify(config))
      
      // 如果禁用了动态壁纸，重置为默认
      if (!config.enabled) {
        resetToDefault()
      }
    }
    
    const loadConfig = () => {
      const saved = localStorage.getItem('wallpaper_config')
      if (saved) {
        Object.assign(config, JSON.parse(saved))
      }
    }
    
    // 生命周期
    onMounted(async () => {
      loadConfig()

      // 确保每次页面加载都生成壁纸（如果启用了自动生成）
      if (config.enabled && config.autoGenerate) {
        await nextTick()
        console.log('页面加载，开始自动生成壁纸')
        generateWallpaper()
      } else {
        resetToDefault()
      }
    })
    
    // 监听场景提示词变化
    watch(() => props.scenePrompt, (newPrompt) => {
      if (config.enabled && config.autoGenerate) {
        generateWallpaper(newPrompt)
      }
    })
    
    return {
      isLoading,
      showConfig,
      currentWallpaper,
      extractedColors,
      config,
      defaultGradient,
      overlayStyles,
      loadingText,
      showDynamicPreview,
      previewVideoUrl,
      generateNewWallpaper,
      generateDynamicWallpaper,
      applyDynamicWallpaper,
      closeDynamicPreview,
      resetToDefault,
      onConfigChange
    }
  }
}
</script>

<style scoped>
.dynamic-wallpaper-manager {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.wallpaper-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1; /* 确保在最底层 */
}

.wallpaper-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  transition: opacity 0.5s ease;
}

.wallpaper-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.wallpaper-default {
  width: 100%;
  height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  font-size: 16px;
}

.loading-spinner i {
  font-size: 32px;
  margin-bottom: 10px;
}

.content-overlay {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 5;
}

/* 全局CSS变量，供子组件使用 */
.content-overlay :deep(.glass-card) {
  background: var(--glass-bg, rgba(255, 255, 255, 0.15));
  border: 1px solid var(--glass-border, rgba(255, 255, 255, 0.2));
  color: var(--text-color, #ffffff);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.config-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 18px;
  cursor: pointer;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  z-index: 1000;
}

.config-toggle:hover,
.config-toggle.active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.config-panel {
  position: fixed;
  top: 80px;
  right: 20px;
  width: 300px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  color: #333;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.config-header h3 {
  margin: 0;
  font-size: 16px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.config-content {
  padding: 20px;
}

.config-item {
  margin-bottom: 15px;
}

.config-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.config-item input[type="range"] {
  flex: 1;
  margin: 0 10px;
}

.config-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.config-actions button {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  background: #4A90E2;
  color: white;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.3s ease;
}

.config-actions button:hover {
  background: #357ABD;
}

.config-actions button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* 动态壁纸预览模态框 */
.preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.preview-content {
  width: 90%;
  max-width: 800px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.preview-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.preview-body {
  padding: 20px;
}

.preview-video-container {
  width: 100%;
  height: 400px;
  background: #000;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 20px;
}

.preview-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-info {
  text-align: center;
  color: #666;
  margin-bottom: 20px;
}

.preview-info p {
  margin: 5px 0;
  font-size: 14px;
}

.preview-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.apply-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  background: #4CAF50;
  color: white;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.apply-btn:hover {
  background: #45a049;
  transform: translateY(-2px);
}

.cancel-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  background: #f44336;
  color: white;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.cancel-btn:hover {
  background: #da190b;
  transform: translateY(-2px);
}

/* 加载文本样式增强 */
.loading-spinner span {
  font-size: 14px;
  text-align: center;
  line-height: 1.4;
  max-width: 300px;
}
</style>
