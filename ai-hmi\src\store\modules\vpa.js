import { defineStore } from 'pinia'

export const useVpaStore = defineStore('vpa', {
  state: () => ({
    // VPA当前状态
    currentMode: 'companion', // companion(陪伴模式) | interaction(交互模式) | hidden(隐藏)
    
    // VPA可用模式
    availableModes: {
      companion: {
        name: '陪伴模式',
        description: 'VPA以小窗形式陪伴用户',
        component: 'VPAAvatarWidget',
        defaultSize: 'vpaWidget', // 2x2
        animation: 'idle',
        position: { x: 15, y: 7 }
      },
      interaction: {
        name: '交互模式',
        description: 'VPA展开为交互面板',
        component: 'VPAInteractionPanel',
        defaultSize: 'vpaInteractionLarge', // 8x9
        animation: 'talking',
        position: { x: 9, y: 1 }
      },
      hidden: {
        name: '隐藏模式',
        description: 'VPA完全隐藏',
        component: null,
        defaultSize: null,
        animation: null,
        position: null
      }
    },
    
    // VPA动画状态
    animationState: 'idle', // idle | talking | listening | thinking | greeting | sleeping
    
    // VPA可用动画
    availableAnimations: {
      idle: {
        name: '待机',
        resource: 'vpa2.gif',
        duration: 3000,
        loop: true
      },
      talking: {
        name: '说话',
        resource: 'vpn1.gif',
        duration: 2000,
        loop: true
      },
      listening: {
        name: '聆听',
        resource: 'vpa2.gif',
        duration: 1500,
        loop: true
      },
      thinking: {
        name: '思考',
        resource: 'vpa2.gif',
        duration: 2500,
        loop: true
      },
      greeting: {
        name: '问候',
        resource: 'vpn1.gif',
        duration: 1000,
        loop: false
      },
      sleeping: {
        name: '休眠',
        resource: 'vpa2.gif',
        duration: 5000,
        loop: true
      }
    },
    
    // VPA个性化设置
    personality: {
      name: 'AI助手',
      voice: 'female',
      language: 'zh-CN',
      responseStyle: 'friendly', // friendly | professional | casual
      emotionalLevel: 'medium' // low | medium | high
    },
    
    // VPA交互历史
    interactionHistory: [],
    
    // VPA当前对话状态
    conversationState: {
      isActive: false,
      currentTopic: null,
      contextLevel: 0, // 0-5, 上下文理解深度
      lastInteraction: null
    },
    
    // VPA能力状态
    capabilities: {
      voiceRecognition: true,
      textToSpeech: true,
      emotionRecognition: false,
      gestureRecognition: false,
      contextAwareness: true
    },
    
    // VPA显示配置
    displayConfig: {
      showName: true,
      showStatus: true,
      showAnimation: true,
      transparency: 0.95,
      blurEffect: true
    }
  }),
  
  getters: {
    // 获取当前VPA模式配置
    currentModeConfig: (state) => {
      return state.availableModes[state.currentMode]
    },
    
    // 获取当前动画配置
    currentAnimationConfig: (state) => {
      return state.availableAnimations[state.animationState]
    },
    
    // 检查VPA是否可见
    isVisible: (state) => {
      return state.currentMode !== 'hidden'
    },
    
    // 检查VPA是否在交互中
    isInteracting: (state) => {
      return state.conversationState.isActive
    },
    
    // 获取VPA组件信息
    vpaComponentInfo: (state) => {
      const modeConfig = state.availableModes[state.currentMode]
      const animationConfig = state.availableAnimations[state.animationState]
      
      return {
        component: modeConfig?.component,
        size: modeConfig?.defaultSize,
        position: modeConfig?.position,
        animation: animationConfig,
        displayConfig: state.displayConfig
      }
    }
  },
  
  actions: {
    // 切换VPA模式
    switchMode(mode) {
      if (this.availableModes[mode]) {
        const previousMode = this.currentMode
        this.currentMode = mode
        
        // 根据模式切换动画
        if (mode === 'interaction') {
          this.setAnimation('talking')
        } else if (mode === 'companion') {
          this.setAnimation('idle')
        }
        
        console.log(`VPA模式已切换: ${previousMode} -> ${mode}`)
        
        // 记录交互历史
        this.addInteractionHistory({
          type: 'mode_switch',
          from: previousMode,
          to: mode,
          timestamp: new Date()
        })
      }
    },
    
    // 设置VPA动画
    setAnimation(animationName) {
      if (this.availableAnimations[animationName]) {
        this.animationState = animationName
        console.log(`VPA动画已切换到: ${animationName}`)
      }
    },
    
    // 开始对话
    startConversation(topic = null) {
      this.conversationState.isActive = true
      this.conversationState.currentTopic = topic
      this.conversationState.lastInteraction = new Date()
      
      // 切换到交互模式
      if (this.currentMode === 'companion') {
        this.switchMode('interaction')
      }
      
      this.setAnimation('listening')
    },
    
    // 结束对话
    endConversation() {
      this.conversationState.isActive = false
      this.conversationState.currentTopic = null
      
      // 切换回陪伴模式
      if (this.currentMode === 'interaction') {
        this.switchMode('companion')
      }
      
      this.setAnimation('idle')
    },
    
    // 添加交互历史
    addInteractionHistory(interaction) {
      this.interactionHistory.unshift(interaction)
      
      // 保持历史记录在合理范围内
      if (this.interactionHistory.length > 100) {
        this.interactionHistory = this.interactionHistory.slice(0, 100)
      }
    },
    
    // 更新个性化设置
    updatePersonality(settings) {
      this.personality = { ...this.personality, ...settings }
    },
    
    // 更新显示配置
    updateDisplayConfig(config) {
      this.displayConfig = { ...this.displayConfig, ...config }
    },
    
    // VPA问候
    greet() {
      this.setAnimation('greeting')
      
      // 2秒后回到待机状态
      setTimeout(() => {
        this.setAnimation('idle')
      }, 2000)
      
      this.addInteractionHistory({
        type: 'greeting',
        timestamp: new Date()
      })
    },
    
    // VPA休眠
    sleep() {
      this.setAnimation('sleeping')
      this.conversationState.isActive = false
    },
    
    // VPA唤醒
    wakeUp() {
      this.setAnimation('greeting')
      
      setTimeout(() => {
        this.setAnimation('idle')
      }, 1000)
    }
  }
})
