<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI HMI - Serenity Theme</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            overflow: hidden;
            font-family: 'system-ui', sans-serif;
        }
        .grid-container {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            grid-template-rows: repeat(4, 1fr);
            gap: 16px;
            width: 100vw;
            height: 100vh;
            padding: 16px;
            box-sizing: border-box;
        }
        .card {
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 12px 16px rgba(0,0,0,0.2);
        }
        .vpa-container {
            grid-column: span 2;
            grid-row: span 4;
        }
        .island-container {
            grid-column: 3 / span 4;
            grid-row: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .small-card-container {
            grid-column: 3 / span 4;
            grid-row: 2;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }
        .large-card-container {
            grid-column: 7 / span 2;
            grid-row: span 4;
        }
        .temp-interaction-container {
            position: absolute;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 50;
        }
    </style>
</head>
<body class="bg-cover bg-center bg-no-repeat" style="background-image: url('https://images.unsplash.com/photo-1441974231531-c6227db76b6e?q=80&w=2560&auto=format&fit=crop');">
    <div class="grid-container">
        <!-- VPA 数字人 -->
        <div class="vpa-container card flex flex-col items-center justify-center p-4">
            <img src="https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=2487&auto=format&fit=crop" alt="VPA Avatar" class="w-32 h-32 rounded-full border-4 border-white shadow-lg mb-4">
            <h2 class="text-xl font-bold text-gray-800">Hi, I'm Kai</h2>
            <p class="text-gray-600 text-center">How can I help you on this beautiful morning?</p>
        </div>

        <!-- 灵动岛 -->
        <div class="island-container card p-2">
            <div class="flex items-center space-x-4">
                <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2z"></path></svg>
                <div>
                    <p class="font-semibold text-gray-700">Now Playing</p>
                    <p class="text-sm text-gray-500">Forest Lullaby - Ambient Mix</p>
                </div>
            </div>
        </div>

        <!-- 横向小卡片 -->
        <div class="small-card-container">
            <div class="card p-4 flex flex-col justify-between">
                <h3 class="font-bold text-gray-800">Weather</h3>
                <div class="flex items-center justify-center space-x-4">
                    <svg class="w-16 h-16 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"></path></svg>
                    <div>
                        <p class="text-3xl font-bold text-gray-700">18°C</p>
                        <p class="text-gray-500">Sunny</p>
                    </div>
                </div>
            </div>
            <div class="card p-4 flex flex-col justify-between">
                <h3 class="font-bold text-gray-800">Next Appointment</h3>
                <div class="text-center">
                    <p class="text-lg font-semibold text-gray-700">Team Sync</p>
                    <p class="text-gray-500">10:00 AM</p>
                    <p class="text-sm text-blue-600">Conference Room B</p>
                </div>
            </div>
        </div>

        <!-- 纵向大卡片 -->
        <div class="large-card-container card p-4 flex flex-col">
            <h3 class="font-bold text-gray-800 mb-2">Morning Commute</h3>
            <div class="flex-grow rounded-lg overflow-hidden relative">
                 <img src="https://images.unsplash.com/photo-1570129477492-45c003edd2be?q=80&w=2670&auto=format&fit=crop" class="w-full h-full object-cover" alt="Map">
                 <div class="absolute bottom-2 left-2 right-2 bg-white/70 p-2 rounded-lg text-sm">
                     <p class="font-bold">35 min</p>
                     <p>Light traffic on main street.</p>
                 </div>
            </div>
        </div>

        <!-- 临时交互组件 -->
        <div class="temp-interaction-container card p-4 flex items-center space-x-4">
            <p class="font-semibold text-gray-700">Incoming Call</p>
            <p class="text-gray-500">Jane Doe</p>
            <button class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">Accept</button>
            <button class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600">Decline</button>
        </div>

        <!-- Empty grid cells for spacing -->
        <div style="grid-column: 3 / span 4; grid-row: 3 / span 2;"></div>

    </div>
</body>
</html>