{"ast": null, "code": "import GlassCard from '../GlassCard.vue';\nexport default {\n  name: 'DefaultCard',\n  components: {\n    GlassCard\n  },\n  props: {\n    scene: {\n      type: Object,\n      required: true\n    },\n    cardType: {\n      type: String,\n      required: true\n    },\n    themeColors: {\n      type: Object,\n      default: null\n    }\n  },\n  setup(props) {\n    // 卡片配置映射\n    const cardConfigs = {\n      // 导航相关\n      navigation: {\n        title: '导航',\n        icon: 'fas fa-route',\n        content: `\n          <div class=\"navigation-content\">\n            <div class=\"destination\">前往公司</div>\n            <div class=\"route-info\">\n              <span class=\"distance\">12.5 公里</span>\n              <span class=\"duration\">25 分钟</span>\n            </div>\n          </div>\n        `\n      },\n      // 音乐相关\n      music: {\n        title: '音乐控制',\n        icon: 'fas fa-music',\n        content: `\n          <div class=\"music-content\">\n            <div class=\"current-song\">正在播放: 日落大道</div>\n            <div class=\"music-controls\">\n              <button class=\"control-btn\"><i class=\"fas fa-backward\"></i></button>\n              <button class=\"control-btn\"><i class=\"fas fa-pause\"></i></button>\n              <button class=\"control-btn\"><i class=\"fas fa-forward\"></i></button>\n            </div>\n          </div>\n        `\n      },\n      // 待办事项\n      todo: {\n        title: '今日待办',\n        icon: 'fas fa-tasks',\n        content: `\n          <div class=\"todo-content\">\n            <div class=\"todo-item\">• 10:00 团队会议</div>\n            <div class=\"todo-item\">• 14:00 项目汇报</div>\n            <div class=\"todo-item\">• 16:30 客户电话</div>\n          </div>\n        `\n      },\n      // 儿童教育\n      kidEducation: {\n        title: '儿童教育',\n        icon: 'fas fa-graduation-cap',\n        content: `\n          <div class=\"kid-education-content\">\n            <div class=\"video-placeholder\">\n              <i class=\"fas fa-play-circle\"></i>\n              <span>正在播放儿童视频...</span>\n            </div>\n          </div>\n        `\n      },\n      // 百科问答\n      pedia: {\n        title: '百科问答',\n        icon: 'fas fa-question-circle',\n        content: `\n          <div class=\"pedia-content\">\n            <div class=\"question\">地球是圆的吗？</div>\n            <div class=\"answer\">是的，地球是一个近似球形的星球...</div>\n          </div>\n        `\n      },\n      // 视频播放器\n      videoPlayer: {\n        title: '视频播放器',\n        icon: 'fas fa-video',\n        content: `\n          <div class=\"video-player-content\">\n            <div class=\"video-screen\">\n              <i class=\"fas fa-play\"></i>\n              <span>视频播放中</span>\n            </div>\n          </div>\n        `\n      },\n      // 新闻摘要\n      news: {\n        title: '新闻摘要',\n        icon: 'fas fa-newspaper',\n        content: `\n          <div class=\"news-content\">\n            <div class=\"news-item\">• AI技术最新进展</div>\n            <div class=\"news-item\">• 智能汽车行业动态</div>\n            <div class=\"news-item\">• 科技创新资讯</div>\n          </div>\n        `\n      },\n      // 环境音\n      ambientSound: {\n        title: '环境音',\n        icon: 'fas fa-volume-up',\n        content: `\n          <div class=\"ambient-sound-content\">\n            <button class=\"sound-btn\">雨声</button>\n            <button class=\"sound-btn\">森林</button>\n            <button class=\"sound-btn\">冥想</button>\n          </div>\n        `\n      },\n      // VPA小窗\n      vpaWidget: {\n        title: '',\n        icon: '',\n        content: `\n          <div class=\"vpa-widget-content\">\n            <div class=\"vpa-conversation\">\n              <p class=\"vpa-greeting\">你好，我是小智，欢迎来到AI-HMI，我是智能桌面助手，可以根据场景定制专属桌面，这是对我说：</p>\n              <div class=\"vpa-actions\">\n                <button class=\"vpa-action-btn\">生成通勤桌面</button>\n                <button class=\"vpa-action-btn\">导航3D效果</button>\n                <button class=\"vpa-action-btn\">帮我规划一个独处的桌面</button>\n                <button class=\"vpa-action-btn\">生成春游桌面</button>\n                <button class=\"vpa-action-btn\">帮我规划一个周末一日游</button>\n              </div>\n              <p class=\"vpa-footer\">根据你的使用习惯，后续会全面智能化桌面，或者也可以在AI-LAB设置你的更多介绍</p>\n            </div>\n            <img src=\"/images/vpa2.gif\" alt=\"VPA智能助手\" class=\"vpa-avatar\" />\n          </div>\n        `\n      },\n      dynamicIsland: {\n        title: '',\n        icon: '',\n        content: `\n          <div class=\"dynamic-island-content\">\n            <div class=\"island-info\">\n              <i class=\"fas fa-route\"></i>\n              <span class=\"island-text\">G2高速 - 距离下一出口 25km</span>\n            </div>\n            <div class=\"island-actions\">\n              <button class=\"island-btn\"><i class=\"fas fa-map\"></i></button>\n              <button class=\"island-btn\"><i class=\"fas fa-phone\"></i></button>\n            </div>\n          </div>\n        `\n      },\n      // 智能家居\n      smartHome: {\n        title: '智能家居',\n        icon: 'fas fa-home',\n        content: `\n          <div class=\"smart-home-content\">\n            <div class=\"home-control\">\n              <button class=\"home-btn\">客厅空调: 开</button>\n              <button class=\"home-btn\">空气净化器: 自动</button>\n            </div>\n          </div>\n        `\n      },\n      // 订单状态\n      orderStatus: {\n        title: '订单状态',\n        icon: 'fas fa-shopping-bag',\n        content: `\n          <div class=\"order-status-content\">\n            <div class=\"order-info\">麦当劳早餐已下单</div>\n            <div class=\"order-time\">预计15分钟后取餐</div>\n          </div>\n        `\n      },\n      // 后座娱乐控制\n      rearSeatControl: {\n        title: '后座娱乐',\n        icon: 'fas fa-gamepad',\n        content: `\n          <div class=\"rear-seat-content\">\n            <div class=\"entertainment-header\">\n              <h4>🎮 后座娱乐中心</h4>\n              <div class=\"seat-status\">\n                <span class=\"seat-indicator active\">左座</span>\n                <span class=\"seat-indicator active\">右座</span>\n              </div>\n            </div>\n            <div class=\"entertainment-options\">\n              <button class=\"entertainment-btn active\">📺 动画片</button>\n              <button class=\"entertainment-btn\">🎵 儿歌</button>\n              <button class=\"entertainment-btn\">🎮 小游戏</button>\n              <button class=\"entertainment-btn\">📚 有声书</button>\n            </div>\n            <div class=\"volume-control\">\n              <span class=\"volume-label\">🔊 音量</span>\n              <div class=\"volume-slider\">\n                <input type=\"range\" min=\"0\" max=\"100\" value=\"60\" class=\"slider\">\n              </div>\n            </div>\n          </div>\n        `\n      },\n      // 设施查找\n      facilityFinder: {\n        title: '智能设施查找',\n        icon: 'fas fa-map-marker-alt',\n        content: `\n          <div class=\"facility-finder-content\">\n            <div class=\"finder-header\">\n              <h4>🗺️ 智能设施查找</h4>\n              <div class=\"location-status\">📍 当前位置: 森林公园</div>\n            </div>\n            <div class=\"facility-grid\">\n              <button class=\"facility-btn urgent\">🚻 洗手间</button>\n              <button class=\"facility-btn\">🍽️ 餐厅</button>\n              <button class=\"facility-btn\">⛽ 加油站</button>\n              <button class=\"facility-btn\">🅿️ 停车场</button>\n            </div>\n            <div class=\"nearby-info\">\n              <div class=\"info-item\">\n                <span class=\"facility-icon\">🚻</span>\n                <span class=\"facility-name\">公园洗手间</span>\n                <span class=\"facility-distance\">200m</span>\n              </div>\n            </div>\n          </div>\n        `\n      },\n      // 行程提醒\n      tripReminder: {\n        title: '行程助理',\n        icon: 'fas fa-clock',\n        content: `\n          <div class=\"trip-reminder-content\">\n            <div class=\"assistant-header\">\n              <h4>⏰ 行程助理</h4>\n              <div class=\"trip-time\">出游时间: 2小时15分钟</div>\n            </div>\n            <div class=\"reminder-list\">\n              <div class=\"reminder-item active\">\n                <span class=\"reminder-icon\">🍿</span>\n                <div class=\"reminder-info\">\n                  <div class=\"reminder-title\">零食时间</div>\n                  <div class=\"reminder-time\">15分钟后</div>\n                </div>\n              </div>\n              <div class=\"reminder-item\">\n                <span class=\"reminder-icon\">🚻</span>\n                <div class=\"reminder-info\">\n                  <div class=\"reminder-title\">休息提醒</div>\n                  <div class=\"reminder-time\">45分钟后</div>\n                </div>\n              </div>\n              <div class=\"reminder-item\">\n                <span class=\"reminder-icon\">🏠</span>\n                <div class=\"reminder-info\">\n                  <div class=\"reminder-title\">返程提醒</div>\n                  <div class=\"reminder-time\">3小时后</div>\n                </div>\n              </div>\n            </div>\n            <div class=\"assistant-actions\">\n              <button class=\"assistant-btn\">➕ 添加提醒</button>\n              <button class=\"assistant-btn\">⚙️ 设置</button>\n            </div>\n          </div>\n        `\n      },\n      // 服务区信息\n      serviceArea: {\n        title: '服务区信息',\n        icon: 'fas fa-gas-pump',\n        content: `\n          <div class=\"service-area-content\">\n            <div class=\"service-info\">前方5公里有服务区</div>\n            <div class=\"services\">加油站 • 餐厅 • 休息区</div>\n          </div>\n        `\n      },\n      // 驾驶员状态\n      driverStatus: {\n        title: '驾驶员状态',\n        icon: 'fas fa-user',\n        content: `\n          <div class=\"driver-status-content\">\n            <div class=\"status-item\">疲劳度: 正常</div>\n            <div class=\"status-item\">注意力: 集中</div>\n          </div>\n        `\n      },\n      // 车辆状态\n      vehicleStatus: {\n        title: '车辆状态',\n        icon: 'fas fa-car',\n        content: `\n          <div class=\"vehicle-status-content\">\n            <div class=\"status-item\">油量: 75%</div>\n            <div class=\"status-item\">轮胎压力: 正常</div>\n          </div>\n        `\n      },\n      // 充电状态\n      chargingStatus: {\n        title: '充电状态',\n        icon: 'fas fa-battery-three-quarters',\n        content: `\n          <div class=\"charging-status-content\">\n            <div class=\"charge-info\">当前电量: 65%</div>\n            <div class=\"charge-time\">预计充满: 45分钟</div>\n          </div>\n        `\n      },\n      // 疲劳预警\n      fatigueWarning: {\n        title: '疲劳预警',\n        icon: 'fas fa-exclamation-triangle',\n        content: `\n          <div class=\"fatigue-warning-content\">\n            <div class=\"warning-text\">检测到疲劳驾驶</div>\n            <div class=\"suggestion\">建议就近休息</div>\n          </div>\n        `\n      },\n      // 紧急联系\n      emergencyContact: {\n        title: '紧急联系',\n        icon: 'fas fa-phone',\n        content: `\n          <div class=\"emergency-contact-content\">\n            <button class=\"emergency-btn\">拨打120</button>\n            <button class=\"emergency-btn\">联系家人</button>\n          </div>\n        `\n      },\n      // 访客模式 - 临时导航\n      tempNavigation: {\n        title: '临时导航',\n        icon: 'fas fa-map',\n        content: `\n          <div class=\"temp-navigation-content\">\n            <div class=\"nav-input\">\n              <input type=\"text\" placeholder=\"请输入临时目的地\" class=\"destination-input\">\n              <button class=\"nav-btn\">前往</button>\n            </div>\n          </div>\n        `\n      },\n      // 访客模式 - 基础音乐\n      basicMusic: {\n        title: '基础音乐',\n        icon: 'fas fa-music',\n        content: `\n          <div class=\"basic-music-content\">\n            <div class=\"music-source\">\n              <button class=\"source-btn\">FM 97.4</button>\n              <button class=\"source-btn\">蓝牙音乐</button>\n            </div>\n            <div class=\"basic-controls\">\n              <button class=\"control-btn\"><i class=\"fas fa-backward\"></i></button>\n              <button class=\"control-btn\"><i class=\"fas fa-pause\"></i></button>\n              <button class=\"control-btn\"><i class=\"fas fa-forward\"></i></button>\n            </div>\n          </div>\n        `\n      },\n      // 访客模式 - 基础控制\n      basicControl: {\n        title: '基础控制',\n        icon: 'fas fa-cog',\n        content: `\n          <div class=\"basic-control-content\">\n            <div class=\"control-grid\">\n              <button class=\"control-grid-btn\">空调</button>\n              <button class=\"control-grid-btn\">车窗</button>\n              <button class=\"control-grid-btn\">音量</button>\n              <button class=\"control-grid-btn\">灯光</button>\n            </div>\n          </div>\n        `\n      },\n      // 宠物模式 - 宠物信息\n      petInfo: {\n        title: '宠物状态',\n        icon: 'fas fa-paw',\n        content: `\n          <div class=\"pet-info-content\">\n            <div class=\"pet-status\">\n              <div class=\"pet-avatar\">🐕</div>\n              <div class=\"pet-details\">\n                <div class=\"pet-name\">小黄</div>\n                <div class=\"pet-mood\">状态：安静</div>\n                <div class=\"pet-time\">留守时间：15分钟</div>\n              </div>\n            </div>\n            <div class=\"pet-message\">\n              我的主人很快就回来！车内温度现在是22°C，很舒适。\n            </div>\n          </div>\n        `\n      },\n      // 宠物模式 - 温度控制\n      climateControl: {\n        title: '温度控制',\n        icon: 'fas fa-thermometer-half',\n        content: `\n          <div class=\"climate-control-content\">\n            <div class=\"temperature-display\">\n              <div class=\"current-temp\">22°C</div>\n              <div class=\"temp-status\">舒适温度</div>\n            </div>\n            <div class=\"climate-controls\">\n              <button class=\"temp-btn\">-</button>\n              <div class=\"temp-range\">18°C - 26°C</div>\n              <button class=\"temp-btn\">+</button>\n            </div>\n            <div class=\"climate-status\">\n              <div class=\"status-item\">✓ 空调：运行中</div>\n              <div class=\"status-item\">✓ 空气循环：开启</div>\n            </div>\n          </div>\n        `\n      },\n      // 洗车模式 - 洗车清单\n      carWashChecklist: {\n        title: '洗车清单',\n        icon: 'fas fa-car',\n        content: `\n          <div class=\"car-wash-checklist-content\">\n            <div class=\"wash-title\">洗车模式已激活</div>\n            <div class=\"checklist-items\">\n              <div class=\"checklist-item completed\">\n                <span class=\"check-icon\">✓</span>\n                <span class=\"check-text\">车窗已关闭并锁定</span>\n              </div>\n              <div class=\"checklist-item completed\">\n                <span class=\"check-icon\">✓</span>\n                <span class=\"check-text\">后视镜已折叠</span>\n              </div>\n              <div class=\"checklist-item completed\">\n                <span class=\"check-icon\">✓</span>\n                <span class=\"check-text\">充电口已锁定</span>\n              </div>\n              <div class=\"checklist-item completed\">\n                <span class=\"check-icon\">✓</span>\n                <span class=\"check-text\">自动雨刷已禁用</span>\n              </div>\n              <div class=\"checklist-item completed\">\n                <span class=\"check-icon\">✓</span>\n                <span class=\"check-text\">空调切换为内循环</span>\n              </div>\n            </div>\n            <div class=\"wash-status\">\n              <div class=\"status-ready\">准备就绪，可以安全洗车！</div>\n            </div>\n          </div>\n        `\n      },\n      // 浪漫模式 - 浪漫音乐\n      romanticMusic: {\n        title: '浪漫音乐',\n        icon: 'fas fa-music',\n        content: `\n          <div class=\"romantic-music-content\">\n            <div class=\"playlist-title\">浪漫爵士乐</div>\n            <div class=\"current-song\">正在播放: Moonlight Serenade</div>\n            <div class=\"music-controls\">\n              <button class=\"control-btn\"><i class=\"fas fa-backward\"></i></button>\n              <button class=\"control-btn\"><i class=\"fas fa-pause\"></i></button>\n              <button class=\"control-btn\"><i class=\"fas fa-forward\"></i></button>\n            </div>\n            <div class=\"playlist-songs\">\n              <div class=\"song-item\">Fly Me to the Moon</div>\n              <div class=\"song-item\">The Way You Look Tonight</div>\n              <div class=\"song-item\">Unchained Melody</div>\n            </div>\n          </div>\n        `\n      },\n      // 浪漫模式 - 氛围灯\n      ambientLight: {\n        title: '氛围灯',\n        icon: 'fas fa-lightbulb',\n        content: `\n          <div class=\"ambient-light-content\">\n            <div class=\"light-title\">氛围灯光</div>\n            <div class=\"color-presets\">\n              <button class=\"color-btn\" style=\"background: #ff69b4\" data-color=\"rose-pink\">玫瑰粉</button>\n              <button class=\"color-btn\" style=\"background: #ffa500\" data-color=\"candle-yellow\">烛光黄</button>\n              <button class=\"color-btn\" style=\"background: #9370db\" data-color=\"star-purple\">星空紫</button>\n            </div>\n            <div class=\"brightness-control\">\n              <div class=\"brightness-label\">亮度: 50%</div>\n              <div class=\"brightness-slider\">\n                <input type=\"range\" min=\"0\" max=\"100\" value=\"50\" class=\"slider\">\n              </div>\n            </div>\n            <div class=\"light-effects\">\n              <button class=\"effect-btn\">呼吸效果</button>\n              <button class=\"effect-btn\">星光闪烁</button>\n            </div>\n          </div>\n        `\n      },\n      // 充电模式 - 娱乐推荐\n      entertainment: {\n        title: '娱乐推荐',\n        icon: 'fas fa-film',\n        content: `\n          <div class=\"entertainment-content\">\n            <div class=\"entertainment-title\">充电期间娱乐</div>\n            <div class=\"entertainment-grid\">\n              <button class=\"entertainment-item\">\n                <i class=\"fas fa-film\"></i>\n                <span>电影</span>\n              </button>\n              <button class=\"entertainment-item\">\n                <i class=\"fas fa-music\"></i>\n                <span>音乐</span>\n              </button>\n              <button class=\"entertainment-item\">\n                <i class=\"fas fa-podcast\"></i>\n                <span>播客</span>\n              </button>\n              <button class=\"entertainment-item\">\n                <i class=\"fas fa-book\"></i>\n                <span>有声书</span>\n              </button>\n              <button class=\"entertainment-item\">\n                <i class=\"fas fa-gamepad\"></i>\n                <span>游戏</span>\n              </button>\n              <button class=\"entertainment-item\">\n                <i class=\"fas fa-newspaper\"></i>\n                <span>新闻</span>\n              </button>\n            </div>\n          </div>\n        `\n      },\n      // 充电模式 - 附近商店\n      nearbyShops: {\n        title: '附近商店',\n        icon: 'fas fa-store',\n        content: `\n          <div class=\"nearby-shops-content\">\n            <div class=\"shops-title\">周边设施</div>\n            <div class=\"shop-items\">\n              <div class=\"shop-item\">\n                <div class=\"shop-icon\">☕</div>\n                <div class=\"shop-info\">\n                  <div class=\"shop-name\">星巴克</div>\n                  <div class=\"shop-distance\">步行2分钟</div>\n                </div>\n                <button class=\"shop-btn\">导航</button>\n              </div>\n              <div class=\"shop-item\">\n                <div class=\"shop-icon\">🛒</div>\n                <div class=\"shop-info\">\n                  <div class=\"shop-name\">便利店</div>\n                  <div class=\"shop-distance\">步行3分钟</div>\n                </div>\n                <button class=\"shop-btn\">导航</button>\n              </div>\n              <div class=\"shop-item\">\n                <div class=\"shop-icon\">🍽️</div>\n                <div class=\"shop-info\">\n                  <div class=\"shop-name\">快餐店</div>\n                  <div class=\"shop-distance\">步行5分钟</div>\n                </div>\n                <button class=\"shop-btn\">导航</button>\n              </div>\n            </div>\n          </div>\n        `\n      },\n      // 紧急情况模式 - 紧急信息\n      emergencyInfo: {\n        title: '紧急信息',\n        icon: 'fas fa-exclamation-triangle',\n        content: `\n          <div class=\"emergency-info-content\">\n            <div class=\"emergency-title\">🚨 检测到紧急情况</div>\n            <div class=\"emergency-status\">\n              <div class=\"status-item\">✓ 已自动联系救援服务</div>\n              <div class=\"status-item\">✓ 救护车预计15分钟到达</div>\n              <div class=\"status-item\">✓ 位置信息已发送</div>\n            </div>\n            <div class=\"emergency-actions\">\n              <button class=\"emergency-action-btn\">我没事</button>\n              <button class=\"emergency-action-btn danger\">需要帮助</button>\n              <button class=\"emergency-action-btn\">联系家人</button>\n            </div>\n          </div>\n        `\n      },\n      // 紧急情况模式 - 急救指导\n      firstAid: {\n        title: '急救指导',\n        icon: 'fas fa-first-aid',\n        content: `\n          <div class=\"first-aid-content\">\n            <div class=\"first-aid-title\">🏥 急救指导</div>\n            <div class=\"first-aid-steps\">\n              <div class=\"step-item\">\n                <div class=\"step-number\">1</div>\n                <div class=\"step-text\">检查意识并呼救</div>\n              </div>\n              <div class=\"step-item\">\n                <div class=\"step-number\">2</div>\n                <div class=\"step-text\">检查呼吸和脉搏</div>\n              </div>\n              <div class=\"step-item\">\n                <div class=\"step-number\">3</div>\n                <div class=\"step-text\">保持体温和舒适体位</div>\n              </div>\n              <div class=\"step-item\">\n                <div class=\"step-number\">4</div>\n                <div class=\"step-text\">等待专业救援到达</div>\n              </div>\n            </div>\n            <div class=\"first-aid-warning\">\n              ⚠️ 请保持冷静，按照语音指导操作\n            </div>\n          </div>\n        `\n      },\n      // 疲劳检测模式 - 休息区信息\n      restArea: {\n        title: '休息区信息',\n        icon: 'fas fa-parking',\n        content: `\n          <div class=\"rest-area-content\">\n            <div class=\"rest-area-title\">🛣️ 最近服务区</div>\n            <div class=\"rest-area-info\">\n              <div class=\"info-item\">\n                <div class=\"info-icon\">📍</div>\n                <div class=\"info-details\">\n                  <div class=\"info-name\">白云服务区</div>\n                  <div class=\"info-distance\">距离: 5km | 预计: 8分钟</div>\n                </div>\n              </div>\n              <div class=\"facilities-list\">\n                <div class=\"facility-item\">⛽ 加油站</div>\n                <div class=\"facility-item\">⚡ 充电桩</div>\n                <div class=\"facility-item\">🍽️ 餐厅</div>\n                <div class=\"facility-item\">🚻 休息室</div>\n                <div class=\"facility-item\">🛒 便利店</div>\n              </div>\n            </div>\n            <div class=\"rest-area-actions\">\n              <button class=\"action-btn primary\">导航前往</button>\n              <button class=\"action-btn secondary\">查看详情</button>\n            </div>\n          </div>\n        `\n      },\n      // 疲劳检测模式 - 提神建议\n      refreshment: {\n        title: '提神建议',\n        icon: 'fas fa-coffee',\n        content: `\n          <div class=\"refreshment-content\">\n            <div class=\"refreshment-title\">☕ 提神建议</div>\n            <div class=\"refreshment-categories\">\n              <div class=\"category\">\n                <div class=\"category-title\">饮品选择</div>\n                <div class=\"refreshment-items\">\n                  <button class=\"refreshment-btn\">☕ 咖啡</button>\n                  <button class=\"refreshment-btn\">🍵 茶</button>\n                  <button class=\"refreshment-btn\">🥤 功能饮料</button>\n                  <button class=\"refreshment-btn\">💧 冰水</button>\n                </div>\n              </div>\n              <div class=\"category\">\n                <div class=\"category-title\">活动建议</div>\n                <div class=\"refreshment-items\">\n                  <button class=\"refreshment-btn\">🚶‍♂️ 下车活动</button>\n                  <button class=\"refreshment-btn\">🧴 洗脸</button>\n                  <button class=\"refreshment-btn\">🎵 播放提神音乐</button>\n                  <button class=\"refreshment-btn\">💤 短暂休息</button>\n                </div>\n              </div>\n            </div>\n            <div class=\"refreshment-tips\">\n              <div class=\"tip-item\">💡 建议每2小时休息15分钟</div>\n              <div class=\"tip-item\">💡 避免空腹或过饱驾驶</div>\n            </div>\n          </div>\n        `\n      },\n      // 用户切换模式 - 用户选择器\n      userSelector: {\n        title: '用户选择',\n        icon: 'fas fa-users',\n        content: `\n          <div class=\"user-selector-content\">\n            <div class=\"selector-title\">👥 选择用户配置文件</div>\n            <div class=\"user-grid\">\n              <div class=\"user-card active\">\n                <div class=\"user-avatar\">👨</div>\n                <div class=\"user-info\">\n                  <div class=\"user-name\">张先生</div>\n                  <div class=\"user-role\">主驾驶员</div>\n                  <div class=\"user-status\">✅ 当前</div>\n                </div>\n              </div>\n              <div class=\"user-card\">\n                <div class=\"user-avatar\">👩</div>\n                <div class=\"user-info\">\n                  <div class=\"user-name\">李女士</div>\n                  <div class=\"user-role\">副驾驶员</div>\n                  <div class=\"user-status\">○ 离线</div>\n                </div>\n              </div>\n              <div class=\"user-card\">\n                <div class=\"user-avatar\">👶</div>\n                <div class=\"user-info\">\n                  <div class=\"user-name\">儿童模式</div>\n                  <div class=\"user-role\">后排乘客</div>\n                  <div class=\"user-status\">○ 未激活</div>\n                </div>\n              </div>\n            </div>\n            <div class=\"selector-actions\">\n              <button class=\"selector-btn\">🎤 语音识别</button>\n              <button class=\"selector-btn\">👤 访客模式</button>\n              <button class=\"selector-btn\">⚙️ 设置</button>\n            </div>\n          </div>\n        `\n      },\n      // 用户切换模式 - 用户偏好\n      userPreferences: {\n        title: '用户偏好',\n        icon: 'fas fa-heart',\n        content: `\n          <div class=\"user-preferences-content\">\n            <div class=\"preferences-title\">🎯 个人偏好设置</div>\n            <div class=\"preference-categories\">\n              <div class=\"preference-group\">\n                <div class=\"group-title\">🎵 音乐偏好</div>\n                <div class=\"preference-items\">\n                  <div class=\"preference-item\">\n                    <div class=\"item-label\">音乐风格</div>\n                    <div class=\"item-value\">流行音乐</div>\n                  </div>\n                  <div class=\"preference-item\">\n                    <div class=\"item-label\">音量偏好</div>\n                    <div class=\"item-value\">中等</div>\n                  </div>\n                </div>\n              </div>\n              <div class=\"preference-group\">\n                <div class=\"group-title\">🌡️ 舒适设置</div>\n                <div class=\"preference-items\">\n                  <div class=\"preference-item\">\n                    <div class=\"item-label\">座椅位置</div>\n                    <div class=\"item-value\">位置3</div>\n                  </div>\n                  <div class=\"preference-item\">\n                    <div class=\"item-label\">空调温度</div>\n                    <div class=\"item-value\">22°C</div>\n                  </div>\n                </div>\n              </div>\n              <div class=\"preference-group\">\n                <div class=\"group-title\">🗺️ 导航偏好</div>\n                <div class=\"preference-items\">\n                  <div class=\"preference-item\">\n                    <div class=\"item-label\">路线选择</div>\n                    <div class=\"item-value\">避开拥堵</div>\n                  </div>\n                  <div class=\"preference-item\">\n                    <div class=\"item-label\">语音播报</div>\n                    <div class=\"item-value\">开启</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"preferences-actions\">\n              <button class=\"preference-btn\">编辑偏好</button>\n              <button class=\"preference-btn\">同步到云端</button>\n            </div>\n          </div>\n        `\n      },\n      // 用户切换模式 - 隐私设置\n      privacySettings: {\n        title: '隐私设置',\n        icon: 'fas fa-shield-alt',\n        content: `\n          <div class=\"privacy-settings-content\">\n            <div class=\"privacy-title\">🛡️ 隐私与安全</div>\n            <div class=\"privacy-items\">\n              <div class=\"privacy-item\">\n                <div class=\"privacy-icon\">📍</div>\n                <div class=\"privacy-info\">\n                  <div class=\"privacy-label\">位置信息</div>\n                  <div class=\"privacy-desc\">仅行程中使用</div>\n                </div>\n                <div class=\"privacy-toggle\">✅</div>\n              </div>\n              <div class=\"privacy-item\">\n                <div class=\"privacy-icon\">🎤</div>\n                <div class=\"privacy-info\">\n                  <div class=\"privacy-label\">语音数据</div>\n                  <div class=\"privacy-desc\">本地处理</div>\n                </div>\n                <div class=\"privacy-toggle\">✅</div>\n              </div>\n              <div class=\"privacy-item\">\n                <div class=\"privacy-icon\">📊</div>\n                <div class=\"privacy-info\">\n                  <div class=\"privacy-label\">使用统计</div>\n                  <div class=\"privacy-desc\">匿名收集</div>\n                </div>\n                <div class=\"privacy-toggle\">⚪</div>\n              </div>\n              <div class=\"privacy-item\">\n                <div class=\"privacy-icon\">🔄</div>\n                <div class=\"privacy-info\">\n                  <div class=\"privacy-label\">数据同步</div>\n                  <div class=\"privacy-desc\">加密传输</div>\n                </div>\n                <div class=\"privacy-toggle\">✅</div>\n              </div>\n            </div>\n            <div class=\"privacy-actions\">\n              <button class=\"privacy-btn primary\">详细设置</button>\n              <button class=\"privacy-btn secondary\">重置隐私</button>\n            </div>\n          </div>\n        `\n      },\n      // 智能泊车模式 - 停车位搜索\n      parkingSearch: {\n        title: '可用车位',\n        icon: 'fas fa-parking',\n        content: `\n          <div class=\"parking-search-content\">\n            <div class=\"parking-title\">🅿️ 可用车位</div>\n            <div class=\"parking-spots\">\n              <div class=\"parking-spot recommended\">\n                <div class=\"spot-header\">\n                  <div class=\"spot-id\">B2-15</div>\n                  <div class=\"spot-recommend\">推荐</div>\n                </div>\n                <div class=\"spot-details\">\n                  <div class=\"spot-distance\">距离: 50m</div>\n                  <div class=\"spot-size\">宽度: 标准</div>\n                  <div class=\"spot-type\">类型: 地下</div>\n                </div>\n                <div class=\"spot-status\">\n                  <div class=\"status-indicator available\"></div>\n                  <span>可用</span>\n                </div>\n              </div>\n              <div class=\"parking-spot\">\n                <div class=\"spot-header\">\n                  <div class=\"spot-id\">B1-08</div>\n                  <div class=\"spot-alternative\">备选</div>\n                </div>\n                <div class=\"spot-details\">\n                  <div class=\"spot-distance\">距离: 80m</div>\n                  <div class=\"spot-size\">宽度: 宽体</div>\n                  <div class=\"spot-type\">类型: 地下</div>\n                </div>\n                <div class=\"spot-status\">\n                  <div class=\"status-indicator available\"></div>\n                  <span>可用</span>\n                </div>\n              </div>\n              <div class=\"parking-spot\">\n                <div class=\"spot-header\">\n                  <div class=\"spot-id\">A3-22</div>\n                  <div class=\"spot-alternative\">备选</div>\n                </div>\n                <div class=\"spot-details\">\n                  <div class=\"spot-distance\">距离: 120m</div>\n                  <div class=\"spot-size\">宽度: 标准</div>\n                  <div class=\"spot-type\">类型: 露天</div>\n                </div>\n                <div class=\"spot-status\">\n                  <div class=\"status-indicator limited\"></div>\n                  <span>紧张</span>\n                </div>\n              </div>\n            </div>\n            <div class=\"parking-actions\">\n              <button class=\"parking-btn primary\">导航至推荐车位</button>\n              <button class=\"parking-btn secondary\">查看更多车位</button>\n            </div>\n          </div>\n        `\n      },\n      // 智能泊车模式 - 泊车辅助\n      parkingAssist: {\n        title: '泊车辅助',\n        icon: 'fas fa-car-side',\n        content: `\n          <div class=\"parking-assist-content\">\n            <div class=\"assist-title\">🚗 泊车辅助</div>\n            <div class=\"assist-modes\">\n              <div class=\"assist-mode\">\n                <div class=\"mode-icon\">🤖</div>\n                <div class=\"mode-info\">\n                  <div class=\"mode-name\">自动泊车</div>\n                  <div class=\"mode-desc\">系统自动完成泊车</div>\n                </div>\n                <button class=\"mode-btn\">启动</button>\n              </div>\n              <div class=\"assist-mode\">\n                <div class=\"mode-icon\">🎯</div>\n                <div class=\"mode-info\">\n                  <div class=\"mode-name\">辅助泊车</div>\n                  <div class=\"mode-desc\">方向盘和刹车辅助</div>\n                </div>\n                <button class=\"mode-btn\">启动</button>\n              </div>\n              <div class=\"assist-mode\">\n                <div class=\"mode-icon\">👤</div>\n                <div class=\"mode-info\">\n                  <div class=\"mode-name\">手动泊车</div>\n                  <div class=\"mode-desc\">仅提供指引</div>\n                </div>\n                <button class=\"mode-btn\">启动</button>\n              </div>\n            </div>\n            <div class=\"camera-view\">\n              <div class=\"view-title\">📹 环视影像</div>\n              <div class=\"camera-grid\">\n                <button class=\"camera-btn\" data-camera=\"front\">前</button>\n                <button class=\"camera-btn\" data-camera=\"rear\">后</button>\n                <button class=\"camera-btn\" data-camera=\"left\">左</button>\n                <button class=\"camera-btn\" data-camera=\"right\">右</button>\n              </div>\n              <div class=\"view-status\">\n                <div class=\"status-item\">✓ 摄像头已激活</div>\n                <div class=\"status-item\">✓ 雷达扫描中</div>\n              </div>\n            </div>\n          </div>\n        `\n      },\n      // 智能泊车模式 - 费用信息\n      costInfo: {\n        title: '费用信息',\n        icon: 'fas fa-coins',\n        content: `\n          <div class=\"cost-info-content\">\n            <div class=\"cost-title\">💰 停车费用</div>\n            <div class=\"pricing-options\">\n              <div class=\"pricing-option\">\n                <div class=\"option-header\">\n                  <div class=\"option-duration\">2小时</div>\n                  <div class=\"option-price\">¥10</div>\n                </div>\n                <div class=\"option-rate\">费率: ¥5/小时</div>\n                <div class=\"option-details\">\n                  <div class=\"detail-item\">• 前30分钟免费</div>\n                  <div class=\"detail-item\">• 超时¥8/小时</div>\n                </div>\n              </div>\n              <div class=\"pricing-option recommended\">\n                <div class=\"option-header\">\n                  <div class=\"option-duration\">全天</div>\n                  <div class=\"option-price\">¥30</div>\n                </div>\n                <div class=\"option-rate\">费率: ¥30/天</div>\n                <div class=\"option-details\">\n                  <div class=\"detail-item\">• 24小时有效</div>\n                  <div class=\"detail-item\">• 可多次进出</div>\n                </div>\n              </div>\n              <div class=\"pricing-option\">\n                <div class=\"option-header\">\n                  <div class=\"option-duration\">包月</div>\n                  <div class=\"option-price\">¥200</div>\n                </div>\n                <div class=\"option-rate\">费率: ¥200/月</div>\n                <div class=\"option-details\">\n                  <div class=\"detail-item\">• 固定车位</div>\n                  <div class=\"detail-item\">• 专属权限</div>\n                </div>\n              </div>\n            </div>\n            <div class=\"payment-section\">\n              <div class=\"payment-title\">支付方式</div>\n              <div class=\"payment-methods\">\n                <button class=\"payment-btn\">💳 支付宝</button>\n                <button class=\"payment-btn\">💳 微信支付</button>\n                <button class=\"payment-btn\">💳 银行卡</button>\n                <button class=\"payment-btn\">🚗 ETC</button>\n              </div>\n            </div>\n            <div class=\"discount-section\">\n              <div class=\"discount-title\">🎫 优惠券</div>\n              <div class=\"discount-items\">\n                <div class=\"discount-item\">\n                  <div class=\"discount-name\">新用户9折</div>\n                  <button class=\"discount-use\">使用</button>\n                </div>\n                <div class=\"discount-item\">\n                  <div class=\"discount-name\">周末8折</div>\n                  <button class=\"discount-use\">使用</button>\n                </div>\n              </div>\n            </div>\n          </div>\n        `\n      }\n    };\n    const getCardTitle = () => {\n      return cardConfigs[props.cardType]?.title || props.cardType;\n    };\n    const getCardIcon = () => {\n      return cardConfigs[props.cardType]?.icon || 'fas fa-cube';\n    };\n    const getCardContent = () => {\n      // 特殊处理VPA组件，根据场景显示不同内容\n      if (props.cardType === 'vpaWidget') {\n        return getVpaContent();\n      }\n      return cardConfigs[props.cardType]?.content || `<div class=\"placeholder\">卡片内容: ${props.cardType}</div>`;\n    };\n    const getVpaContent = () => {\n      // 根据场景ID返回不同的VPA内容\n      switch (props.scene.id) {\n        case 'familyTrip':\n          return `\n            <div class=\"vpa-widget-content family-trip-vpa\">\n              <div class=\"vpa-storyteller-mode\">\n                <div class=\"storyteller-header\">\n                  <h3>🎭 故事讲述模式</h3>\n                  <p>小智正在为孩子们准备精彩的故事</p>\n                </div>\n                <div class=\"story-content\">\n                  <div class=\"current-story\">\n                    <div class=\"story-title\">🌳 森林探险记</div>\n                    <div class=\"story-progress\">\n                      <div class=\"progress-bar\">\n                        <div class=\"progress-fill\" style=\"width: 35%\"></div>\n                      </div>\n                      <span class=\"progress-text\">第3章 / 共8章</span>\n                    </div>\n                  </div>\n                  <div class=\"story-controls\">\n                    <button class=\"story-btn pause\">⏸️ 暂停</button>\n                    <button class=\"story-btn next\">⏭️ 下一章</button>\n                    <button class=\"story-btn change\">🔄 换个故事</button>\n                  </div>\n                  <div class=\"story-options\">\n                    <div class=\"option-title\">📚 故事库</div>\n                    <div class=\"story-list\">\n                      <button class=\"story-option\">🦁 动物王国</button>\n                      <button class=\"story-option\">🚀 太空冒险</button>\n                      <button class=\"story-option\">🏰 童话城堡</button>\n                      <button class=\"story-option\">🌊 海底世界</button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              <div class=\"vpa-avatar-container\">\n                <img src=\"/images/vpa2.gif\" alt=\"VPA智能助手\" class=\"vpa-avatar storyteller\" />\n                <div class=\"avatar-status\">正在讲故事...</div>\n              </div>\n            </div>\n          `;\n        default:\n          return cardConfigs.vpaWidget.content;\n      }\n    };\n    const getCardStyle = () => {\n      // 根据场景主题返回不同的样式\n      const themeStyles = {\n        warm: {\n          background: 'rgba(255, 193, 7, 0.15)',\n          border: '1px solid rgba(255, 193, 7, 0.3)'\n        },\n        calm: {\n          background: 'rgba(0, 123, 255, 0.15)',\n          border: '1px solid rgba(0, 123, 255, 0.3)'\n        },\n        evening: {\n          background: 'rgba(255, 87, 34, 0.15)',\n          border: '1px solid rgba(255, 87, 34, 0.3)'\n        },\n        dark: {\n          background: 'rgba(33, 37, 41, 0.15)',\n          border: '1px solid rgba(33, 37, 41, 0.3)'\n        },\n        emergency: {\n          background: 'rgba(220, 53, 69, 0.15)',\n          border: '1px solid rgba(220, 53, 69, 0.3)'\n        }\n      };\n      return themeStyles[props.scene.theme] || {};\n    };\n    return {\n      getCardTitle,\n      getCardIcon,\n      getCardContent,\n      getCardStyle\n    };\n  }\n};", "map": {"version": 3, "names": ["GlassCard", "name", "components", "props", "scene", "type", "Object", "required", "cardType", "String", "themeColors", "default", "setup", "cardConfigs", "navigation", "title", "icon", "content", "music", "todo", "kidEducation", "pedia", "videoPlayer", "news", "ambientSound", "vpaWidget", "dynamicIsland", "smartHome", "orderStatus", "rearSeatControl", "facilityFinder", "tripReminder", "serviceArea", "driverStatus", "vehicleStatus", "chargingStatus", "fatigueWarning", "emergencyContact", "tempNavigation", "basicMusic", "basicControl", "petInfo", "climateControl", "carWash<PERSON>heck<PERSON>", "romanticMusic", "ambientLight", "entertainment", "nearbyShops", "emergencyInfo", "firstAid", "restArea", "refreshment", "userSelector", "userPreferences", "privacySettings", "parkingSearch", "parkingAssist", "costInfo", "getCardTitle", "getCardIcon", "getCardContent", "getVpaC<PERSON>nt", "id", "getCardStyle", "themeStyles", "warm", "background", "border", "calm", "evening", "dark", "emergency", "theme"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\DefaultCard.vue"], "sourcesContent": ["<template>\r\n  <div class=\"default-card\" :class=\"[`card-${cardType}`, `layout-${scene.layout}`]\">\r\n    <GlassCard\r\n      :title=\"getCardTitle()\"\r\n      :icon=\"getCardIcon()\"\r\n      :style=\"getCardStyle()\"\r\n      :theme-colors=\"themeColors\"\r\n    >\r\n      <div class=\"card-content\" v-html=\"getCardContent()\"></div>\r\n    </GlassCard>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport GlassCard from '../GlassCard.vue'\r\n\r\nexport default {\r\n  name: 'DefaultCard',\r\n  components: {\r\n    GlassCard\r\n  },\r\n  props: {\r\n    scene: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    cardType: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    themeColors: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n\r\n  setup(props) {\r\n    // 卡片配置映射\r\n    const cardConfigs = {\r\n      // 导航相关\r\n      navigation: {\r\n        title: '导航',\r\n        icon: 'fas fa-route',\r\n        content: `\r\n          <div class=\"navigation-content\">\r\n            <div class=\"destination\">前往公司</div>\r\n            <div class=\"route-info\">\r\n              <span class=\"distance\">12.5 公里</span>\r\n              <span class=\"duration\">25 分钟</span>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n      \r\n      // 音乐相关\r\n      music: {\r\n        title: '音乐控制',\r\n        icon: 'fas fa-music',\r\n        content: `\r\n          <div class=\"music-content\">\r\n            <div class=\"current-song\">正在播放: 日落大道</div>\r\n            <div class=\"music-controls\">\r\n              <button class=\"control-btn\"><i class=\"fas fa-backward\"></i></button>\r\n              <button class=\"control-btn\"><i class=\"fas fa-pause\"></i></button>\r\n              <button class=\"control-btn\"><i class=\"fas fa-forward\"></i></button>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n      \r\n      // 待办事项\r\n      todo: {\r\n        title: '今日待办',\r\n        icon: 'fas fa-tasks',\r\n        content: `\r\n          <div class=\"todo-content\">\r\n            <div class=\"todo-item\">• 10:00 团队会议</div>\r\n            <div class=\"todo-item\">• 14:00 项目汇报</div>\r\n            <div class=\"todo-item\">• 16:30 客户电话</div>\r\n          </div>\r\n        `\r\n      },\r\n      \r\n      // 儿童教育\r\n      kidEducation: {\r\n        title: '儿童教育',\r\n        icon: 'fas fa-graduation-cap',\r\n        content: `\r\n          <div class=\"kid-education-content\">\r\n            <div class=\"video-placeholder\">\r\n              <i class=\"fas fa-play-circle\"></i>\r\n              <span>正在播放儿童视频...</span>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n      \r\n      // 百科问答\r\n      pedia: {\r\n        title: '百科问答',\r\n        icon: 'fas fa-question-circle',\r\n        content: `\r\n          <div class=\"pedia-content\">\r\n            <div class=\"question\">地球是圆的吗？</div>\r\n            <div class=\"answer\">是的，地球是一个近似球形的星球...</div>\r\n          </div>\r\n        `\r\n      },\r\n      \r\n      // 视频播放器\r\n      videoPlayer: {\r\n        title: '视频播放器',\r\n        icon: 'fas fa-video',\r\n        content: `\r\n          <div class=\"video-player-content\">\r\n            <div class=\"video-screen\">\r\n              <i class=\"fas fa-play\"></i>\r\n              <span>视频播放中</span>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n      \r\n      // 新闻摘要\r\n      news: {\r\n        title: '新闻摘要',\r\n        icon: 'fas fa-newspaper',\r\n        content: `\r\n          <div class=\"news-content\">\r\n            <div class=\"news-item\">• AI技术最新进展</div>\r\n            <div class=\"news-item\">• 智能汽车行业动态</div>\r\n            <div class=\"news-item\">• 科技创新资讯</div>\r\n          </div>\r\n        `\r\n      },\r\n      \r\n      // 环境音\r\n      ambientSound: {\r\n        title: '环境音',\r\n        icon: 'fas fa-volume-up',\r\n        content: `\r\n          <div class=\"ambient-sound-content\">\r\n            <button class=\"sound-btn\">雨声</button>\r\n            <button class=\"sound-btn\">森林</button>\r\n            <button class=\"sound-btn\">冥想</button>\r\n          </div>\r\n        `\r\n      },\r\n\r\n      // VPA小窗\r\n      vpaWidget: {\r\n        title: '',\r\n        icon: '',\r\n        content: `\r\n          <div class=\"vpa-widget-content\">\r\n            <div class=\"vpa-conversation\">\r\n              <p class=\"vpa-greeting\">你好，我是小智，欢迎来到AI-HMI，我是智能桌面助手，可以根据场景定制专属桌面，这是对我说：</p>\r\n              <div class=\"vpa-actions\">\r\n                <button class=\"vpa-action-btn\">生成通勤桌面</button>\r\n                <button class=\"vpa-action-btn\">导航3D效果</button>\r\n                <button class=\"vpa-action-btn\">帮我规划一个独处的桌面</button>\r\n                <button class=\"vpa-action-btn\">生成春游桌面</button>\r\n                <button class=\"vpa-action-btn\">帮我规划一个周末一日游</button>\r\n              </div>\r\n              <p class=\"vpa-footer\">根据你的使用习惯，后续会全面智能化桌面，或者也可以在AI-LAB设置你的更多介绍</p>\r\n            </div>\r\n            <img src=\"/images/vpa2.gif\" alt=\"VPA智能助手\" class=\"vpa-avatar\" />\r\n          </div>\r\n        `\r\n      },\r\n\r\n      dynamicIsland: {\r\n        title: '',\r\n        icon: '',\r\n        content: `\r\n          <div class=\"dynamic-island-content\">\r\n            <div class=\"island-info\">\r\n              <i class=\"fas fa-route\"></i>\r\n              <span class=\"island-text\">G2高速 - 距离下一出口 25km</span>\r\n            </div>\r\n            <div class=\"island-actions\">\r\n              <button class=\"island-btn\"><i class=\"fas fa-map\"></i></button>\r\n              <button class=\"island-btn\"><i class=\"fas fa-phone\"></i></button>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n      \r\n      // 智能家居\r\n      smartHome: {\r\n        title: '智能家居',\r\n        icon: 'fas fa-home',\r\n        content: `\r\n          <div class=\"smart-home-content\">\r\n            <div class=\"home-control\">\r\n              <button class=\"home-btn\">客厅空调: 开</button>\r\n              <button class=\"home-btn\">空气净化器: 自动</button>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n      \r\n      // 订单状态\r\n      orderStatus: {\r\n        title: '订单状态',\r\n        icon: 'fas fa-shopping-bag',\r\n        content: `\r\n          <div class=\"order-status-content\">\r\n            <div class=\"order-info\">麦当劳早餐已下单</div>\r\n            <div class=\"order-time\">预计15分钟后取餐</div>\r\n          </div>\r\n        `\r\n      },\r\n      \r\n      // 后座娱乐控制\r\n      rearSeatControl: {\r\n        title: '后座娱乐',\r\n        icon: 'fas fa-gamepad',\r\n        content: `\r\n          <div class=\"rear-seat-content\">\r\n            <div class=\"entertainment-header\">\r\n              <h4>🎮 后座娱乐中心</h4>\r\n              <div class=\"seat-status\">\r\n                <span class=\"seat-indicator active\">左座</span>\r\n                <span class=\"seat-indicator active\">右座</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"entertainment-options\">\r\n              <button class=\"entertainment-btn active\">📺 动画片</button>\r\n              <button class=\"entertainment-btn\">🎵 儿歌</button>\r\n              <button class=\"entertainment-btn\">🎮 小游戏</button>\r\n              <button class=\"entertainment-btn\">📚 有声书</button>\r\n            </div>\r\n            <div class=\"volume-control\">\r\n              <span class=\"volume-label\">🔊 音量</span>\r\n              <div class=\"volume-slider\">\r\n                <input type=\"range\" min=\"0\" max=\"100\" value=\"60\" class=\"slider\">\r\n              </div>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n      \r\n      // 设施查找\r\n      facilityFinder: {\r\n        title: '智能设施查找',\r\n        icon: 'fas fa-map-marker-alt',\r\n        content: `\r\n          <div class=\"facility-finder-content\">\r\n            <div class=\"finder-header\">\r\n              <h4>🗺️ 智能设施查找</h4>\r\n              <div class=\"location-status\">📍 当前位置: 森林公园</div>\r\n            </div>\r\n            <div class=\"facility-grid\">\r\n              <button class=\"facility-btn urgent\">🚻 洗手间</button>\r\n              <button class=\"facility-btn\">🍽️ 餐厅</button>\r\n              <button class=\"facility-btn\">⛽ 加油站</button>\r\n              <button class=\"facility-btn\">🅿️ 停车场</button>\r\n            </div>\r\n            <div class=\"nearby-info\">\r\n              <div class=\"info-item\">\r\n                <span class=\"facility-icon\">🚻</span>\r\n                <span class=\"facility-name\">公园洗手间</span>\r\n                <span class=\"facility-distance\">200m</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n      \r\n      // 行程提醒\r\n      tripReminder: {\r\n        title: '行程助理',\r\n        icon: 'fas fa-clock',\r\n        content: `\r\n          <div class=\"trip-reminder-content\">\r\n            <div class=\"assistant-header\">\r\n              <h4>⏰ 行程助理</h4>\r\n              <div class=\"trip-time\">出游时间: 2小时15分钟</div>\r\n            </div>\r\n            <div class=\"reminder-list\">\r\n              <div class=\"reminder-item active\">\r\n                <span class=\"reminder-icon\">🍿</span>\r\n                <div class=\"reminder-info\">\r\n                  <div class=\"reminder-title\">零食时间</div>\r\n                  <div class=\"reminder-time\">15分钟后</div>\r\n                </div>\r\n              </div>\r\n              <div class=\"reminder-item\">\r\n                <span class=\"reminder-icon\">🚻</span>\r\n                <div class=\"reminder-info\">\r\n                  <div class=\"reminder-title\">休息提醒</div>\r\n                  <div class=\"reminder-time\">45分钟后</div>\r\n                </div>\r\n              </div>\r\n              <div class=\"reminder-item\">\r\n                <span class=\"reminder-icon\">🏠</span>\r\n                <div class=\"reminder-info\">\r\n                  <div class=\"reminder-title\">返程提醒</div>\r\n                  <div class=\"reminder-time\">3小时后</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"assistant-actions\">\r\n              <button class=\"assistant-btn\">➕ 添加提醒</button>\r\n              <button class=\"assistant-btn\">⚙️ 设置</button>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n      \r\n      // 服务区信息\r\n      serviceArea: {\r\n        title: '服务区信息',\r\n        icon: 'fas fa-gas-pump',\r\n        content: `\r\n          <div class=\"service-area-content\">\r\n            <div class=\"service-info\">前方5公里有服务区</div>\r\n            <div class=\"services\">加油站 • 餐厅 • 休息区</div>\r\n          </div>\r\n        `\r\n      },\r\n      \r\n      // 驾驶员状态\r\n      driverStatus: {\r\n        title: '驾驶员状态',\r\n        icon: 'fas fa-user',\r\n        content: `\r\n          <div class=\"driver-status-content\">\r\n            <div class=\"status-item\">疲劳度: 正常</div>\r\n            <div class=\"status-item\">注意力: 集中</div>\r\n          </div>\r\n        `\r\n      },\r\n      \r\n      // 车辆状态\r\n      vehicleStatus: {\r\n        title: '车辆状态',\r\n        icon: 'fas fa-car',\r\n        content: `\r\n          <div class=\"vehicle-status-content\">\r\n            <div class=\"status-item\">油量: 75%</div>\r\n            <div class=\"status-item\">轮胎压力: 正常</div>\r\n          </div>\r\n        `\r\n      },\r\n      \r\n      // 充电状态\r\n      chargingStatus: {\r\n        title: '充电状态',\r\n        icon: 'fas fa-battery-three-quarters',\r\n        content: `\r\n          <div class=\"charging-status-content\">\r\n            <div class=\"charge-info\">当前电量: 65%</div>\r\n            <div class=\"charge-time\">预计充满: 45分钟</div>\r\n          </div>\r\n        `\r\n      },\r\n      \r\n      // 疲劳预警\r\n      fatigueWarning: {\r\n        title: '疲劳预警',\r\n        icon: 'fas fa-exclamation-triangle',\r\n        content: `\r\n          <div class=\"fatigue-warning-content\">\r\n            <div class=\"warning-text\">检测到疲劳驾驶</div>\r\n            <div class=\"suggestion\">建议就近休息</div>\r\n          </div>\r\n        `\r\n      },\r\n      \r\n      // 紧急联系\r\n      emergencyContact: {\r\n        title: '紧急联系',\r\n        icon: 'fas fa-phone',\r\n        content: `\r\n          <div class=\"emergency-contact-content\">\r\n            <button class=\"emergency-btn\">拨打120</button>\r\n            <button class=\"emergency-btn\">联系家人</button>\r\n          </div>\r\n        `\r\n      },\r\n\r\n      // 访客模式 - 临时导航\r\n      tempNavigation: {\r\n        title: '临时导航',\r\n        icon: 'fas fa-map',\r\n        content: `\r\n          <div class=\"temp-navigation-content\">\r\n            <div class=\"nav-input\">\r\n              <input type=\"text\" placeholder=\"请输入临时目的地\" class=\"destination-input\">\r\n              <button class=\"nav-btn\">前往</button>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n\r\n      // 访客模式 - 基础音乐\r\n      basicMusic: {\r\n        title: '基础音乐',\r\n        icon: 'fas fa-music',\r\n        content: `\r\n          <div class=\"basic-music-content\">\r\n            <div class=\"music-source\">\r\n              <button class=\"source-btn\">FM 97.4</button>\r\n              <button class=\"source-btn\">蓝牙音乐</button>\r\n            </div>\r\n            <div class=\"basic-controls\">\r\n              <button class=\"control-btn\"><i class=\"fas fa-backward\"></i></button>\r\n              <button class=\"control-btn\"><i class=\"fas fa-pause\"></i></button>\r\n              <button class=\"control-btn\"><i class=\"fas fa-forward\"></i></button>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n\r\n      // 访客模式 - 基础控制\r\n      basicControl: {\r\n        title: '基础控制',\r\n        icon: 'fas fa-cog',\r\n        content: `\r\n          <div class=\"basic-control-content\">\r\n            <div class=\"control-grid\">\r\n              <button class=\"control-grid-btn\">空调</button>\r\n              <button class=\"control-grid-btn\">车窗</button>\r\n              <button class=\"control-grid-btn\">音量</button>\r\n              <button class=\"control-grid-btn\">灯光</button>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n\r\n      // 宠物模式 - 宠物信息\r\n      petInfo: {\r\n        title: '宠物状态',\r\n        icon: 'fas fa-paw',\r\n        content: `\r\n          <div class=\"pet-info-content\">\r\n            <div class=\"pet-status\">\r\n              <div class=\"pet-avatar\">🐕</div>\r\n              <div class=\"pet-details\">\r\n                <div class=\"pet-name\">小黄</div>\r\n                <div class=\"pet-mood\">状态：安静</div>\r\n                <div class=\"pet-time\">留守时间：15分钟</div>\r\n              </div>\r\n            </div>\r\n            <div class=\"pet-message\">\r\n              我的主人很快就回来！车内温度现在是22°C，很舒适。\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n\r\n      // 宠物模式 - 温度控制\r\n      climateControl: {\r\n        title: '温度控制',\r\n        icon: 'fas fa-thermometer-half',\r\n        content: `\r\n          <div class=\"climate-control-content\">\r\n            <div class=\"temperature-display\">\r\n              <div class=\"current-temp\">22°C</div>\r\n              <div class=\"temp-status\">舒适温度</div>\r\n            </div>\r\n            <div class=\"climate-controls\">\r\n              <button class=\"temp-btn\">-</button>\r\n              <div class=\"temp-range\">18°C - 26°C</div>\r\n              <button class=\"temp-btn\">+</button>\r\n            </div>\r\n            <div class=\"climate-status\">\r\n              <div class=\"status-item\">✓ 空调：运行中</div>\r\n              <div class=\"status-item\">✓ 空气循环：开启</div>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n\r\n      // 洗车模式 - 洗车清单\r\n      carWashChecklist: {\r\n        title: '洗车清单',\r\n        icon: 'fas fa-car',\r\n        content: `\r\n          <div class=\"car-wash-checklist-content\">\r\n            <div class=\"wash-title\">洗车模式已激活</div>\r\n            <div class=\"checklist-items\">\r\n              <div class=\"checklist-item completed\">\r\n                <span class=\"check-icon\">✓</span>\r\n                <span class=\"check-text\">车窗已关闭并锁定</span>\r\n              </div>\r\n              <div class=\"checklist-item completed\">\r\n                <span class=\"check-icon\">✓</span>\r\n                <span class=\"check-text\">后视镜已折叠</span>\r\n              </div>\r\n              <div class=\"checklist-item completed\">\r\n                <span class=\"check-icon\">✓</span>\r\n                <span class=\"check-text\">充电口已锁定</span>\r\n              </div>\r\n              <div class=\"checklist-item completed\">\r\n                <span class=\"check-icon\">✓</span>\r\n                <span class=\"check-text\">自动雨刷已禁用</span>\r\n              </div>\r\n              <div class=\"checklist-item completed\">\r\n                <span class=\"check-icon\">✓</span>\r\n                <span class=\"check-text\">空调切换为内循环</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"wash-status\">\r\n              <div class=\"status-ready\">准备就绪，可以安全洗车！</div>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n\r\n      // 浪漫模式 - 浪漫音乐\r\n      romanticMusic: {\r\n        title: '浪漫音乐',\r\n        icon: 'fas fa-music',\r\n        content: `\r\n          <div class=\"romantic-music-content\">\r\n            <div class=\"playlist-title\">浪漫爵士乐</div>\r\n            <div class=\"current-song\">正在播放: Moonlight Serenade</div>\r\n            <div class=\"music-controls\">\r\n              <button class=\"control-btn\"><i class=\"fas fa-backward\"></i></button>\r\n              <button class=\"control-btn\"><i class=\"fas fa-pause\"></i></button>\r\n              <button class=\"control-btn\"><i class=\"fas fa-forward\"></i></button>\r\n            </div>\r\n            <div class=\"playlist-songs\">\r\n              <div class=\"song-item\">Fly Me to the Moon</div>\r\n              <div class=\"song-item\">The Way You Look Tonight</div>\r\n              <div class=\"song-item\">Unchained Melody</div>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n\r\n      // 浪漫模式 - 氛围灯\r\n      ambientLight: {\r\n        title: '氛围灯',\r\n        icon: 'fas fa-lightbulb',\r\n        content: `\r\n          <div class=\"ambient-light-content\">\r\n            <div class=\"light-title\">氛围灯光</div>\r\n            <div class=\"color-presets\">\r\n              <button class=\"color-btn\" style=\"background: #ff69b4\" data-color=\"rose-pink\">玫瑰粉</button>\r\n              <button class=\"color-btn\" style=\"background: #ffa500\" data-color=\"candle-yellow\">烛光黄</button>\r\n              <button class=\"color-btn\" style=\"background: #9370db\" data-color=\"star-purple\">星空紫</button>\r\n            </div>\r\n            <div class=\"brightness-control\">\r\n              <div class=\"brightness-label\">亮度: 50%</div>\r\n              <div class=\"brightness-slider\">\r\n                <input type=\"range\" min=\"0\" max=\"100\" value=\"50\" class=\"slider\">\r\n              </div>\r\n            </div>\r\n            <div class=\"light-effects\">\r\n              <button class=\"effect-btn\">呼吸效果</button>\r\n              <button class=\"effect-btn\">星光闪烁</button>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n\r\n      // 充电模式 - 娱乐推荐\r\n      entertainment: {\r\n        title: '娱乐推荐',\r\n        icon: 'fas fa-film',\r\n        content: `\r\n          <div class=\"entertainment-content\">\r\n            <div class=\"entertainment-title\">充电期间娱乐</div>\r\n            <div class=\"entertainment-grid\">\r\n              <button class=\"entertainment-item\">\r\n                <i class=\"fas fa-film\"></i>\r\n                <span>电影</span>\r\n              </button>\r\n              <button class=\"entertainment-item\">\r\n                <i class=\"fas fa-music\"></i>\r\n                <span>音乐</span>\r\n              </button>\r\n              <button class=\"entertainment-item\">\r\n                <i class=\"fas fa-podcast\"></i>\r\n                <span>播客</span>\r\n              </button>\r\n              <button class=\"entertainment-item\">\r\n                <i class=\"fas fa-book\"></i>\r\n                <span>有声书</span>\r\n              </button>\r\n              <button class=\"entertainment-item\">\r\n                <i class=\"fas fa-gamepad\"></i>\r\n                <span>游戏</span>\r\n              </button>\r\n              <button class=\"entertainment-item\">\r\n                <i class=\"fas fa-newspaper\"></i>\r\n                <span>新闻</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n\r\n      // 充电模式 - 附近商店\r\n      nearbyShops: {\r\n        title: '附近商店',\r\n        icon: 'fas fa-store',\r\n        content: `\r\n          <div class=\"nearby-shops-content\">\r\n            <div class=\"shops-title\">周边设施</div>\r\n            <div class=\"shop-items\">\r\n              <div class=\"shop-item\">\r\n                <div class=\"shop-icon\">☕</div>\r\n                <div class=\"shop-info\">\r\n                  <div class=\"shop-name\">星巴克</div>\r\n                  <div class=\"shop-distance\">步行2分钟</div>\r\n                </div>\r\n                <button class=\"shop-btn\">导航</button>\r\n              </div>\r\n              <div class=\"shop-item\">\r\n                <div class=\"shop-icon\">🛒</div>\r\n                <div class=\"shop-info\">\r\n                  <div class=\"shop-name\">便利店</div>\r\n                  <div class=\"shop-distance\">步行3分钟</div>\r\n                </div>\r\n                <button class=\"shop-btn\">导航</button>\r\n              </div>\r\n              <div class=\"shop-item\">\r\n                <div class=\"shop-icon\">🍽️</div>\r\n                <div class=\"shop-info\">\r\n                  <div class=\"shop-name\">快餐店</div>\r\n                  <div class=\"shop-distance\">步行5分钟</div>\r\n                </div>\r\n                <button class=\"shop-btn\">导航</button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n\r\n      // 紧急情况模式 - 紧急信息\r\n      emergencyInfo: {\r\n        title: '紧急信息',\r\n        icon: 'fas fa-exclamation-triangle',\r\n        content: `\r\n          <div class=\"emergency-info-content\">\r\n            <div class=\"emergency-title\">🚨 检测到紧急情况</div>\r\n            <div class=\"emergency-status\">\r\n              <div class=\"status-item\">✓ 已自动联系救援服务</div>\r\n              <div class=\"status-item\">✓ 救护车预计15分钟到达</div>\r\n              <div class=\"status-item\">✓ 位置信息已发送</div>\r\n            </div>\r\n            <div class=\"emergency-actions\">\r\n              <button class=\"emergency-action-btn\">我没事</button>\r\n              <button class=\"emergency-action-btn danger\">需要帮助</button>\r\n              <button class=\"emergency-action-btn\">联系家人</button>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n\r\n      // 紧急情况模式 - 急救指导\r\n      firstAid: {\r\n        title: '急救指导',\r\n        icon: 'fas fa-first-aid',\r\n        content: `\r\n          <div class=\"first-aid-content\">\r\n            <div class=\"first-aid-title\">🏥 急救指导</div>\r\n            <div class=\"first-aid-steps\">\r\n              <div class=\"step-item\">\r\n                <div class=\"step-number\">1</div>\r\n                <div class=\"step-text\">检查意识并呼救</div>\r\n              </div>\r\n              <div class=\"step-item\">\r\n                <div class=\"step-number\">2</div>\r\n                <div class=\"step-text\">检查呼吸和脉搏</div>\r\n              </div>\r\n              <div class=\"step-item\">\r\n                <div class=\"step-number\">3</div>\r\n                <div class=\"step-text\">保持体温和舒适体位</div>\r\n              </div>\r\n              <div class=\"step-item\">\r\n                <div class=\"step-number\">4</div>\r\n                <div class=\"step-text\">等待专业救援到达</div>\r\n              </div>\r\n            </div>\r\n            <div class=\"first-aid-warning\">\r\n              ⚠️ 请保持冷静，按照语音指导操作\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n\r\n      // 疲劳检测模式 - 休息区信息\r\n      restArea: {\r\n        title: '休息区信息',\r\n        icon: 'fas fa-parking',\r\n        content: `\r\n          <div class=\"rest-area-content\">\r\n            <div class=\"rest-area-title\">🛣️ 最近服务区</div>\r\n            <div class=\"rest-area-info\">\r\n              <div class=\"info-item\">\r\n                <div class=\"info-icon\">📍</div>\r\n                <div class=\"info-details\">\r\n                  <div class=\"info-name\">白云服务区</div>\r\n                  <div class=\"info-distance\">距离: 5km | 预计: 8分钟</div>\r\n                </div>\r\n              </div>\r\n              <div class=\"facilities-list\">\r\n                <div class=\"facility-item\">⛽ 加油站</div>\r\n                <div class=\"facility-item\">⚡ 充电桩</div>\r\n                <div class=\"facility-item\">🍽️ 餐厅</div>\r\n                <div class=\"facility-item\">🚻 休息室</div>\r\n                <div class=\"facility-item\">🛒 便利店</div>\r\n              </div>\r\n            </div>\r\n            <div class=\"rest-area-actions\">\r\n              <button class=\"action-btn primary\">导航前往</button>\r\n              <button class=\"action-btn secondary\">查看详情</button>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n\r\n      // 疲劳检测模式 - 提神建议\r\n      refreshment: {\r\n        title: '提神建议',\r\n        icon: 'fas fa-coffee',\r\n        content: `\r\n          <div class=\"refreshment-content\">\r\n            <div class=\"refreshment-title\">☕ 提神建议</div>\r\n            <div class=\"refreshment-categories\">\r\n              <div class=\"category\">\r\n                <div class=\"category-title\">饮品选择</div>\r\n                <div class=\"refreshment-items\">\r\n                  <button class=\"refreshment-btn\">☕ 咖啡</button>\r\n                  <button class=\"refreshment-btn\">🍵 茶</button>\r\n                  <button class=\"refreshment-btn\">🥤 功能饮料</button>\r\n                  <button class=\"refreshment-btn\">💧 冰水</button>\r\n                </div>\r\n              </div>\r\n              <div class=\"category\">\r\n                <div class=\"category-title\">活动建议</div>\r\n                <div class=\"refreshment-items\">\r\n                  <button class=\"refreshment-btn\">🚶‍♂️ 下车活动</button>\r\n                  <button class=\"refreshment-btn\">🧴 洗脸</button>\r\n                  <button class=\"refreshment-btn\">🎵 播放提神音乐</button>\r\n                  <button class=\"refreshment-btn\">💤 短暂休息</button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"refreshment-tips\">\r\n              <div class=\"tip-item\">💡 建议每2小时休息15分钟</div>\r\n              <div class=\"tip-item\">💡 避免空腹或过饱驾驶</div>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n\r\n      // 用户切换模式 - 用户选择器\r\n      userSelector: {\r\n        title: '用户选择',\r\n        icon: 'fas fa-users',\r\n        content: `\r\n          <div class=\"user-selector-content\">\r\n            <div class=\"selector-title\">👥 选择用户配置文件</div>\r\n            <div class=\"user-grid\">\r\n              <div class=\"user-card active\">\r\n                <div class=\"user-avatar\">👨</div>\r\n                <div class=\"user-info\">\r\n                  <div class=\"user-name\">张先生</div>\r\n                  <div class=\"user-role\">主驾驶员</div>\r\n                  <div class=\"user-status\">✅ 当前</div>\r\n                </div>\r\n              </div>\r\n              <div class=\"user-card\">\r\n                <div class=\"user-avatar\">👩</div>\r\n                <div class=\"user-info\">\r\n                  <div class=\"user-name\">李女士</div>\r\n                  <div class=\"user-role\">副驾驶员</div>\r\n                  <div class=\"user-status\">○ 离线</div>\r\n                </div>\r\n              </div>\r\n              <div class=\"user-card\">\r\n                <div class=\"user-avatar\">👶</div>\r\n                <div class=\"user-info\">\r\n                  <div class=\"user-name\">儿童模式</div>\r\n                  <div class=\"user-role\">后排乘客</div>\r\n                  <div class=\"user-status\">○ 未激活</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"selector-actions\">\r\n              <button class=\"selector-btn\">🎤 语音识别</button>\r\n              <button class=\"selector-btn\">👤 访客模式</button>\r\n              <button class=\"selector-btn\">⚙️ 设置</button>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n\r\n      // 用户切换模式 - 用户偏好\r\n      userPreferences: {\r\n        title: '用户偏好',\r\n        icon: 'fas fa-heart',\r\n        content: `\r\n          <div class=\"user-preferences-content\">\r\n            <div class=\"preferences-title\">🎯 个人偏好设置</div>\r\n            <div class=\"preference-categories\">\r\n              <div class=\"preference-group\">\r\n                <div class=\"group-title\">🎵 音乐偏好</div>\r\n                <div class=\"preference-items\">\r\n                  <div class=\"preference-item\">\r\n                    <div class=\"item-label\">音乐风格</div>\r\n                    <div class=\"item-value\">流行音乐</div>\r\n                  </div>\r\n                  <div class=\"preference-item\">\r\n                    <div class=\"item-label\">音量偏好</div>\r\n                    <div class=\"item-value\">中等</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"preference-group\">\r\n                <div class=\"group-title\">🌡️ 舒适设置</div>\r\n                <div class=\"preference-items\">\r\n                  <div class=\"preference-item\">\r\n                    <div class=\"item-label\">座椅位置</div>\r\n                    <div class=\"item-value\">位置3</div>\r\n                  </div>\r\n                  <div class=\"preference-item\">\r\n                    <div class=\"item-label\">空调温度</div>\r\n                    <div class=\"item-value\">22°C</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"preference-group\">\r\n                <div class=\"group-title\">🗺️ 导航偏好</div>\r\n                <div class=\"preference-items\">\r\n                  <div class=\"preference-item\">\r\n                    <div class=\"item-label\">路线选择</div>\r\n                    <div class=\"item-value\">避开拥堵</div>\r\n                  </div>\r\n                  <div class=\"preference-item\">\r\n                    <div class=\"item-label\">语音播报</div>\r\n                    <div class=\"item-value\">开启</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"preferences-actions\">\r\n              <button class=\"preference-btn\">编辑偏好</button>\r\n              <button class=\"preference-btn\">同步到云端</button>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n\r\n      // 用户切换模式 - 隐私设置\r\n      privacySettings: {\r\n        title: '隐私设置',\r\n        icon: 'fas fa-shield-alt',\r\n        content: `\r\n          <div class=\"privacy-settings-content\">\r\n            <div class=\"privacy-title\">🛡️ 隐私与安全</div>\r\n            <div class=\"privacy-items\">\r\n              <div class=\"privacy-item\">\r\n                <div class=\"privacy-icon\">📍</div>\r\n                <div class=\"privacy-info\">\r\n                  <div class=\"privacy-label\">位置信息</div>\r\n                  <div class=\"privacy-desc\">仅行程中使用</div>\r\n                </div>\r\n                <div class=\"privacy-toggle\">✅</div>\r\n              </div>\r\n              <div class=\"privacy-item\">\r\n                <div class=\"privacy-icon\">🎤</div>\r\n                <div class=\"privacy-info\">\r\n                  <div class=\"privacy-label\">语音数据</div>\r\n                  <div class=\"privacy-desc\">本地处理</div>\r\n                </div>\r\n                <div class=\"privacy-toggle\">✅</div>\r\n              </div>\r\n              <div class=\"privacy-item\">\r\n                <div class=\"privacy-icon\">📊</div>\r\n                <div class=\"privacy-info\">\r\n                  <div class=\"privacy-label\">使用统计</div>\r\n                  <div class=\"privacy-desc\">匿名收集</div>\r\n                </div>\r\n                <div class=\"privacy-toggle\">⚪</div>\r\n              </div>\r\n              <div class=\"privacy-item\">\r\n                <div class=\"privacy-icon\">🔄</div>\r\n                <div class=\"privacy-info\">\r\n                  <div class=\"privacy-label\">数据同步</div>\r\n                  <div class=\"privacy-desc\">加密传输</div>\r\n                </div>\r\n                <div class=\"privacy-toggle\">✅</div>\r\n              </div>\r\n            </div>\r\n            <div class=\"privacy-actions\">\r\n              <button class=\"privacy-btn primary\">详细设置</button>\r\n              <button class=\"privacy-btn secondary\">重置隐私</button>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n\r\n      // 智能泊车模式 - 停车位搜索\r\n      parkingSearch: {\r\n        title: '可用车位',\r\n        icon: 'fas fa-parking',\r\n        content: `\r\n          <div class=\"parking-search-content\">\r\n            <div class=\"parking-title\">🅿️ 可用车位</div>\r\n            <div class=\"parking-spots\">\r\n              <div class=\"parking-spot recommended\">\r\n                <div class=\"spot-header\">\r\n                  <div class=\"spot-id\">B2-15</div>\r\n                  <div class=\"spot-recommend\">推荐</div>\r\n                </div>\r\n                <div class=\"spot-details\">\r\n                  <div class=\"spot-distance\">距离: 50m</div>\r\n                  <div class=\"spot-size\">宽度: 标准</div>\r\n                  <div class=\"spot-type\">类型: 地下</div>\r\n                </div>\r\n                <div class=\"spot-status\">\r\n                  <div class=\"status-indicator available\"></div>\r\n                  <span>可用</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"parking-spot\">\r\n                <div class=\"spot-header\">\r\n                  <div class=\"spot-id\">B1-08</div>\r\n                  <div class=\"spot-alternative\">备选</div>\r\n                </div>\r\n                <div class=\"spot-details\">\r\n                  <div class=\"spot-distance\">距离: 80m</div>\r\n                  <div class=\"spot-size\">宽度: 宽体</div>\r\n                  <div class=\"spot-type\">类型: 地下</div>\r\n                </div>\r\n                <div class=\"spot-status\">\r\n                  <div class=\"status-indicator available\"></div>\r\n                  <span>可用</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"parking-spot\">\r\n                <div class=\"spot-header\">\r\n                  <div class=\"spot-id\">A3-22</div>\r\n                  <div class=\"spot-alternative\">备选</div>\r\n                </div>\r\n                <div class=\"spot-details\">\r\n                  <div class=\"spot-distance\">距离: 120m</div>\r\n                  <div class=\"spot-size\">宽度: 标准</div>\r\n                  <div class=\"spot-type\">类型: 露天</div>\r\n                </div>\r\n                <div class=\"spot-status\">\r\n                  <div class=\"status-indicator limited\"></div>\r\n                  <span>紧张</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"parking-actions\">\r\n              <button class=\"parking-btn primary\">导航至推荐车位</button>\r\n              <button class=\"parking-btn secondary\">查看更多车位</button>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n\r\n      // 智能泊车模式 - 泊车辅助\r\n      parkingAssist: {\r\n        title: '泊车辅助',\r\n        icon: 'fas fa-car-side',\r\n        content: `\r\n          <div class=\"parking-assist-content\">\r\n            <div class=\"assist-title\">🚗 泊车辅助</div>\r\n            <div class=\"assist-modes\">\r\n              <div class=\"assist-mode\">\r\n                <div class=\"mode-icon\">🤖</div>\r\n                <div class=\"mode-info\">\r\n                  <div class=\"mode-name\">自动泊车</div>\r\n                  <div class=\"mode-desc\">系统自动完成泊车</div>\r\n                </div>\r\n                <button class=\"mode-btn\">启动</button>\r\n              </div>\r\n              <div class=\"assist-mode\">\r\n                <div class=\"mode-icon\">🎯</div>\r\n                <div class=\"mode-info\">\r\n                  <div class=\"mode-name\">辅助泊车</div>\r\n                  <div class=\"mode-desc\">方向盘和刹车辅助</div>\r\n                </div>\r\n                <button class=\"mode-btn\">启动</button>\r\n              </div>\r\n              <div class=\"assist-mode\">\r\n                <div class=\"mode-icon\">👤</div>\r\n                <div class=\"mode-info\">\r\n                  <div class=\"mode-name\">手动泊车</div>\r\n                  <div class=\"mode-desc\">仅提供指引</div>\r\n                </div>\r\n                <button class=\"mode-btn\">启动</button>\r\n              </div>\r\n            </div>\r\n            <div class=\"camera-view\">\r\n              <div class=\"view-title\">📹 环视影像</div>\r\n              <div class=\"camera-grid\">\r\n                <button class=\"camera-btn\" data-camera=\"front\">前</button>\r\n                <button class=\"camera-btn\" data-camera=\"rear\">后</button>\r\n                <button class=\"camera-btn\" data-camera=\"left\">左</button>\r\n                <button class=\"camera-btn\" data-camera=\"right\">右</button>\r\n              </div>\r\n              <div class=\"view-status\">\r\n                <div class=\"status-item\">✓ 摄像头已激活</div>\r\n                <div class=\"status-item\">✓ 雷达扫描中</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        `\r\n      },\r\n\r\n      // 智能泊车模式 - 费用信息\r\n      costInfo: {\r\n        title: '费用信息',\r\n        icon: 'fas fa-coins',\r\n        content: `\r\n          <div class=\"cost-info-content\">\r\n            <div class=\"cost-title\">💰 停车费用</div>\r\n            <div class=\"pricing-options\">\r\n              <div class=\"pricing-option\">\r\n                <div class=\"option-header\">\r\n                  <div class=\"option-duration\">2小时</div>\r\n                  <div class=\"option-price\">¥10</div>\r\n                </div>\r\n                <div class=\"option-rate\">费率: ¥5/小时</div>\r\n                <div class=\"option-details\">\r\n                  <div class=\"detail-item\">• 前30分钟免费</div>\r\n                  <div class=\"detail-item\">• 超时¥8/小时</div>\r\n                </div>\r\n              </div>\r\n              <div class=\"pricing-option recommended\">\r\n                <div class=\"option-header\">\r\n                  <div class=\"option-duration\">全天</div>\r\n                  <div class=\"option-price\">¥30</div>\r\n                </div>\r\n                <div class=\"option-rate\">费率: ¥30/天</div>\r\n                <div class=\"option-details\">\r\n                  <div class=\"detail-item\">• 24小时有效</div>\r\n                  <div class=\"detail-item\">• 可多次进出</div>\r\n                </div>\r\n              </div>\r\n              <div class=\"pricing-option\">\r\n                <div class=\"option-header\">\r\n                  <div class=\"option-duration\">包月</div>\r\n                  <div class=\"option-price\">¥200</div>\r\n                </div>\r\n                <div class=\"option-rate\">费率: ¥200/月</div>\r\n                <div class=\"option-details\">\r\n                  <div class=\"detail-item\">• 固定车位</div>\r\n                  <div class=\"detail-item\">• 专属权限</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"payment-section\">\r\n              <div class=\"payment-title\">支付方式</div>\r\n              <div class=\"payment-methods\">\r\n                <button class=\"payment-btn\">💳 支付宝</button>\r\n                <button class=\"payment-btn\">💳 微信支付</button>\r\n                <button class=\"payment-btn\">💳 银行卡</button>\r\n                <button class=\"payment-btn\">🚗 ETC</button>\r\n              </div>\r\n            </div>\r\n            <div class=\"discount-section\">\r\n              <div class=\"discount-title\">🎫 优惠券</div>\r\n              <div class=\"discount-items\">\r\n                <div class=\"discount-item\">\r\n                  <div class=\"discount-name\">新用户9折</div>\r\n                  <button class=\"discount-use\">使用</button>\r\n                </div>\r\n                <div class=\"discount-item\">\r\n                  <div class=\"discount-name\">周末8折</div>\r\n                  <button class=\"discount-use\">使用</button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        `\r\n      }\r\n    }\r\n\r\n    const getCardTitle = () => {\r\n      return cardConfigs[props.cardType]?.title || props.cardType\r\n    }\r\n\r\n    const getCardIcon = () => {\r\n      return cardConfigs[props.cardType]?.icon || 'fas fa-cube'\r\n    }\r\n\r\n    const getCardContent = () => {\r\n      // 特殊处理VPA组件，根据场景显示不同内容\r\n      if (props.cardType === 'vpaWidget') {\r\n        return getVpaContent()\r\n      }\r\n      return cardConfigs[props.cardType]?.content || `<div class=\"placeholder\">卡片内容: ${props.cardType}</div>`\r\n    }\r\n\r\n    const getVpaContent = () => {\r\n      // 根据场景ID返回不同的VPA内容\r\n      switch (props.scene.id) {\r\n        case 'familyTrip':\r\n          return `\r\n            <div class=\"vpa-widget-content family-trip-vpa\">\r\n              <div class=\"vpa-storyteller-mode\">\r\n                <div class=\"storyteller-header\">\r\n                  <h3>🎭 故事讲述模式</h3>\r\n                  <p>小智正在为孩子们准备精彩的故事</p>\r\n                </div>\r\n                <div class=\"story-content\">\r\n                  <div class=\"current-story\">\r\n                    <div class=\"story-title\">🌳 森林探险记</div>\r\n                    <div class=\"story-progress\">\r\n                      <div class=\"progress-bar\">\r\n                        <div class=\"progress-fill\" style=\"width: 35%\"></div>\r\n                      </div>\r\n                      <span class=\"progress-text\">第3章 / 共8章</span>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"story-controls\">\r\n                    <button class=\"story-btn pause\">⏸️ 暂停</button>\r\n                    <button class=\"story-btn next\">⏭️ 下一章</button>\r\n                    <button class=\"story-btn change\">🔄 换个故事</button>\r\n                  </div>\r\n                  <div class=\"story-options\">\r\n                    <div class=\"option-title\">📚 故事库</div>\r\n                    <div class=\"story-list\">\r\n                      <button class=\"story-option\">🦁 动物王国</button>\r\n                      <button class=\"story-option\">🚀 太空冒险</button>\r\n                      <button class=\"story-option\">🏰 童话城堡</button>\r\n                      <button class=\"story-option\">🌊 海底世界</button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"vpa-avatar-container\">\r\n                <img src=\"/images/vpa2.gif\" alt=\"VPA智能助手\" class=\"vpa-avatar storyteller\" />\r\n                <div class=\"avatar-status\">正在讲故事...</div>\r\n              </div>\r\n            </div>\r\n          `\r\n        default:\r\n          return cardConfigs.vpaWidget.content\r\n      }\r\n    }\r\n\r\n    const getCardStyle = () => {\r\n      // 根据场景主题返回不同的样式\r\n      const themeStyles = {\r\n        warm: {\r\n          background: 'rgba(255, 193, 7, 0.15)',\r\n          border: '1px solid rgba(255, 193, 7, 0.3)'\r\n        },\r\n        calm: {\r\n          background: 'rgba(0, 123, 255, 0.15)',\r\n          border: '1px solid rgba(0, 123, 255, 0.3)'\r\n        },\r\n        evening: {\r\n          background: 'rgba(255, 87, 34, 0.15)',\r\n          border: '1px solid rgba(255, 87, 34, 0.3)'\r\n        },\r\n        dark: {\r\n          background: 'rgba(33, 37, 41, 0.15)',\r\n          border: '1px solid rgba(33, 37, 41, 0.3)'\r\n        },\r\n        emergency: {\r\n          background: 'rgba(220, 53, 69, 0.15)',\r\n          border: '1px solid rgba(220, 53, 69, 0.3)'\r\n        }\r\n      }\r\n      \r\n      return themeStyles[props.scene.theme] || {}\r\n    }\r\n\r\n    return {\r\n      getCardTitle,\r\n      getCardIcon,\r\n      getCardContent,\r\n      getCardStyle\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.default-card {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* 卡片内容样式 */\r\n.card-content {\r\n  color: white;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 导航内容 */\r\n.navigation-content .destination {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.navigation-content .route-info {\r\n  display: flex;\r\n  gap: 15px;\r\n}\r\n\r\n.navigation-content .distance,\r\n.navigation-content .duration {\r\n  padding: 4px 8px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 8px;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 音乐控制 */\r\n.music-content .current-song {\r\n  margin-bottom: 10px;\r\n  font-weight: bold;\r\n}\r\n\r\n.music-controls {\r\n  display: flex;\r\n  gap: 10px;\r\n  justify-content: center;\r\n}\r\n\r\n.control-btn {\r\n  background: var(--button-bg, rgba(74, 144, 226, 0.8));\r\n  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));\r\n  color: var(--button-color, white);\r\n  padding: 8px;\r\n  border-radius: 50%;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-shadow: var(--button-text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.control-btn:hover {\r\n  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* 待办事项 */\r\n.todo-content .todo-item {\r\n  margin-bottom: 8px;\r\n  padding: 4px 0;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n/* 儿童教育 */\r\n.kid-education-content .video-placeholder {\r\n  text-align: center;\r\n  padding: 20px;\r\n  background: rgba(0, 0, 0, 0.2);\r\n  border-radius: 8px;\r\n}\r\n\r\n.kid-education-content .video-placeholder i {\r\n  font-size: 24px;\r\n  margin-bottom: 10px;\r\n  display: block;\r\n}\r\n\r\n/* 百科问答 */\r\n.pedia-content .question {\r\n  font-weight: bold;\r\n  margin-bottom: 8px;\r\n  color: #ffc107;\r\n}\r\n\r\n.pedia-content .answer {\r\n  font-size: 12px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 通用按钮样式 */\r\n.sound-btn, .home-btn, .entertainment-btn, .facility-btn, .emergency-btn {\r\n  background: var(--button-bg, rgba(74, 144, 226, 0.8));\r\n  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));\r\n  color: var(--button-color, white);\r\n  padding: 6px 12px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  margin: 4px;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n  text-shadow: var(--button-text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n  backdrop-filter: blur(4px);\r\n}\r\n\r\n.sound-btn:hover, .home-btn:hover, .entertainment-btn:hover,\r\n.facility-btn:hover, .emergency-btn:hover {\r\n  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* 状态信息 */\r\n.status-item, .charge-info, .charge-time, .order-info, .order-time,\r\n.service-info, .services, .warning-text, .suggestion, .reminder {\r\n  margin-bottom: 6px;\r\n  padding: 4px 0;\r\n}\r\n\r\n/* 紧急模式特殊样式 */\r\n.card-emergencyMode .emergency-btn {\r\n  background: rgba(220, 53, 69, 0.3);\r\n  border-color: rgba(220, 53, 69, 0.5);\r\n}\r\n\r\n/* 响应式布局 */\r\n@media (max-width: 768px) {\r\n  .card-content {\r\n    font-size: 12px;\r\n  }\r\n  \r\n  .music-controls {\r\n    gap: 5px;\r\n  }\r\n  \r\n  .control-btn {\r\n    padding: 6px;\r\n  }\r\n}\r\n\r\n/* VPA小窗样式 */\r\n.vpa-widget-content {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: stretch;\r\n  gap: 20px;\r\n  background: transparent;\r\n  padding: 20px;\r\n}\r\n\r\n.vpa-conversation {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n}\r\n\r\n.vpa-greeting {\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  color: var(--text-primary, #333);\r\n  margin: 0;\r\n}\r\n\r\n.vpa-actions {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n.vpa-action-btn {\r\n  padding: 8px 12px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 20px;\r\n  color: var(--text-primary, #333);\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.vpa-action-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.vpa-footer {\r\n  font-size: 12px;\r\n  color: var(--text-secondary, #666);\r\n  margin: 0;\r\n  line-height: 1.4;\r\n}\r\n\r\n.vpa-avatar {\r\n  width: 120px;\r\n  height: 120px;\r\n  object-fit: contain;\r\n  border-radius: 50%;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* 家庭出游VPA故事讲述模式样式 */\r\n.family-trip-vpa {\r\n  flex-direction: column;\r\n  gap: 15px;\r\n  padding: 15px;\r\n}\r\n\r\n.vpa-storyteller-mode {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.storyteller-header {\r\n  text-align: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.storyteller-header h3 {\r\n  margin: 0 0 5px 0;\r\n  font-size: 16px;\r\n  color: var(--text-primary, #333);\r\n}\r\n\r\n.storyteller-header p {\r\n  margin: 0;\r\n  font-size: 12px;\r\n  color: var(--text-secondary, #666);\r\n}\r\n\r\n.story-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n}\r\n\r\n.current-story {\r\n  background: rgba(255, 193, 7, 0.1);\r\n  padding: 10px;\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(255, 193, 7, 0.3);\r\n}\r\n\r\n.story-title {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n  color: var(--text-primary, #333);\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.story-progress {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.progress-bar {\r\n  flex: 1;\r\n  height: 4px;\r\n  background: rgba(255, 193, 7, 0.2);\r\n  border-radius: 2px;\r\n  overflow: hidden;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: #ffc107;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-text {\r\n  font-size: 11px;\r\n  color: var(--text-secondary, #666);\r\n}\r\n\r\n.story-controls {\r\n  display: flex;\r\n  gap: 6px;\r\n  justify-content: center;\r\n}\r\n\r\n.story-btn {\r\n  padding: 6px 10px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 15px;\r\n  color: var(--text-primary, #333);\r\n  font-size: 11px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.story-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.story-options {\r\n  margin-top: 8px;\r\n}\r\n\r\n.option-title {\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n  color: var(--text-primary, #333);\r\n  margin-bottom: 6px;\r\n  text-align: center;\r\n}\r\n\r\n.story-list {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 4px;\r\n}\r\n\r\n.story-option {\r\n  padding: 4px 6px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  border-radius: 10px;\r\n  color: var(--text-primary, #333);\r\n  font-size: 10px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-align: center;\r\n}\r\n\r\n.story-option:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  transform: scale(1.02);\r\n}\r\n\r\n.vpa-avatar-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.vpa-avatar.storyteller {\r\n  width: 80px;\r\n  height: 80px;\r\n  border: 3px solid #ffc107;\r\n  box-shadow: 0 0 15px rgba(255, 193, 7, 0.3);\r\n  animation: storytellerGlow 3s ease-in-out infinite;\r\n}\r\n\r\n.avatar-status {\r\n  font-size: 11px;\r\n  color: var(--text-secondary, #666);\r\n  text-align: center;\r\n  padding: 4px 8px;\r\n  background: rgba(255, 193, 7, 0.1);\r\n  border-radius: 10px;\r\n  border: 1px solid rgba(255, 193, 7, 0.2);\r\n}\r\n\r\n@keyframes storytellerGlow {\r\n  0%, 100% {\r\n    box-shadow: 0 0 15px rgba(255, 193, 7, 0.3);\r\n  }\r\n  50% {\r\n    box-shadow: 0 0 25px rgba(255, 193, 7, 0.6);\r\n  }\r\n}\r\n\r\n/* 灵动岛样式 */\r\n.dynamic-island-content {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  border-radius: 25px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 20px;\r\n  backdrop-filter: blur(20px);\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.island-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  color: white;\r\n}\r\n\r\n.island-text {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.island-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.island-btn {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  background: var(--button-bg, rgba(74, 144, 226, 0.8));\r\n  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));\r\n  color: var(--button-color, white);\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n  text-shadow: var(--button-text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.island-btn:hover {\r\n  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));\r\n  transform: scale(1.1);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* 访客模式样式 */\r\n.temp-navigation-content .nav-input {\r\n  display: flex;\r\n  gap: 8px;\r\n  align-items: center;\r\n}\r\n\r\n.temp-navigation-content .destination-input {\r\n  flex: 1;\r\n  padding: 8px 12px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 8px;\r\n  color: white;\r\n  font-size: 14px;\r\n}\r\n\r\n.temp-navigation-content .destination-input::placeholder {\r\n  color: rgba(255, 255, 255, 0.6);\r\n}\r\n\r\n.temp-navigation-content .nav-btn {\r\n  padding: 8px 16px;\r\n  background: var(--button-bg, rgba(74, 144, 226, 0.8));\r\n  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));\r\n  border-radius: 8px;\r\n  color: white;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.temp-navigation-content .nav-btn:hover {\r\n  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));\r\n}\r\n\r\n.basic-music-content .music-source {\r\n  display: flex;\r\n  gap: 8px;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.basic-music-content .source-btn {\r\n  flex: 1;\r\n  padding: 8px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 8px;\r\n  color: white;\r\n  cursor: pointer;\r\n  font-size: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.basic-music-content .source-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.basic-music-content .basic-controls {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n.basic-control-content .control-grid {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 8px;\r\n}\r\n\r\n.basic-control-content .control-grid-btn {\r\n  padding: 12px;\r\n  background: var(--button-bg, rgba(74, 144, 226, 0.8));\r\n  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));\r\n  border-radius: 8px;\r\n  color: white;\r\n  cursor: pointer;\r\n  font-size: 12px;\r\n  transition: all 0.3s ease;\r\n  text-align: center;\r\n}\r\n\r\n.basic-control-content .control-grid-btn:hover {\r\n  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 宠物模式样式 */\r\n.pet-info-content .pet-status {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.pet-info-content .pet-avatar {\r\n  font-size: 32px;\r\n  width: 50px;\r\n  height: 50px;\r\n  background: rgba(255, 193, 7, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.pet-info-content .pet-details {\r\n  flex: 1;\r\n}\r\n\r\n.pet-info-content .pet-name {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.pet-info-content .pet-mood,\r\n.pet-info-content .pet-time {\r\n  font-size: 12px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.pet-info-content .pet-message {\r\n  background: rgba(255, 193, 7, 0.1);\r\n  padding: 8px;\r\n  border-radius: 8px;\r\n  font-size: 12px;\r\n  text-align: center;\r\n  border: 1px solid rgba(255, 193, 7, 0.3);\r\n}\r\n\r\n.climate-control-content .temperature-display {\r\n  text-align: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.climate-control-content .current-temp {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #ffc107;\r\n}\r\n\r\n.climate-control-content .temp-status {\r\n  font-size: 12px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.climate-control-content .climate-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.climate-control-content .temp-btn {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  background: var(--button-bg, rgba(74, 144, 226, 0.8));\r\n  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));\r\n  color: white;\r\n  cursor: pointer;\r\n  font-size: 18px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.climate-control-content .temp-btn:hover {\r\n  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));\r\n  transform: scale(1.1);\r\n}\r\n\r\n.climate-control-content .temp-range {\r\n  font-size: 12px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.climate-control-content .climate-status {\r\n  font-size: 12px;\r\n}\r\n\r\n.climate-control-content .status-item {\r\n  margin-bottom: 4px;\r\n  color: #4caf50;\r\n}\r\n\r\n/* 洗车模式样式 */\r\n.car-wash-checklist-content .wash-title {\r\n  text-align: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 16px;\r\n  color: #17a2b8;\r\n}\r\n\r\n.car-wash-checklist-content .checklist-items {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.car-wash-checklist-content .checklist-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 8px;\r\n  padding: 8px;\r\n  background: rgba(23, 162, 184, 0.1);\r\n  border-radius: 6px;\r\n  border-left: 3px solid #17a2b8;\r\n}\r\n\r\n.car-wash-checklist-content .checklist-item.completed {\r\n  border-left-color: #28a745;\r\n}\r\n\r\n.car-wash-checklist-content .check-icon {\r\n  color: #28a745;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n}\r\n\r\n.car-wash-checklist-content .check-text {\r\n  font-size: 12px;\r\n  color: white;\r\n}\r\n\r\n.car-wash-checklist-content .wash-status {\r\n  text-align: center;\r\n}\r\n\r\n.car-wash-checklist-content .status-ready {\r\n  background: rgba(40, 167, 69, 0.2);\r\n  color: #28a745;\r\n  padding: 8px 16px;\r\n  border-radius: 8px;\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n  border: 1px solid rgba(40, 167, 69, 0.3);\r\n}\r\n\r\n/* 浪漫模式样式 */\r\n.romantic-music-content .playlist-title {\r\n  text-align: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 12px;\r\n  color: #ff69b4;\r\n}\r\n\r\n.romantic-music-content .current-song {\r\n  text-align: center;\r\n  margin-bottom: 12px;\r\n  font-size: 14px;\r\n  color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.romantic-music-content .music-controls {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.romantic-music-content .playlist-songs {\r\n  font-size: 12px;\r\n}\r\n\r\n.romantic-music-content .song-item {\r\n  padding: 4px 8px;\r\n  margin-bottom: 4px;\r\n  background: rgba(255, 105, 180, 0.1);\r\n  border-radius: 4px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.ambient-light-content .light-title {\r\n  text-align: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 12px;\r\n  color: #ffa500;\r\n}\r\n\r\n.ambient-light-content .color-presets {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.ambient-light-content .color-btn {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n  cursor: pointer;\r\n  font-size: 10px;\r\n  color: white;\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.ambient-light-content .color-btn:hover {\r\n  transform: scale(1.1);\r\n  border-color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.ambient-light-content .brightness-control {\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.ambient-light-content .brightness-label {\r\n  font-size: 12px;\r\n  margin-bottom: 4px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.ambient-light-content .brightness-slider {\r\n  width: 100%;\r\n}\r\n\r\n.ambient-light-content .slider {\r\n  width: 100%;\r\n  height: 4px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 2px;\r\n  outline: none;\r\n  -webkit-appearance: none;\r\n}\r\n\r\n.ambient-light-content .light-effects {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n.ambient-light-content .effect-btn {\r\n  padding: 6px 12px;\r\n  background: rgba(255, 165, 0, 0.2);\r\n  border: 1px solid rgba(255, 165, 0, 0.4);\r\n  border-radius: 6px;\r\n  color: #ffa500;\r\n  font-size: 11px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.ambient-light-content .effect-btn:hover {\r\n  background: rgba(255, 165, 0, 0.3);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 充电模式样式 */\r\n.entertainment-content .entertainment-title {\r\n  text-align: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 12px;\r\n  color: #17a2b8;\r\n}\r\n\r\n.entertainment-content .entertainment-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 8px;\r\n}\r\n\r\n.entertainment-content .entertainment-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 4px;\r\n  padding: 12px 8px;\r\n  background: rgba(23, 162, 184, 0.1);\r\n  border: 1px solid rgba(23, 162, 184, 0.3);\r\n  border-radius: 8px;\r\n  color: white;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.entertainment-content .entertainment-item:hover {\r\n  background: rgba(23, 162, 184, 0.2);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.entertainment-content .entertainment-item i {\r\n  font-size: 20px;\r\n  color: #17a2b8;\r\n}\r\n\r\n.entertainment-content .entertainment-item span {\r\n  font-size: 12px;\r\n  text-align: center;\r\n}\r\n\r\n.nearby-shops-content .shops-title {\r\n  text-align: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 12px;\r\n  color: #28a745;\r\n}\r\n\r\n.nearby-shops-content .shop-items {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.nearby-shops-content .shop-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 8px;\r\n  background: rgba(40, 167, 69, 0.1);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(40, 167, 69, 0.2);\r\n}\r\n\r\n.nearby-shops-content .shop-icon {\r\n  font-size: 20px;\r\n  width: 32px;\r\n  height: 32px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.nearby-shops-content .shop-info {\r\n  flex: 1;\r\n}\r\n\r\n.nearby-shops-content .shop-name {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n  color: white;\r\n}\r\n\r\n.nearby-shops-content .shop-distance {\r\n  font-size: 11px;\r\n  color: rgba(255, 255, 255, 0.7);\r\n}\r\n\r\n.nearby-shops-content .shop-btn {\r\n  padding: 4px 8px;\r\n  background: rgba(40, 167, 69, 0.8);\r\n  border: 1px solid rgba(40, 167, 69, 0.3);\r\n  border-radius: 4px;\r\n  color: white;\r\n  font-size: 11px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n/* 紧急情况模式样式 */\r\n.emergency-info-content .emergency-title {\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  margin-bottom: 16px;\r\n  color: #dc3545;\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% { opacity: 1; }\r\n  50% { opacity: 0.6; }\r\n  100% { opacity: 1; }\r\n}\r\n\r\n.emergency-info-content .emergency-status {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.emergency-info-content .status-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n  padding: 6px 0;\r\n  color: #4caf50;\r\n  font-size: 13px;\r\n}\r\n\r\n.emergency-info-content .status-item:before {\r\n  content: \"✓\";\r\n  margin-right: 8px;\r\n  font-weight: bold;\r\n}\r\n\r\n.emergency-info-content .emergency-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.emergency-info-content .emergency-action-btn {\r\n  padding: 8px 16px;\r\n  border-radius: 8px;\r\n  border: 1px solid;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-align: center;\r\n  min-width: 80px;\r\n}\r\n\r\n.emergency-info-content .emergency-action-btn:first-child {\r\n  background: rgba(40, 167, 69, 0.8);\r\n  border-color: rgba(40, 167, 69, 0.3);\r\n  color: white;\r\n}\r\n\r\n.emergency-info-content .emergency-action-btn:first-child:hover {\r\n  background: rgba(40, 167, 69, 0.9);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.emergency-info-content .emergency-action-btn.danger {\r\n  background: rgba(220, 53, 69, 0.8);\r\n  border-color: rgba(220, 53, 69, 0.3);\r\n  color: white;\r\n}\r\n\r\n.emergency-info-content .emergency-action-btn.danger:hover {\r\n  background: rgba(220, 53, 69, 0.9);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.emergency-info-content .emergency-action-btn:last-child {\r\n  background: rgba(255, 193, 7, 0.8);\r\n  border-color: rgba(255, 193, 7, 0.3);\r\n  color: #212529;\r\n}\r\n\r\n.emergency-info-content .emergency-action-btn:last-child:hover {\r\n  background: rgba(255, 193, 7, 0.9);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.first-aid-content .first-aid-title {\r\n  text-align: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 16px;\r\n  color: #17a2b8;\r\n}\r\n\r\n.first-aid-content .first-aid-steps {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.first-aid-content .step-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-bottom: 12px;\r\n  padding: 8px;\r\n  background: rgba(23, 162, 184, 0.1);\r\n  border-radius: 6px;\r\n  border-left: 3px solid #17a2b8;\r\n}\r\n\r\n.first-aid-content .step-number {\r\n  width: 24px;\r\n  height: 24px;\r\n  background: #17a2b8;\r\n  color: white;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n  margin-right: 12px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.first-aid-content .step-text {\r\n  font-size: 12px;\r\n  color: white;\r\n  line-height: 1.4;\r\n  flex: 1;\r\n}\r\n\r\n.first-aid-content .first-aid-warning {\r\n  text-align: center;\r\n  padding: 8px;\r\n  background: rgba(255, 193, 7, 0.2);\r\n  border: 1px solid rgba(255, 193, 7, 0.4);\r\n  border-radius: 6px;\r\n  color: #ffc107;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 疲劳检测模式样式 */\r\n.rest-area-content .rest-area-title {\r\n  text-align: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 16px;\r\n  color: #ffc107;\r\n}\r\n\r\n.rest-area-content .rest-area-info {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.rest-area-content .info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  margin-bottom: 12px;\r\n  padding: 8px;\r\n  background: rgba(255, 193, 7, 0.1);\r\n  border-radius: 8px;\r\n}\r\n\r\n.rest-area-content .info-icon {\r\n  font-size: 24px;\r\n  width: 32px;\r\n  height: 32px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.rest-area-content .info-details {\r\n  flex: 1;\r\n}\r\n\r\n.rest-area-content .info-name {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n  color: white;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.rest-area-content .info-distance {\r\n  font-size: 11px;\r\n  color: rgba(255, 255, 255, 0.7);\r\n}\r\n\r\n.rest-area-content .facilities-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 6px;\r\n  margin-top: 8px;\r\n}\r\n\r\n.rest-area-content .facility-item {\r\n  padding: 4px 8px;\r\n  background: rgba(255, 193, 7, 0.2);\r\n  border: 1px solid rgba(255, 193, 7, 0.3);\r\n  border-radius: 4px;\r\n  font-size: 10px;\r\n  color: #ffc107;\r\n}\r\n\r\n.rest-area-content .rest-area-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n.rest-area-content .action-btn {\r\n  padding: 8px 16px;\r\n  border-radius: 6px;\r\n  border: 1px solid;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.rest-area-content .action-btn.primary {\r\n  background: rgba(255, 193, 7, 0.8);\r\n  border-color: rgba(255, 193, 7, 0.3);\r\n  color: #212529;\r\n}\r\n\r\n.rest-area-content .action-btn.primary:hover {\r\n  background: rgba(255, 193, 7, 0.9);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.rest-area-content .action-btn.secondary {\r\n  background: rgba(108, 117, 125, 0.8);\r\n  border-color: rgba(108, 117, 125, 0.3);\r\n  color: white;\r\n}\r\n\r\n.rest-area-content .action-btn.secondary:hover {\r\n  background: rgba(108, 117, 125, 0.9);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.refreshment-content .refreshment-title {\r\n  text-align: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 16px;\r\n  color: #17a2b8;\r\n}\r\n\r\n.refreshment-content .refreshment-categories {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.refreshment-content .category {\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.refreshment-content .category-title {\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n  color: #17a2b8;\r\n  margin-bottom: 8px;\r\n  text-align: center;\r\n}\r\n\r\n.refreshment-content .refreshment-items {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 6px;\r\n}\r\n\r\n.refreshment-content .refreshment-btn {\r\n  padding: 8px;\r\n  background: rgba(23, 162, 184, 0.1);\r\n  border: 1px solid rgba(23, 162, 184, 0.3);\r\n  border-radius: 6px;\r\n  color: white;\r\n  font-size: 11px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-align: center;\r\n}\r\n\r\n.refreshment-content .refreshment-btn:hover {\r\n  background: rgba(23, 162, 184, 0.2);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.refreshment-content .refreshment-tips {\r\n  background: rgba(23, 162, 184, 0.1);\r\n  border: 1px solid rgba(23, 162, 184, 0.3);\r\n  border-radius: 6px;\r\n  padding: 8px;\r\n}\r\n\r\n.refreshment-content .tip-item {\r\n  font-size: 11px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  margin-bottom: 4px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 6px;\r\n}\r\n\r\n.refreshment-content .tip-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 用户切换模式样式 */\r\n.user-selector-content .selector-title {\r\n  text-align: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 16px;\r\n  color: #6f42c1;\r\n}\r\n\r\n.user-selector-content .user-grid {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.user-selector-content .user-card {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px;\r\n  background: rgba(111, 66, 193, 0.1);\r\n  border: 1px solid rgba(111, 66, 193, 0.3);\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.user-selector-content .user-card:hover {\r\n  background: rgba(111, 66, 193, 0.2);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.user-selector-content .user-card.active {\r\n  background: rgba(111, 66, 193, 0.3);\r\n  border-color: rgba(111, 66, 193, 0.6);\r\n}\r\n\r\n.user-selector-content .user-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  font-size: 24px;\r\n  background: rgba(111, 66, 193, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.user-selector-content .user-info {\r\n  flex: 1;\r\n}\r\n\r\n.user-selector-content .user-name {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n  color: white;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.user-selector-content .user-role {\r\n  font-size: 11px;\r\n  color: rgba(255, 255, 255, 0.7);\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.user-selector-content .user-status {\r\n  font-size: 11px;\r\n  color: #4caf50;\r\n}\r\n\r\n.user-selector-content .selector-actions {\r\n  display: flex;\r\n  gap: 6px;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.user-selector-content .selector-btn {\r\n  padding: 8px 12px;\r\n  background: rgba(111, 66, 193, 0.8);\r\n  border: 1px solid rgba(111, 66, 193, 0.3);\r\n  border-radius: 6px;\r\n  color: white;\r\n  font-size: 11px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.user-selector-content .selector-btn:hover {\r\n  background: rgba(111, 66, 193, 0.9);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.user-preferences-content .preferences-title {\r\n  text-align: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 16px;\r\n  color: #e83e8c;\r\n}\r\n\r\n.user-preferences-content .preference-categories {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.user-preferences-content .preference-group {\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.user-preferences-content .group-title {\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n  color: #e83e8c;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.user-preferences-content .preference-items {\r\n  background: rgba(232, 62, 140, 0.1);\r\n  border: 1px solid rgba(232, 62, 140, 0.3);\r\n  border-radius: 6px;\r\n  padding: 8px;\r\n}\r\n\r\n.user-preferences-content .preference-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 4px 0;\r\n  font-size: 11px;\r\n}\r\n\r\n.user-preferences-content .item-label {\r\n  color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.user-preferences-content .item-value {\r\n  color: #e83e8c;\r\n  font-weight: 500;\r\n}\r\n\r\n.user-preferences-content .preferences-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n.user-preferences-content .preference-btn {\r\n  padding: 8px 16px;\r\n  background: rgba(232, 62, 140, 0.8);\r\n  border: 1px solid rgba(232, 62, 140, 0.3);\r\n  border-radius: 6px;\r\n  color: white;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.user-preferences-content .preference-btn:hover {\r\n  background: rgba(232, 62, 140, 0.9);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.privacy-settings-content .privacy-title {\r\n  text-align: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 16px;\r\n  color: #20c997;\r\n}\r\n\r\n.privacy-settings-content .privacy-items {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.privacy-settings-content .privacy-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 8px;\r\n  background: rgba(32, 201, 151, 0.1);\r\n  border: 1px solid rgba(32, 201, 151, 0.3);\r\n  border-radius: 6px;\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.privacy-settings-content .privacy-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.privacy-settings-content .privacy-icon {\r\n  font-size: 20px;\r\n  width: 32px;\r\n  height: 32px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.privacy-settings-content .privacy-info {\r\n  flex: 1;\r\n}\r\n\r\n.privacy-settings-content .privacy-label {\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n  color: white;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.privacy-settings-content .privacy-desc {\r\n  font-size: 10px;\r\n  color: rgba(255, 255, 255, 0.7);\r\n}\r\n\r\n.privacy-settings-content .privacy-toggle {\r\n  font-size: 14px;\r\n  color: #4caf50;\r\n  font-weight: bold;\r\n  cursor: pointer;\r\n}\r\n\r\n.privacy-settings-content .privacy-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n.privacy-settings-content .privacy-btn {\r\n  padding: 8px 16px;\r\n  border-radius: 6px;\r\n  border: 1px solid;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.privacy-settings-content .privacy-btn.primary {\r\n  background: rgba(32, 201, 151, 0.8);\r\n  border-color: rgba(32, 201, 151, 0.3);\r\n  color: white;\r\n}\r\n\r\n.privacy-settings-content .privacy-btn.primary:hover {\r\n  background: rgba(32, 201, 151, 0.9);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.privacy-settings-content .privacy-btn.secondary {\r\n  background: rgba(108, 117, 125, 0.8);\r\n  border-color: rgba(108, 117, 125, 0.3);\r\n  color: white;\r\n}\r\n\r\n.privacy-settings-content .privacy-btn.secondary:hover {\r\n  background: rgba(108, 117, 125, 0.9);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 用户切换模式主题覆盖 */\r\n.card-userSwitchMode {\r\n  background: rgba(111, 66, 193, 0.1) !important;\r\n  border: 1px solid rgba(111, 66, 193, 0.3) !important;\r\n}\r\n\r\n.card-userSwitchMode .glass-card {\r\n  background: rgba(111, 66, 193, 0.2) !important;\r\n  backdrop-filter: blur(8px) !important;\r\n}\r\n\r\n/* 疲劳检测模式主题覆盖 */\r\n.card-fatigueMode {\r\n  background: rgba(255, 193, 7, 0.1) !important;\r\n  border: 1px solid rgba(255, 193, 7, 0.3) !important;\r\n}\r\n\r\n.card-fatigueMode .glass-card {\r\n  background: rgba(255, 140, 0, 0.2) !important;\r\n  backdrop-filter: blur(8px) !important;\r\n}\r\n\r\n/* 紧急模式主题覆盖 */\r\n.card-emergencyMode {\r\n  background: rgba(220, 53, 69, 0.1) !important;\r\n  border: 1px solid rgba(220, 53, 69, 0.3) !important;\r\n}\r\n\r\n.card-emergencyMode .glass-card {\r\n  background: rgba(139, 0, 0, 0.2) !important;\r\n  backdrop-filter: blur(8px) !important;\r\n}\r\n\r\n.nearby-shops-content .shop-btn:hover {\r\n  background: rgba(40, 167, 69, 0.9);\r\n}\r\n\r\n/* 智能泊车模式样式 */\r\n.parking-search-content .parking-title {\r\n  text-align: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 16px;\r\n  color: #6f42c1;\r\n}\r\n\r\n.parking-search-content .parking-spots {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.parking-search-content .parking-spot {\r\n  padding: 12px;\r\n  margin-bottom: 8px;\r\n  background: rgba(111, 66, 193, 0.1);\r\n  border: 1px solid rgba(111, 66, 193, 0.3);\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.parking-search-content .parking-spot:hover {\r\n  background: rgba(111, 66, 193, 0.2);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.parking-search-content .parking-spot.recommended {\r\n  border-color: rgba(111, 66, 193, 0.6);\r\n  background: rgba(111, 66, 193, 0.2);\r\n}\r\n\r\n.parking-search-content .spot-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.parking-search-content .spot-id {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n  color: white;\r\n}\r\n\r\n.parking-search-content .spot-recommend {\r\n  font-size: 10px;\r\n  background: rgba(111, 66, 193, 0.8);\r\n  color: white;\r\n  padding: 2px 6px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.parking-search-content .spot-alternative {\r\n  font-size: 10px;\r\n  background: rgba(108, 117, 125, 0.6);\r\n  color: white;\r\n  padding: 2px 6px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.parking-search-content .spot-details {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.parking-search-content .spot-distance,\r\n.parking-search-content .spot-size,\r\n.parking-search-content .spot-type {\r\n  font-size: 11px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.parking-search-content .spot-status {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 11px;\r\n}\r\n\r\n.parking-search-content .status-indicator {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n}\r\n\r\n.parking-search-content .status-indicator.available {\r\n  background: #28a745;\r\n}\r\n\r\n.parking-search-content .status-indicator.limited {\r\n  background: #ffc107;\r\n}\r\n\r\n.parking-search-content .parking-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: center;\r\n}\r\n\r\n.parking-search-content .parking-btn {\r\n  padding: 8px 16px;\r\n  border-radius: 6px;\r\n  border: 1px solid;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.parking-search-content .parking-btn.primary {\r\n  background: rgba(111, 66, 193, 0.8);\r\n  border-color: rgba(111, 66, 193, 0.3);\r\n  color: white;\r\n}\r\n\r\n.parking-search-content .parking-btn.primary:hover {\r\n  background: rgba(111, 66, 193, 0.9);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.parking-search-content .parking-btn.secondary {\r\n  background: rgba(108, 117, 125, 0.8);\r\n  border-color: rgba(108, 117, 125, 0.3);\r\n  color: white;\r\n}\r\n\r\n.parking-search-content .parking-btn.secondary:hover {\r\n  background: rgba(108, 117, 125, 0.9);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.parking-assist-content .assist-title {\r\n  text-align: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 16px;\r\n  color: #17a2b8;\r\n}\r\n\r\n.parking-assist-content .assist-modes {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.parking-assist-content .assist-mode {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 8px;\r\n  margin-bottom: 8px;\r\n  background: rgba(23, 162, 184, 0.1);\r\n  border: 1px solid rgba(23, 162, 184, 0.3);\r\n  border-radius: 8px;\r\n}\r\n\r\n.parking-assist-content .mode-icon {\r\n  font-size: 20px;\r\n  width: 32px;\r\n  height: 32px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.parking-assist-content .mode-info {\r\n  flex: 1;\r\n}\r\n\r\n.parking-assist-content .mode-name {\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n  color: white;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.parking-assist-content .mode-desc {\r\n  font-size: 10px;\r\n  color: rgba(255, 255, 255, 0.7);\r\n}\r\n\r\n.parking-assist-content .mode-btn {\r\n  padding: 6px 12px;\r\n  background: rgba(23, 162, 184, 0.8);\r\n  border: 1px solid rgba(23, 162, 184, 0.3);\r\n  border-radius: 4px;\r\n  color: white;\r\n  font-size: 10px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.parking-assist-content .mode-btn:hover {\r\n  background: rgba(23, 162, 184, 0.9);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.parking-assist-content .camera-view {\r\n  background: rgba(23, 162, 184, 0.1);\r\n  border: 1px solid rgba(23, 162, 184, 0.3);\r\n  border-radius: 8px;\r\n  padding: 8px;\r\n}\r\n\r\n.parking-assist-content .view-title {\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n  color: #17a2b8;\r\n  margin-bottom: 8px;\r\n  text-align: center;\r\n}\r\n\r\n.parking-assist-content .camera-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 6px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.parking-assist-content .camera-btn {\r\n  padding: 8px;\r\n  background: rgba(23, 162, 184, 0.2);\r\n  border: 1px solid rgba(23, 162, 184, 0.4);\r\n  border-radius: 4px;\r\n  color: #17a2b8;\r\n  font-size: 10px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.parking-assist-content .camera-btn:hover {\r\n  background: rgba(23, 162, 184, 0.3);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.parking-assist-content .view-status {\r\n  font-size: 10px;\r\n}\r\n\r\n.parking-assist-content .status-item {\r\n  color: #4caf50;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.cost-info-content .cost-title {\r\n  text-align: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 16px;\r\n  color: #28a745;\r\n}\r\n\r\n.cost-info-content .pricing-options {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.cost-info-content .pricing-option {\r\n  padding: 12px;\r\n  margin-bottom: 8px;\r\n  background: rgba(40, 167, 69, 0.1);\r\n  border: 1px solid rgba(40, 167, 69, 0.3);\r\n  border-radius: 8px;\r\n}\r\n\r\n.cost-info-content .pricing-option.recommended {\r\n  border-color: rgba(40, 167, 69, 0.6);\r\n  background: rgba(40, 167, 69, 0.2);\r\n}\r\n\r\n.cost-info-content .option-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.cost-info-content .option-duration {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n  color: white;\r\n}\r\n\r\n.cost-info-content .option-price {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n  color: #28a745;\r\n}\r\n\r\n.cost-info-content .option-rate {\r\n  font-size: 11px;\r\n  color: rgba(255, 255, 255, 0.7);\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.cost-info-content .option-details {\r\n  font-size: 10px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.cost-info-content .detail-item {\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.cost-info-content .payment-section {\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.cost-info-content .payment-title {\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n  color: #28a745;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.cost-info-content .payment-methods {\r\n  display: flex;\r\n  gap: 6px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.cost-info-content .payment-btn {\r\n  padding: 6px 12px;\r\n  background: rgba(40, 167, 69, 0.8);\r\n  border: 1px solid rgba(40, 167, 69, 0.3);\r\n  border-radius: 4px;\r\n  color: white;\r\n  font-size: 10px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.cost-info-content .payment-btn:hover {\r\n  background: rgba(40, 167, 69, 0.9);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.cost-info-content .discount-section {\r\n  background: rgba(255, 193, 7, 0.1);\r\n  border: 1px solid rgba(255, 193, 7, 0.3);\r\n  border-radius: 6px;\r\n  padding: 8px;\r\n}\r\n\r\n.cost-info-content .discount-title {\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n  color: #ffc107;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.cost-info-content .discount-items {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 6px;\r\n}\r\n\r\n.cost-info-content .discount-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.cost-info-content .discount-name {\r\n  font-size: 11px;\r\n  color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.cost-info-content .discount-use {\r\n  padding: 4px 8px;\r\n  background: rgba(255, 193, 7, 0.8);\r\n  border: 1px solid rgba(255, 193, 7, 0.3);\r\n  border-radius: 4px;\r\n  color: #212529;\r\n  font-size: 9px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.cost-info-content .discount-use:hover {\r\n  background: rgba(255, 193, 7, 0.9);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 智能泊车模式主题覆盖 */\r\n.card-parkingMode {\r\n  background: rgba(111, 66, 193, 0.1) !important;\r\n  border: 1px solid rgba(111, 66, 193, 0.3) !important;\r\n}\r\n\r\n.card-parkingMode .glass-card {\r\n  background: rgba(111, 66, 193, 0.2) !important;\r\n  backdrop-filter: blur(8px) !important;\r\n}\r\n</style>\r\n"], "mappings": "AAcA,OAAOA,SAAQ,MAAO,kBAAiB;AAEvC,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,KAAK,EAAE;IACLC,KAAK,EAAE;MACLC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,QAAQ,EAAE;MACRH,IAAI,EAAEI,MAAM;MACZF,QAAQ,EAAE;IACZ,CAAC;IACDG,WAAW,EAAE;MACXL,IAAI,EAAEC,MAAM;MACZK,OAAO,EAAE;IACX;EACF,CAAC;EAEDC,KAAKA,CAACT,KAAK,EAAE;IACX;IACA,MAAMU,WAAU,GAAI;MAClB;MACAC,UAAU,EAAE;QACVC,KAAK,EAAE,IAAI;QACXC,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE;;;;;;;;;MASX,CAAC;MAED;MACAC,KAAK,EAAE;QACLH,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE;;;;;;;;;;MAUX,CAAC;MAED;MACAE,IAAI,EAAE;QACJJ,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE;;;;;;;MAOX,CAAC;MAED;MACAG,YAAY,EAAE;QACZL,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,uBAAuB;QAC7BC,OAAO,EAAE;;;;;;;;MAQX,CAAC;MAED;MACAI,KAAK,EAAE;QACLN,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,wBAAwB;QAC9BC,OAAO,EAAE;;;;;;MAMX,CAAC;MAED;MACAK,WAAW,EAAE;QACXP,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE;;;;;;;;MAQX,CAAC;MAED;MACAM,IAAI,EAAE;QACJR,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,kBAAkB;QACxBC,OAAO,EAAE;;;;;;;MAOX,CAAC;MAED;MACAO,YAAY,EAAE;QACZT,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE,kBAAkB;QACxBC,OAAO,EAAE;;;;;;;MAOX,CAAC;MAED;MACAQ,SAAS,EAAE;QACTV,KAAK,EAAE,EAAE;QACTC,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE;;;;;;;;;;;;;;;;MAgBX,CAAC;MAEDS,aAAa,EAAE;QACbX,KAAK,EAAE,EAAE;QACTC,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE;;;;;;;;;;;;MAYX,CAAC;MAED;MACAU,SAAS,EAAE;QACTZ,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,aAAa;QACnBC,OAAO,EAAE;;;;;;;;MAQX,CAAC;MAED;MACAW,WAAW,EAAE;QACXb,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,qBAAqB;QAC3BC,OAAO,EAAE;;;;;;MAMX,CAAC;MAED;MACAY,eAAe,EAAE;QACfd,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,gBAAgB;QACtBC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;MAuBX,CAAC;MAED;MACAa,cAAc,EAAE;QACdf,KAAK,EAAE,QAAQ;QACfC,IAAI,EAAE,uBAAuB;QAC7BC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;MAqBX,CAAC;MAED;MACAc,YAAY,EAAE;QACZhB,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAmCX,CAAC;MAED;MACAe,WAAW,EAAE;QACXjB,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE,iBAAiB;QACvBC,OAAO,EAAE;;;;;;MAMX,CAAC;MAED;MACAgB,YAAY,EAAE;QACZlB,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE,aAAa;QACnBC,OAAO,EAAE;;;;;;MAMX,CAAC;MAED;MACAiB,aAAa,EAAE;QACbnB,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,YAAY;QAClBC,OAAO,EAAE;;;;;;MAMX,CAAC;MAED;MACAkB,cAAc,EAAE;QACdpB,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,+BAA+B;QACrCC,OAAO,EAAE;;;;;;MAMX,CAAC;MAED;MACAmB,cAAc,EAAE;QACdrB,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,6BAA6B;QACnCC,OAAO,EAAE;;;;;;MAMX,CAAC;MAED;MACAoB,gBAAgB,EAAE;QAChBtB,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE;;;;;;MAMX,CAAC;MAED;MACAqB,cAAc,EAAE;QACdvB,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,YAAY;QAClBC,OAAO,EAAE;;;;;;;;MAQX,CAAC;MAED;MACAsB,UAAU,EAAE;QACVxB,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE;;;;;;;;;;;;;MAaX,CAAC;MAED;MACAuB,YAAY,EAAE;QACZzB,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,YAAY;QAClBC,OAAO,EAAE;;;;;;;;;;MAUX,CAAC;MAED;MACAwB,OAAO,EAAE;QACP1B,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,YAAY;QAClBC,OAAO,EAAE;;;;;;;;;;;;;;;MAeX,CAAC;MAED;MACAyB,cAAc,EAAE;QACd3B,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,yBAAyB;QAC/BC,OAAO,EAAE;;;;;;;;;;;;;;;;;MAiBX,CAAC;MAED;MACA0B,gBAAgB,EAAE;QAChB5B,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,YAAY;QAClBC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA8BX,CAAC;MAED;MACA2B,aAAa,EAAE;QACb7B,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE;;;;;;;;;;;;;;;;MAgBX,CAAC;MAED;MACA4B,YAAY,EAAE;QACZ9B,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE,kBAAkB;QACxBC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;MAoBX,CAAC;MAED;MACA6B,aAAa,EAAE;QACb/B,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,aAAa;QACnBC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA+BX,CAAC;MAED;MACA8B,WAAW,EAAE;QACXhC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA+BX,CAAC;MAED;MACA+B,aAAa,EAAE;QACbjC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,6BAA6B;QACnCC,OAAO,EAAE;;;;;;;;;;;;;;;MAeX,CAAC;MAED;MACAgC,QAAQ,EAAE;QACRlC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,kBAAkB;QACxBC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;MA0BX,CAAC;MAED;MACAiC,QAAQ,EAAE;QACRnC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE,gBAAgB;QACtBC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;MAyBX,CAAC;MAED;MACAkC,WAAW,EAAE;QACXpC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,eAAe;QACrBC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA6BX,CAAC;MAED;MACAmC,YAAY,EAAE;QACZrC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAoCX,CAAC;MAED;MACAoC,eAAe,EAAE;QACftC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAkDX,CAAC;MAED;MACAqC,eAAe,EAAE;QACfvC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,mBAAmB;QACzBC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA2CX,CAAC;MAED;MACAsC,aAAa,EAAE;QACbxC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,gBAAgB;QACtBC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAwDX,CAAC;MAED;MACAuC,aAAa,EAAE;QACbzC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,iBAAiB;QACvBC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA4CX,CAAC;MAED;MACAwC,QAAQ,EAAE;QACR1C,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA8DX;IACF;IAEA,MAAMyC,YAAW,GAAIA,CAAA,KAAM;MACzB,OAAO7C,WAAW,CAACV,KAAK,CAACK,QAAQ,CAAC,EAAEO,KAAI,IAAKZ,KAAK,CAACK,QAAO;IAC5D;IAEA,MAAMmD,WAAU,GAAIA,CAAA,KAAM;MACxB,OAAO9C,WAAW,CAACV,KAAK,CAACK,QAAQ,CAAC,EAAEQ,IAAG,IAAK,aAAY;IAC1D;IAEA,MAAM4C,cAAa,GAAIA,CAAA,KAAM;MAC3B;MACA,IAAIzD,KAAK,CAACK,QAAO,KAAM,WAAW,EAAE;QAClC,OAAOqD,aAAa,CAAC;MACvB;MACA,OAAOhD,WAAW,CAACV,KAAK,CAACK,QAAQ,CAAC,EAAES,OAAM,IAAK,kCAAkCd,KAAK,CAACK,QAAQ,QAAO;IACxG;IAEA,MAAMqD,aAAY,GAAIA,CAAA,KAAM;MAC1B;MACA,QAAQ1D,KAAK,CAACC,KAAK,CAAC0D,EAAE;QACpB,KAAK,YAAY;UACf,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;WAsCP;QACF;UACE,OAAOjD,WAAW,CAACY,SAAS,CAACR,OAAM;MACvC;IACF;IAEA,MAAM8C,YAAW,GAAIA,CAAA,KAAM;MACzB;MACA,MAAMC,WAAU,GAAI;QAClBC,IAAI,EAAE;UACJC,UAAU,EAAE,yBAAyB;UACrCC,MAAM,EAAE;QACV,CAAC;QACDC,IAAI,EAAE;UACJF,UAAU,EAAE,yBAAyB;UACrCC,MAAM,EAAE;QACV,CAAC;QACDE,OAAO,EAAE;UACPH,UAAU,EAAE,yBAAyB;UACrCC,MAAM,EAAE;QACV,CAAC;QACDG,IAAI,EAAE;UACJJ,UAAU,EAAE,wBAAwB;UACpCC,MAAM,EAAE;QACV,CAAC;QACDI,SAAS,EAAE;UACTL,UAAU,EAAE,yBAAyB;UACrCC,MAAM,EAAE;QACV;MACF;MAEA,OAAOH,WAAW,CAAC7D,KAAK,CAACC,KAAK,CAACoE,KAAK,KAAK,CAAC;IAC5C;IAEA,OAAO;MACLd,YAAY;MACZC,WAAW;MACXC,cAAc;MACdG;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}