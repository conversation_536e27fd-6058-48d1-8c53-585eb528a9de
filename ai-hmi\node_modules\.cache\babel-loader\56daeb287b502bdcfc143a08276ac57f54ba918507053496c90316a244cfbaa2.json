{"ast": null, "code": "import { ref, computed, onMounted } from 'vue';\nimport { useVpaStore } from '@/store/modules/vpa';\nimport VPAAvatarWidget from './vpa/VPAAvatarWidget.vue';\nexport default {\n  name: 'VPATestPage',\n  components: {\n    VPAAvatarWidget\n  },\n  setup() {\n    const vpaStore = useVpaStore();\n\n    // 响应式数据\n    const currentSize = ref('medium');\n    const availableModes = ['companion', 'interaction', 'hidden'];\n    const availableSizes = ['small', 'medium', 'large'];\n    const availableAnimations = ['idle', 'talking', 'listening', 'thinking', 'greeting', 'sleeping'];\n\n    // 主题颜色\n    const themeColors = {\n      primary: '#667eea',\n      secondary: '#764ba2',\n      background: 'rgba(102, 126, 234, 0.1)',\n      text: '#ffffff'\n    };\n\n    // 测试场景\n    const testScenes = [{\n      id: 'family',\n      name: '家庭出行模式',\n      layout: 'family',\n      vpaSize: 'small',\n      vpaPosition: {\n        x: 15,\n        y: 7\n      }\n    }, {\n      id: 'focus',\n      name: '专注通勤模式',\n      layout: 'focus',\n      vpaSize: 'small',\n      vpaPosition: {\n        x: 15,\n        y: 7\n      }\n    }, {\n      id: 'entertainment',\n      name: '娱乐等待模式',\n      layout: 'entertainment',\n      vpaSize: 'small',\n      vpaPosition: {\n        x: 1,\n        y: 1\n      }\n    }, {\n      id: 'minimal',\n      name: '极简模式',\n      layout: 'minimal',\n      vpaSize: 'medium',\n      vpaPosition: {\n        x: 1,\n        y: 1\n      }\n    }];\n\n    // 计算属性\n    const currentMode = computed(() => vpaStore.currentMode);\n    const currentAnimation = computed(() => vpaStore.animationState);\n    const isVisible = computed(() => vpaStore.isVisible);\n    const isInteracting = computed(() => vpaStore.isInteracting);\n\n    // 方法\n    const switchVpaMode = mode => {\n      vpaStore.switchMode(mode);\n    };\n    const setVpaAnimation = animation => {\n      vpaStore.setAnimation(animation);\n    };\n    const getModeLabel = mode => {\n      const labels = {\n        companion: 'div模式 (陪伴)',\n        interaction: '交互模式',\n        hidden: '隐藏模式'\n      };\n      return labels[mode] || mode;\n    };\n    const getAnimationLabel = animation => {\n      const labels = {\n        idle: '待机',\n        talking: '说话',\n        listening: '聆听',\n        thinking: '思考',\n        greeting: '问候',\n        sleeping: '休眠'\n      };\n      return labels[animation] || animation;\n    };\n    const handleVpaClick = data => {\n      console.log('VPA点击事件:', data);\n    };\n    const handleVpaModeChanged = mode => {\n      console.log('VPA模式变更:', mode);\n    };\n    const handleVpaAnimationChanged = animationData => {\n      console.log('VPA动画变更:', animationData);\n    };\n\n    // 生命周期\n    onMounted(() => {\n      console.log('VPA测试页面已加载');\n      // 确保VPA处于companion模式\n      vpaStore.switchMode('companion');\n    });\n    return {\n      currentSize,\n      availableModes,\n      availableSizes,\n      availableAnimations,\n      testScenes,\n      themeColors,\n      currentMode,\n      currentAnimation,\n      isVisible,\n      isInteracting,\n      switchVpaMode,\n      setVpaAnimation,\n      getModeLabel,\n      getAnimationLabel,\n      handleVpaClick,\n      handleVpaModeChanged,\n      handleVpaAnimationChanged\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "useVpaStore", "VPAAvatarWidget", "name", "components", "setup", "vpaStore", "currentSize", "availableModes", "availableSizes", "availableAnimations", "themeColors", "primary", "secondary", "background", "text", "testScenes", "id", "layout", "vpaSize", "vpaPosition", "x", "y", "currentMode", "currentAnimation", "animationState", "isVisible", "isInteracting", "switchVpaMode", "mode", "switchMode", "setVpaAnimation", "animation", "setAnimation", "getModeLabel", "labels", "companion", "interaction", "hidden", "getAnimationLabel", "idle", "talking", "listening", "thinking", "greeting", "sleeping", "handleVpaClick", "data", "console", "log", "handleVpaModeChanged", "handleVpaAnimationChanged", "animationData"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\VPATestPage.vue"], "sourcesContent": ["<template>\n  <div class=\"vpa-test-page\">\n    <h1>VPA数字人显示测试</h1>\n    \n    <!-- 模式切换控制 -->\n    <div class=\"mode-controls\">\n      <h3>VPA模式切换</h3>\n      <button \n        v-for=\"mode in availableModes\" \n        :key=\"mode\"\n        @click=\"switchVpaMode(mode)\"\n        :class=\"['mode-btn', { active: currentMode === mode }]\"\n      >\n        {{ getModeLabel(mode) }}\n      </button>\n    </div>\n\n    <!-- 尺寸测试 -->\n    <div class=\"size-controls\">\n      <h3>VPA尺寸测试</h3>\n      <button \n        v-for=\"size in availableSizes\" \n        :key=\"size\"\n        @click=\"currentSize = size\"\n        :class=\"['size-btn', { active: currentSize === size }]\"\n      >\n        {{ size }}\n      </button>\n    </div>\n\n    <!-- 动画状态测试 -->\n    <div class=\"animation-controls\">\n      <h3>VPA动画状态测试</h3>\n      <button \n        v-for=\"animation in availableAnimations\" \n        :key=\"animation\"\n        @click=\"setVpaAnimation(animation)\"\n        :class=\"['animation-btn', { active: currentAnimation === animation }]\"\n      >\n        {{ getAnimationLabel(animation) }}\n      </button>\n    </div>\n\n    <!-- VPA组件显示区域 -->\n    <div class=\"vpa-display-area\">\n      <h3>VPA组件显示 (当前模式: {{ getModeLabel(currentMode) }})</h3>\n      \n      <div class=\"vpa-container\">\n        <VPAAvatarWidget\n          v-if=\"currentMode !== 'hidden'\"\n          :size=\"currentSize\"\n          :position=\"{ x: 1, y: 1 }\"\n          :theme=\"'glass'\"\n          :theme-colors=\"themeColors\"\n          @avatar-click=\"handleVpaClick\"\n          @mode-changed=\"handleVpaModeChanged\"\n          @animation-changed=\"handleVpaAnimationChanged\"\n        />\n        \n        <div v-else class=\"hidden-message\">\n          VPA已隐藏\n        </div>\n      </div>\n    </div>\n\n    <!-- 状态信息显示 -->\n    <div class=\"status-info\">\n      <h3>VPA状态信息</h3>\n      <div class=\"info-grid\">\n        <div class=\"info-item\">\n          <label>当前模式:</label>\n          <span>{{ getModeLabel(currentMode) }}</span>\n        </div>\n        <div class=\"info-item\">\n          <label>当前尺寸:</label>\n          <span>{{ currentSize }}</span>\n        </div>\n        <div class=\"info-item\">\n          <label>当前动画:</label>\n          <span>{{ getAnimationLabel(currentAnimation) }}</span>\n        </div>\n        <div class=\"info-item\">\n          <label>是否可见:</label>\n          <span>{{ isVisible ? '是' : '否' }}</span>\n        </div>\n        <div class=\"info-item\">\n          <label>是否交互中:</label>\n          <span>{{ isInteracting ? '是' : '否' }}</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- 场景测试 -->\n    <div class=\"scene-test\">\n      <h3>场景中的VPA显示测试</h3>\n      <div class=\"scene-grid\">\n        <div class=\"scene-item\" v-for=\"scene in testScenes\" :key=\"scene.id\">\n          <h4>{{ scene.name }}</h4>\n          <div class=\"scene-preview\">\n            <div :class=\"['scene-layout', `layout-${scene.layout}`]\">\n              <div class=\"vpa-widget-preview\">\n                <VPAAvatarWidget\n                  :size=\"scene.vpaSize || 'small'\"\n                  :position=\"scene.vpaPosition || { x: 1, y: 1 }\"\n                  :theme=\"'glass'\"\n                  :theme-colors=\"themeColors\"\n                  @avatar-click=\"handleVpaClick\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted } from 'vue'\nimport { useVpaStore } from '@/store/modules/vpa'\nimport VPAAvatarWidget from './vpa/VPAAvatarWidget.vue'\n\nexport default {\n  name: 'VPATestPage',\n  components: {\n    VPAAvatarWidget\n  },\n  \n  setup() {\n    const vpaStore = useVpaStore()\n    \n    // 响应式数据\n    const currentSize = ref('medium')\n    const availableModes = ['companion', 'interaction', 'hidden']\n    const availableSizes = ['small', 'medium', 'large']\n    const availableAnimations = ['idle', 'talking', 'listening', 'thinking', 'greeting', 'sleeping']\n    \n    // 主题颜色\n    const themeColors = {\n      primary: '#667eea',\n      secondary: '#764ba2',\n      background: 'rgba(102, 126, 234, 0.1)',\n      text: '#ffffff'\n    }\n    \n    // 测试场景\n    const testScenes = [\n      {\n        id: 'family',\n        name: '家庭出行模式',\n        layout: 'family',\n        vpaSize: 'small',\n        vpaPosition: { x: 15, y: 7 }\n      },\n      {\n        id: 'focus',\n        name: '专注通勤模式',\n        layout: 'focus',\n        vpaSize: 'small',\n        vpaPosition: { x: 15, y: 7 }\n      },\n      {\n        id: 'entertainment',\n        name: '娱乐等待模式',\n        layout: 'entertainment',\n        vpaSize: 'small',\n        vpaPosition: { x: 1, y: 1 }\n      },\n      {\n        id: 'minimal',\n        name: '极简模式',\n        layout: 'minimal',\n        vpaSize: 'medium',\n        vpaPosition: { x: 1, y: 1 }\n      }\n    ]\n    \n    // 计算属性\n    const currentMode = computed(() => vpaStore.currentMode)\n    const currentAnimation = computed(() => vpaStore.animationState)\n    const isVisible = computed(() => vpaStore.isVisible)\n    const isInteracting = computed(() => vpaStore.isInteracting)\n    \n    // 方法\n    const switchVpaMode = (mode) => {\n      vpaStore.switchMode(mode)\n    }\n    \n    const setVpaAnimation = (animation) => {\n      vpaStore.setAnimation(animation)\n    }\n    \n    const getModeLabel = (mode) => {\n      const labels = {\n        companion: 'div模式 (陪伴)',\n        interaction: '交互模式',\n        hidden: '隐藏模式'\n      }\n      return labels[mode] || mode\n    }\n    \n    const getAnimationLabel = (animation) => {\n      const labels = {\n        idle: '待机',\n        talking: '说话',\n        listening: '聆听',\n        thinking: '思考',\n        greeting: '问候',\n        sleeping: '休眠'\n      }\n      return labels[animation] || animation\n    }\n    \n    const handleVpaClick = (data) => {\n      console.log('VPA点击事件:', data)\n    }\n    \n    const handleVpaModeChanged = (mode) => {\n      console.log('VPA模式变更:', mode)\n    }\n    \n    const handleVpaAnimationChanged = (animationData) => {\n      console.log('VPA动画变更:', animationData)\n    }\n    \n    // 生命周期\n    onMounted(() => {\n      console.log('VPA测试页面已加载')\n      // 确保VPA处于companion模式\n      vpaStore.switchMode('companion')\n    })\n    \n    return {\n      currentSize,\n      availableModes,\n      availableSizes,\n      availableAnimations,\n      testScenes,\n      themeColors,\n      currentMode,\n      currentAnimation,\n      isVisible,\n      isInteracting,\n      switchVpaMode,\n      setVpaAnimation,\n      getModeLabel,\n      getAnimationLabel,\n      handleVpaClick,\n      handleVpaModeChanged,\n      handleVpaAnimationChanged\n    }\n  }\n}\n</script>\n\n<style scoped>\n.vpa-test-page {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  min-height: 100vh;\n  color: white;\n}\n\n.vpa-test-page h1 {\n  text-align: center;\n  margin-bottom: 30px;\n  font-size: 2.5em;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.vpa-test-page h3 {\n  margin-bottom: 15px;\n  color: #ffffff;\n  font-size: 1.3em;\n}\n\n/* 控制按钮样式 */\n.mode-controls,\n.size-controls,\n.animation-controls {\n  margin-bottom: 30px;\n  padding: 20px;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.mode-btn,\n.size-btn,\n.animation-btn {\n  margin: 5px;\n  padding: 10px 20px;\n  border: none;\n  border-radius: 25px;\n  background: rgba(255, 255, 255, 0.2);\n  color: white;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-weight: 500;\n}\n\n.mode-btn:hover,\n.size-btn:hover,\n.animation-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: translateY(-2px);\n}\n\n.mode-btn.active,\n.size-btn.active,\n.animation-btn.active {\n  background: rgba(255, 255, 255, 0.4);\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\n}\n\n/* VPA显示区域 */\n.vpa-display-area {\n  margin-bottom: 30px;\n  padding: 20px;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.vpa-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 200px;\n  background: rgba(0, 0, 0, 0.1);\n  border-radius: 10px;\n  margin-top: 15px;\n}\n\n.hidden-message {\n  font-size: 1.2em;\n  color: rgba(255, 255, 255, 0.7);\n  text-align: center;\n}\n\n/* 状态信息 */\n.status-info {\n  margin-bottom: 30px;\n  padding: 20px;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 15px;\n  margin-top: 15px;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  padding: 10px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n}\n\n.info-item label {\n  font-weight: 600;\n}\n\n/* 场景测试 */\n.scene-test {\n  padding: 20px;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.scene-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n  margin-top: 15px;\n}\n\n.scene-item {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 10px;\n  padding: 15px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.scene-item h4 {\n  margin-bottom: 10px;\n  text-align: center;\n  color: #ffffff;\n}\n\n.scene-preview {\n  height: 150px;\n  background: rgba(0, 0, 0, 0.2);\n  border-radius: 8px;\n  position: relative;\n  overflow: hidden;\n}\n\n.scene-layout {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n\n.vpa-widget-preview {\n  position: absolute;\n  bottom: 10px;\n  right: 10px;\n}\n\n.layout-family .vpa-widget-preview {\n  bottom: 10px;\n  right: 10px;\n}\n\n.layout-focus .vpa-widget-preview {\n  bottom: 10px;\n  right: 10px;\n}\n\n.layout-entertainment .vpa-widget-preview {\n  top: 50%;\n  right: 10px;\n  transform: translateY(-50%);\n}\n\n.layout-minimal .vpa-widget-preview {\n  bottom: 10px;\n  left: 10px;\n}\n</style>"], "mappings": "AAsHA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAQ,QAAS,KAAI;AAC7C,SAASC,WAAU,QAAS,qBAAoB;AAChD,OAAOC,eAAc,MAAO,2BAA0B;AAEtD,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE;IACVF;EACF,CAAC;EAEDG,KAAKA,CAAA,EAAG;IACN,MAAMC,QAAO,GAAIL,WAAW,CAAC;;IAE7B;IACA,MAAMM,WAAU,GAAIT,GAAG,CAAC,QAAQ;IAChC,MAAMU,cAAa,GAAI,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ;IAC5D,MAAMC,cAAa,GAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO;IAClD,MAAMC,mBAAkB,GAAI,CAAC,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;;IAE/F;IACA,MAAMC,WAAU,GAAI;MAClBC,OAAO,EAAE,SAAS;MAClBC,SAAS,EAAE,SAAS;MACpBC,UAAU,EAAE,0BAA0B;MACtCC,IAAI,EAAE;IACR;;IAEA;IACA,MAAMC,UAAS,GAAI,CACjB;MACEC,EAAE,EAAE,QAAQ;MACZd,IAAI,EAAE,QAAQ;MACde,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE;QAAEC,CAAC,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE;IAC7B,CAAC,EACD;MACEL,EAAE,EAAE,OAAO;MACXd,IAAI,EAAE,QAAQ;MACde,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE;QAAEC,CAAC,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE;IAC7B,CAAC,EACD;MACEL,EAAE,EAAE,eAAe;MACnBd,IAAI,EAAE,QAAQ;MACde,MAAM,EAAE,eAAe;MACvBC,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE;IAC5B,CAAC,EACD;MACEL,EAAE,EAAE,SAAS;MACbd,IAAI,EAAE,MAAM;MACZe,MAAM,EAAE,SAAS;MACjBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE;IAC5B,EACF;;IAEA;IACA,MAAMC,WAAU,GAAIxB,QAAQ,CAAC,MAAMO,QAAQ,CAACiB,WAAW;IACvD,MAAMC,gBAAe,GAAIzB,QAAQ,CAAC,MAAMO,QAAQ,CAACmB,cAAc;IAC/D,MAAMC,SAAQ,GAAI3B,QAAQ,CAAC,MAAMO,QAAQ,CAACoB,SAAS;IACnD,MAAMC,aAAY,GAAI5B,QAAQ,CAAC,MAAMO,QAAQ,CAACqB,aAAa;;IAE3D;IACA,MAAMC,aAAY,GAAKC,IAAI,IAAK;MAC9BvB,QAAQ,CAACwB,UAAU,CAACD,IAAI;IAC1B;IAEA,MAAME,eAAc,GAAKC,SAAS,IAAK;MACrC1B,QAAQ,CAAC2B,YAAY,CAACD,SAAS;IACjC;IAEA,MAAME,YAAW,GAAKL,IAAI,IAAK;MAC7B,MAAMM,MAAK,GAAI;QACbC,SAAS,EAAE,YAAY;QACvBC,WAAW,EAAE,MAAM;QACnBC,MAAM,EAAE;MACV;MACA,OAAOH,MAAM,CAACN,IAAI,KAAKA,IAAG;IAC5B;IAEA,MAAMU,iBAAgB,GAAKP,SAAS,IAAK;MACvC,MAAMG,MAAK,GAAI;QACbK,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,IAAI;QACbC,SAAS,EAAE,IAAI;QACfC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE;MACZ;MACA,OAAOV,MAAM,CAACH,SAAS,KAAKA,SAAQ;IACtC;IAEA,MAAMc,cAAa,GAAKC,IAAI,IAAK;MAC/BC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEF,IAAI;IAC9B;IAEA,MAAMG,oBAAmB,GAAKrB,IAAI,IAAK;MACrCmB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEpB,IAAI;IAC9B;IAEA,MAAMsB,yBAAwB,GAAKC,aAAa,IAAK;MACnDJ,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEG,aAAa;IACvC;;IAEA;IACApD,SAAS,CAAC,MAAM;MACdgD,OAAO,CAACC,GAAG,CAAC,YAAY;MACxB;MACA3C,QAAQ,CAACwB,UAAU,CAAC,WAAW;IACjC,CAAC;IAED,OAAO;MACLvB,WAAW;MACXC,cAAc;MACdC,cAAc;MACdC,mBAAmB;MACnBM,UAAU;MACVL,WAAW;MACXY,WAAW;MACXC,gBAAgB;MAChBE,SAAS;MACTC,aAAa;MACbC,aAAa;MACbG,eAAe;MACfG,YAAY;MACZK,iBAAiB;MACjBO,cAAc;MACdI,oBAAoB;MACpBC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}