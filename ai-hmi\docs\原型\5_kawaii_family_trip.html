<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可爱家庭出游 (Kawaii Family Trip)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Mochiy+Pop+One&display=swap');
        body {
            font-family: 'Mochiy Pop One', sans-serif;
            background-color: #fff0f5; /* 柔和粉色背景 */
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            overflow: hidden; /* 禁止滚动 */
            height: 100vh;
            width: 100vw;
            position: relative;
        }
        /* 动态氛围层 - 可爱风格 */
        .ambiance-layer {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }
        .floating-hearts {
            position: absolute;
            font-size: 20px;
            color: #ff69b4;
            animation: heartFloat 6s infinite ease-in-out;
        }
        .floating-hearts:nth-child(1) { left: 15%; animation-delay: 0s; }
        .floating-hearts:nth-child(2) { left: 35%; animation-delay: 1.5s; }
        .floating-hearts:nth-child(3) { left: 55%; animation-delay: 3s; }
        .floating-hearts:nth-child(4) { left: 75%; animation-delay: 4.5s; }
        .rainbow-sparkles {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #ffd700;
            border-radius: 50%;
            animation: sparkle 3s infinite ease-in-out;
        }
        .rainbow-sparkles:nth-child(5) { top: 20%; left: 20%; background: #ff69b4; animation-delay: 0s; }
        .rainbow-sparkles:nth-child(6) { top: 30%; left: 60%; background: #87ceeb; animation-delay: 1s; }
        .rainbow-sparkles:nth-child(7) { top: 70%; left: 40%; background: #98fb98; animation-delay: 2s; }
        .rainbow-sparkles:nth-child(8) { top: 80%; left: 80%; background: #dda0dd; animation-delay: 0.5s; }
        @keyframes heartFloat {
            0%, 100% { transform: translateY(100vh) scale(0); opacity: 0; }
            10% { opacity: 1; transform: scale(1); }
            90% { opacity: 1; }
        }
        @keyframes sparkle {
            0%, 100% { opacity: 0; transform: scale(0) rotate(0deg); }
            50% { opacity: 1; transform: scale(1.5) rotate(180deg); }
        }
        .card {
            background-color: rgba(255, 255, 255, 0.8);
            border: 3px solid #ffc0cb; /* 粉色边框 */
            border-radius: 30px; /* 大圆角 */
            box-shadow: 0 6px 20px 0 rgba(255, 105, 180, 0.2);
            transition: all 0.3s cubic-bezier(.25,.8,.25,1);
            color: #5c5c5c;
        }
        .card:hover {
            transform: translateY(-6px) rotate(1deg);
            box-shadow: 0 10px 25px 0 rgba(255, 105, 180, 0.3);
        }
        .grid-container {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            grid-template-rows: repeat(4, 1fr);
            gap: 2rem; /* 卡片间距 */
            width: 100%;
            height: 100%;
            padding: 2rem;
            position: relative;
            z-index: 2;
        }
        .icon-style {
            transition: transform 0.3s ease;
        }
        .icon-style:hover {
            transform: scale(1.2) rotate(-10deg);
        }
        .button-kawaii {
            background-color: #ff9a8b;
            background-image: linear-gradient(90deg, #ff9a8b 0%, #ff6a88 55%, #ff99ac 100%);
            border: none;
            border-radius: 20px;
            padding: 12px 24px;
            color: white;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .button-kawaii:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }
        
        /* 返回按钮样式 - 可爱风格 */
        .back-button {
            position: fixed;
            top: 2rem;
            left: 2rem;
            z-index: 1000;
            padding: 1rem 1.5rem;
            border-radius: 25px;
            background: linear-gradient(135deg, #ff9a8b 0%, #ff6a88 55%, #ff99ac 100%);
            color: white;
            text-decoration: none;
            font-weight: bold;
            box-shadow: 0 6px 20px rgba(255, 105, 180, 0.3);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border: 3px solid #ffc0cb;
        }
        
        .back-button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 25px rgba(255, 105, 180, 0.4);
            color: white;
        }
    </style>
</head>
<body style="background-image: url('https://images.unsplash.com/photo-1469474968028-56623f02e42e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2074&q=80');">

    <!-- 动态氛围层 -->
    <div class="ambiance-layer">
        <div class="floating-hearts">💖</div>
        <div class="floating-hearts">🌟</div>
        <div class="floating-hearts">💕</div>
        <div class="floating-hearts">✨</div>
        <div class="rainbow-sparkles"></div>
        <div class="rainbow-sparkles"></div>
        <div class="rainbow-sparkles"></div>
        <div class="rainbow-sparkles"></div>
    </div>

    <!-- 返回按钮 -->
    <a href="complete_transition_demo.html" class="back-button" data-transition="ripple_expand">
        <i class="fas fa-arrow-left"></i>
        返回演示
    </a>

    <div class="grid-container">

        <!-- Dynamic Island (4x1) -->
        <div class="card col-span-4 row-span-1 flex items-center justify-between px-6">
            <div class="flex items-center space-x-3">
                <i class="fas fa-map-signs fa-lg text-pink-400 icon-style"></i>
                <div>
                    <p class="text-lg font-bold text-gray-700">下一站：动物园</p>
                    <p class="text-sm text-gray-500">还有15公里</p>
                </div>
            </div>
            <div class="text-right">
                <p class="text-3xl font-bold text-pink-500">10:30</p>
                <p class="text-sm text-gray-500">AM</p>
            </div>
        </div>

        <!-- Weather Card (4x1) -->
        <div class="card col-span-4 row-span-1 flex items-center justify-between px-6">
            <div class="flex items-center space-x-3">
                <i class="fas fa-sun fa-lg text-yellow-400 icon-style"></i>
                <div>
                    <p class="text-lg font-bold text-gray-700">晴朗</p>
                    <p class="text-sm text-gray-500">适合出游</p>
                </div>
            </div>
            <div class="text-right">
                <p class="text-3xl font-bold text-yellow-500">25°C</p>
                <p class="text-sm text-gray-500">体感 27°C</p>
            </div>
        </div>

        <!-- VPA Avatar (2x2) -->
        <div class="card col-span-2 row-span-2 flex flex-col items-center justify-center p-4">
            <img src="https://media.giphy.com/media/3oKIPnAiaMCws8nOsE/giphy.gif" alt="VPA Avatar" class="w-32 h-32 rounded-full border-4 border-pink-300 object-cover shadow-lg">
            <p class="mt-4 text-xl font-semibold text-pink-600">小绿</p>
            <p class="text-pink-500">出发啦~ (ﾉ◕ヮ◕)ﾉ*:･ﾟ✧</p>
        </div>

        <!-- Games Card (4x2) -->
        <div class="card col-span-4 row-span-2 flex flex-col p-6">
            <h3 class="text-3xl font-bold text-center text-gray-700 mb-4">车内小游戏</h3>
            <div class="flex-grow grid grid-cols-2 gap-4">
                <div class="bg-yellow-200 rounded-2xl flex flex-col items-center justify-center cursor-pointer hover:bg-yellow-300 transition-colors">
                    <i class="fas fa-microphone-alt fa-4x text-yellow-600 icon-style"></i>
                    <p class="mt-2 font-semibold text-yellow-800">猜歌名</p>
                </div>
                <div class="bg-blue-200 rounded-2xl flex flex-col items-center justify-center cursor-pointer hover:bg-blue-300 transition-colors">
                    <i class="fas fa-puzzle-piece fa-4x text-blue-600 icon-style"></i>
                    <p class="mt-2 font-semibold text-blue-800">成语接龙</p>
                </div>
            </div>
        </div>

        <!-- Snack Card (2x2) -->
        <div class="card col-span-2 row-span-2 flex flex-col items-center justify-center p-6">
            <i class="fas fa-cookie-bite fa-6x text-amber-600 icon-style"></i>
            <p class="text-2xl font-bold text-gray-700 mt-4">零食时间</p>
            <p class="text-lg text-gray-500">要吃点饼干吗？</p>
        </div>

        <!-- Photo Album Card (8x1) -->
        <div class="card col-span-8 row-span-1 flex items-center justify-between px-8">
             <div class="flex items-center space-x-4">
                <i class="fas fa-camera-retro fa-2x text-purple-400 icon-style"></i>
                <div>
                    <p class="text-2xl font-bold text-gray-700">旅途相册</p>
                    <p class="text-lg text-gray-500">已为您抓拍12张精彩瞬间！</p>
                </div>
            </div>
            <button class="button-kawaii">
                查看相册
            </button>
        </div>

    </div>

    <script src="enhanced_transition_manager.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化增强过渡管理器
            const transitionManager = new EnhancedTransitionManager({
                debug: false,
                enableSnapshots: true,
                transitionDuration: 1000
            });
        });
    </script>

</body>
</html>