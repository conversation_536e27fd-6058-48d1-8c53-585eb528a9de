<template>
  <BaseCard
    :card-type="'smart-home'"
    :size="size"
    :position="position"
    :theme="theme"
    :theme-colors="themeColors"
    :show-header="true"
    :show-footer="false"
    :title="cardTitle"
    :icon="'fas fa-home'"
    class="smart-home-control-card"
  >
    <div class="smart-home-container" :class="[`size-${size}`, `mode-${displayMode}`]">
      <!-- 家庭状态概览 -->
      <div class="home-status-overview">
        <div class="status-header">
          <div class="home-info">
            <h3 class="home-name">{{ homeInfo.name }}</h3>
            <div class="home-address">{{ homeInfo.address }}</div>
          </div>
          <div class="overall-status" :class="overallStatus.type">
            <i :class="overallStatus.icon"></i>
            <span>{{ overallStatus.text }}</span>
          </div>
        </div>
        
        <div class="quick-stats">
          <div class="stat-item">
            <div class="stat-value">{{ homeStats.temperature }}°C</div>
            <div class="stat-label">室内温度</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ homeStats.humidity }}%</div>
            <div class="stat-label">湿度</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ homeStats.activeDevices }}</div>
            <div class="stat-label">活跃设备</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ homeStats.energyUsage }}W</div>
            <div class="stat-label">功耗</div>
          </div>
        </div>
      </div>

      <!-- 场景模式快捷控制 -->
      <div class="scene-modes" v-if="showSceneModes">
        <div class="modes-header">
          <i class="fas fa-magic"></i>
          <span>场景模式</span>
        </div>
        <div class="modes-grid">
          <button
            v-for="scene in sceneModes"
            :key="scene.id"
            @click="activateScene(scene)"
            :class="['scene-btn', { active: scene.id === activeSceneId }]"
            :disabled="isProcessing"
          >
            <div class="scene-icon">
              <i :class="scene.icon"></i>
            </div>
            <div class="scene-info">
              <div class="scene-name">{{ scene.name }}</div>
              <div class="scene-desc">{{ scene.description }}</div>
            </div>
          </button>
        </div>
      </div>

      <!-- 设备控制区域 -->
      <div class="device-controls">
        <div class="controls-header">
          <span>设备控制</span>
          <div class="view-toggle">
            <button 
              @click="viewMode = 'grid'"
              :class="['toggle-btn', { active: viewMode === 'grid' }]"
            >
              <i class="fas fa-th"></i>
            </button>
            <button 
              @click="viewMode = 'list'"
              :class="['toggle-btn', { active: viewMode === 'list' }]"
            >
              <i class="fas fa-list"></i>
            </button>
          </div>
        </div>
        
        <div :class="['devices-container', `view-${viewMode}`]">
          <div
            v-for="device in filteredDevices"
            :key="device.id"
            :class="['device-item', `type-${device.type}`, `status-${device.status}`]"
            @click="handleDeviceClick(device)"
          >
            <div class="device-header">
              <div class="device-icon">
                <i :class="device.icon"></i>
              </div>
              <div class="device-info">
                <div class="device-name">{{ device.name }}</div>
                <div class="device-room">{{ device.room }}</div>
              </div>
              <div class="device-status-indicator" :class="device.status">
                <div class="status-dot"></div>
              </div>
            </div>
            
            <div class="device-controls-area">
              <!-- 开关控制 -->
              <div v-if="device.type === 'switch'" class="switch-control">
                <label class="switch">
                  <input 
                    type="checkbox" 
                    :checked="device.state.on"
                    @change="toggleDevice(device)"
                  >
                  <span class="slider"></span>
                </label>
              </div>
              
              <!-- 调光控制 -->
              <div v-else-if="device.type === 'light'" class="light-control">
                <div class="brightness-control">
                  <input
                    type="range"
                    :value="device.state.brightness"
                    @input="adjustBrightness(device, $event.target.value)"
                    min="0"
                    max="100"
                    class="brightness-slider"
                  >
                  <span class="brightness-value">{{ device.state.brightness }}%</span>
                </div>
                <div class="color-control" v-if="device.state.colorSupport">
                  <div class="color-presets">
                    <button
                      v-for="color in colorPresets"
                      :key="color.name"
                      @click="setLightColor(device, color)"
                      :class="['color-preset', { active: device.state.color === color.value }]"
                      :style="{ backgroundColor: color.value }"
                    ></button>
                  </div>
                </div>
              </div>
              
              <!-- 温控器控制 -->
              <div v-else-if="device.type === 'thermostat'" class="thermostat-control">
                <div class="temperature-display">
                  <span class="current-temp">{{ device.state.currentTemp }}°C</span>
                  <span class="target-temp">目标: {{ device.state.targetTemp }}°C</span>
                </div>
                <div class="temp-controls">
                  <button @click="adjustTemperature(device, -1)" class="temp-btn">
                    <i class="fas fa-minus"></i>
                  </button>
                  <button @click="adjustTemperature(device, 1)" class="temp-btn">
                    <i class="fas fa-plus"></i>
                  </button>
                </div>
              </div>
              
              <!-- 安防设备控制 -->
              <div v-else-if="device.type === 'security'" class="security-control">
                <div class="security-status">
                  <span :class="['status-text', device.state.armed ? 'armed' : 'disarmed']">
                    {{ device.state.armed ? '已布防' : '已撤防' }}
                  </span>
                </div>
                <button 
                  @click="toggleSecurity(device)"
                  :class="['security-toggle', device.state.armed ? 'disarm' : 'arm']"
                >
                  {{ device.state.armed ? '撤防' : '布防' }}
                </button>
              </div>
              
              <!-- 通用状态显示 -->
              <div v-else class="generic-status">
                <span class="status-text">{{ getDeviceStatusText(device) }}</span>
              </div>
            </div>
            
            <!-- 设备详细信息 -->
            <div v-if="device.showDetails" class="device-details">
              <div class="detail-item" v-for="(value, key) in device.details" :key="key">
                <span class="detail-label">{{ key }}:</span>
                <span class="detail-value">{{ value }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- AI智能建议 -->
      <div class="ai-suggestions" v-if="aiSuggestions.length > 0">
        <div class="suggestions-header">
          <i class="fas fa-lightbulb"></i>
          <span>AI智能建议</span>
        </div>
        <div class="suggestions-list">
          <div
            v-for="suggestion in aiSuggestions"
            :key="suggestion.id"
            class="suggestion-item"
          >
            <div class="suggestion-content">
              <div class="suggestion-text">{{ suggestion.text }}</div>
              <div class="suggestion-meta">
                <span class="energy-saving">节能 {{ suggestion.energySaving }}</span>
                <span class="confidence">可信度 {{ Math.round(suggestion.confidence * 100) }}%</span>
              </div>
            </div>
            <button 
              @click="applySuggestion(suggestion)"
              class="apply-btn"
              :disabled="isProcessing"
            >
              <i class="fas fa-check"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在连接智能设备...</div>
      </div>
    </div>
  </BaseCard>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import BaseCard from '@/components/cards/BaseCard.vue'
import mockDataService from '@/services/MockDataService.js'

export default {
  name: 'SmartHomeControlCard',
  components: {
    BaseCard
  },
  props: {
    size: {
      type: String,
      default: 'large',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    position: {
      type: Object,
      default: () => ({ x: 0, y: 0 })
    },
    theme: {
      type: String,
      default: 'glassmorphism'
    },
    themeColors: {
      type: Object,
      default: () => ({})
    },
    displayMode: {
      type: String,
      default: 'full',
      validator: (value) => ['compact', 'standard', 'full'].includes(value)
    },
    showSceneModes: {
      type: Boolean,
      default: true
    },
    deviceFilter: {
      type: String,
      default: 'all',
      validator: (value) => ['all', 'lights', 'security', 'climate', 'entertainment'].includes(value)
    },
    autoRefresh: {
      type: Boolean,
      default: true
    },
    refreshInterval: {
      type: Number,
      default: 30000 // 30秒
    }
  },
  emits: ['device-control', 'scene-activated', 'suggestion-applied'],
  setup(props, { emit }) {
    // 响应式数据
    const isLoading = ref(false)
    const isProcessing = ref(false)
    const viewMode = ref('grid')
    const activeSceneId = ref(null)
    
    const homeInfo = ref({
      name: '我的家',
      address: '北京市朝阳区'
    })
    
    const homeStats = ref({
      temperature: 22,
      humidity: 45,
      activeDevices: 12,
      energyUsage: 1250
    })
    
    const devices = ref([])
    const sceneModes = ref([])
    const aiSuggestions = ref([])
    
    const colorPresets = ref([
      { name: '暖白', value: '#FFF8DC' },
      { name: '冷白', value: '#F0F8FF' },
      { name: '红色', value: '#FF6B6B' },
      { name: '绿色', value: '#4ECDC4' },
      { name: '蓝色', value: '#45B7D1' },
      { name: '紫色', value: '#96CEB4' }
    ])

    // 计算属性
    const cardTitle = computed(() => {
      const onlineDevices = devices.value.filter(d => d.status === 'online').length
      return `智能家居 (${onlineDevices}/${devices.value.length})`
    })
    
    const overallStatus = computed(() => {
      const onlineDevices = devices.value.filter(d => d.status === 'online').length
      const totalDevices = devices.value.length
      
      if (totalDevices === 0) {
        return { type: 'unknown', icon: 'fas fa-question', text: '未知' }
      }
      
      const onlinePercentage = (onlineDevices / totalDevices) * 100
      
      if (onlinePercentage >= 90) {
        return { type: 'excellent', icon: 'fas fa-check-circle', text: '运行良好' }
      } else if (onlinePercentage >= 70) {
        return { type: 'good', icon: 'fas fa-exclamation-triangle', text: '基本正常' }
      } else {
        return { type: 'warning', icon: 'fas fa-exclamation-circle', text: '需要关注' }
      }
    })
    
    const filteredDevices = computed(() => {
      if (props.deviceFilter === 'all') {
        return devices.value
      }
      return devices.value.filter(device => device.category === props.deviceFilter)
    })

    // 方法
    const loadSmartHomeData = async () => {
      try {
        isLoading.value = true
        const smartHomeData = await mockDataService.getSmartHomeData()
        
        devices.value = smartHomeData.devices || []
        sceneModes.value = smartHomeData.scenes || []
        aiSuggestions.value = smartHomeData.aiSuggestions || []
        activeSceneId.value = smartHomeData.activeScene || null
        
        // 更新家庭统计信息
        updateHomeStats()
        
      } catch (error) {
        console.error('Failed to load smart home data:', error)
      } finally {
        isLoading.value = false
      }
    }
    
    const updateHomeStats = () => {
      const thermostat = devices.value.find(d => d.type === 'thermostat')
      if (thermostat) {
        homeStats.value.temperature = thermostat.state.currentTemp
        homeStats.value.humidity = thermostat.state.humidity || 45
      }
      
      homeStats.value.activeDevices = devices.value.filter(d => d.status === 'online').length
      
      // 计算总功耗
      const totalPower = devices.value.reduce((sum, device) => {
        return sum + (device.state.powerUsage || 0)
      }, 0)
      homeStats.value.energyUsage = totalPower
    }
    
    const handleDeviceClick = (device) => {
      device.showDetails = !device.showDetails
    }
    
    const toggleDevice = async (device) => {
      try {
        isProcessing.value = true
        
        const newState = !device.state.on
        await mockDataService.controlDevice(device.id, { on: newState })
        
        device.state.on = newState
        device.status = newState ? 'online' : 'offline'
        
        emit('device-control', {
          deviceId: device.id,
          action: 'toggle',
          state: { on: newState }
        })
        
        updateHomeStats()
        
      } catch (error) {
        console.error('Failed to toggle device:', error)
      } finally {
        isProcessing.value = false
      }
    }
    
    const adjustBrightness = async (device, brightness) => {
      try {
        const brightnessValue = parseInt(brightness)
        await mockDataService.controlDevice(device.id, { brightness: brightnessValue })
        
        device.state.brightness = brightnessValue
        
        emit('device-control', {
          deviceId: device.id,
          action: 'brightness',
          state: { brightness: brightnessValue }
        })
        
      } catch (error) {
        console.error('Failed to adjust brightness:', error)
      }
    }
    
    const setLightColor = async (device, color) => {
      try {
        await mockDataService.controlDevice(device.id, { color: color.value })
        
        device.state.color = color.value
        
        emit('device-control', {
          deviceId: device.id,
          action: 'color',
          state: { color: color.value }
        })
        
      } catch (error) {
        console.error('Failed to set light color:', error)
      }
    }
    
    const adjustTemperature = async (device, delta) => {
      try {
        const newTemp = device.state.targetTemp + delta
        if (newTemp < 16 || newTemp > 30) return
        
        await mockDataService.controlDevice(device.id, { targetTemp: newTemp })
        
        device.state.targetTemp = newTemp
        
        emit('device-control', {
          deviceId: device.id,
          action: 'temperature',
          state: { targetTemp: newTemp }
        })
        
      } catch (error) {
        console.error('Failed to adjust temperature:', error)
      }
    }
    
    const toggleSecurity = async (device) => {
      try {
        isProcessing.value = true
        
        const newArmedState = !device.state.armed
        await mockDataService.controlDevice(device.id, { armed: newArmedState })
        
        device.state.armed = newArmedState
        
        emit('device-control', {
          deviceId: device.id,
          action: 'security',
          state: { armed: newArmedState }
        })
        
      } catch (error) {
        console.error('Failed to toggle security:', error)
      } finally {
        isProcessing.value = false
      }
    }
    
    const activateScene = async (scene) => {
      try {
        isProcessing.value = true
        
        await mockDataService.activateScene(scene.id)
        
        activeSceneId.value = scene.id
        
        // 应用场景设置到设备
        scene.deviceSettings.forEach(setting => {
          const device = devices.value.find(d => d.id === setting.deviceId)
          if (device) {
            Object.assign(device.state, setting.state)
          }
        })
        
        emit('scene-activated', scene)
        updateHomeStats()
        
      } catch (error) {
        console.error('Failed to activate scene:', error)
      } finally {
        isProcessing.value = false
      }
    }
    
    const applySuggestion = async (suggestion) => {
      try {
        isProcessing.value = true
        
        // 应用AI建议
        await mockDataService.applySuggestion(suggestion.id)
        
        // 从建议列表中移除
        const index = aiSuggestions.value.findIndex(s => s.id === suggestion.id)
        if (index > -1) {
          aiSuggestions.value.splice(index, 1)
        }
        
        emit('suggestion-applied', suggestion)
        
        // 重新加载数据以反映变化
        await loadSmartHomeData()
        
      } catch (error) {
        console.error('Failed to apply suggestion:', error)
      } finally {
        isProcessing.value = false
      }
    }
    
    const getDeviceStatusText = (device) => {
      switch (device.status) {
        case 'online':
          return '在线'
        case 'offline':
          return '离线'
        case 'error':
          return '故障'
        default:
          return '未知'
      }
    }

    // 生命周期
    let refreshTimer = null
    
    onMounted(async () => {
      await mockDataService.initialize()
      await loadSmartHomeData()
      
      // 设置自动刷新
      if (props.autoRefresh) {
        refreshTimer = setInterval(() => {
          loadSmartHomeData()
        }, props.refreshInterval)
      }
    })

    onUnmounted(() => {
      if (refreshTimer) {
        clearInterval(refreshTimer)
      }
    })

    return {
      // 响应式数据
      isLoading,
      isProcessing,
      viewMode,
      activeSceneId,
      homeInfo,
      homeStats,
      devices,
      sceneModes,
      aiSuggestions,
      colorPresets,
      
      // 计算属性
      cardTitle,
      overallStatus,
      filteredDevices,
      
      // 方法
      handleDeviceClick,
      toggleDevice,
      adjustBrightness,
      setLightColor,
      adjustTemperature,
      toggleSecurity,
      activateScene,
      applySuggestion,
      getDeviceStatusText
    }
  }
}
</script>

<style scoped>
.smart-home-control-card {
  height: 100%;
}

.smart-home-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  position: relative;
}

/* 家庭状态概览 */
.home-status-overview {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.home-info h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.home-address {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.overall-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.overall-status.excellent {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.overall-status.good {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.overall-status.warning {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.quick-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
}

/* 场景模式 */
.scene-modes {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  padding: 16px;
}

.modes-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 12px;
}

.modes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
}

.scene-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.scene-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.scene-btn.active {
  background: rgba(74, 144, 226, 0.3);
  color: #4a90e2;
}

.scene-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.scene-info {
  flex: 1;
}

.scene-name {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 2px;
}

.scene-desc {
  font-size: 10px;
  opacity: 0.7;
}

/* 设备控制 */
.device-controls {
  flex: 1;
  min-height: 0;
}

.controls-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.controls-header span {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.view-toggle {
  display: flex;
  gap: 4px;
}

.toggle-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.toggle-btn:hover,
.toggle-btn.active {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

.devices-container {
  max-height: 400px;
  overflow-y: auto;
}

.devices-container.view-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.devices-container.view-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.device-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.device-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.device-item.status-online {
  border-left-color: #10b981;
}

.device-item.status-offline {
  border-left-color: #6b7280;
}

.device-item.status-error {
  border-left-color: #ef4444;
}

.device-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.device-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2px;
}

.device-room {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.device-status-indicator {
  width: 12px;
  height: 12px;
  position: relative;
}

.status-dot {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #6b7280;
}

.device-status-indicator.online .status-dot {
  background: #10b981;
  animation: pulse 2s infinite;
}

.device-status-indicator.error .status-dot {
  background: #ef4444;
}

/* 设备控制区域 */
.device-controls-area {
  margin-bottom: 8px;
}

/* 开关控制 */
.switch-control {
  display: flex;
  justify-content: center;
}

.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.2);
  transition: 0.3s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #4a90e2;
}

input:checked + .slider:before {
  transform: translateX(20px);
}

/* 调光控制 */
.light-control {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.brightness-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.brightness-slider {
  flex: 1;
  height: 4px;
  border-radius: 2px;
  background: rgba(255, 255, 255, 0.2);
  outline: none;
  -webkit-appearance: none;
}

.brightness-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #4a90e2;
  cursor: pointer;
}

.brightness-value {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  min-width: 30px;
  text-align: right;
}

.color-presets {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.color-preset {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.color-preset:hover,
.color-preset.active {
  border-color: rgba(255, 255, 255, 0.8);
  transform: scale(1.1);
}

/* 温控器控制 */
.thermostat-control {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.temperature-display {
  text-align: center;
}

.current-temp {
  font-size: 18px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  display: block;
}

.target-temp {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.temp-controls {
  display: flex;
  gap: 8px;
}

.temp-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.temp-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

/* 安防控制 */
.security-control {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.security-status .status-text.armed {
  color: #ef4444;
}

.security-status .status-text.disarmed {
  color: #10b981;
}

.security-toggle {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.security-toggle.arm {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.security-toggle.disarm {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

/* 通用状态 */
.generic-status {
  text-align: center;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

/* 设备详细信息 */
.device-details {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 8px;
  margin-top: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  margin-bottom: 4px;
}

.detail-label {
  color: rgba(255, 255, 255, 0.6);
}

.detail-value {
  color: rgba(255, 255, 255, 0.8);
}

/* AI建议 */
.ai-suggestions {
  background: rgba(126, 211, 33, 0.1);
  border-radius: 12px;
  padding: 16px;
}

.suggestions-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #7ed321;
  margin-bottom: 12px;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.suggestion-content {
  flex: 1;
}

.suggestion-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 4px;
}

.suggestion-meta {
  display: flex;
  gap: 12px;
  font-size: 10px;
}

.energy-saving {
  color: #7ed321;
}

.confidence {
  color: #4a90e2;
}

.apply-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background: rgba(126, 211, 33, 0.3);
  color: #7ed321;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.apply-btn:hover {
  background: rgba(126, 211, 33, 0.5);
  transform: scale(1.05);
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  border-radius: 12px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-top: 3px solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

/* 动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
.size-small .smart-home-container {
  padding: 12px;
  gap: 12px;
}

.size-small .quick-stats {
  grid-template-columns: repeat(2, 1fr);
}

.size-small .modes-grid {
  grid-template-columns: repeat(2, 1fr);
}

.mode-compact .scene-modes,
.mode-compact .ai-suggestions {
  display: none;
}

.mode-compact .devices-container {
  max-height: 250px;
}
</style>