{"asset": {"generator": "Khronos glTF Blender I/O v3.5.30", "version": "2.0"}, "extensionsUsed": ["KHR_materials_specular", "KHR_materials_ior"], "scene": 0, "scenes": [{"name": "Scene", "nodes": [0, 1]}], "nodes": [{"mesh": 0, "name": "light.001", "rotation": [0, -1, 0, 1.6292068494294654e-07], "translation": [0, 2.87214994430542, 0]}, {"mesh": 1, "name": "ReflecFloor", "rotation": [0, -1, 0, 2.821299744937278e-07], "translation": [11.865281105041504, 0.006904084701091051, 0]}], "materials": [{"doubleSided": true, "name": "light", "pbrMetallicRoughness": {}}, {"doubleSided": true, "extensions": {"KHR_materials_specular": {"specularColorFactor": [0, 0, 0]}, "KHR_materials_ior": {"ior": 1.4500000476837158}}, "name": "floor", "pbrMetallicRoughness": {"metallicFactor": 0.41709843277931213, "roughnessFactor": 0.14507770538330078}}], "meshes": [{"name": "Plane.023", "primitives": [{"attributes": {"POSITION": 0, "TEXCOORD_0": 1, "NORMAL": 2}, "indices": 3, "material": 0}]}, {"name": "Plane.020", "primitives": [{"attributes": {"POSITION": 4, "TEXCOORD_0": 5, "TEXCOORD_1": 6, "NORMAL": 7}, "indices": 8, "material": 1}]}], "accessors": [{"bufferView": 0, "componentType": 5126, "count": 4, "max": [2.9542529582977295, 0.05927114188671112, 1.4155222177505493], "min": [-2.9542529582977295, 0.0592702180147171, -1.4155222177505493], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 2, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 3, "componentType": 5123, "count": 6, "type": "SCALAR"}, {"bufferView": 4, "componentType": 5126, "count": 8, "max": [127.80513000488281, 0, 15.004532814025879], "min": [-104.07454681396484, 0, -15.004525184631348], "type": "VEC3"}, {"bufferView": 5, "componentType": 5126, "count": 8, "type": "VEC2"}, {"bufferView": 6, "componentType": 5126, "count": 8, "type": "VEC2"}, {"bufferView": 7, "componentType": 5126, "count": 8, "type": "VEC3"}, {"bufferView": 8, "componentType": 5123, "count": 18, "type": "SCALAR"}], "bufferViews": [{"buffer": 0, "byteLength": 48, "byteOffset": 0, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 48, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 80, "target": 34962}, {"buffer": 0, "byteLength": 12, "byteOffset": 128, "target": 34963}, {"buffer": 0, "byteLength": 96, "byteOffset": 140, "target": 34962}, {"buffer": 0, "byteLength": 64, "byteOffset": 236, "target": 34962}, {"buffer": 0, "byteLength": 64, "byteOffset": 300, "target": 34962}, {"buffer": 0, "byteLength": 96, "byteOffset": 364, "target": 34962}, {"buffer": 0, "byteLength": 36, "byteOffset": 460, "target": 34963}], "buffers": [{"uri": "sm_startroom.raw_data.bin", "byteLength": 496}]}