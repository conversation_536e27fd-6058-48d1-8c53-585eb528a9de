import { defineStore } from 'pinia'

export const useLayoutStore = defineStore('layout', {
  state: () => ({
    // 16x9网格系统配置
    gridConfig: {
      columns: 16,
      rows: 9,
      gap: 'min(1vw, 10px)',
      padding: 'min(1vw, 10px)'
    },
    
    // 当前布局模式
    currentLayout: 'default',
    
    // 可用布局模式
    availableLayouts: {
      default: {
        name: '默认布局',
        description: '标准16x9网格布局',
        gridTemplate: 'repeat(16, 1fr) / repeat(9, 1fr)'
      },
      family: {
        name: '家庭出行',
        description: '适合家庭出行的布局',
        gridTemplate: 'repeat(16, 1fr) / repeat(9, 1fr)'
      },
      focus: {
        name: '专注通勤',
        description: '专注工作的通勤布局',
        gridTemplate: 'repeat(16, 1fr) / repeat(9, 1fr)'
      },
      entertainment: {
        name: '娱乐模式',
        description: '娱乐和休闲布局',
        gridTemplate: 'repeat(16, 1fr) / repeat(9, 1fr)'
      },
      minimal: {
        name: '极简模式',
        description: '极简雨夜模式布局',
        gridTemplate: 'repeat(16, 1fr) / repeat(9, 1fr)'
      },
      immersive: {
        name: '沉浸式',
        description: '沉浸式桌面壁纸布局',
        gridTemplate: '1fr'
      }
    },
    
    // 组件尺寸预设
    componentSizes: {
      // 灵动岛
      dynamicIsland: { width: 16, height: 1 },
      
      // VPA组件尺寸
      vpaWidget: { width: 2, height: 2 },
      vpaWidgetMedium: { width: 2, height: 4 },
      vpaWidgetLarge: { width: 3, height: 3 },
      vpaInteractionSmall: { width: 4, height: 4 },
      vpaInteractionLarge: { width: 8, height: 9 },
      
      // 卡片组件尺寸
      cardSmall: { width: 4, height: 2 },
      cardMedium: { width: 8, height: 4 },
      cardLarge: { width: 8, height: 8 },
      cardFull: { width: 16, height: 9 },
      
      // 特定组件尺寸
      kidEducation: { width: 8, height: 8 },
      pedia: { width: 8, height: 3 },
      music: { width: 8, height: 8 },
      todo: { width: 8, height: 3 },
      orderStatus: { width: 4, height: 2 },
      smartHome: { width: 8, height: 4 },
      news: { width: 8, height: 4 },
      entertainment: { width: 16, height: 5 },
      
      // 安全组件尺寸
      fatigueWarning: { width: 8, height: 4 },
      emergencyContact: { width: 8, height: 6 },
      firstAid: { width: 8, height: 6 }
    },
    
    // 响应式断点
    breakpoints: {
      mobile: 768,
      tablet: 1024,
      desktop: 1440,
      large: 1920
    },
    
    // 当前屏幕尺寸
    screenSize: 'desktop',
    
    // 布局动画配置
    animations: {
      duration: 0.5,
      easing: 'power2.out',
      stagger: 0.1
    }
  }),
  
  getters: {
    // 获取当前网格配置
    currentGridConfig: (state) => {
      return {
        gridTemplateColumns: `repeat(${state.gridConfig.columns}, 1fr)`,
        gridTemplateRows: `repeat(${state.gridConfig.rows}, 1fr)`,
        gap: state.gridConfig.gap,
        padding: state.gridConfig.padding
      }
    },
    
    // 获取组件网格位置
    getComponentPosition: (state) => (componentType, position = { x: 1, y: 1 }) => {
      const size = state.componentSizes[componentType] || { width: 4, height: 2 }
      return {
        gridColumn: `${position.x} / ${position.x + size.width}`,
        gridRow: `${position.y} / ${position.y + size.height}`,
        width: '100%',
        height: '100%',
        minHeight: 0,
        overflow: 'hidden'
      }
    },
    
    // 获取响应式网格配置
    responsiveGridConfig: (state) => {
      const baseConfig = state.currentGridConfig
      
      switch (state.screenSize) {
        case 'mobile':
          return {
            ...baseConfig,
            gridTemplateColumns: 'repeat(8, 1fr)',
            gridTemplateRows: 'repeat(12, 1fr)'
          }
        case 'tablet':
          return {
            ...baseConfig,
            gridTemplateColumns: 'repeat(12, 1fr)',
            gridTemplateRows: 'repeat(10, 1fr)'
          }
        default:
          return baseConfig
      }
    }
  },
  
  actions: {
    // 切换布局模式
    switchLayout(layoutName) {
      if (this.availableLayouts[layoutName]) {
        this.currentLayout = layoutName
        console.log(`布局已切换到: ${layoutName}`)
      }
    },
    
    // 更新屏幕尺寸
    updateScreenSize(width) {
      if (width <= this.breakpoints.mobile) {
        this.screenSize = 'mobile'
      } else if (width <= this.breakpoints.tablet) {
        this.screenSize = 'tablet'
      } else if (width <= this.breakpoints.desktop) {
        this.screenSize = 'desktop'
      } else {
        this.screenSize = 'large'
      }
    },
    
    // 注册自定义组件尺寸
    registerComponentSize(componentType, size) {
      this.componentSizes[componentType] = size
    },
    
    // 获取组件的CSS网格样式
    getComponentGridStyle(componentType, position = { x: 1, y: 1 }) {
      return this.getComponentPosition(componentType, position)
    }
  }
})
