<template>
  <div class="default-card" :class="[`card-${cardType}`, `layout-${scene.layout}`]">
    <GlassCard
      :title="getCardTitle()"
      :icon="getCardIcon()"
      :style="getCardStyle()"
      :theme-colors="themeColors"
    >
      <div class="card-content" v-html="getCardContent()"></div>
    </GlassCard>
  </div>
</template>

<script>
import GlassCard from '../GlassCard.vue'

export default {
  name: 'DefaultCard',
  components: {
    GlassCard
  },
  props: {
    scene: {
      type: Object,
      required: true
    },
    cardType: {
      type: String,
      required: true
    },
    themeColors: {
      type: Object,
      default: null
    }
  },

  setup(props) {
    // 卡片配置映射
    const cardConfigs = {
      // 导航相关
      navigation: {
        title: '导航',
        icon: 'fas fa-route',
        content: `
          <div class="navigation-content">
            <div class="destination">前往公司</div>
            <div class="route-info">
              <span class="distance">12.5 公里</span>
              <span class="duration">25 分钟</span>
            </div>
          </div>
        `
      },
      
      // 音乐相关
      music: {
        title: '音乐控制',
        icon: 'fas fa-music',
        content: `
          <div class="music-content">
            <div class="current-song">正在播放: 日落大道</div>
            <div class="music-controls">
              <button class="control-btn"><i class="fas fa-backward"></i></button>
              <button class="control-btn"><i class="fas fa-pause"></i></button>
              <button class="control-btn"><i class="fas fa-forward"></i></button>
            </div>
          </div>
        `
      },
      
      // 待办事项
      todo: {
        title: '今日待办',
        icon: 'fas fa-tasks',
        content: `
          <div class="todo-content">
            <div class="todo-item">• 10:00 团队会议</div>
            <div class="todo-item">• 14:00 项目汇报</div>
            <div class="todo-item">• 16:30 客户电话</div>
          </div>
        `
      },
      
      // 儿童教育
      kidEducation: {
        title: '儿童教育',
        icon: 'fas fa-graduation-cap',
        content: `
          <div class="kid-education-content">
            <div class="video-placeholder">
              <i class="fas fa-play-circle"></i>
              <span>正在播放儿童视频...</span>
            </div>
          </div>
        `
      },
      
      // 百科问答
      pedia: {
        title: '百科问答',
        icon: 'fas fa-question-circle',
        content: `
          <div class="pedia-content">
            <div class="question">地球是圆的吗？</div>
            <div class="answer">是的，地球是一个近似球形的星球...</div>
          </div>
        `
      },
      
      // 视频播放器
      videoPlayer: {
        title: '视频播放器',
        icon: 'fas fa-video',
        content: `
          <div class="video-player-content">
            <div class="video-screen">
              <i class="fas fa-play"></i>
              <span>视频播放中</span>
            </div>
          </div>
        `
      },
      
      // 新闻摘要
      news: {
        title: '新闻摘要',
        icon: 'fas fa-newspaper',
        content: `
          <div class="news-content">
            <div class="news-item">• AI技术最新进展</div>
            <div class="news-item">• 智能汽车行业动态</div>
            <div class="news-item">• 科技创新资讯</div>
          </div>
        `
      },
      
      // 环境音
      ambientSound: {
        title: '环境音',
        icon: 'fas fa-volume-up',
        content: `
          <div class="ambient-sound-content">
            <button class="sound-btn">雨声</button>
            <button class="sound-btn">森林</button>
            <button class="sound-btn">冥想</button>
          </div>
        `
      },

      // VPA小窗
      vpaWidget: {
        title: '',
        icon: '',
        content: `
          <div class="vpa-widget-content">
            <div class="vpa-conversation">
              <p class="vpa-greeting">你好，我是小智，欢迎来到AI-HMI，我是智能桌面助手，可以根据场景定制专属桌面，这是对我说：</p>
              <div class="vpa-actions">
                <button class="vpa-action-btn">生成通勤桌面</button>
                <button class="vpa-action-btn">导航3D效果</button>
                <button class="vpa-action-btn">帮我规划一个独处的桌面</button>
                <button class="vpa-action-btn">生成春游桌面</button>
                <button class="vpa-action-btn">帮我规划一个周末一日游</button>
              </div>
              <p class="vpa-footer">根据你的使用习惯，后续会全面智能化桌面，或者也可以在AI-LAB设置你的更多介绍</p>
            </div>
            <img src="/images/vpa2.gif" alt="VPA智能助手" class="vpa-avatar" />
          </div>
        `
      },

      dynamicIsland: {
        title: '',
        icon: '',
        content: `
          <div class="dynamic-island-content">
            <div class="island-info">
              <i class="fas fa-route"></i>
              <span class="island-text">G2高速 - 距离下一出口 25km</span>
            </div>
            <div class="island-actions">
              <button class="island-btn"><i class="fas fa-map"></i></button>
              <button class="island-btn"><i class="fas fa-phone"></i></button>
            </div>
          </div>
        `
      },
      
      // 智能家居
      smartHome: {
        title: '智能家居',
        icon: 'fas fa-home',
        content: `
          <div class="smart-home-content">
            <div class="home-control">
              <button class="home-btn">客厅空调: 开</button>
              <button class="home-btn">空气净化器: 自动</button>
            </div>
          </div>
        `
      },
      
      // 订单状态
      orderStatus: {
        title: '订单状态',
        icon: 'fas fa-shopping-bag',
        content: `
          <div class="order-status-content">
            <div class="order-info">麦当劳早餐已下单</div>
            <div class="order-time">预计15分钟后取餐</div>
          </div>
        `
      },
      
      // 后座娱乐控制
      rearSeatControl: {
        title: '后座娱乐',
        icon: 'fas fa-gamepad',
        content: `
          <div class="rear-seat-content">
            <button class="entertainment-btn">动画片</button>
            <button class="entertainment-btn">游戏</button>
          </div>
        `
      },
      
      // 设施查找
      facilityFinder: {
        title: '查找设施',
        icon: 'fas fa-map-marker-alt',
        content: `
          <div class="facility-finder-content">
            <button class="facility-btn">查找最近的洗手间</button>
          </div>
        `
      },
      
      // 行程提醒
      tripReminder: {
        title: '行程提醒',
        icon: 'fas fa-clock',
        content: `
          <div class="trip-reminder-content">
            <div class="reminder">零食提醒: 30分钟后</div>
          </div>
        `
      },
      
      // 服务区信息
      serviceArea: {
        title: '服务区信息',
        icon: 'fas fa-gas-pump',
        content: `
          <div class="service-area-content">
            <div class="service-info">前方5公里有服务区</div>
            <div class="services">加油站 • 餐厅 • 休息区</div>
          </div>
        `
      },
      
      // 驾驶员状态
      driverStatus: {
        title: '驾驶员状态',
        icon: 'fas fa-user',
        content: `
          <div class="driver-status-content">
            <div class="status-item">疲劳度: 正常</div>
            <div class="status-item">注意力: 集中</div>
          </div>
        `
      },
      
      // 车辆状态
      vehicleStatus: {
        title: '车辆状态',
        icon: 'fas fa-car',
        content: `
          <div class="vehicle-status-content">
            <div class="status-item">油量: 75%</div>
            <div class="status-item">轮胎压力: 正常</div>
          </div>
        `
      },
      
      // 充电状态
      chargingStatus: {
        title: '充电状态',
        icon: 'fas fa-battery-three-quarters',
        content: `
          <div class="charging-status-content">
            <div class="charge-info">当前电量: 65%</div>
            <div class="charge-time">预计充满: 45分钟</div>
          </div>
        `
      },
      
      // 疲劳预警
      fatigueWarning: {
        title: '疲劳预警',
        icon: 'fas fa-exclamation-triangle',
        content: `
          <div class="fatigue-warning-content">
            <div class="warning-text">检测到疲劳驾驶</div>
            <div class="suggestion">建议就近休息</div>
          </div>
        `
      },
      
      // 紧急联系
      emergencyContact: {
        title: '紧急联系',
        icon: 'fas fa-phone',
        content: `
          <div class="emergency-contact-content">
            <button class="emergency-btn">拨打120</button>
            <button class="emergency-btn">联系家人</button>
          </div>
        `
      },

      // 访客模式 - 临时导航
      tempNavigation: {
        title: '临时导航',
        icon: 'fas fa-map',
        content: `
          <div class="temp-navigation-content">
            <div class="nav-input">
              <input type="text" placeholder="请输入临时目的地" class="destination-input">
              <button class="nav-btn">前往</button>
            </div>
          </div>
        `
      },

      // 访客模式 - 基础音乐
      basicMusic: {
        title: '基础音乐',
        icon: 'fas fa-music',
        content: `
          <div class="basic-music-content">
            <div class="music-source">
              <button class="source-btn">FM 97.4</button>
              <button class="source-btn">蓝牙音乐</button>
            </div>
            <div class="basic-controls">
              <button class="control-btn"><i class="fas fa-backward"></i></button>
              <button class="control-btn"><i class="fas fa-pause"></i></button>
              <button class="control-btn"><i class="fas fa-forward"></i></button>
            </div>
          </div>
        `
      },

      // 访客模式 - 基础控制
      basicControl: {
        title: '基础控制',
        icon: 'fas fa-cog',
        content: `
          <div class="basic-control-content">
            <div class="control-grid">
              <button class="control-grid-btn">空调</button>
              <button class="control-grid-btn">车窗</button>
              <button class="control-grid-btn">音量</button>
              <button class="control-grid-btn">灯光</button>
            </div>
          </div>
        `
      },

      // 宠物模式 - 宠物信息
      petInfo: {
        title: '宠物状态',
        icon: 'fas fa-paw',
        content: `
          <div class="pet-info-content">
            <div class="pet-status">
              <div class="pet-avatar">🐕</div>
              <div class="pet-details">
                <div class="pet-name">小黄</div>
                <div class="pet-mood">状态：安静</div>
                <div class="pet-time">留守时间：15分钟</div>
              </div>
            </div>
            <div class="pet-message">
              我的主人很快就回来！车内温度现在是22°C，很舒适。
            </div>
          </div>
        `
      },

      // 宠物模式 - 温度控制
      climateControl: {
        title: '温度控制',
        icon: 'fas fa-thermometer-half',
        content: `
          <div class="climate-control-content">
            <div class="temperature-display">
              <div class="current-temp">22°C</div>
              <div class="temp-status">舒适温度</div>
            </div>
            <div class="climate-controls">
              <button class="temp-btn">-</button>
              <div class="temp-range">18°C - 26°C</div>
              <button class="temp-btn">+</button>
            </div>
            <div class="climate-status">
              <div class="status-item">✓ 空调：运行中</div>
              <div class="status-item">✓ 空气循环：开启</div>
            </div>
          </div>
        `
      },

      // 洗车模式 - 洗车清单
      carWashChecklist: {
        title: '洗车清单',
        icon: 'fas fa-car',
        content: `
          <div class="car-wash-checklist-content">
            <div class="wash-title">洗车模式已激活</div>
            <div class="checklist-items">
              <div class="checklist-item completed">
                <span class="check-icon">✓</span>
                <span class="check-text">车窗已关闭并锁定</span>
              </div>
              <div class="checklist-item completed">
                <span class="check-icon">✓</span>
                <span class="check-text">后视镜已折叠</span>
              </div>
              <div class="checklist-item completed">
                <span class="check-icon">✓</span>
                <span class="check-text">充电口已锁定</span>
              </div>
              <div class="checklist-item completed">
                <span class="check-icon">✓</span>
                <span class="check-text">自动雨刷已禁用</span>
              </div>
              <div class="checklist-item completed">
                <span class="check-icon">✓</span>
                <span class="check-text">空调切换为内循环</span>
              </div>
            </div>
            <div class="wash-status">
              <div class="status-ready">准备就绪，可以安全洗车！</div>
            </div>
          </div>
        `
      },

      // 浪漫模式 - 浪漫音乐
      romanticMusic: {
        title: '浪漫音乐',
        icon: 'fas fa-music',
        content: `
          <div class="romantic-music-content">
            <div class="playlist-title">浪漫爵士乐</div>
            <div class="current-song">正在播放: Moonlight Serenade</div>
            <div class="music-controls">
              <button class="control-btn"><i class="fas fa-backward"></i></button>
              <button class="control-btn"><i class="fas fa-pause"></i></button>
              <button class="control-btn"><i class="fas fa-forward"></i></button>
            </div>
            <div class="playlist-songs">
              <div class="song-item">Fly Me to the Moon</div>
              <div class="song-item">The Way You Look Tonight</div>
              <div class="song-item">Unchained Melody</div>
            </div>
          </div>
        `
      },

      // 浪漫模式 - 氛围灯
      ambientLight: {
        title: '氛围灯',
        icon: 'fas fa-lightbulb',
        content: `
          <div class="ambient-light-content">
            <div class="light-title">氛围灯光</div>
            <div class="color-presets">
              <button class="color-btn" style="background: #ff69b4" data-color="rose-pink">玫瑰粉</button>
              <button class="color-btn" style="background: #ffa500" data-color="candle-yellow">烛光黄</button>
              <button class="color-btn" style="background: #9370db" data-color="star-purple">星空紫</button>
            </div>
            <div class="brightness-control">
              <div class="brightness-label">亮度: 50%</div>
              <div class="brightness-slider">
                <input type="range" min="0" max="100" value="50" class="slider">
              </div>
            </div>
            <div class="light-effects">
              <button class="effect-btn">呼吸效果</button>
              <button class="effect-btn">星光闪烁</button>
            </div>
          </div>
        `
      },

      // 充电模式 - 娱乐推荐
      entertainment: {
        title: '娱乐推荐',
        icon: 'fas fa-film',
        content: `
          <div class="entertainment-content">
            <div class="entertainment-title">充电期间娱乐</div>
            <div class="entertainment-grid">
              <button class="entertainment-item">
                <i class="fas fa-film"></i>
                <span>电影</span>
              </button>
              <button class="entertainment-item">
                <i class="fas fa-music"></i>
                <span>音乐</span>
              </button>
              <button class="entertainment-item">
                <i class="fas fa-podcast"></i>
                <span>播客</span>
              </button>
              <button class="entertainment-item">
                <i class="fas fa-book"></i>
                <span>有声书</span>
              </button>
              <button class="entertainment-item">
                <i class="fas fa-gamepad"></i>
                <span>游戏</span>
              </button>
              <button class="entertainment-item">
                <i class="fas fa-newspaper"></i>
                <span>新闻</span>
              </button>
            </div>
          </div>
        `
      },

      // 充电模式 - 附近商店
      nearbyShops: {
        title: '附近商店',
        icon: 'fas fa-store',
        content: `
          <div class="nearby-shops-content">
            <div class="shops-title">周边设施</div>
            <div class="shop-items">
              <div class="shop-item">
                <div class="shop-icon">☕</div>
                <div class="shop-info">
                  <div class="shop-name">星巴克</div>
                  <div class="shop-distance">步行2分钟</div>
                </div>
                <button class="shop-btn">导航</button>
              </div>
              <div class="shop-item">
                <div class="shop-icon">🛒</div>
                <div class="shop-info">
                  <div class="shop-name">便利店</div>
                  <div class="shop-distance">步行3分钟</div>
                </div>
                <button class="shop-btn">导航</button>
              </div>
              <div class="shop-item">
                <div class="shop-icon">🍽️</div>
                <div class="shop-info">
                  <div class="shop-name">快餐店</div>
                  <div class="shop-distance">步行5分钟</div>
                </div>
                <button class="shop-btn">导航</button>
              </div>
            </div>
          </div>
        `
      },

      // 紧急情况模式 - 紧急信息
      emergencyInfo: {
        title: '紧急信息',
        icon: 'fas fa-exclamation-triangle',
        content: `
          <div class="emergency-info-content">
            <div class="emergency-title">🚨 检测到紧急情况</div>
            <div class="emergency-status">
              <div class="status-item">✓ 已自动联系救援服务</div>
              <div class="status-item">✓ 救护车预计15分钟到达</div>
              <div class="status-item">✓ 位置信息已发送</div>
            </div>
            <div class="emergency-actions">
              <button class="emergency-action-btn">我没事</button>
              <button class="emergency-action-btn danger">需要帮助</button>
              <button class="emergency-action-btn">联系家人</button>
            </div>
          </div>
        `
      },

      // 紧急情况模式 - 急救指导
      firstAid: {
        title: '急救指导',
        icon: 'fas fa-first-aid',
        content: `
          <div class="first-aid-content">
            <div class="first-aid-title">🏥 急救指导</div>
            <div class="first-aid-steps">
              <div class="step-item">
                <div class="step-number">1</div>
                <div class="step-text">检查意识并呼救</div>
              </div>
              <div class="step-item">
                <div class="step-number">2</div>
                <div class="step-text">检查呼吸和脉搏</div>
              </div>
              <div class="step-item">
                <div class="step-number">3</div>
                <div class="step-text">保持体温和舒适体位</div>
              </div>
              <div class="step-item">
                <div class="step-number">4</div>
                <div class="step-text">等待专业救援到达</div>
              </div>
            </div>
            <div class="first-aid-warning">
              ⚠️ 请保持冷静，按照语音指导操作
            </div>
          </div>
        `
      },

      // 疲劳检测模式 - 休息区信息
      restArea: {
        title: '休息区信息',
        icon: 'fas fa-parking',
        content: `
          <div class="rest-area-content">
            <div class="rest-area-title">🛣️ 最近服务区</div>
            <div class="rest-area-info">
              <div class="info-item">
                <div class="info-icon">📍</div>
                <div class="info-details">
                  <div class="info-name">白云服务区</div>
                  <div class="info-distance">距离: 5km | 预计: 8分钟</div>
                </div>
              </div>
              <div class="facilities-list">
                <div class="facility-item">⛽ 加油站</div>
                <div class="facility-item">⚡ 充电桩</div>
                <div class="facility-item">🍽️ 餐厅</div>
                <div class="facility-item">🚻 休息室</div>
                <div class="facility-item">🛒 便利店</div>
              </div>
            </div>
            <div class="rest-area-actions">
              <button class="action-btn primary">导航前往</button>
              <button class="action-btn secondary">查看详情</button>
            </div>
          </div>
        `
      },

      // 疲劳检测模式 - 提神建议
      refreshment: {
        title: '提神建议',
        icon: 'fas fa-coffee',
        content: `
          <div class="refreshment-content">
            <div class="refreshment-title">☕ 提神建议</div>
            <div class="refreshment-categories">
              <div class="category">
                <div class="category-title">饮品选择</div>
                <div class="refreshment-items">
                  <button class="refreshment-btn">☕ 咖啡</button>
                  <button class="refreshment-btn">🍵 茶</button>
                  <button class="refreshment-btn">🥤 功能饮料</button>
                  <button class="refreshment-btn">💧 冰水</button>
                </div>
              </div>
              <div class="category">
                <div class="category-title">活动建议</div>
                <div class="refreshment-items">
                  <button class="refreshment-btn">🚶‍♂️ 下车活动</button>
                  <button class="refreshment-btn">🧴 洗脸</button>
                  <button class="refreshment-btn">🎵 播放提神音乐</button>
                  <button class="refreshment-btn">💤 短暂休息</button>
                </div>
              </div>
            </div>
            <div class="refreshment-tips">
              <div class="tip-item">💡 建议每2小时休息15分钟</div>
              <div class="tip-item">💡 避免空腹或过饱驾驶</div>
            </div>
          </div>
        `
      },

      // 用户切换模式 - 用户选择器
      userSelector: {
        title: '用户选择',
        icon: 'fas fa-users',
        content: `
          <div class="user-selector-content">
            <div class="selector-title">👥 选择用户配置文件</div>
            <div class="user-grid">
              <div class="user-card active">
                <div class="user-avatar">👨</div>
                <div class="user-info">
                  <div class="user-name">张先生</div>
                  <div class="user-role">主驾驶员</div>
                  <div class="user-status">✅ 当前</div>
                </div>
              </div>
              <div class="user-card">
                <div class="user-avatar">👩</div>
                <div class="user-info">
                  <div class="user-name">李女士</div>
                  <div class="user-role">副驾驶员</div>
                  <div class="user-status">○ 离线</div>
                </div>
              </div>
              <div class="user-card">
                <div class="user-avatar">👶</div>
                <div class="user-info">
                  <div class="user-name">儿童模式</div>
                  <div class="user-role">后排乘客</div>
                  <div class="user-status">○ 未激活</div>
                </div>
              </div>
            </div>
            <div class="selector-actions">
              <button class="selector-btn">🎤 语音识别</button>
              <button class="selector-btn">👤 访客模式</button>
              <button class="selector-btn">⚙️ 设置</button>
            </div>
          </div>
        `
      },

      // 用户切换模式 - 用户偏好
      userPreferences: {
        title: '用户偏好',
        icon: 'fas fa-heart',
        content: `
          <div class="user-preferences-content">
            <div class="preferences-title">🎯 个人偏好设置</div>
            <div class="preference-categories">
              <div class="preference-group">
                <div class="group-title">🎵 音乐偏好</div>
                <div class="preference-items">
                  <div class="preference-item">
                    <div class="item-label">音乐风格</div>
                    <div class="item-value">流行音乐</div>
                  </div>
                  <div class="preference-item">
                    <div class="item-label">音量偏好</div>
                    <div class="item-value">中等</div>
                  </div>
                </div>
              </div>
              <div class="preference-group">
                <div class="group-title">🌡️ 舒适设置</div>
                <div class="preference-items">
                  <div class="preference-item">
                    <div class="item-label">座椅位置</div>
                    <div class="item-value">位置3</div>
                  </div>
                  <div class="preference-item">
                    <div class="item-label">空调温度</div>
                    <div class="item-value">22°C</div>
                  </div>
                </div>
              </div>
              <div class="preference-group">
                <div class="group-title">🗺️ 导航偏好</div>
                <div class="preference-items">
                  <div class="preference-item">
                    <div class="item-label">路线选择</div>
                    <div class="item-value">避开拥堵</div>
                  </div>
                  <div class="preference-item">
                    <div class="item-label">语音播报</div>
                    <div class="item-value">开启</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="preferences-actions">
              <button class="preference-btn">编辑偏好</button>
              <button class="preference-btn">同步到云端</button>
            </div>
          </div>
        `
      },

      // 用户切换模式 - 隐私设置
      privacySettings: {
        title: '隐私设置',
        icon: 'fas fa-shield-alt',
        content: `
          <div class="privacy-settings-content">
            <div class="privacy-title">🛡️ 隐私与安全</div>
            <div class="privacy-items">
              <div class="privacy-item">
                <div class="privacy-icon">📍</div>
                <div class="privacy-info">
                  <div class="privacy-label">位置信息</div>
                  <div class="privacy-desc">仅行程中使用</div>
                </div>
                <div class="privacy-toggle">✅</div>
              </div>
              <div class="privacy-item">
                <div class="privacy-icon">🎤</div>
                <div class="privacy-info">
                  <div class="privacy-label">语音数据</div>
                  <div class="privacy-desc">本地处理</div>
                </div>
                <div class="privacy-toggle">✅</div>
              </div>
              <div class="privacy-item">
                <div class="privacy-icon">📊</div>
                <div class="privacy-info">
                  <div class="privacy-label">使用统计</div>
                  <div class="privacy-desc">匿名收集</div>
                </div>
                <div class="privacy-toggle">⚪</div>
              </div>
              <div class="privacy-item">
                <div class="privacy-icon">🔄</div>
                <div class="privacy-info">
                  <div class="privacy-label">数据同步</div>
                  <div class="privacy-desc">加密传输</div>
                </div>
                <div class="privacy-toggle">✅</div>
              </div>
            </div>
            <div class="privacy-actions">
              <button class="privacy-btn primary">详细设置</button>
              <button class="privacy-btn secondary">重置隐私</button>
            </div>
          </div>
        `
      },

      // 智能泊车模式 - 停车位搜索
      parkingSearch: {
        title: '可用车位',
        icon: 'fas fa-parking',
        content: `
          <div class="parking-search-content">
            <div class="parking-title">🅿️ 可用车位</div>
            <div class="parking-spots">
              <div class="parking-spot recommended">
                <div class="spot-header">
                  <div class="spot-id">B2-15</div>
                  <div class="spot-recommend">推荐</div>
                </div>
                <div class="spot-details">
                  <div class="spot-distance">距离: 50m</div>
                  <div class="spot-size">宽度: 标准</div>
                  <div class="spot-type">类型: 地下</div>
                </div>
                <div class="spot-status">
                  <div class="status-indicator available"></div>
                  <span>可用</span>
                </div>
              </div>
              <div class="parking-spot">
                <div class="spot-header">
                  <div class="spot-id">B1-08</div>
                  <div class="spot-alternative">备选</div>
                </div>
                <div class="spot-details">
                  <div class="spot-distance">距离: 80m</div>
                  <div class="spot-size">宽度: 宽体</div>
                  <div class="spot-type">类型: 地下</div>
                </div>
                <div class="spot-status">
                  <div class="status-indicator available"></div>
                  <span>可用</span>
                </div>
              </div>
              <div class="parking-spot">
                <div class="spot-header">
                  <div class="spot-id">A3-22</div>
                  <div class="spot-alternative">备选</div>
                </div>
                <div class="spot-details">
                  <div class="spot-distance">距离: 120m</div>
                  <div class="spot-size">宽度: 标准</div>
                  <div class="spot-type">类型: 露天</div>
                </div>
                <div class="spot-status">
                  <div class="status-indicator limited"></div>
                  <span>紧张</span>
                </div>
              </div>
            </div>
            <div class="parking-actions">
              <button class="parking-btn primary">导航至推荐车位</button>
              <button class="parking-btn secondary">查看更多车位</button>
            </div>
          </div>
        `
      },

      // 智能泊车模式 - 泊车辅助
      parkingAssist: {
        title: '泊车辅助',
        icon: 'fas fa-car-side',
        content: `
          <div class="parking-assist-content">
            <div class="assist-title">🚗 泊车辅助</div>
            <div class="assist-modes">
              <div class="assist-mode">
                <div class="mode-icon">🤖</div>
                <div class="mode-info">
                  <div class="mode-name">自动泊车</div>
                  <div class="mode-desc">系统自动完成泊车</div>
                </div>
                <button class="mode-btn">启动</button>
              </div>
              <div class="assist-mode">
                <div class="mode-icon">🎯</div>
                <div class="mode-info">
                  <div class="mode-name">辅助泊车</div>
                  <div class="mode-desc">方向盘和刹车辅助</div>
                </div>
                <button class="mode-btn">启动</button>
              </div>
              <div class="assist-mode">
                <div class="mode-icon">👤</div>
                <div class="mode-info">
                  <div class="mode-name">手动泊车</div>
                  <div class="mode-desc">仅提供指引</div>
                </div>
                <button class="mode-btn">启动</button>
              </div>
            </div>
            <div class="camera-view">
              <div class="view-title">📹 环视影像</div>
              <div class="camera-grid">
                <button class="camera-btn" data-camera="front">前</button>
                <button class="camera-btn" data-camera="rear">后</button>
                <button class="camera-btn" data-camera="left">左</button>
                <button class="camera-btn" data-camera="right">右</button>
              </div>
              <div class="view-status">
                <div class="status-item">✓ 摄像头已激活</div>
                <div class="status-item">✓ 雷达扫描中</div>
              </div>
            </div>
          </div>
        `
      },

      // 智能泊车模式 - 费用信息
      costInfo: {
        title: '费用信息',
        icon: 'fas fa-coins',
        content: `
          <div class="cost-info-content">
            <div class="cost-title">💰 停车费用</div>
            <div class="pricing-options">
              <div class="pricing-option">
                <div class="option-header">
                  <div class="option-duration">2小时</div>
                  <div class="option-price">¥10</div>
                </div>
                <div class="option-rate">费率: ¥5/小时</div>
                <div class="option-details">
                  <div class="detail-item">• 前30分钟免费</div>
                  <div class="detail-item">• 超时¥8/小时</div>
                </div>
              </div>
              <div class="pricing-option recommended">
                <div class="option-header">
                  <div class="option-duration">全天</div>
                  <div class="option-price">¥30</div>
                </div>
                <div class="option-rate">费率: ¥30/天</div>
                <div class="option-details">
                  <div class="detail-item">• 24小时有效</div>
                  <div class="detail-item">• 可多次进出</div>
                </div>
              </div>
              <div class="pricing-option">
                <div class="option-header">
                  <div class="option-duration">包月</div>
                  <div class="option-price">¥200</div>
                </div>
                <div class="option-rate">费率: ¥200/月</div>
                <div class="option-details">
                  <div class="detail-item">• 固定车位</div>
                  <div class="detail-item">• 专属权限</div>
                </div>
              </div>
            </div>
            <div class="payment-section">
              <div class="payment-title">支付方式</div>
              <div class="payment-methods">
                <button class="payment-btn">💳 支付宝</button>
                <button class="payment-btn">💳 微信支付</button>
                <button class="payment-btn">💳 银行卡</button>
                <button class="payment-btn">🚗 ETC</button>
              </div>
            </div>
            <div class="discount-section">
              <div class="discount-title">🎫 优惠券</div>
              <div class="discount-items">
                <div class="discount-item">
                  <div class="discount-name">新用户9折</div>
                  <button class="discount-use">使用</button>
                </div>
                <div class="discount-item">
                  <div class="discount-name">周末8折</div>
                  <button class="discount-use">使用</button>
                </div>
              </div>
            </div>
          </div>
        `
      }
    }

    const getCardTitle = () => {
      return cardConfigs[props.cardType]?.title || props.cardType
    }

    const getCardIcon = () => {
      return cardConfigs[props.cardType]?.icon || 'fas fa-cube'
    }

    const getCardContent = () => {
      return cardConfigs[props.cardType]?.content || `<div class="placeholder">卡片内容: ${props.cardType}</div>`
    }

    const getCardStyle = () => {
      // 根据场景主题返回不同的样式
      const themeStyles = {
        warm: {
          background: 'rgba(255, 193, 7, 0.15)',
          border: '1px solid rgba(255, 193, 7, 0.3)'
        },
        calm: {
          background: 'rgba(0, 123, 255, 0.15)',
          border: '1px solid rgba(0, 123, 255, 0.3)'
        },
        evening: {
          background: 'rgba(255, 87, 34, 0.15)',
          border: '1px solid rgba(255, 87, 34, 0.3)'
        },
        dark: {
          background: 'rgba(33, 37, 41, 0.15)',
          border: '1px solid rgba(33, 37, 41, 0.3)'
        },
        emergency: {
          background: 'rgba(220, 53, 69, 0.15)',
          border: '1px solid rgba(220, 53, 69, 0.3)'
        }
      }
      
      return themeStyles[props.scene.theme] || {}
    }

    return {
      getCardTitle,
      getCardIcon,
      getCardContent,
      getCardStyle
    }
  }
}
</script>

<style scoped>
.default-card {
  width: 100%;
  height: 100%;
}

/* 卡片内容样式 */
.card-content {
  color: white;
  font-size: 14px;
}

/* 导航内容 */
.navigation-content .destination {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.navigation-content .route-info {
  display: flex;
  gap: 15px;
}

.navigation-content .distance,
.navigation-content .duration {
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  font-size: 12px;
}

/* 音乐控制 */
.music-content .current-song {
  margin-bottom: 10px;
  font-weight: bold;
}

.music-controls {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.control-btn {
  background: var(--button-bg, rgba(74, 144, 226, 0.8));
  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));
  color: var(--button-color, white);
  padding: 8px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  text-shadow: var(--button-text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.control-btn:hover {
  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 待办事项 */
.todo-content .todo-item {
  margin-bottom: 8px;
  padding: 4px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 儿童教育 */
.kid-education-content .video-placeholder {
  text-align: center;
  padding: 20px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.kid-education-content .video-placeholder i {
  font-size: 24px;
  margin-bottom: 10px;
  display: block;
}

/* 百科问答 */
.pedia-content .question {
  font-weight: bold;
  margin-bottom: 8px;
  color: #ffc107;
}

.pedia-content .answer {
  font-size: 12px;
  line-height: 1.4;
}

/* 通用按钮样式 */
.sound-btn, .home-btn, .entertainment-btn, .facility-btn, .emergency-btn {
  background: var(--button-bg, rgba(74, 144, 226, 0.8));
  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));
  color: var(--button-color, white);
  padding: 6px 12px;
  border-radius: 8px;
  cursor: pointer;
  margin: 4px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  text-shadow: var(--button-text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(4px);
}

.sound-btn:hover, .home-btn:hover, .entertainment-btn:hover,
.facility-btn:hover, .emergency-btn:hover {
  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 状态信息 */
.status-item, .charge-info, .charge-time, .order-info, .order-time,
.service-info, .services, .warning-text, .suggestion, .reminder {
  margin-bottom: 6px;
  padding: 4px 0;
}

/* 紧急模式特殊样式 */
.card-emergencyMode .emergency-btn {
  background: rgba(220, 53, 69, 0.3);
  border-color: rgba(220, 53, 69, 0.5);
}

/* 响应式布局 */
@media (max-width: 768px) {
  .card-content {
    font-size: 12px;
  }
  
  .music-controls {
    gap: 5px;
  }
  
  .control-btn {
    padding: 6px;
  }
}

/* VPA小窗样式 */
.vpa-widget-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: stretch;
  gap: 20px;
  background: transparent;
  padding: 20px;
}

.vpa-conversation {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.vpa-greeting {
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary, #333);
  margin: 0;
}

.vpa-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.vpa-action-btn {
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  color: var(--text-primary, #333);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.vpa-action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.vpa-footer {
  font-size: 12px;
  color: var(--text-secondary, #666);
  margin: 0;
  line-height: 1.4;
}

.vpa-avatar {
  width: 120px;
  height: 120px;
  object-fit: contain;
  border-radius: 50%;
  flex-shrink: 0;
}

/* 灵动岛样式 */
.dynamic-island-content {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.island-info {
  display: flex;
  align-items: center;
  gap: 10px;
  color: white;
}

.island-text {
  font-size: 14px;
  font-weight: 500;
}

.island-actions {
  display: flex;
  gap: 10px;
}

.island-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--button-bg, rgba(74, 144, 226, 0.8));
  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));
  color: var(--button-color, white);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  text-shadow: var(--button-text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.island-btn:hover {
  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 访客模式样式 */
.temp-navigation-content .nav-input {
  display: flex;
  gap: 8px;
  align-items: center;
}

.temp-navigation-content .destination-input {
  flex: 1;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: white;
  font-size: 14px;
}

.temp-navigation-content .destination-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.temp-navigation-content .nav-btn {
  padding: 8px 16px;
  background: var(--button-bg, rgba(74, 144, 226, 0.8));
  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));
  border-radius: 8px;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.temp-navigation-content .nav-btn:hover {
  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));
}

.basic-music-content .music-source {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.basic-music-content .source-btn {
  flex: 1;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: white;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.basic-music-content .source-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.basic-music-content .basic-controls {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.basic-control-content .control-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.basic-control-content .control-grid-btn {
  padding: 12px;
  background: var(--button-bg, rgba(74, 144, 226, 0.8));
  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));
  border-radius: 8px;
  color: white;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
  text-align: center;
}

.basic-control-content .control-grid-btn:hover {
  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));
  transform: translateY(-1px);
}

/* 宠物模式样式 */
.pet-info-content .pet-status {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.pet-info-content .pet-avatar {
  font-size: 32px;
  width: 50px;
  height: 50px;
  background: rgba(255, 193, 7, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pet-info-content .pet-details {
  flex: 1;
}

.pet-info-content .pet-name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 4px;
}

.pet-info-content .pet-mood,
.pet-info-content .pet-time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.pet-info-content .pet-message {
  background: rgba(255, 193, 7, 0.1);
  padding: 8px;
  border-radius: 8px;
  font-size: 12px;
  text-align: center;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.climate-control-content .temperature-display {
  text-align: center;
  margin-bottom: 16px;
}

.climate-control-content .current-temp {
  font-size: 24px;
  font-weight: bold;
  color: #ffc107;
}

.climate-control-content .temp-status {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.climate-control-content .climate-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.climate-control-content .temp-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--button-bg, rgba(74, 144, 226, 0.8));
  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));
  color: white;
  cursor: pointer;
  font-size: 18px;
  transition: all 0.3s ease;
}

.climate-control-content .temp-btn:hover {
  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));
  transform: scale(1.1);
}

.climate-control-content .temp-range {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.climate-control-content .climate-status {
  font-size: 12px;
}

.climate-control-content .status-item {
  margin-bottom: 4px;
  color: #4caf50;
}

/* 洗车模式样式 */
.car-wash-checklist-content .wash-title {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #17a2b8;
}

.car-wash-checklist-content .checklist-items {
  margin-bottom: 16px;
}

.car-wash-checklist-content .checklist-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding: 8px;
  background: rgba(23, 162, 184, 0.1);
  border-radius: 6px;
  border-left: 3px solid #17a2b8;
}

.car-wash-checklist-content .checklist-item.completed {
  border-left-color: #28a745;
}

.car-wash-checklist-content .check-icon {
  color: #28a745;
  font-weight: bold;
  font-size: 14px;
}

.car-wash-checklist-content .check-text {
  font-size: 12px;
  color: white;
}

.car-wash-checklist-content .wash-status {
  text-align: center;
}

.car-wash-checklist-content .status-ready {
  background: rgba(40, 167, 69, 0.2);
  color: #28a745;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: bold;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

/* 浪漫模式样式 */
.romantic-music-content .playlist-title {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #ff69b4;
}

.romantic-music-content .current-song {
  text-align: center;
  margin-bottom: 12px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.romantic-music-content .music-controls {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin-bottom: 12px;
}

.romantic-music-content .playlist-songs {
  font-size: 12px;
}

.romantic-music-content .song-item {
  padding: 4px 8px;
  margin-bottom: 4px;
  background: rgba(255, 105, 180, 0.1);
  border-radius: 4px;
  color: rgba(255, 255, 255, 0.8);
}

.ambient-light-content .light-title {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #ffa500;
}

.ambient-light-content .color-presets {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin-bottom: 12px;
}

.ambient-light-content .color-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  font-size: 10px;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  transition: all 0.3s ease;
}

.ambient-light-content .color-btn:hover {
  transform: scale(1.1);
  border-color: rgba(255, 255, 255, 0.8);
}

.ambient-light-content .brightness-control {
  margin-bottom: 12px;
}

.ambient-light-content .brightness-label {
  font-size: 12px;
  margin-bottom: 4px;
  color: rgba(255, 255, 255, 0.8);
}

.ambient-light-content .brightness-slider {
  width: 100%;
}

.ambient-light-content .slider {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  outline: none;
  -webkit-appearance: none;
}

.ambient-light-content .light-effects {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.ambient-light-content .effect-btn {
  padding: 6px 12px;
  background: rgba(255, 165, 0, 0.2);
  border: 1px solid rgba(255, 165, 0, 0.4);
  border-radius: 6px;
  color: #ffa500;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.ambient-light-content .effect-btn:hover {
  background: rgba(255, 165, 0, 0.3);
  transform: translateY(-1px);
}

/* 充电模式样式 */
.entertainment-content .entertainment-title {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #17a2b8;
}

.entertainment-content .entertainment-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.entertainment-content .entertainment-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 8px;
  background: rgba(23, 162, 184, 0.1);
  border: 1px solid rgba(23, 162, 184, 0.3);
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.entertainment-content .entertainment-item:hover {
  background: rgba(23, 162, 184, 0.2);
  transform: translateY(-2px);
}

.entertainment-content .entertainment-item i {
  font-size: 20px;
  color: #17a2b8;
}

.entertainment-content .entertainment-item span {
  font-size: 12px;
  text-align: center;
}

.nearby-shops-content .shops-title {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #28a745;
}

.nearby-shops-content .shop-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.nearby-shops-content .shop-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: rgba(40, 167, 69, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(40, 167, 69, 0.2);
}

.nearby-shops-content .shop-icon {
  font-size: 20px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nearby-shops-content .shop-info {
  flex: 1;
}

.nearby-shops-content .shop-name {
  font-size: 14px;
  font-weight: bold;
  color: white;
}

.nearby-shops-content .shop-distance {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
}

.nearby-shops-content .shop-btn {
  padding: 4px 8px;
  background: rgba(40, 167, 69, 0.8);
  border: 1px solid rgba(40, 167, 69, 0.3);
  border-radius: 4px;
  color: white;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* 紧急情况模式样式 */
.emergency-info-content .emergency-title {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #dc3545;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}

.emergency-info-content .emergency-status {
  margin-bottom: 16px;
}

.emergency-info-content .status-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 6px 0;
  color: #4caf50;
  font-size: 13px;
}

.emergency-info-content .status-item:before {
  content: "✓";
  margin-right: 8px;
  font-weight: bold;
}

.emergency-info-content .emergency-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.emergency-info-content .emergency-action-btn {
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  min-width: 80px;
}

.emergency-info-content .emergency-action-btn:first-child {
  background: rgba(40, 167, 69, 0.8);
  border-color: rgba(40, 167, 69, 0.3);
  color: white;
}

.emergency-info-content .emergency-action-btn:first-child:hover {
  background: rgba(40, 167, 69, 0.9);
  transform: translateY(-1px);
}

.emergency-info-content .emergency-action-btn.danger {
  background: rgba(220, 53, 69, 0.8);
  border-color: rgba(220, 53, 69, 0.3);
  color: white;
}

.emergency-info-content .emergency-action-btn.danger:hover {
  background: rgba(220, 53, 69, 0.9);
  transform: translateY(-1px);
}

.emergency-info-content .emergency-action-btn:last-child {
  background: rgba(255, 193, 7, 0.8);
  border-color: rgba(255, 193, 7, 0.3);
  color: #212529;
}

.emergency-info-content .emergency-action-btn:last-child:hover {
  background: rgba(255, 193, 7, 0.9);
  transform: translateY(-1px);
}

.first-aid-content .first-aid-title {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #17a2b8;
}

.first-aid-content .first-aid-steps {
  margin-bottom: 16px;
}

.first-aid-content .step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 8px;
  background: rgba(23, 162, 184, 0.1);
  border-radius: 6px;
  border-left: 3px solid #17a2b8;
}

.first-aid-content .step-number {
  width: 24px;
  height: 24px;
  background: #17a2b8;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  margin-right: 12px;
  flex-shrink: 0;
}

.first-aid-content .step-text {
  font-size: 12px;
  color: white;
  line-height: 1.4;
  flex: 1;
}

.first-aid-content .first-aid-warning {
  text-align: center;
  padding: 8px;
  background: rgba(255, 193, 7, 0.2);
  border: 1px solid rgba(255, 193, 7, 0.4);
  border-radius: 6px;
  color: #ffc107;
  font-size: 12px;
  font-weight: 500;
}

/* 疲劳检测模式样式 */
.rest-area-content .rest-area-title {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #ffc107;
}

.rest-area-content .rest-area-info {
  margin-bottom: 16px;
}

.rest-area-content .info-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  padding: 8px;
  background: rgba(255, 193, 7, 0.1);
  border-radius: 8px;
}

.rest-area-content .info-icon {
  font-size: 24px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.rest-area-content .info-details {
  flex: 1;
}

.rest-area-content .info-name {
  font-size: 14px;
  font-weight: bold;
  color: white;
  margin-bottom: 2px;
}

.rest-area-content .info-distance {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
}

.rest-area-content .facilities-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 8px;
}

.rest-area-content .facility-item {
  padding: 4px 8px;
  background: rgba(255, 193, 7, 0.2);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 4px;
  font-size: 10px;
  color: #ffc107;
}

.rest-area-content .rest-area-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.rest-area-content .action-btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.rest-area-content .action-btn.primary {
  background: rgba(255, 193, 7, 0.8);
  border-color: rgba(255, 193, 7, 0.3);
  color: #212529;
}

.rest-area-content .action-btn.primary:hover {
  background: rgba(255, 193, 7, 0.9);
  transform: translateY(-1px);
}

.rest-area-content .action-btn.secondary {
  background: rgba(108, 117, 125, 0.8);
  border-color: rgba(108, 117, 125, 0.3);
  color: white;
}

.rest-area-content .action-btn.secondary:hover {
  background: rgba(108, 117, 125, 0.9);
  transform: translateY(-1px);
}

.refreshment-content .refreshment-title {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #17a2b8;
}

.refreshment-content .refreshment-categories {
  margin-bottom: 16px;
}

.refreshment-content .category {
  margin-bottom: 12px;
}

.refreshment-content .category-title {
  font-size: 12px;
  font-weight: bold;
  color: #17a2b8;
  margin-bottom: 8px;
  text-align: center;
}

.refreshment-content .refreshment-items {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 6px;
}

.refreshment-content .refreshment-btn {
  padding: 8px;
  background: rgba(23, 162, 184, 0.1);
  border: 1px solid rgba(23, 162, 184, 0.3);
  border-radius: 6px;
  color: white;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.refreshment-content .refreshment-btn:hover {
  background: rgba(23, 162, 184, 0.2);
  transform: translateY(-1px);
}

.refreshment-content .refreshment-tips {
  background: rgba(23, 162, 184, 0.1);
  border: 1px solid rgba(23, 162, 184, 0.3);
  border-radius: 6px;
  padding: 8px;
}

.refreshment-content .tip-item {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4px;
  display: flex;
  align-items: flex-start;
  gap: 6px;
}

.refreshment-content .tip-item:last-child {
  margin-bottom: 0;
}

/* 用户切换模式样式 */
.user-selector-content .selector-title {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #6f42c1;
}

.user-selector-content .user-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.user-selector-content .user-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(111, 66, 193, 0.1);
  border: 1px solid rgba(111, 66, 193, 0.3);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-selector-content .user-card:hover {
  background: rgba(111, 66, 193, 0.2);
  transform: translateY(-1px);
}

.user-selector-content .user-card.active {
  background: rgba(111, 66, 193, 0.3);
  border-color: rgba(111, 66, 193, 0.6);
}

.user-selector-content .user-avatar {
  width: 40px;
  height: 40px;
  font-size: 24px;
  background: rgba(111, 66, 193, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.user-selector-content .user-info {
  flex: 1;
}

.user-selector-content .user-name {
  font-size: 14px;
  font-weight: bold;
  color: white;
  margin-bottom: 2px;
}

.user-selector-content .user-role {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 2px;
}

.user-selector-content .user-status {
  font-size: 11px;
  color: #4caf50;
}

.user-selector-content .selector-actions {
  display: flex;
  gap: 6px;
  justify-content: center;
  flex-wrap: wrap;
}

.user-selector-content .selector-btn {
  padding: 8px 12px;
  background: rgba(111, 66, 193, 0.8);
  border: 1px solid rgba(111, 66, 193, 0.3);
  border-radius: 6px;
  color: white;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-selector-content .selector-btn:hover {
  background: rgba(111, 66, 193, 0.9);
  transform: translateY(-1px);
}

.user-preferences-content .preferences-title {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #e83e8c;
}

.user-preferences-content .preference-categories {
  margin-bottom: 16px;
}

.user-preferences-content .preference-group {
  margin-bottom: 12px;
}

.user-preferences-content .group-title {
  font-size: 12px;
  font-weight: bold;
  color: #e83e8c;
  margin-bottom: 8px;
}

.user-preferences-content .preference-items {
  background: rgba(232, 62, 140, 0.1);
  border: 1px solid rgba(232, 62, 140, 0.3);
  border-radius: 6px;
  padding: 8px;
}

.user-preferences-content .preference-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 11px;
}

.user-preferences-content .item-label {
  color: rgba(255, 255, 255, 0.8);
}

.user-preferences-content .item-value {
  color: #e83e8c;
  font-weight: 500;
}

.user-preferences-content .preferences-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.user-preferences-content .preference-btn {
  padding: 8px 16px;
  background: rgba(232, 62, 140, 0.8);
  border: 1px solid rgba(232, 62, 140, 0.3);
  border-radius: 6px;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-preferences-content .preference-btn:hover {
  background: rgba(232, 62, 140, 0.9);
  transform: translateY(-1px);
}

.privacy-settings-content .privacy-title {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #20c997;
}

.privacy-settings-content .privacy-items {
  margin-bottom: 16px;
}

.privacy-settings-content .privacy-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background: rgba(32, 201, 151, 0.1);
  border: 1px solid rgba(32, 201, 151, 0.3);
  border-radius: 6px;
  margin-bottom: 6px;
}

.privacy-settings-content .privacy-item:last-child {
  margin-bottom: 0;
}

.privacy-settings-content .privacy-icon {
  font-size: 20px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.privacy-settings-content .privacy-info {
  flex: 1;
}

.privacy-settings-content .privacy-label {
  font-size: 12px;
  font-weight: bold;
  color: white;
  margin-bottom: 2px;
}

.privacy-settings-content .privacy-desc {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.7);
}

.privacy-settings-content .privacy-toggle {
  font-size: 14px;
  color: #4caf50;
  font-weight: bold;
  cursor: pointer;
}

.privacy-settings-content .privacy-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.privacy-settings-content .privacy-btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.privacy-settings-content .privacy-btn.primary {
  background: rgba(32, 201, 151, 0.8);
  border-color: rgba(32, 201, 151, 0.3);
  color: white;
}

.privacy-settings-content .privacy-btn.primary:hover {
  background: rgba(32, 201, 151, 0.9);
  transform: translateY(-1px);
}

.privacy-settings-content .privacy-btn.secondary {
  background: rgba(108, 117, 125, 0.8);
  border-color: rgba(108, 117, 125, 0.3);
  color: white;
}

.privacy-settings-content .privacy-btn.secondary:hover {
  background: rgba(108, 117, 125, 0.9);
  transform: translateY(-1px);
}

/* 用户切换模式主题覆盖 */
.card-userSwitchMode {
  background: rgba(111, 66, 193, 0.1) !important;
  border: 1px solid rgba(111, 66, 193, 0.3) !important;
}

.card-userSwitchMode .glass-card {
  background: rgba(111, 66, 193, 0.2) !important;
  backdrop-filter: blur(8px) !important;
}

/* 疲劳检测模式主题覆盖 */
.card-fatigueMode {
  background: rgba(255, 193, 7, 0.1) !important;
  border: 1px solid rgba(255, 193, 7, 0.3) !important;
}

.card-fatigueMode .glass-card {
  background: rgba(255, 140, 0, 0.2) !important;
  backdrop-filter: blur(8px) !important;
}

/* 紧急模式主题覆盖 */
.card-emergencyMode {
  background: rgba(220, 53, 69, 0.1) !important;
  border: 1px solid rgba(220, 53, 69, 0.3) !important;
}

.card-emergencyMode .glass-card {
  background: rgba(139, 0, 0, 0.2) !important;
  backdrop-filter: blur(8px) !important;
}

.nearby-shops-content .shop-btn:hover {
  background: rgba(40, 167, 69, 0.9);
}

/* 智能泊车模式样式 */
.parking-search-content .parking-title {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #6f42c1;
}

.parking-search-content .parking-spots {
  margin-bottom: 16px;
}

.parking-search-content .parking-spot {
  padding: 12px;
  margin-bottom: 8px;
  background: rgba(111, 66, 193, 0.1);
  border: 1px solid rgba(111, 66, 193, 0.3);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.parking-search-content .parking-spot:hover {
  background: rgba(111, 66, 193, 0.2);
  transform: translateY(-1px);
}

.parking-search-content .parking-spot.recommended {
  border-color: rgba(111, 66, 193, 0.6);
  background: rgba(111, 66, 193, 0.2);
}

.parking-search-content .spot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.parking-search-content .spot-id {
  font-size: 14px;
  font-weight: bold;
  color: white;
}

.parking-search-content .spot-recommend {
  font-size: 10px;
  background: rgba(111, 66, 193, 0.8);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
}

.parking-search-content .spot-alternative {
  font-size: 10px;
  background: rgba(108, 117, 125, 0.6);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
}

.parking-search-content .spot-details {
  margin-bottom: 8px;
}

.parking-search-content .spot-distance,
.parking-search-content .spot-size,
.parking-search-content .spot-type {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2px;
}

.parking-search-content .spot-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
}

.parking-search-content .status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.parking-search-content .status-indicator.available {
  background: #28a745;
}

.parking-search-content .status-indicator.limited {
  background: #ffc107;
}

.parking-search-content .parking-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.parking-search-content .parking-btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.parking-search-content .parking-btn.primary {
  background: rgba(111, 66, 193, 0.8);
  border-color: rgba(111, 66, 193, 0.3);
  color: white;
}

.parking-search-content .parking-btn.primary:hover {
  background: rgba(111, 66, 193, 0.9);
  transform: translateY(-1px);
}

.parking-search-content .parking-btn.secondary {
  background: rgba(108, 117, 125, 0.8);
  border-color: rgba(108, 117, 125, 0.3);
  color: white;
}

.parking-search-content .parking-btn.secondary:hover {
  background: rgba(108, 117, 125, 0.9);
  transform: translateY(-1px);
}

.parking-assist-content .assist-title {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #17a2b8;
}

.parking-assist-content .assist-modes {
  margin-bottom: 16px;
}

.parking-assist-content .assist-mode {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  margin-bottom: 8px;
  background: rgba(23, 162, 184, 0.1);
  border: 1px solid rgba(23, 162, 184, 0.3);
  border-radius: 8px;
}

.parking-assist-content .mode-icon {
  font-size: 20px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.parking-assist-content .mode-info {
  flex: 1;
}

.parking-assist-content .mode-name {
  font-size: 12px;
  font-weight: bold;
  color: white;
  margin-bottom: 2px;
}

.parking-assist-content .mode-desc {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.7);
}

.parking-assist-content .mode-btn {
  padding: 6px 12px;
  background: rgba(23, 162, 184, 0.8);
  border: 1px solid rgba(23, 162, 184, 0.3);
  border-radius: 4px;
  color: white;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.parking-assist-content .mode-btn:hover {
  background: rgba(23, 162, 184, 0.9);
  transform: translateY(-1px);
}

.parking-assist-content .camera-view {
  background: rgba(23, 162, 184, 0.1);
  border: 1px solid rgba(23, 162, 184, 0.3);
  border-radius: 8px;
  padding: 8px;
}

.parking-assist-content .view-title {
  font-size: 12px;
  font-weight: bold;
  color: #17a2b8;
  margin-bottom: 8px;
  text-align: center;
}

.parking-assist-content .camera-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 6px;
  margin-bottom: 8px;
}

.parking-assist-content .camera-btn {
  padding: 8px;
  background: rgba(23, 162, 184, 0.2);
  border: 1px solid rgba(23, 162, 184, 0.4);
  border-radius: 4px;
  color: #17a2b8;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.parking-assist-content .camera-btn:hover {
  background: rgba(23, 162, 184, 0.3);
  transform: translateY(-1px);
}

.parking-assist-content .view-status {
  font-size: 10px;
}

.parking-assist-content .status-item {
  color: #4caf50;
  margin-bottom: 2px;
}

.cost-info-content .cost-title {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #28a745;
}

.cost-info-content .pricing-options {
  margin-bottom: 16px;
}

.cost-info-content .pricing-option {
  padding: 12px;
  margin-bottom: 8px;
  background: rgba(40, 167, 69, 0.1);
  border: 1px solid rgba(40, 167, 69, 0.3);
  border-radius: 8px;
}

.cost-info-content .pricing-option.recommended {
  border-color: rgba(40, 167, 69, 0.6);
  background: rgba(40, 167, 69, 0.2);
}

.cost-info-content .option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.cost-info-content .option-duration {
  font-size: 14px;
  font-weight: bold;
  color: white;
}

.cost-info-content .option-price {
  font-size: 14px;
  font-weight: bold;
  color: #28a745;
}

.cost-info-content .option-rate {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 6px;
}

.cost-info-content .option-details {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
}

.cost-info-content .detail-item {
  margin-bottom: 2px;
}

.cost-info-content .payment-section {
  margin-bottom: 12px;
}

.cost-info-content .payment-title {
  font-size: 12px;
  font-weight: bold;
  color: #28a745;
  margin-bottom: 8px;
}

.cost-info-content .payment-methods {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.cost-info-content .payment-btn {
  padding: 6px 12px;
  background: rgba(40, 167, 69, 0.8);
  border: 1px solid rgba(40, 167, 69, 0.3);
  border-radius: 4px;
  color: white;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cost-info-content .payment-btn:hover {
  background: rgba(40, 167, 69, 0.9);
  transform: translateY(-1px);
}

.cost-info-content .discount-section {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 6px;
  padding: 8px;
}

.cost-info-content .discount-title {
  font-size: 12px;
  font-weight: bold;
  color: #ffc107;
  margin-bottom: 8px;
}

.cost-info-content .discount-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.cost-info-content .discount-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cost-info-content .discount-name {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.9);
}

.cost-info-content .discount-use {
  padding: 4px 8px;
  background: rgba(255, 193, 7, 0.8);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 4px;
  color: #212529;
  font-size: 9px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cost-info-content .discount-use:hover {
  background: rgba(255, 193, 7, 0.9);
  transform: translateY(-1px);
}

/* 智能泊车模式主题覆盖 */
.card-parkingMode {
  background: rgba(111, 66, 193, 0.1) !important;
  border: 1px solid rgba(111, 66, 193, 0.3) !important;
}

.card-parkingMode .glass-card {
  background: rgba(111, 66, 193, 0.2) !important;
  backdrop-filter: blur(8px) !important;
}
</style>
