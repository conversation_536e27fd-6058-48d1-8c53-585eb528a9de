{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass, withModifiers as _withModifiers, vShow as _vShow, withDirectives as _withDirectives, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  key: 0,\n  class: \"current-orders\"\n};\nconst _hoisted_2 = {\n  class: \"orders-header\"\n};\nconst _hoisted_3 = {\n  class: \"orders-count\"\n};\nconst _hoisted_4 = {\n  class: \"orders-list\"\n};\nconst _hoisted_5 = [\"onClick\"];\nconst _hoisted_6 = {\n  class: \"order-icon\"\n};\nconst _hoisted_7 = {\n  class: \"order-info\"\n};\nconst _hoisted_8 = {\n  class: \"order-title\"\n};\nconst _hoisted_9 = {\n  class: \"order-subtitle\"\n};\nconst _hoisted_10 = {\n  class: \"order-status-text\"\n};\nconst _hoisted_11 = {\n  class: \"order-meta\"\n};\nconst _hoisted_12 = {\n  class: \"order-time\"\n};\nconst _hoisted_13 = {\n  key: 0,\n  class: \"order-price\"\n};\nconst _hoisted_14 = {\n  class: \"order-actions\"\n};\nconst _hoisted_15 = [\"onClick\"];\nconst _hoisted_16 = [\"onClick\"];\nconst _hoisted_17 = {\n  key: 1,\n  class: \"ai-recommendations\"\n};\nconst _hoisted_18 = {\n  class: \"recommendations-header\"\n};\nconst _hoisted_19 = {\n  class: \"recommendation-reason\"\n};\nconst _hoisted_20 = {\n  class: \"recommendations-list\"\n};\nconst _hoisted_21 = [\"onClick\"];\nconst _hoisted_22 = {\n  class: \"recommendation-image\"\n};\nconst _hoisted_23 = [\"src\", \"alt\"];\nconst _hoisted_24 = {\n  key: 0,\n  class: \"recommendation-badge\"\n};\nconst _hoisted_25 = {\n  class: \"recommendation-content\"\n};\nconst _hoisted_26 = {\n  class: \"recommendation-title\"\n};\nconst _hoisted_27 = {\n  class: \"recommendation-description\"\n};\nconst _hoisted_28 = {\n  class: \"recommendation-details\"\n};\nconst _hoisted_29 = {\n  class: \"detail-item\"\n};\nconst _hoisted_30 = {\n  class: \"detail-item\"\n};\nconst _hoisted_31 = {\n  class: \"detail-item\"\n};\nconst _hoisted_32 = {\n  class: \"recommendation-price\"\n};\nconst _hoisted_33 = {\n  class: \"current-price\"\n};\nconst _hoisted_34 = {\n  key: 0,\n  class: \"original-price\"\n};\nconst _hoisted_35 = {\n  key: 1,\n  class: \"discount\"\n};\nconst _hoisted_36 = {\n  class: \"recommendation-actions\"\n};\nconst _hoisted_37 = [\"onClick\", \"disabled\"];\nconst _hoisted_38 = {\n  key: 2,\n  class: \"quick-orders\"\n};\nconst _hoisted_39 = {\n  class: \"quick-orders-header\"\n};\nconst _hoisted_40 = {\n  class: \"quick-orders-grid\"\n};\nconst _hoisted_41 = [\"onClick\", \"disabled\"];\nconst _hoisted_42 = {\n  class: \"quick-order-icon\"\n};\nconst _hoisted_43 = {\n  class: \"quick-order-label\"\n};\nconst _hoisted_44 = {\n  class: \"quick-order-subtitle\"\n};\nconst _hoisted_45 = {\n  key: 3,\n  class: \"order-history\"\n};\nconst _hoisted_46 = {\n  class: \"history-header\"\n};\nconst _hoisted_47 = {\n  class: \"history-list\"\n};\nconst _hoisted_48 = [\"onClick\"];\nconst _hoisted_49 = {\n  class: \"history-icon\"\n};\nconst _hoisted_50 = {\n  class: \"history-info\"\n};\nconst _hoisted_51 = {\n  class: \"history-title\"\n};\nconst _hoisted_52 = {\n  class: \"history-date\"\n};\nconst _hoisted_53 = {\n  class: \"history-actions\"\n};\nconst _hoisted_54 = [\"onClick\", \"disabled\"];\nconst _hoisted_55 = {\n  key: 4,\n  class: \"promotions\"\n};\nconst _hoisted_56 = {\n  class: \"promotions-list\"\n};\nconst _hoisted_57 = [\"onClick\"];\nconst _hoisted_58 = {\n  class: \"promotion-icon\"\n};\nconst _hoisted_59 = {\n  class: \"promotion-content\"\n};\nconst _hoisted_60 = {\n  class: \"promotion-title\"\n};\nconst _hoisted_61 = {\n  class: \"promotion-description\"\n};\nconst _hoisted_62 = {\n  class: \"promotion-validity\"\n};\nconst _hoisted_63 = {\n  class: \"promotion-value\"\n};\nconst _hoisted_64 = {\n  class: \"value-text\"\n};\nconst _hoisted_65 = {\n  class: \"value-type\"\n};\nconst _hoisted_66 = {\n  key: 5,\n  class: \"smart-reminders\"\n};\nconst _hoisted_67 = {\n  class: \"reminders-list\"\n};\nconst _hoisted_68 = {\n  class: \"reminder-icon\"\n};\nconst _hoisted_69 = {\n  class: \"reminder-content\"\n};\nconst _hoisted_70 = {\n  class: \"reminder-text\"\n};\nconst _hoisted_71 = {\n  class: \"reminder-time\"\n};\nconst _hoisted_72 = {\n  class: \"reminder-actions\"\n};\nconst _hoisted_73 = [\"onClick\"];\nconst _hoisted_74 = [\"onClick\"];\nconst _hoisted_75 = {\n  key: 6,\n  class: \"no-orders\"\n};\nconst _hoisted_76 = {\n  class: \"start-ordering\"\n};\nconst _hoisted_77 = {\n  key: 7,\n  class: \"loading-overlay\"\n};\nconst _hoisted_78 = {\n  class: \"loading-text\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_BaseCard = _resolveComponent(\"BaseCard\");\n  return _openBlock(), _createBlock(_component_BaseCard, {\n    \"card-type\": 'ai-order',\n    size: $props.size,\n    position: $props.position,\n    theme: $props.theme,\n    \"theme-colors\": $props.themeColors,\n    \"show-header\": true,\n    \"show-footer\": false,\n    title: $setup.cardTitle,\n    icon: 'fas fa-shopping-cart',\n    class: \"ai-order-card\"\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"order-container\", [`size-${$props.size}`, `mode-${$props.displayMode}`]])\n    }, [_createCommentVNode(\" 当前订单状态 \"), $setup.currentOrders.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[3] || (_cache[3] = _createElementVNode(\"h3\", null, \"进行中的订单\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, _toDisplayString($setup.currentOrders.length) + \"个\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_4, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.currentOrders, order => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: order.id,\n        class: _normalizeClass(['order-item', `status-${order.status}`]),\n        onClick: $event => $setup.viewOrderDetails(order)\n      }, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"i\", {\n        class: _normalizeClass($setup.getOrderIcon(order.type))\n      }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, _toDisplayString(order.title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_9, _toDisplayString(order.subtitle), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_10, _toDisplayString($setup.getStatusText(order.status)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, _toDisplayString(order.estimatedTime), 1 /* TEXT */), order.price ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, \"¥\" + _toDisplayString(order.price), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_14, [order.trackable ? (_openBlock(), _createElementBlock(\"button\", {\n        key: 0,\n        onClick: _withModifiers($event => $setup.trackOrder(order), [\"stop\"]),\n        class: \"track-btn\"\n      }, [...(_cache[4] || (_cache[4] = [_createElementVNode(\"i\", {\n        class: \"fas fa-map-marker-alt\"\n      }, null, -1 /* CACHED */)]))], 8 /* PROPS */, _hoisted_15)) : _createCommentVNode(\"v-if\", true), order.contactable ? (_openBlock(), _createElementBlock(\"button\", {\n        key: 1,\n        onClick: _withModifiers($event => $setup.contactMerchant(order), [\"stop\"]),\n        class: \"contact-btn\"\n      }, [...(_cache[5] || (_cache[5] = [_createElementVNode(\"i\", {\n        class: \"fas fa-phone\"\n      }, null, -1 /* CACHED */)]))], 8 /* PROPS */, _hoisted_16)) : _createCommentVNode(\"v-if\", true)])], 10 /* CLASS, PROPS */, _hoisted_5);\n    }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" AI推荐订单 \"), $setup.aiRecommendations.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n      class: \"header-content\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-magic\"\n    }), _createElementVNode(\"span\", null, \"AI智能推荐\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_19, _toDisplayString($setup.recommendationReason), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_20, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.aiRecommendations, recommendation => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: recommendation.id,\n        class: \"recommendation-item\",\n        onClick: $event => $setup.viewRecommendation(recommendation)\n      }, [_createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"img\", {\n        src: recommendation.image,\n        alt: recommendation.title\n      }, null, 8 /* PROPS */, _hoisted_23), recommendation.badge ? (_openBlock(), _createElementBlock(\"div\", _hoisted_24, _toDisplayString(recommendation.badge), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"div\", _hoisted_26, _toDisplayString(recommendation.title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_27, _toDisplayString(recommendation.description), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"div\", _hoisted_29, [_cache[7] || (_cache[7] = _createElementVNode(\"i\", {\n        class: \"fas fa-star\"\n      }, null, -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString(recommendation.rating), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_30, [_cache[8] || (_cache[8] = _createElementVNode(\"i\", {\n        class: \"fas fa-clock\"\n      }, null, -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString(recommendation.deliveryTime), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_31, [_cache[9] || (_cache[9] = _createElementVNode(\"i\", {\n        class: \"fas fa-shipping-fast\"\n      }, null, -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString(recommendation.shippingFee), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_32, [_createElementVNode(\"span\", _hoisted_33, \"¥\" + _toDisplayString(recommendation.price), 1 /* TEXT */), recommendation.originalPrice ? (_openBlock(), _createElementBlock(\"span\", _hoisted_34, \" ¥\" + _toDisplayString(recommendation.originalPrice), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), recommendation.discount ? (_openBlock(), _createElementBlock(\"span\", _hoisted_35, _toDisplayString(recommendation.discount) + \"折 \", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"button\", {\n        onClick: _withModifiers($event => $setup.quickOrder(recommendation), [\"stop\"]),\n        class: \"quick-order-btn\",\n        disabled: $setup.isProcessing\n      }, [...(_cache[10] || (_cache[10] = [_createElementVNode(\"i\", {\n        class: \"fas fa-plus\"\n      }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"快速下单\", -1 /* CACHED */)]))], 8 /* PROPS */, _hoisted_37)])], 8 /* PROPS */, _hoisted_21);\n    }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 快速订单 \"), $props.showQuickOrders ? (_openBlock(), _createElementBlock(\"div\", _hoisted_38, [_createElementVNode(\"div\", _hoisted_39, [_cache[11] || (_cache[11] = _createElementVNode(\"h3\", null, \"快速订单\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n      onClick: _cache[0] || (_cache[0] = (...args) => $setup.toggleQuickOrdersExpanded && $setup.toggleQuickOrdersExpanded(...args)),\n      class: \"expand-btn\"\n    }, [_createElementVNode(\"i\", {\n      class: _normalizeClass($setup.quickOrdersExpanded ? 'fas fa-chevron-up' : 'fas fa-chevron-down')\n    }, null, 2 /* CLASS */)])]), _withDirectives(_createElementVNode(\"div\", _hoisted_40, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.quickOrderTypes, quickOrder => {\n      return _openBlock(), _createElementBlock(\"button\", {\n        key: quickOrder.id,\n        onClick: $event => $setup.startQuickOrder(quickOrder),\n        class: \"quick-order-type\",\n        disabled: $setup.isProcessing\n      }, [_createElementVNode(\"div\", _hoisted_42, [_createElementVNode(\"i\", {\n        class: _normalizeClass(quickOrder.icon)\n      }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_43, _toDisplayString(quickOrder.label), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_44, _toDisplayString(quickOrder.subtitle), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_41);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vShow, $setup.quickOrdersExpanded]])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 订单历史 \"), $props.showOrderHistory && $setup.recentOrders.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_45, [_createElementVNode(\"div\", _hoisted_46, [_cache[12] || (_cache[12] = _createElementVNode(\"h3\", null, \"最近订单\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n      onClick: _cache[1] || (_cache[1] = (...args) => $setup.viewAllOrders && $setup.viewAllOrders(...args)),\n      class: \"view-all-btn\"\n    }, \" 查看全部 \")]), _createElementVNode(\"div\", _hoisted_47, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.recentOrders.slice(0, 3), order => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: order.id,\n        class: \"history-item\",\n        onClick: $event => $setup.reorderItem(order)\n      }, [_createElementVNode(\"div\", _hoisted_49, [_createElementVNode(\"i\", {\n        class: _normalizeClass($setup.getOrderIcon(order.type))\n      }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_50, [_createElementVNode(\"div\", _hoisted_51, _toDisplayString(order.title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_52, _toDisplayString($setup.formatDate(order.date)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_53, [_createElementVNode(\"button\", {\n        onClick: _withModifiers($event => $setup.reorderItem(order), [\"stop\"]),\n        class: \"reorder-btn\",\n        disabled: $setup.isProcessing\n      }, [...(_cache[13] || (_cache[13] = [_createElementVNode(\"i\", {\n        class: \"fas fa-redo\"\n      }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"再来一单\", -1 /* CACHED */)]))], 8 /* PROPS */, _hoisted_54)])], 8 /* PROPS */, _hoisted_48);\n    }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 优惠券和活动 \"), $setup.promotions.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_55, [_cache[14] || (_cache[14] = _createElementVNode(\"div\", {\n      class: \"promotions-header\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-gift\"\n    }), _createElementVNode(\"span\", null, \"优惠活动\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_56, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.promotions, promotion => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: promotion.id,\n        class: _normalizeClass(['promotion-item', `type-${promotion.type}`]),\n        onClick: $event => $setup.usePromotion(promotion)\n      }, [_createElementVNode(\"div\", _hoisted_58, [_createElementVNode(\"i\", {\n        class: _normalizeClass(promotion.icon)\n      }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_59, [_createElementVNode(\"div\", _hoisted_60, _toDisplayString(promotion.title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_61, _toDisplayString(promotion.description), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_62, _toDisplayString(promotion.validity), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_63, [_createElementVNode(\"div\", _hoisted_64, _toDisplayString(promotion.value), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_65, _toDisplayString(promotion.valueType), 1 /* TEXT */)])], 10 /* CLASS, PROPS */, _hoisted_57);\n    }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 智能提醒 \"), $setup.smartReminders.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_66, [_cache[16] || (_cache[16] = _createElementVNode(\"div\", {\n      class: \"reminders-header\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-bell\"\n    }), _createElementVNode(\"span\", null, \"智能提醒\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_67, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.smartReminders, reminder => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: reminder.id,\n        class: _normalizeClass(['reminder-item', `priority-${reminder.priority}`])\n      }, [_createElementVNode(\"div\", _hoisted_68, [_createElementVNode(\"i\", {\n        class: _normalizeClass(reminder.icon)\n      }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_69, [_createElementVNode(\"div\", _hoisted_70, _toDisplayString(reminder.text), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_71, _toDisplayString(reminder.time), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_72, [reminder.actionable ? (_openBlock(), _createElementBlock(\"button\", {\n        key: 0,\n        onClick: $event => $setup.handleReminderAction(reminder),\n        class: \"reminder-action-btn\"\n      }, _toDisplayString(reminder.actionText), 9 /* TEXT, PROPS */, _hoisted_73)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"button\", {\n        onClick: $event => $setup.dismissReminder(reminder),\n        class: \"dismiss-btn\"\n      }, [...(_cache[15] || (_cache[15] = [_createElementVNode(\"i\", {\n        class: \"fas fa-times\"\n      }, null, -1 /* CACHED */)]))], 8 /* PROPS */, _hoisted_74)])], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 无订单状态 \"), !$setup.hasAnyContent ? (_openBlock(), _createElementBlock(\"div\", _hoisted_75, [_cache[18] || (_cache[18] = _createElementVNode(\"div\", {\n      class: \"no-orders-icon\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-shopping-bag\"\n    })], -1 /* CACHED */)), _cache[19] || (_cache[19] = _createElementVNode(\"div\", {\n      class: \"no-orders-text\"\n    }, [_createElementVNode(\"h3\", null, \"暂无订单\"), _createElementVNode(\"p\", null, \"开始您的第一个订单吧\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_76, [_createElementVNode(\"button\", {\n      onClick: _cache[2] || (_cache[2] = (...args) => $setup.startBrowsing && $setup.startBrowsing(...args)),\n      class: \"start-browse-btn\"\n    }, _cache[17] || (_cache[17] = [_createElementVNode(\"i\", {\n      class: \"fas fa-search\"\n    }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"开始浏览\", -1 /* CACHED */)]))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 加载状态 \"), $setup.isLoading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_77, [_cache[20] || (_cache[20] = _createElementVNode(\"div\", {\n      class: \"loading-spinner\"\n    }, null, -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_78, _toDisplayString($setup.loadingText), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"size\", \"position\", \"theme\", \"theme-colors\", \"title\"]);\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_component_BaseCard", "size", "$props", "position", "theme", "themeColors", "title", "$setup", "cardTitle", "icon", "_createElementVNode", "_normalizeClass", "displayMode", "_createCommentVNode", "currentOrders", "length", "_createElementBlock", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_toDisplayString", "_hoisted_4", "_Fragment", "_renderList", "order", "key", "id", "status", "onClick", "$event", "viewOrderDetails", "_hoisted_6", "getOrderIcon", "type", "_hoisted_7", "_hoisted_8", "_hoisted_9", "subtitle", "_hoisted_10", "getStatusText", "_hoisted_11", "_hoisted_12", "estimatedTime", "price", "_hoisted_13", "_hoisted_14", "trackable", "_withModifiers", "trackOrder", "contactable", "contactMerchant", "aiRecommendations", "_hoisted_17", "_hoisted_18", "_hoisted_19", "recommendationReason", "_hoisted_20", "recommendation", "viewRecommendation", "_hoisted_22", "src", "image", "alt", "badge", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "description", "_hoisted_28", "_hoisted_29", "rating", "_hoisted_30", "deliveryTime", "_hoisted_31", "shippingFee", "_hoisted_32", "_hoisted_33", "originalPrice", "_hoisted_34", "discount", "_hoisted_35", "_hoisted_36", "quickOrder", "disabled", "isProcessing", "showQuickOrders", "_hoisted_38", "_hoisted_39", "_cache", "args", "toggleQuickOrdersExpanded", "quickOrdersExpanded", "_hoisted_40", "quickOrderTypes", "startQuickOrder", "_hoisted_42", "_hoisted_43", "label", "_hoisted_44", "showOrderHistory", "recentOrders", "_hoisted_45", "_hoisted_46", "viewAllOrders", "_hoisted_47", "slice", "reorderItem", "_hoisted_49", "_hoisted_50", "_hoisted_51", "_hoisted_52", "formatDate", "date", "_hoisted_53", "promotions", "_hoisted_55", "_hoisted_56", "promotion", "usePromotion", "_hoisted_58", "_hoisted_59", "_hoisted_60", "_hoisted_61", "_hoisted_62", "validity", "_hoisted_63", "_hoisted_64", "value", "_hoisted_65", "valueType", "smartReminders", "_hoisted_66", "_hoisted_67", "reminder", "priority", "_hoisted_68", "_hoisted_69", "_hoisted_70", "text", "_hoisted_71", "time", "_hoisted_72", "actionable", "handleReminderAction", "actionText", "_hoisted_73", "dismiss<PERSON><PERSON><PERSON>", "has<PERSON>ny<PERSON><PERSON>nt", "_hoisted_75", "_hoisted_76", "startBrowsing", "isLoading", "_hoisted_77", "_hoisted_78", "loadingText"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\AIOrderCard.vue"], "sourcesContent": ["<template>\n  <BaseCard\n    :card-type=\"'ai-order'\"\n    :size=\"size\"\n    :position=\"position\"\n    :theme=\"theme\"\n    :theme-colors=\"themeColors\"\n    :show-header=\"true\"\n    :show-footer=\"false\"\n    :title=\"cardTitle\"\n    :icon=\"'fas fa-shopping-cart'\"\n    class=\"ai-order-card\"\n  >\n    <div class=\"order-container\" :class=\"[`size-${size}`, `mode-${displayMode}`]\">\n      <!-- 当前订单状态 -->\n      <div class=\"current-orders\" v-if=\"currentOrders.length > 0\">\n        <div class=\"orders-header\">\n          <h3>进行中的订单</h3>\n          <div class=\"orders-count\">{{ currentOrders.length }}个</div>\n        </div>\n        \n        <div class=\"orders-list\">\n          <div \n            v-for=\"order in currentOrders\" \n            :key=\"order.id\"\n            :class=\"['order-item', `status-${order.status}`]\"\n            @click=\"viewOrderDetails(order)\"\n          >\n            <div class=\"order-icon\">\n              <i :class=\"getOrderIcon(order.type)\"></i>\n            </div>\n            \n            <div class=\"order-info\">\n              <div class=\"order-title\">{{ order.title }}</div>\n              <div class=\"order-subtitle\">{{ order.subtitle }}</div>\n              <div class=\"order-status-text\">{{ getStatusText(order.status) }}</div>\n            </div>\n            \n            <div class=\"order-meta\">\n              <div class=\"order-time\">{{ order.estimatedTime }}</div>\n              <div class=\"order-price\" v-if=\"order.price\">¥{{ order.price }}</div>\n            </div>\n            \n            <div class=\"order-actions\">\n              <button \n                v-if=\"order.trackable\"\n                @click.stop=\"trackOrder(order)\"\n                class=\"track-btn\"\n              >\n                <i class=\"fas fa-map-marker-alt\"></i>\n              </button>\n              <button \n                v-if=\"order.contactable\"\n                @click.stop=\"contactMerchant(order)\"\n                class=\"contact-btn\"\n              >\n                <i class=\"fas fa-phone\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- AI推荐订单 -->\n      <div class=\"ai-recommendations\" v-if=\"aiRecommendations.length > 0\">\n        <div class=\"recommendations-header\">\n          <div class=\"header-content\">\n            <i class=\"fas fa-magic\"></i>\n            <span>AI智能推荐</span>\n          </div>\n          <div class=\"recommendation-reason\">{{ recommendationReason }}</div>\n        </div>\n        \n        <div class=\"recommendations-list\">\n          <div \n            v-for=\"recommendation in aiRecommendations\" \n            :key=\"recommendation.id\"\n            class=\"recommendation-item\"\n            @click=\"viewRecommendation(recommendation)\"\n          >\n            <div class=\"recommendation-image\">\n              <img :src=\"recommendation.image\" :alt=\"recommendation.title\" />\n              <div class=\"recommendation-badge\" v-if=\"recommendation.badge\">\n                {{ recommendation.badge }}\n              </div>\n            </div>\n            \n            <div class=\"recommendation-content\">\n              <div class=\"recommendation-title\">{{ recommendation.title }}</div>\n              <div class=\"recommendation-description\">{{ recommendation.description }}</div>\n              \n              <div class=\"recommendation-details\">\n                <div class=\"detail-item\">\n                  <i class=\"fas fa-star\"></i>\n                  <span>{{ recommendation.rating }}</span>\n                </div>\n                <div class=\"detail-item\">\n                  <i class=\"fas fa-clock\"></i>\n                  <span>{{ recommendation.deliveryTime }}</span>\n                </div>\n                <div class=\"detail-item\">\n                  <i class=\"fas fa-shipping-fast\"></i>\n                  <span>{{ recommendation.shippingFee }}</span>\n                </div>\n              </div>\n              \n              <div class=\"recommendation-price\">\n                <span class=\"current-price\">¥{{ recommendation.price }}</span>\n                <span class=\"original-price\" v-if=\"recommendation.originalPrice\">\n                  ¥{{ recommendation.originalPrice }}\n                </span>\n                <span class=\"discount\" v-if=\"recommendation.discount\">\n                  {{ recommendation.discount }}折\n                </span>\n              </div>\n            </div>\n            \n            <div class=\"recommendation-actions\">\n              <button \n                @click.stop=\"quickOrder(recommendation)\"\n                class=\"quick-order-btn\"\n                :disabled=\"isProcessing\"\n              >\n                <i class=\"fas fa-plus\"></i>\n                <span>快速下单</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 快速订单 -->\n      <div class=\"quick-orders\" v-if=\"showQuickOrders\">\n        <div class=\"quick-orders-header\">\n          <h3>快速订单</h3>\n          <button @click=\"toggleQuickOrdersExpanded\" class=\"expand-btn\">\n            <i :class=\"quickOrdersExpanded ? 'fas fa-chevron-up' : 'fas fa-chevron-down'\"></i>\n          </button>\n        </div>\n        \n        <div class=\"quick-orders-grid\" v-show=\"quickOrdersExpanded\">\n          <button \n            v-for=\"quickOrder in quickOrderTypes\" \n            :key=\"quickOrder.id\"\n            @click=\"startQuickOrder(quickOrder)\"\n            class=\"quick-order-type\"\n            :disabled=\"isProcessing\"\n          >\n            <div class=\"quick-order-icon\">\n              <i :class=\"quickOrder.icon\"></i>\n            </div>\n            <div class=\"quick-order-label\">{{ quickOrder.label }}</div>\n            <div class=\"quick-order-subtitle\">{{ quickOrder.subtitle }}</div>\n          </button>\n        </div>\n      </div>\n\n      <!-- 订单历史 -->\n      <div class=\"order-history\" v-if=\"showOrderHistory && recentOrders.length > 0\">\n        <div class=\"history-header\">\n          <h3>最近订单</h3>\n          <button @click=\"viewAllOrders\" class=\"view-all-btn\">\n            查看全部\n          </button>\n        </div>\n        \n        <div class=\"history-list\">\n          <div \n            v-for=\"order in recentOrders.slice(0, 3)\" \n            :key=\"order.id\"\n            class=\"history-item\"\n            @click=\"reorderItem(order)\"\n          >\n            <div class=\"history-icon\">\n              <i :class=\"getOrderIcon(order.type)\"></i>\n            </div>\n            \n            <div class=\"history-info\">\n              <div class=\"history-title\">{{ order.title }}</div>\n              <div class=\"history-date\">{{ formatDate(order.date) }}</div>\n            </div>\n            \n            <div class=\"history-actions\">\n              <button \n                @click.stop=\"reorderItem(order)\"\n                class=\"reorder-btn\"\n                :disabled=\"isProcessing\"\n              >\n                <i class=\"fas fa-redo\"></i>\n                <span>再来一单</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 优惠券和活动 -->\n      <div class=\"promotions\" v-if=\"promotions.length > 0\">\n        <div class=\"promotions-header\">\n          <i class=\"fas fa-gift\"></i>\n          <span>优惠活动</span>\n        </div>\n        \n        <div class=\"promotions-list\">\n          <div \n            v-for=\"promotion in promotions\" \n            :key=\"promotion.id\"\n            :class=\"['promotion-item', `type-${promotion.type}`]\"\n            @click=\"usePromotion(promotion)\"\n          >\n            <div class=\"promotion-icon\">\n              <i :class=\"promotion.icon\"></i>\n            </div>\n            \n            <div class=\"promotion-content\">\n              <div class=\"promotion-title\">{{ promotion.title }}</div>\n              <div class=\"promotion-description\">{{ promotion.description }}</div>\n              <div class=\"promotion-validity\">{{ promotion.validity }}</div>\n            </div>\n            \n            <div class=\"promotion-value\">\n              <div class=\"value-text\">{{ promotion.value }}</div>\n              <div class=\"value-type\">{{ promotion.valueType }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 智能提醒 -->\n      <div class=\"smart-reminders\" v-if=\"smartReminders.length > 0\">\n        <div class=\"reminders-header\">\n          <i class=\"fas fa-bell\"></i>\n          <span>智能提醒</span>\n        </div>\n        \n        <div class=\"reminders-list\">\n          <div \n            v-for=\"reminder in smartReminders\" \n            :key=\"reminder.id\"\n            :class=\"['reminder-item', `priority-${reminder.priority}`]\"\n          >\n            <div class=\"reminder-icon\">\n              <i :class=\"reminder.icon\"></i>\n            </div>\n            \n            <div class=\"reminder-content\">\n              <div class=\"reminder-text\">{{ reminder.text }}</div>\n              <div class=\"reminder-time\">{{ reminder.time }}</div>\n            </div>\n            \n            <div class=\"reminder-actions\">\n              <button \n                v-if=\"reminder.actionable\"\n                @click=\"handleReminderAction(reminder)\"\n                class=\"reminder-action-btn\"\n              >\n                {{ reminder.actionText }}\n              </button>\n              <button \n                @click=\"dismissReminder(reminder)\"\n                class=\"dismiss-btn\"\n              >\n                <i class=\"fas fa-times\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 无订单状态 -->\n      <div v-if=\"!hasAnyContent\" class=\"no-orders\">\n        <div class=\"no-orders-icon\">\n          <i class=\"fas fa-shopping-bag\"></i>\n        </div>\n        <div class=\"no-orders-text\">\n          <h3>暂无订单</h3>\n          <p>开始您的第一个订单吧</p>\n        </div>\n        <div class=\"start-ordering\">\n          <button @click=\"startBrowsing\" class=\"start-browse-btn\">\n            <i class=\"fas fa-search\"></i>\n            <span>开始浏览</span>\n          </button>\n        </div>\n      </div>\n\n      <!-- 加载状态 -->\n      <div v-if=\"isLoading\" class=\"loading-overlay\">\n        <div class=\"loading-spinner\"></div>\n        <div class=\"loading-text\">{{ loadingText }}</div>\n      </div>\n    </div>\n  </BaseCard>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, onUnmounted } from 'vue'\nimport BaseCard from '@/components/cards/BaseCard.vue'\nimport mockDataService from '@/services/MockDataService.js'\n\nexport default {\n  name: 'AIOrderCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    size: {\n      type: String,\n      default: 'large',\n      validator: (value) => ['small', 'medium', 'large'].includes(value)\n    },\n    position: {\n      type: Object,\n      default: () => ({ x: 0, y: 0 })\n    },\n    theme: {\n      type: String,\n      default: 'glassmorphism'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({})\n    },\n    displayMode: {\n      type: String,\n      default: 'full',\n      validator: (value) => ['compact', 'standard', 'full'].includes(value)\n    },\n    showQuickOrders: {\n      type: Boolean,\n      default: true\n    },\n    showOrderHistory: {\n      type: Boolean,\n      default: true\n    },\n    autoRefresh: {\n      type: Boolean,\n      default: true\n    },\n    refreshInterval: {\n      type: Number,\n      default: 60000 // 60秒\n    }\n  },\n  emits: ['order-placed', 'order-tracked', 'promotion-used', 'recommendation-viewed'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const isLoading = ref(false)\n    const isProcessing = ref(false)\n    const loadingText = ref('正在加载订单信息...')\n    const quickOrdersExpanded = ref(true)\n    \n    const currentOrders = ref([])\n    const aiRecommendations = ref([])\n    const recentOrders = ref([])\n    const promotions = ref([])\n    const smartReminders = ref([])\n    const recommendationReason = ref('')\n    \n    const quickOrderTypes = ref([\n      {\n        id: 1,\n        label: '外卖',\n        subtitle: '美食配送',\n        icon: 'fas fa-utensils',\n        type: 'food'\n      },\n      {\n        id: 2,\n        label: '购物',\n        subtitle: '日用百货',\n        icon: 'fas fa-shopping-cart',\n        type: 'shopping'\n      },\n      {\n        id: 3,\n        label: '打车',\n        subtitle: '出行服务',\n        icon: 'fas fa-car',\n        type: 'ride'\n      },\n      {\n        id: 4,\n        label: '快递',\n        subtitle: '同城配送',\n        icon: 'fas fa-shipping-fast',\n        type: 'delivery'\n      },\n      {\n        id: 5,\n        label: '服务',\n        subtitle: '生活服务',\n        icon: 'fas fa-concierge-bell',\n        type: 'service'\n      },\n      {\n        id: 6,\n        label: '更多',\n        subtitle: '全部分类',\n        icon: 'fas fa-th',\n        type: 'more'\n      }\n    ])\n\n    // 计算属性\n    const cardTitle = computed(() => {\n      const activeCount = currentOrders.value.length\n      if (activeCount > 0) {\n        return `订单管理 (${activeCount}个进行中)`\n      }\n      return 'AI订单助手'\n    })\n    \n    const hasAnyContent = computed(() => {\n      return currentOrders.value.length > 0 || \n             aiRecommendations.value.length > 0 || \n             recentOrders.value.length > 0 || \n             promotions.value.length > 0 || \n             smartReminders.value.length > 0\n    })\n\n    // 方法\n    const loadOrderData = async () => {\n      try {\n        isLoading.value = true\n        loadingText.value = '正在加载订单信息...'\n        \n        const orderData = await mockDataService.getOrderData()\n        \n        currentOrders.value = orderData.currentOrders || []\n        aiRecommendations.value = orderData.aiRecommendations || []\n        recentOrders.value = orderData.recentOrders || []\n        promotions.value = orderData.promotions || []\n        smartReminders.value = orderData.smartReminders || []\n        recommendationReason.value = orderData.recommendationReason || ''\n        \n      } catch (error) {\n        console.error('Failed to load order data:', error)\n      } finally {\n        isLoading.value = false\n      }\n    }\n    \n    const getOrderIcon = (type) => {\n      const icons = {\n        food: 'fas fa-utensils',\n        shopping: 'fas fa-shopping-bag',\n        ride: 'fas fa-car',\n        delivery: 'fas fa-shipping-fast',\n        service: 'fas fa-concierge-bell',\n        grocery: 'fas fa-apple-alt',\n        pharmacy: 'fas fa-pills',\n        gas: 'fas fa-gas-pump'\n      }\n      return icons[type] || 'fas fa-shopping-cart'\n    }\n    \n    const getStatusText = (status) => {\n      const statusTexts = {\n        pending: '待确认',\n        confirmed: '已确认',\n        preparing: '准备中',\n        shipping: '配送中',\n        delivered: '已送达',\n        completed: '已完成',\n        cancelled: '已取消'\n      }\n      return statusTexts[status] || '未知状态'\n    }\n    \n    const formatDate = (dateString) => {\n      const date = new Date(dateString)\n      const now = new Date()\n      const diffTime = Math.abs(now - date)\n      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n      \n      if (diffDays === 1) {\n        return '昨天'\n      } else if (diffDays <= 7) {\n        return `${diffDays}天前`\n      } else {\n        return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })\n      }\n    }\n    \n    const viewOrderDetails = (order) => {\n      console.log('Viewing order details:', order)\n      // 实际应用中会打开订单详情页面\n    }\n    \n    const trackOrder = (order) => {\n      console.log('Tracking order:', order)\n      // 实际应用中会打开订单跟踪页面\n    }\n    \n    const contactMerchant = (order) => {\n      console.log('Contacting merchant for order:', order)\n      // 实际应用中会打开联系商家功能\n    }\n    \n    const viewRecommendation = (recommendation) => {\n      console.log('Viewing recommendation:', recommendation)\n      emit('recommendation-viewed', recommendation)\n      // 实际应用中会打开商品详情页面\n    }\n    \n    const quickOrder = async (recommendation) => {\n      try {\n        isProcessing.value = true\n        \n        const orderResult = await mockDataService.placeQuickOrder(recommendation)\n        \n        // 添加到当前订单列表\n        currentOrders.value.unshift(orderResult)\n        \n        emit('order-placed', orderResult)\n        \n        // 重新加载数据以获取更新的推荐\n        await loadOrderData()\n        \n      } catch (error) {\n        console.error('Failed to place quick order:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n    \n    const toggleQuickOrdersExpanded = () => {\n      quickOrdersExpanded.value = !quickOrdersExpanded.value\n    }\n    \n    const startQuickOrder = (quickOrderType) => {\n      console.log('Starting quick order:', quickOrderType)\n      // 实际应用中会打开对应的订单页面\n    }\n    \n    const viewAllOrders = () => {\n      console.log('Viewing all orders')\n      // 实际应用中会打开订单历史页面\n    }\n    \n    const reorderItem = async (order) => {\n      try {\n        isProcessing.value = true\n        \n        const reorderResult = await mockDataService.reorderItem(order.id)\n        \n        // 添加到当前订单列表\n        currentOrders.value.unshift(reorderResult)\n        \n        emit('order-placed', reorderResult)\n        \n      } catch (error) {\n        console.error('Failed to reorder item:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n    \n    const usePromotion = (promotion) => {\n      console.log('Using promotion:', promotion)\n      emit('promotion-used', promotion)\n      // 实际应用中会应用优惠券\n    }\n    \n    const handleReminderAction = (reminder) => {\n      console.log('Handling reminder action:', reminder)\n      // 根据提醒类型执行相应操作\n    }\n    \n    const dismissReminder = (reminder) => {\n      const index = smartReminders.value.findIndex(r => r.id === reminder.id)\n      if (index > -1) {\n        smartReminders.value.splice(index, 1)\n      }\n    }\n    \n    const startBrowsing = () => {\n      console.log('Starting to browse')\n      // 实际应用中会打开商品浏览页面\n    }\n\n    // 生命周期\n    let refreshTimer = null\n    \n    onMounted(async () => {\n      await mockDataService.initialize()\n      await loadOrderData()\n      \n      // 设置自动刷新\n      if (props.autoRefresh) {\n        refreshTimer = setInterval(() => {\n          loadOrderData()\n        }, props.refreshInterval)\n      }\n    })\n\n    onUnmounted(() => {\n      if (refreshTimer) {\n        clearInterval(refreshTimer)\n      }\n    })\n\n    return {\n      // 响应式数据\n      isLoading,\n      isProcessing,\n      loadingText,\n      quickOrdersExpanded,\n      currentOrders,\n      aiRecommendations,\n      recentOrders,\n      promotions,\n      smartReminders,\n      recommendationReason,\n      quickOrderTypes,\n      \n      // 计算属性\n      cardTitle,\n      hasAnyContent,\n      \n      // 方法\n      getOrderIcon,\n      getStatusText,\n      formatDate,\n      viewOrderDetails,\n      trackOrder,\n      contactMerchant,\n      viewRecommendation,\n      quickOrder,\n      toggleQuickOrdersExpanded,\n      startQuickOrder,\n      viewAllOrders,\n      reorderItem,\n      usePromotion,\n      handleReminderAction,\n      dismissReminder,\n      startBrowsing\n    }\n  }\n}\n</script>\n\n<style scoped>\n.ai-order-card {\n  height: 100%;\n}\n\n.order-container {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  padding: 16px;\n  position: relative;\n  overflow-y: auto;\n}\n\n/* 当前订单 */\n.current-orders {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.orders-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.orders-header h3 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.orders-count {\n  background: rgba(74, 144, 226, 0.3);\n  color: #4a90e2;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.orders-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.order-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border-left: 3px solid transparent;\n}\n\n.order-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateX(2px);\n}\n\n.order-item.status-pending {\n  border-left-color: #f59e0b;\n}\n\n.order-item.status-confirmed,\n.order-item.status-preparing {\n  border-left-color: #4a90e2;\n}\n\n.order-item.status-shipping {\n  border-left-color: #8b5cf6;\n}\n\n.order-item.status-delivered,\n.order-item.status-completed {\n  border-left-color: #10b981;\n}\n\n.order-icon {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.8);\n  flex-shrink: 0;\n}\n\n.order-info {\n  flex: 1;\n}\n\n.order-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n}\n\n.order-subtitle {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n  margin-bottom: 2px;\n}\n\n.order-status-text {\n  font-size: 11px;\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.order-meta {\n  text-align: right;\n  margin-right: 8px;\n}\n\n.order-time {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.8);\n  margin-bottom: 2px;\n}\n\n.order-price {\n  font-size: 14px;\n  font-weight: 600;\n  color: #7ed321;\n}\n\n.order-actions {\n  display: flex;\n  gap: 4px;\n}\n\n.track-btn,\n.contact-btn {\n  width: 28px;\n  height: 28px;\n  border: none;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0.2);\n  color: rgba(255, 255, 255, 0.7);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n}\n\n.track-btn:hover {\n  background: rgba(74, 144, 226, 0.3);\n  color: #4a90e2;\n}\n\n.contact-btn:hover {\n  background: rgba(126, 211, 33, 0.3);\n  color: #7ed321;\n}\n\n/* AI推荐 */\n.ai-recommendations {\n  background: rgba(126, 211, 33, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.recommendations-header {\n  margin-bottom: 12px;\n}\n\n.header-content {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  color: #7ed321;\n  margin-bottom: 4px;\n}\n\n.recommendation-reason {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.recommendations-list {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.recommendation-item {\n  display: flex;\n  gap: 12px;\n  padding: 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.recommendation-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateY(-1px);\n}\n\n.recommendation-image {\n  width: 60px;\n  height: 60px;\n  border-radius: 8px;\n  overflow: hidden;\n  position: relative;\n  flex-shrink: 0;\n}\n\n.recommendation-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.recommendation-badge {\n  position: absolute;\n  top: 4px;\n  right: 4px;\n  background: #ef4444;\n  color: white;\n  font-size: 10px;\n  padding: 2px 4px;\n  border-radius: 4px;\n}\n\n.recommendation-content {\n  flex: 1;\n}\n\n.recommendation-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 4px;\n}\n\n.recommendation-description {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n  margin-bottom: 8px;\n}\n\n.recommendation-details {\n  display: flex;\n  gap: 12px;\n  margin-bottom: 8px;\n}\n\n.detail-item {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 11px;\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.recommendation-price {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.current-price {\n  font-size: 16px;\n  font-weight: 600;\n  color: #7ed321;\n}\n\n.original-price {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.5);\n  text-decoration: line-through;\n}\n\n.discount {\n  background: #ef4444;\n  color: white;\n  font-size: 10px;\n  padding: 2px 4px;\n  border-radius: 4px;\n}\n\n.recommendation-actions {\n  display: flex;\n  align-items: center;\n}\n\n.quick-order-btn {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 8px 12px;\n  border: none;\n  border-radius: 6px;\n  background: rgba(126, 211, 33, 0.3);\n  color: #7ed321;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.quick-order-btn:hover:not(:disabled) {\n  background: rgba(126, 211, 33, 0.5);\n  transform: scale(1.05);\n}\n\n.quick-order-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* 快速订单 */\n.quick-orders {\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.quick-orders-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.quick-orders-header h3 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.expand-btn {\n  width: 24px;\n  height: 24px;\n  border: none;\n  border-radius: 4px;\n  background: rgba(255, 255, 255, 0.2);\n  color: rgba(255, 255, 255, 0.7);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n}\n\n.expand-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n}\n\n.quick-orders-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 8px;\n}\n\n.quick-order-type {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 6px;\n  padding: 12px 8px;\n  border: none;\n  border-radius: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  color: rgba(255, 255, 255, 0.8);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-align: center;\n}\n\n.quick-order-type:hover:not(:disabled) {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateY(-2px);\n}\n\n.quick-order-type:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.quick-order-icon {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n}\n\n.quick-order-label {\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.quick-order-subtitle {\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n/* 订单历史 */\n.order-history {\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.history-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.history-header h3 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.view-all-btn {\n  background: none;\n  border: none;\n  color: #4a90e2;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.view-all-btn:hover {\n  color: #7ed321;\n}\n\n.history-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.history-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.history-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n}\n\n.history-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.7);\n  flex-shrink: 0;\n}\n\n.history-info {\n  flex: 1;\n}\n\n.history-title {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n}\n\n.history-date {\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.history-actions {\n  display: flex;\n}\n\n.reorder-btn {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  padding: 4px 8px;\n  border: none;\n  border-radius: 4px;\n  background: rgba(74, 144, 226, 0.2);\n  color: #4a90e2;\n  font-size: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.reorder-btn:hover:not(:disabled) {\n  background: rgba(74, 144, 226, 0.3);\n}\n\n.reorder-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* 优惠券和活动 */\n.promotions {\n  background: rgba(245, 158, 11, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.promotions-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  color: #f59e0b;\n  margin-bottom: 12px;\n}\n\n.promotions-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.promotion-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.promotion-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateX(2px);\n}\n\n.promotion-icon {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: rgba(245, 158, 11, 0.3);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  color: #f59e0b;\n  flex-shrink: 0;\n}\n\n.promotion-content {\n  flex: 1;\n}\n\n.promotion-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n}\n\n.promotion-description {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n  margin-bottom: 2px;\n}\n\n.promotion-validity {\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.5);\n}\n\n.promotion-value {\n  text-align: right;\n}\n\n.value-text {\n  font-size: 16px;\n  font-weight: 600;\n  color: #f59e0b;\n}\n\n.value-type {\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n/* 智能提醒 */\n.smart-reminders {\n  background: rgba(139, 92, 246, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.reminders-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  color: #8b5cf6;\n  margin-bottom: 12px;\n}\n\n.reminders-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.reminder-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n  border-left: 3px solid transparent;\n}\n\n.reminder-item.priority-high {\n  border-left-color: #ef4444;\n}\n\n.reminder-item.priority-medium {\n  border-left-color: #f59e0b;\n}\n\n.reminder-item.priority-low {\n  border-left-color: #10b981;\n}\n\n.reminder-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background: rgba(139, 92, 246, 0.3);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  color: #8b5cf6;\n  flex-shrink: 0;\n}\n\n.reminder-content {\n  flex: 1;\n}\n\n.reminder-text {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n}\n\n.reminder-time {\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.reminder-actions {\n  display: flex;\n  gap: 4px;\n}\n\n.reminder-action-btn {\n  padding: 4px 8px;\n  border: none;\n  border-radius: 4px;\n  background: rgba(139, 92, 246, 0.3);\n  color: #8b5cf6;\n  font-size: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.reminder-action-btn:hover {\n  background: rgba(139, 92, 246, 0.5);\n}\n\n.dismiss-btn {\n  width: 20px;\n  height: 20px;\n  border: none;\n  border-radius: 4px;\n  background: rgba(255, 255, 255, 0.2);\n  color: rgba(255, 255, 255, 0.6);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n}\n\n.dismiss-btn:hover {\n  background: rgba(239, 68, 68, 0.3);\n  color: #ef4444;\n}\n\n/* 无订单状态 */\n.no-orders {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  padding: 32px 16px;\n  flex: 1;\n}\n\n.no-orders-icon {\n  font-size: 48px;\n  color: rgba(255, 255, 255, 0.3);\n  margin-bottom: 16px;\n}\n\n.no-orders-text h3 {\n  margin: 0 0 8px 0;\n  font-size: 18px;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.no-orders-text p {\n  margin: 0 0 24px 0;\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.start-ordering {\n  width: 100%;\n  max-width: 200px;\n}\n\n.start-browse-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  width: 100%;\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  background: rgba(74, 144, 226, 0.3);\n  color: #4a90e2;\n  font-size: 14px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.start-browse-btn:hover {\n  background: rgba(74, 144, 226, 0.5);\n  transform: translateY(-2px);\n}\n\n/* 加载状态 */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.3);\n  backdrop-filter: blur(5px);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 12px;\n  border-radius: 12px;\n}\n\n.loading-spinner {\n  width: 32px;\n  height: 32px;\n  border: 3px solid rgba(255, 255, 255, 0.2);\n  border-top: 3px solid #4a90e2;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.loading-text {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* 动画 */\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* 响应式设计 */\n.size-small .order-container {\n  padding: 12px;\n  gap: 12px;\n}\n\n.size-small .quick-orders-grid {\n  grid-template-columns: repeat(2, 1fr);\n}\n\n.mode-compact .ai-recommendations,\n.mode-compact .order-history,\n.mode-compact .promotions,\n.mode-compact .smart-reminders {\n  display: none;\n}\n\n.mode-compact .quick-orders-grid {\n  grid-template-columns: repeat(4, 1fr);\n}\n</style>"], "mappings": ";;;EAeWA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAc;;EAGtBA,KAAK,EAAC;AAAa;;;EAOfA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAgB;;EACtBA,KAAK,EAAC;AAAmB;;EAG3BA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAY;;;EAClBA,KAAK,EAAC;;;EAGRA,KAAK,EAAC;AAAe;;;;;EAqB3BA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAwB;;EAK5BA,KAAK,EAAC;AAAuB;;EAG/BA,KAAK,EAAC;AAAsB;;;EAOxBA,KAAK,EAAC;AAAsB;;;;EAE1BA,KAAK,EAAC;;;EAKRA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAAsB;;EAC5BA,KAAK,EAAC;AAA4B;;EAElCA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAMrBA,KAAK,EAAC;AAAsB;;EACzBA,KAAK,EAAC;AAAe;;;EACrBA,KAAK,EAAC;;;;EAGNA,KAAK,EAAC;;;EAMXA,KAAK,EAAC;AAAwB;;;;EAepCA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAqB;;EAO3BA,KAAK,EAAC;AAAmB;;;EAQrBA,KAAK,EAAC;AAAkB;;EAGxBA,KAAK,EAAC;AAAmB;;EACzBA,KAAK,EAAC;AAAsB;;;EAMlCA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAgB;;EAOtBA,KAAK,EAAC;AAAc;;;EAOhBA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAe;;EACrBA,KAAK,EAAC;AAAc;;EAGtBA,KAAK,EAAC;AAAiB;;;;EAe7BA,KAAK,EAAC;;;EAMJA,KAAK,EAAC;AAAiB;;;EAOnBA,KAAK,EAAC;AAAgB;;EAItBA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAiB;;EACvBA,KAAK,EAAC;AAAuB;;EAC7BA,KAAK,EAAC;AAAoB;;EAG5BA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAY;;EAClBA,KAAK,EAAC;AAAY;;;EAO1BA,KAAK,EAAC;;;EAMJA,KAAK,EAAC;AAAgB;;EAMlBA,KAAK,EAAC;AAAe;;EAIrBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAe;;EACrBA,KAAK,EAAC;AAAe;;EAGvBA,KAAK,EAAC;AAAkB;;;;;EAoBRA,KAAK,EAAC;;;EAQ1BA,KAAK,EAAC;AAAgB;;;EASPA,KAAK,EAAC;;;EAErBA,KAAK,EAAC;AAAc;;;uBAhS/BC,YAAA,CAmSWC,mBAAA;IAlSR,WAAS,EAAE,UAAU;IACrBC,IAAI,EAAEC,MAAA,CAAAD,IAAI;IACVE,QAAQ,EAAED,MAAA,CAAAC,QAAQ;IAClBC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACZ,cAAY,EAAEF,MAAA,CAAAG,WAAW;IACzB,aAAW,EAAE,IAAI;IACjB,aAAW,EAAE,KAAK;IAClBC,KAAK,EAAEC,MAAA,CAAAC,SAAS;IAChBC,IAAI,EAAE,sBAAsB;IAC7BX,KAAK,EAAC;;sBAEN,MAsRM,CAtRNY,mBAAA,CAsRM;MAtRDZ,KAAK,EAAAa,eAAA,EAAC,iBAAiB,WAAkBT,MAAA,CAAAD,IAAI,YAAYC,MAAA,CAAAU,WAAW;QACvEC,mBAAA,YAAe,EACmBN,MAAA,CAAAO,aAAa,CAACC,MAAM,Q,cAAtDC,mBAAA,CA8CM,OA9CNC,UA8CM,GA7CJP,mBAAA,CAGM,OAHNQ,UAGM,G,0BAFJR,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAA2D,OAA3DS,UAA2D,EAAAC,gBAAA,CAA9Bb,MAAA,CAAAO,aAAa,CAACC,MAAM,IAAG,GAAC,gB,GAGvDL,mBAAA,CAuCM,OAvCNW,UAuCM,I,kBAtCJL,mBAAA,CAqCMM,SAAA,QAAAC,WAAA,CApCYhB,MAAA,CAAAO,aAAa,EAAtBU,KAAK;2BADdR,mBAAA,CAqCM;QAnCHS,GAAG,EAAED,KAAK,CAACE,EAAE;QACb5B,KAAK,EAAAa,eAAA,0BAA2Ba,KAAK,CAACG,MAAM;QAC5CC,OAAK,EAAAC,MAAA,IAAEtB,MAAA,CAAAuB,gBAAgB,CAACN,KAAK;UAE9Bd,mBAAA,CAEM,OAFNqB,UAEM,GADJrB,mBAAA,CAAyC;QAArCZ,KAAK,EAAAa,eAAA,CAAEJ,MAAA,CAAAyB,YAAY,CAACR,KAAK,CAACS,IAAI;iCAGpCvB,mBAAA,CAIM,OAJNwB,UAIM,GAHJxB,mBAAA,CAAgD,OAAhDyB,UAAgD,EAAAf,gBAAA,CAApBI,KAAK,CAAClB,KAAK,kBACvCI,mBAAA,CAAsD,OAAtD0B,UAAsD,EAAAhB,gBAAA,CAAvBI,KAAK,CAACa,QAAQ,kBAC7C3B,mBAAA,CAAsE,OAAtE4B,WAAsE,EAAAlB,gBAAA,CAApCb,MAAA,CAAAgC,aAAa,CAACf,KAAK,CAACG,MAAM,kB,GAG9DjB,mBAAA,CAGM,OAHN8B,WAGM,GAFJ9B,mBAAA,CAAuD,OAAvD+B,WAAuD,EAAArB,gBAAA,CAA5BI,KAAK,CAACkB,aAAa,kBACflB,KAAK,CAACmB,KAAK,I,cAA1C3B,mBAAA,CAAoE,OAApE4B,WAAoE,EAAxB,GAAC,GAAAxB,gBAAA,CAAGI,KAAK,CAACmB,KAAK,oB,qCAG7DjC,mBAAA,CAeM,OAfNmC,WAeM,GAbIrB,KAAK,CAACsB,SAAS,I,cADvB9B,mBAAA,CAMS;;QAJNY,OAAK,EAAAmB,cAAA,CAAAlB,MAAA,IAAOtB,MAAA,CAAAyC,UAAU,CAACxB,KAAK;QAC7B1B,KAAK,EAAC;yCAENY,mBAAA,CAAqC;QAAlCZ,KAAK,EAAC;MAAuB,0B,uEAG1B0B,KAAK,CAACyB,WAAW,I,cADzBjC,mBAAA,CAMS;;QAJNY,OAAK,EAAAmB,cAAA,CAAAlB,MAAA,IAAOtB,MAAA,CAAA2C,eAAe,CAAC1B,KAAK;QAClC1B,KAAK,EAAC;yCAENY,mBAAA,CAA4B;QAAzBZ,KAAK,EAAC;MAAc,0B;6EAOjCe,mBAAA,YAAe,EACuBN,MAAA,CAAA4C,iBAAiB,CAACpC,MAAM,Q,cAA9DC,mBAAA,CAiEM,OAjENoC,WAiEM,GAhEJ1C,mBAAA,CAMM,OANN2C,WAMM,G,0BALJ3C,mBAAA,CAGM;MAHDZ,KAAK,EAAC;IAAgB,IACzBY,mBAAA,CAA4B;MAAzBZ,KAAK,EAAC;IAAc,IACvBY,mBAAA,CAAmB,cAAb,QAAM,E,qBAEdA,mBAAA,CAAmE,OAAnE4C,WAAmE,EAAAlC,gBAAA,CAA7Bb,MAAA,CAAAgD,oBAAoB,iB,GAG5D7C,mBAAA,CAuDM,OAvDN8C,WAuDM,I,kBAtDJxC,mBAAA,CAqDMM,SAAA,QAAAC,WAAA,CApDqBhB,MAAA,CAAA4C,iBAAiB,EAAnCM,cAAc;2BADvBzC,mBAAA,CAqDM;QAnDHS,GAAG,EAAEgC,cAAc,CAAC/B,EAAE;QACvB5B,KAAK,EAAC,qBAAqB;QAC1B8B,OAAK,EAAAC,MAAA,IAAEtB,MAAA,CAAAmD,kBAAkB,CAACD,cAAc;UAEzC/C,mBAAA,CAKM,OALNiD,WAKM,GAJJjD,mBAAA,CAA+D;QAAzDkD,GAAG,EAAEH,cAAc,CAACI,KAAK;QAAGC,GAAG,EAAEL,cAAc,CAACnD;4CACdmD,cAAc,CAACM,KAAK,I,cAA5D/C,mBAAA,CAEM,OAFNgD,WAEM,EAAA5C,gBAAA,CADDqC,cAAc,CAACM,KAAK,oB,qCAI3BrD,mBAAA,CA4BM,OA5BNuD,WA4BM,GA3BJvD,mBAAA,CAAkE,OAAlEwD,WAAkE,EAAA9C,gBAAA,CAA7BqC,cAAc,CAACnD,KAAK,kBACzDI,mBAAA,CAA8E,OAA9EyD,WAA8E,EAAA/C,gBAAA,CAAnCqC,cAAc,CAACW,WAAW,kBAErE1D,mBAAA,CAaM,OAbN2D,WAaM,GAZJ3D,mBAAA,CAGM,OAHN4D,WAGM,G,0BAFJ5D,mBAAA,CAA2B;QAAxBZ,KAAK,EAAC;MAAa,4BACtBY,mBAAA,CAAwC,cAAAU,gBAAA,CAA/BqC,cAAc,CAACc,MAAM,iB,GAEhC7D,mBAAA,CAGM,OAHN8D,WAGM,G,0BAFJ9D,mBAAA,CAA4B;QAAzBZ,KAAK,EAAC;MAAc,4BACvBY,mBAAA,CAA8C,cAAAU,gBAAA,CAArCqC,cAAc,CAACgB,YAAY,iB,GAEtC/D,mBAAA,CAGM,OAHNgE,WAGM,G,0BAFJhE,mBAAA,CAAoC;QAAjCZ,KAAK,EAAC;MAAsB,4BAC/BY,mBAAA,CAA6C,cAAAU,gBAAA,CAApCqC,cAAc,CAACkB,WAAW,iB,KAIvCjE,mBAAA,CAQM,OARNkE,WAQM,GAPJlE,mBAAA,CAA8D,QAA9DmE,WAA8D,EAAlC,GAAC,GAAAzD,gBAAA,CAAGqC,cAAc,CAACd,KAAK,kBACjBc,cAAc,CAACqB,aAAa,I,cAA/D9D,mBAAA,CAEO,QAFP+D,WAEO,EAF0D,IAC9D,GAAA3D,gBAAA,CAAGqC,cAAc,CAACqB,aAAa,oB,mCAELrB,cAAc,CAACuB,QAAQ,I,cAApDhE,mBAAA,CAEO,QAFPiE,WAEO,EAAA7D,gBAAA,CADFqC,cAAc,CAACuB,QAAQ,IAAG,IAC/B,mB,uCAIJtE,mBAAA,CASM,OATNwE,WASM,GARJxE,mBAAA,CAOS;QANNkB,OAAK,EAAAmB,cAAA,CAAAlB,MAAA,IAAOtB,MAAA,CAAA4E,UAAU,CAAC1B,cAAc;QACtC3D,KAAK,EAAC,iBAAiB;QACtBsF,QAAQ,EAAE7E,MAAA,CAAA8E;2CAEX3E,mBAAA,CAA2B;QAAxBZ,KAAK,EAAC;MAAa,2BACtBY,mBAAA,CAAiB,cAAX,MAAI,mB;6EAOpBG,mBAAA,UAAa,EACmBX,MAAA,CAAAoF,eAAe,I,cAA/CtE,mBAAA,CAuBM,OAvBNuE,WAuBM,GAtBJ7E,mBAAA,CAKM,OALN8E,WAKM,G,4BAJJ9E,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAES;MAFAkB,OAAK,EAAA6D,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEnF,MAAA,CAAAoF,yBAAA,IAAApF,MAAA,CAAAoF,yBAAA,IAAAD,IAAA,CAAyB;MAAE5F,KAAK,EAAC;QAC/CY,mBAAA,CAAkF;MAA9EZ,KAAK,EAAAa,eAAA,CAAEJ,MAAA,CAAAqF,mBAAmB;iDAIlClF,mBAAA,CAcM,OAdNmF,WAcM,I,kBAbJ7E,mBAAA,CAYSM,SAAA,QAAAC,WAAA,CAXchB,MAAA,CAAAuF,eAAe,EAA7BX,UAAU;2BADnBnE,mBAAA,CAYS;QAVNS,GAAG,EAAE0D,UAAU,CAACzD,EAAE;QAClBE,OAAK,EAAAC,MAAA,IAAEtB,MAAA,CAAAwF,eAAe,CAACZ,UAAU;QAClCrF,KAAK,EAAC,kBAAkB;QACvBsF,QAAQ,EAAE7E,MAAA,CAAA8E;UAEX3E,mBAAA,CAEM,OAFNsF,WAEM,GADJtF,mBAAA,CAAgC;QAA5BZ,KAAK,EAAAa,eAAA,CAAEwE,UAAU,CAAC1E,IAAI;iCAE5BC,mBAAA,CAA2D,OAA3DuF,WAA2D,EAAA7E,gBAAA,CAAzB+D,UAAU,CAACe,KAAK,kBAClDxF,mBAAA,CAAiE,OAAjEyF,WAAiE,EAAA/E,gBAAA,CAA5B+D,UAAU,CAAC9C,QAAQ,iB;sEAZrB9B,MAAA,CAAAqF,mBAAmB,E,0CAiB5D/E,mBAAA,UAAa,EACoBX,MAAA,CAAAkG,gBAAgB,IAAI7F,MAAA,CAAA8F,YAAY,CAACtF,MAAM,Q,cAAxEC,mBAAA,CAoCM,OApCNsF,WAoCM,GAnCJ5F,mBAAA,CAKM,OALN6F,WAKM,G,4BAJJ7F,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAES;MAFAkB,OAAK,EAAA6D,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEnF,MAAA,CAAAiG,aAAA,IAAAjG,MAAA,CAAAiG,aAAA,IAAAd,IAAA,CAAa;MAAE5F,KAAK,EAAC;OAAe,QAEpD,E,GAGFY,mBAAA,CA2BM,OA3BN+F,WA2BM,I,kBA1BJzF,mBAAA,CAyBMM,SAAA,QAAAC,WAAA,CAxBYhB,MAAA,CAAA8F,YAAY,CAACK,KAAK,QAA3BlF,KAAK;2BADdR,mBAAA,CAyBM;QAvBHS,GAAG,EAAED,KAAK,CAACE,EAAE;QACd5B,KAAK,EAAC,cAAc;QACnB8B,OAAK,EAAAC,MAAA,IAAEtB,MAAA,CAAAoG,WAAW,CAACnF,KAAK;UAEzBd,mBAAA,CAEM,OAFNkG,WAEM,GADJlG,mBAAA,CAAyC;QAArCZ,KAAK,EAAAa,eAAA,CAAEJ,MAAA,CAAAyB,YAAY,CAACR,KAAK,CAACS,IAAI;iCAGpCvB,mBAAA,CAGM,OAHNmG,WAGM,GAFJnG,mBAAA,CAAkD,OAAlDoG,WAAkD,EAAA1F,gBAAA,CAApBI,KAAK,CAAClB,KAAK,kBACzCI,mBAAA,CAA4D,OAA5DqG,WAA4D,EAAA3F,gBAAA,CAA/Bb,MAAA,CAAAyG,UAAU,CAACxF,KAAK,CAACyF,IAAI,kB,GAGpDvG,mBAAA,CASM,OATNwG,WASM,GARJxG,mBAAA,CAOS;QANNkB,OAAK,EAAAmB,cAAA,CAAAlB,MAAA,IAAOtB,MAAA,CAAAoG,WAAW,CAACnF,KAAK;QAC9B1B,KAAK,EAAC,aAAa;QAClBsF,QAAQ,EAAE7E,MAAA,CAAA8E;2CAEX3E,mBAAA,CAA2B;QAAxBZ,KAAK,EAAC;MAAa,2BACtBY,mBAAA,CAAiB,cAAX,MAAI,mB;6EAOpBG,mBAAA,YAAe,EACeN,MAAA,CAAA4G,UAAU,CAACpG,MAAM,Q,cAA/CC,mBAAA,CA6BM,OA7BNoG,WA6BM,G,4BA5BJ1G,mBAAA,CAGM;MAHDZ,KAAK,EAAC;IAAmB,IAC5BY,mBAAA,CAA2B;MAAxBZ,KAAK,EAAC;IAAa,IACtBY,mBAAA,CAAiB,cAAX,MAAI,E,qBAGZA,mBAAA,CAsBM,OAtBN2G,WAsBM,I,kBArBJrG,mBAAA,CAoBMM,SAAA,QAAAC,WAAA,CAnBgBhB,MAAA,CAAA4G,UAAU,EAAvBG,SAAS;2BADlBtG,mBAAA,CAoBM;QAlBHS,GAAG,EAAE6F,SAAS,CAAC5F,EAAE;QACjB5B,KAAK,EAAAa,eAAA,4BAA6B2G,SAAS,CAACrF,IAAI;QAChDL,OAAK,EAAAC,MAAA,IAAEtB,MAAA,CAAAgH,YAAY,CAACD,SAAS;UAE9B5G,mBAAA,CAEM,OAFN8G,WAEM,GADJ9G,mBAAA,CAA+B;QAA3BZ,KAAK,EAAAa,eAAA,CAAE2G,SAAS,CAAC7G,IAAI;iCAG3BC,mBAAA,CAIM,OAJN+G,WAIM,GAHJ/G,mBAAA,CAAwD,OAAxDgH,WAAwD,EAAAtG,gBAAA,CAAxBkG,SAAS,CAAChH,KAAK,kBAC/CI,mBAAA,CAAoE,OAApEiH,WAAoE,EAAAvG,gBAAA,CAA9BkG,SAAS,CAAClD,WAAW,kBAC3D1D,mBAAA,CAA8D,OAA9DkH,WAA8D,EAAAxG,gBAAA,CAA3BkG,SAAS,CAACO,QAAQ,iB,GAGvDnH,mBAAA,CAGM,OAHNoH,WAGM,GAFJpH,mBAAA,CAAmD,OAAnDqH,WAAmD,EAAA3G,gBAAA,CAAxBkG,SAAS,CAACU,KAAK,kBAC1CtH,mBAAA,CAAuD,OAAvDuH,WAAuD,EAAA7G,gBAAA,CAA5BkG,SAAS,CAACY,SAAS,iB;6EAMtDrH,mBAAA,UAAa,EACsBN,MAAA,CAAA4H,cAAc,CAACpH,MAAM,Q,cAAxDC,mBAAA,CAsCM,OAtCNoH,WAsCM,G,4BArCJ1H,mBAAA,CAGM;MAHDZ,KAAK,EAAC;IAAkB,IAC3BY,mBAAA,CAA2B;MAAxBZ,KAAK,EAAC;IAAa,IACtBY,mBAAA,CAAiB,cAAX,MAAI,E,qBAGZA,mBAAA,CA+BM,OA/BN2H,WA+BM,I,kBA9BJrH,mBAAA,CA6BMM,SAAA,QAAAC,WAAA,CA5BehB,MAAA,CAAA4H,cAAc,EAA1BG,QAAQ;2BADjBtH,mBAAA,CA6BM;QA3BHS,GAAG,EAAE6G,QAAQ,CAAC5G,EAAE;QAChB5B,KAAK,EAAAa,eAAA,+BAAgC2H,QAAQ,CAACC,QAAQ;UAEvD7H,mBAAA,CAEM,OAFN8H,WAEM,GADJ9H,mBAAA,CAA8B;QAA1BZ,KAAK,EAAAa,eAAA,CAAE2H,QAAQ,CAAC7H,IAAI;iCAG1BC,mBAAA,CAGM,OAHN+H,WAGM,GAFJ/H,mBAAA,CAAoD,OAApDgI,WAAoD,EAAAtH,gBAAA,CAAtBkH,QAAQ,CAACK,IAAI,kBAC3CjI,mBAAA,CAAoD,OAApDkI,WAAoD,EAAAxH,gBAAA,CAAtBkH,QAAQ,CAACO,IAAI,iB,GAG7CnI,mBAAA,CAcM,OAdNoI,WAcM,GAZIR,QAAQ,CAACS,UAAU,I,cAD3B/H,mBAAA,CAMS;;QAJNY,OAAK,EAAAC,MAAA,IAAEtB,MAAA,CAAAyI,oBAAoB,CAACV,QAAQ;QACrCxI,KAAK,EAAC;0BAEHwI,QAAQ,CAACW,UAAU,wBAAAC,WAAA,K,mCAExBxI,mBAAA,CAKS;QAJNkB,OAAK,EAAAC,MAAA,IAAEtB,MAAA,CAAA4I,eAAe,CAACb,QAAQ;QAChCxI,KAAK,EAAC;2CAENY,mBAAA,CAA4B;QAAzBZ,KAAK,EAAC;MAAc,0B;6EAOjCe,mBAAA,WAAc,E,CACFN,MAAA,CAAA6I,aAAa,I,cAAzBpI,mBAAA,CAcM,OAdNqI,WAcM,G,4BAbJ3I,mBAAA,CAEM;MAFDZ,KAAK,EAAC;IAAgB,IACzBY,mBAAA,CAAmC;MAAhCZ,KAAK,EAAC;IAAqB,G,iDAEhCY,mBAAA,CAGM;MAHDZ,KAAK,EAAC;IAAgB,IACzBY,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAiB,WAAd,YAAU,E,qBAEfA,mBAAA,CAKM,OALN4I,WAKM,GAJJ5I,mBAAA,CAGS;MAHAkB,OAAK,EAAA6D,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEnF,MAAA,CAAAgJ,aAAA,IAAAhJ,MAAA,CAAAgJ,aAAA,IAAA7D,IAAA,CAAa;MAAE5F,KAAK,EAAC;oCACnCY,mBAAA,CAA6B;MAA1BZ,KAAK,EAAC;IAAe,2BACxBY,mBAAA,CAAiB,cAAX,MAAI,mB,6CAKhBG,mBAAA,UAAa,EACFN,MAAA,CAAAiJ,SAAS,I,cAApBxI,mBAAA,CAGM,OAHNyI,WAGM,G,4BAFJ/I,mBAAA,CAAmC;MAA9BZ,KAAK,EAAC;IAAiB,4BAC5BY,mBAAA,CAAiD,OAAjDgJ,WAAiD,EAAAtI,gBAAA,CAApBb,MAAA,CAAAoJ,WAAW,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}