{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, normalizeStyle as _normalizeStyle, createTextVNode as _createTextVNode, renderSlot as _renderSlot, resolveComponent as _resolveComponent, createVNode as _createVNode, createBlock as _createBlock, Transition as _Transition, withCtx as _withCtx } from \"vue\";\nconst _hoisted_1 = {\n  key: 0,\n  class: \"scene-indicator\"\n};\nconst _hoisted_2 = {\n  class: \"scene-info\"\n};\nconst _hoisted_3 = {\n  class: \"scene-name\"\n};\nconst _hoisted_4 = {\n  class: \"scene-description\"\n};\nconst _hoisted_5 = {\n  class: \"scene-controls\"\n};\nconst _hoisted_6 = {\n  key: 1,\n  class: \"scene-selector\"\n};\nconst _hoisted_7 = {\n  class: \"selector-header\"\n};\nconst _hoisted_8 = {\n  class: \"scene-grid\"\n};\nconst _hoisted_9 = [\"onClick\"];\nconst _hoisted_10 = {\n  class: \"scene-card-header\"\n};\nconst _hoisted_11 = {\n  class: \"scene-description\"\n};\nconst _hoisted_12 = {\n  class: \"scene-meta\"\n};\nconst _hoisted_13 = {\n  key: 0,\n  class: \"scene-auto-switch\"\n};\nconst _hoisted_14 = {\n  key: 0,\n  class: \"recommended-scenes\"\n};\nconst _hoisted_15 = {\n  class: \"recommendation-list\"\n};\nconst _hoisted_16 = [\"onClick\"];\nconst _hoisted_17 = {\n  class: \"recommendation-info\"\n};\nconst _hoisted_18 = {\n  class: \"recommendation-name\"\n};\nconst _hoisted_19 = {\n  class: \"recommendation-score\"\n};\nconst _hoisted_20 = {\n  key: 0,\n  class: \"family-layout\"\n};\nconst _hoisted_21 = {\n  class: \"dynamic-island\"\n};\nconst _hoisted_22 = {\n  class: \"kid-education-card\"\n};\nconst _hoisted_23 = {\n  class: \"pedia-card\"\n};\nconst _hoisted_24 = {\n  class: \"vpa-widget\"\n};\nconst _hoisted_25 = {\n  key: 1,\n  class: \"focus-layout\"\n};\nconst _hoisted_26 = {\n  class: \"dynamic-island\"\n};\nconst _hoisted_27 = {\n  class: \"music-card\"\n};\nconst _hoisted_28 = {\n  class: \"schedule-card\"\n};\nconst _hoisted_29 = {\n  class: \"order-card\"\n};\nconst _hoisted_30 = {\n  class: \"smarthome-card\"\n};\nconst _hoisted_31 = {\n  class: \"navigation-card\"\n};\nconst _hoisted_32 = {\n  class: \"vpa-widget\"\n};\nconst _hoisted_33 = {\n  class: \"immersive-layout\"\n};\nconst _hoisted_34 = {\n  key: 3,\n  class: \"entertainment-layout\"\n};\nconst _hoisted_35 = {\n  class: \"layout-row video-row\"\n};\nconst _hoisted_36 = {\n  class: \"card-video\"\n};\nconst _hoisted_37 = {\n  class: \"vpa-widget\"\n};\nconst _hoisted_38 = {\n  class: \"layout-row bottom-row\"\n};\nconst _hoisted_39 = {\n  class: \"card-small\"\n};\nconst _hoisted_40 = {\n  class: \"card-small\"\n};\nconst _hoisted_41 = {\n  key: 4,\n  class: \"minimal-layout\"\n};\nconst _hoisted_42 = {\n  class: \"dynamic-island-minimal\"\n};\nconst _hoisted_43 = {\n  class: \"bottom-components\"\n};\nconst _hoisted_44 = {\n  class: \"vpa-minimal\"\n};\nconst _hoisted_45 = {\n  class: \"card-minimal\"\n};\nconst _hoisted_46 = {\n  key: 5,\n  class: \"default-layout\"\n};\nconst _hoisted_47 = {\n  class: \"layout-grid\"\n};\nconst _hoisted_48 = {\n  key: 0,\n  class: \"scene-transition-overlay\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_DefaultCard = _resolveComponent(\"DefaultCard\");\n  const _component_KidEducationCard = _resolveComponent(\"KidEducationCard\");\n  const _component_AIPediaCard = _resolveComponent(\"AIPediaCard\");\n  const _component_VPAAvatarWidget = _resolveComponent(\"VPAAvatarWidget\");\n  const _component_MusicControlCard = _resolveComponent(\"MusicControlCard\");\n  const _component_AIScheduleAssistantCard = _resolveComponent(\"AIScheduleAssistantCard\");\n  const _component_AIOrderCard = _resolveComponent(\"AIOrderCard\");\n  const _component_SmartHomeControlCard = _resolveComponent(\"SmartHomeControlCard\");\n  const _component_NavigationCard = _resolveComponent(\"NavigationCard\");\n  const _component_ImmersiveWallpaperInterface = _resolveComponent(\"ImmersiveWallpaperInterface\");\n  const _component_VoiceInteractionManager = _resolveComponent(\"VoiceInteractionManager\");\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: \"scene-manager\",\n    style: _normalizeStyle($setup.sceneStyles)\n  }, [_createCommentVNode(\" 场景指示器 \"), $props.showIndicator ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"i\", {\n    class: _normalizeClass($setup.getSceneIcon)\n  }, null, 2 /* CLASS */), _createElementVNode(\"span\", _hoisted_3, _toDisplayString($setup.currentScene.name), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_4, _toDisplayString($setup.currentScene.description), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = (...args) => $setup.toggleSceneSelector && $setup.toggleSceneSelector(...args)),\n    class: \"control-btn\"\n  }, _cache[3] || (_cache[3] = [_createElementVNode(\"i\", {\n    class: \"fas fa-th-large\"\n  }, null, -1 /* CACHED */)])), _createElementVNode(\"button\", {\n    onClick: _cache[1] || (_cache[1] = (...args) => $setup.toggleAutoSwitch && $setup.toggleAutoSwitch(...args)),\n    class: _normalizeClass([\"control-btn\", {\n      active: $setup.autoSwitchEnabled\n    }])\n  }, _cache[4] || (_cache[4] = [_createElementVNode(\"i\", {\n    class: \"fas fa-magic\"\n  }, null, -1 /* CACHED */)]), 2 /* CLASS */)])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 场景选择器 \"), $setup.showSceneSelector ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_cache[6] || (_cache[6] = _createElementVNode(\"h3\", null, \"选择场景\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[2] || (_cache[2] = (...args) => $setup.closeSceneSelector && $setup.closeSceneSelector(...args)),\n    class: \"close-btn\"\n  }, _cache[5] || (_cache[5] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times\"\n  }, null, -1 /* CACHED */)]))]), _createElementVNode(\"div\", _hoisted_8, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.availableScenes, scene => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: scene.id,\n      class: _normalizeClass(['scene-card', {\n        active: $setup.currentScene.id === scene.id\n      }]),\n      onClick: $event => $setup.selectScene(scene.id)\n    }, [_createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"i\", {\n      class: _normalizeClass($setup.getSceneIconByType(scene.id))\n    }, null, 2 /* CLASS */), _createElementVNode(\"h4\", null, _toDisplayString(scene.name), 1 /* TEXT */)]), _createElementVNode(\"p\", _hoisted_11, _toDisplayString(scene.description), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"span\", {\n      class: \"scene-priority\",\n      style: _normalizeStyle({\n        backgroundColor: $setup.getPriorityColor(scene.priority)\n      })\n    }, \" 优先级 \" + _toDisplayString(scene.priority), 5 /* TEXT, STYLE */), scene.autoSwitch ? (_openBlock(), _createElementBlock(\"span\", _hoisted_13, [...(_cache[7] || (_cache[7] = [_createElementVNode(\"i\", {\n      class: \"fas fa-magic\"\n    }, null, -1 /* CACHED */), _createTextVNode(\" 自动 \", -1 /* CACHED */)]))])) : _createCommentVNode(\"v-if\", true)])], 10 /* CLASS, PROPS */, _hoisted_9);\n  }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 推荐场景 \"), $setup.recommendedScenes.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [_cache[9] || (_cache[9] = _createElementVNode(\"h4\", null, \"推荐场景\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_15, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.recommendedScenes, recommendation => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: recommendation.sceneId,\n      class: \"recommendation-item\",\n      onClick: $event => $setup.selectScene(recommendation.sceneId)\n    }, [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"i\", {\n      class: _normalizeClass($setup.getSceneIconByType(recommendation.sceneId))\n    }, null, 2 /* CLASS */), _createElementVNode(\"div\", null, [_createElementVNode(\"div\", _hoisted_18, _toDisplayString(recommendation.scene.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_19, \"匹配度: \" + _toDisplayString(recommendation.score), 1 /* TEXT */)])]), _cache[8] || (_cache[8] = _createElementVNode(\"button\", {\n      class: \"apply-btn\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-check\"\n    })], -1 /* CACHED */))], 8 /* PROPS */, _hoisted_16);\n  }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 场景内容区域 \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"scene-content\", $setup.currentScene.layout])\n  }, [_renderSlot(_ctx.$slots, \"default\", {\n    scene: $setup.currentScene,\n    layout: $setup.currentScene.layout\n  }, () => [_createCommentVNode(\" 场景布局内容 \"), _createElementVNode(\"div\", {\n    class: _normalizeClass(['scene-layout', `layout-${$setup.currentScene.layout}`])\n  }, [_createCommentVNode(\" 根据场景文档的ASCII图来布局卡片 \"), $setup.currentScene.layout === 'family' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, [_createCommentVNode(\" 灵动岛 16x1 \"), _createElementVNode(\"div\", _hoisted_21, [_createVNode(_component_DefaultCard, {\n    scene: $setup.currentScene,\n    \"card-type\": \"dynamicIsland\",\n    \"theme-colors\": $props.themeColors\n  }, null, 8 /* PROPS */, [\"scene\", \"theme-colors\"])]), _createCommentVNode(\" 儿童教育卡片 8x8 \"), _createElementVNode(\"div\", _hoisted_22, [$setup.currentScene.cards.includes('kidEducation') ? (_openBlock(), _createBlock(_component_KidEducationCard, {\n    key: 0,\n    position: {\n      x: 1,\n      y: 2\n    },\n    theme: 'glass',\n    \"theme-colors\": $props.themeColors,\n    onCardClick: $setup.handleCardClick,\n    onModeChanged: $setup.handleEducationModeChanged,\n    onLessonCompleted: $setup.handleLessonCompleted\n  }, null, 8 /* PROPS */, [\"theme-colors\", \"onCardClick\", \"onModeChanged\", \"onLessonCompleted\"])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 百科问答卡片 8x3 \"), _createElementVNode(\"div\", _hoisted_23, [$setup.currentScene.cards.includes('pedia') ? (_openBlock(), _createBlock(_component_AIPediaCard, {\n    key: 0,\n    position: {\n      x: 9,\n      y: 2\n    },\n    size: 'medium',\n    theme: 'glass',\n    \"theme-colors\": $props.themeColors,\n    onQuestionAsked: $setup.handleQuestionAsked,\n    onAnswerReceived: $setup.handleAnswerReceived,\n    onCategoryExplored: $setup.handleCategoryExplored,\n    onKnowledgeShared: $setup.handleKnowledgeShared\n  }, null, 8 /* PROPS */, [\"theme-colors\", \"onQuestionAsked\", \"onAnswerReceived\", \"onCategoryExplored\", \"onKnowledgeShared\"])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" VPA小窗 2x2 \"), _createElementVNode(\"div\", _hoisted_24, [_createVNode(_component_VPAAvatarWidget, {\n    size: 'small',\n    position: {\n      x: 15,\n      y: 7\n    },\n    theme: 'glass',\n    \"theme-colors\": $props.themeColors,\n    onAvatarClick: $setup.handleVpaClick,\n    onModeChanged: $setup.handleVpaModeChanged,\n    onAnimationChanged: $setup.handleVpaAnimationChanged\n  }, null, 8 /* PROPS */, [\"theme-colors\", \"onAvatarClick\", \"onModeChanged\", \"onAnimationChanged\"])])])) : $setup.currentScene.layout === 'focus' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_25, [_createCommentVNode(\" 灵动岛 16x1 \"), _createElementVNode(\"div\", _hoisted_26, [_createVNode(_component_DefaultCard, {\n    scene: $setup.currentScene,\n    \"card-type\": \"dynamicIsland\"\n  }, null, 8 /* PROPS */, [\"scene\"])]), _createCommentVNode(\" 音乐控制卡片 8x8 \"), _createElementVNode(\"div\", _hoisted_27, [$setup.currentScene.cards.includes('music') ? (_openBlock(), _createBlock(_component_MusicControlCard, {\n    key: 0,\n    position: {\n      x: 1,\n      y: 2\n    },\n    theme: 'glass',\n    \"theme-colors\": $props.themeColors,\n    onCardClick: $setup.handleCardClick,\n    onSongChanged: $setup.handleSongChanged,\n    onPlayStateChanged: $setup.handlePlayStateChanged\n  }, null, 8 /* PROPS */, [\"theme-colors\", \"onCardClick\", \"onSongChanged\", \"onPlayStateChanged\"])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" AI日程助手卡片 8x3 \"), _createElementVNode(\"div\", _hoisted_28, [$setup.currentScene.cards.includes('schedule') ? (_openBlock(), _createBlock(_component_AIScheduleAssistantCard, {\n    key: 0,\n    position: {\n      x: 9,\n      y: 2\n    },\n    size: 'medium',\n    theme: 'glass',\n    \"theme-colors\": $props.themeColors,\n    onScheduleUpdated: $setup.handleScheduleUpdated,\n    onReminderSet: $setup.handleReminderSet,\n    onOptimizationApplied: $setup.handleOptimizationApplied\n  }, null, 8 /* PROPS */, [\"theme-colors\", \"onScheduleUpdated\", \"onReminderSet\", \"onOptimizationApplied\"])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" AI订单助手卡片 4x2 \"), _createElementVNode(\"div\", _hoisted_29, [$setup.currentScene.cards.includes('order') ? (_openBlock(), _createBlock(_component_AIOrderCard, {\n    key: 0,\n    position: {\n      x: 9,\n      y: 6\n    },\n    size: 'small',\n    theme: 'glass',\n    \"theme-colors\": $props.themeColors,\n    onOrderPlaced: $setup.handleOrderPlaced,\n    onOrderTracked: $setup.handleOrderTracked,\n    onRecommendationSelected: $setup.handleRecommendationSelected\n  }, null, 8 /* PROPS */, [\"theme-colors\", \"onOrderPlaced\", \"onOrderTracked\", \"onRecommendationSelected\"])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 智能家居控制卡片 4x4 \"), _createElementVNode(\"div\", _hoisted_30, [$setup.currentScene.cards.includes('smarthome') ? (_openBlock(), _createBlock(_component_SmartHomeControlCard, {\n    key: 0,\n    position: {\n      x: 13,\n      y: 2\n    },\n    size: 'small',\n    theme: 'glass',\n    \"theme-colors\": $props.themeColors,\n    onDeviceControlled: $setup.handleDeviceControlled,\n    onSceneModeChanged: $setup.handleSceneModeChanged,\n    onSuggestionApplied: $setup.handleSuggestionApplied\n  }, null, 8 /* PROPS */, [\"theme-colors\", \"onDeviceControlled\", \"onSceneModeChanged\", \"onSuggestionApplied\"])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 导航卡片 4x3 \"), _createElementVNode(\"div\", _hoisted_31, [$setup.currentScene.cards.includes('navigation') ? (_openBlock(), _createBlock(_component_NavigationCard, {\n    key: 0,\n    position: {\n      x: 13,\n      y: 6\n    },\n    size: 'small',\n    theme: 'glass',\n    \"theme-colors\": $props.themeColors,\n    onNavigationStarted: $setup.handleNavigationStarted,\n    onRouteChanged: $setup.handleRouteChanged,\n    onNavigationStopped: $setup.handleNavigationStopped\n  }, null, 8 /* PROPS */, [\"theme-colors\", \"onNavigationStarted\", \"onRouteChanged\", \"onNavigationStopped\"])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" VPA小窗 2x2 \"), _createElementVNode(\"div\", _hoisted_32, [_createVNode(_component_DefaultCard, {\n    scene: $setup.currentScene,\n    \"card-type\": \"vpaWidget\"\n  }, null, 8 /* PROPS */, [\"scene\"])])])) : $setup.currentScene.layout === 'immersive' ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createCommentVNode(\" 沉浸式桌面壁纸界面 \"), _createElementVNode(\"div\", _hoisted_33, [_createVNode(_component_ImmersiveWallpaperInterface, {\n    onWallpaperPromptReady: $setup.handleWallpaperPrompt\n  }, null, 8 /* PROPS */, [\"onWallpaperPromptReady\"])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : $setup.currentScene.layout === 'entertainment' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_34, [_createCommentVNode(\" 等待/娱乐模式布局 \"), _createElementVNode(\"div\", _hoisted_35, [_createElementVNode(\"div\", _hoisted_36, [$setup.currentScene.cards.includes('videoPlayer') ? (_openBlock(), _createBlock(_component_DefaultCard, {\n    key: 0,\n    scene: $setup.currentScene,\n    \"card-type\": \"videoPlayer\",\n    \"theme-colors\": $props.themeColors\n  }, null, 8 /* PROPS */, [\"scene\", \"theme-colors\"])) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" VPA小窗 \"), _createElementVNode(\"div\", _hoisted_37, [$setup.currentScene.cards.includes('vpaWidget') ? (_openBlock(), _createBlock(_component_DefaultCard, {\n    key: 0,\n    scene: $setup.currentScene,\n    \"card-type\": \"vpaWidget\",\n    \"theme-colors\": $props.themeColors\n  }, null, 8 /* PROPS */, [\"scene\", \"theme-colors\"])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"div\", _hoisted_39, [$setup.currentScene.cards.includes('news') ? (_openBlock(), _createBlock(_component_DefaultCard, {\n    key: 0,\n    scene: $setup.currentScene,\n    \"card-type\": \"news\",\n    \"theme-colors\": $props.themeColors\n  }, null, 8 /* PROPS */, [\"scene\", \"theme-colors\"])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_40, [$setup.currentScene.cards.includes('ambientSound') ? (_openBlock(), _createBlock(_component_DefaultCard, {\n    key: 0,\n    scene: $setup.currentScene,\n    \"card-type\": \"ambientSound\",\n    \"theme-colors\": $props.themeColors\n  }, null, 8 /* PROPS */, [\"scene\", \"theme-colors\"])) : _createCommentVNode(\"v-if\", true)])])])) : $setup.currentScene.layout === 'minimal' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_41, [_createCommentVNode(\" 雨夜模式极简布局 \"), _createCommentVNode(\" 灵动岛 \"), _createElementVNode(\"div\", _hoisted_42, [$setup.currentScene.cards.includes('navigation') ? (_openBlock(), _createBlock(_component_DefaultCard, {\n    key: 0,\n    scene: $setup.currentScene,\n    \"card-type\": \"dynamicIsland\",\n    \"theme-colors\": $props.themeColors\n  }, null, 8 /* PROPS */, [\"scene\", \"theme-colors\"])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 底部组件区域 \"), _createElementVNode(\"div\", _hoisted_43, [_createCommentVNode(\" VPA组件 (左下角) \"), _createElementVNode(\"div\", _hoisted_44, [$setup.currentScene.cards.includes('vpaWidget') ? (_openBlock(), _createBlock(_component_DefaultCard, {\n    key: 0,\n    scene: $setup.currentScene,\n    \"card-type\": \"vpaWidget\",\n    \"theme-colors\": $props.themeColors\n  }, null, 8 /* PROPS */, [\"scene\", \"theme-colors\"])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 音乐控制 (右下角) \"), _createElementVNode(\"div\", _hoisted_45, [$setup.currentScene.cards.includes('music') ? (_openBlock(), _createBlock(_component_DefaultCard, {\n    key: 0,\n    scene: $setup.currentScene,\n    \"card-type\": \"music\",\n    \"theme-colors\": $props.themeColors\n  }, null, 8 /* PROPS */, [\"scene\", \"theme-colors\"])) : _createCommentVNode(\"v-if\", true)])])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_46, [_createCommentVNode(\" 默认网格布局 \"), _createElementVNode(\"div\", _hoisted_47, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.currentScene.cards, (card, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: index,\n      class: \"scene-card-slot\"\n    }, [_createCommentVNode(\" VPA组件使用专用组件 \"), card === 'vpaWidget' ? (_openBlock(), _createBlock(_component_VPAAvatarWidget, {\n      key: 0,\n      size: 'small',\n      position: {\n        x: 1,\n        y: 1\n      },\n      theme: 'glass',\n      \"theme-colors\": $props.themeColors,\n      onAvatarClick: $setup.handleVpaClick,\n      onModeChanged: $setup.handleVpaModeChanged,\n      onAnimationChanged: $setup.handleVpaAnimationChanged\n    }, null, 8 /* PROPS */, [\"theme-colors\", \"onAvatarClick\", \"onModeChanged\", \"onAnimationChanged\"])) : (_openBlock(), _createElementBlock(_Fragment, {\n      key: 1\n    }, [_createCommentVNode(\" 其他卡片使用DefaultCard \"), _createVNode(_component_DefaultCard, {\n      scene: $setup.currentScene,\n      \"card-type\": card,\n      \"theme-colors\": $props.themeColors\n    }, null, 8 /* PROPS */, [\"scene\", \"card-type\", \"theme-colors\"])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))]);\n  }), 128 /* KEYED_FRAGMENT */))])]))], 2 /* CLASS */)], true)], 2 /* CLASS */), _createCommentVNode(\" 场景切换动画 \"), _createVNode(_Transition, {\n    name: \"scene-transition\"\n  }, {\n    default: _withCtx(() => [$setup.isTransitioning ? (_openBlock(), _createElementBlock(\"div\", _hoisted_48, _cache[10] || (_cache[10] = [_createElementVNode(\"div\", {\n      class: \"transition-content\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-spinner fa-spin\"\n    }), _createElementVNode(\"span\", null, \"正在切换场景...\")], -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 语音交互管理器 \"), _createVNode(_component_VoiceInteractionManager, {\n    onSceneSwitchRequested: $setup.handleVoiceSceneSwitch,\n    onWallpaperPromptReady: $setup.handleWallpaperPrompt\n  }, null, 8 /* PROPS */, [\"onSceneSwitchRequested\", \"onWallpaperPromptReady\"])], 4 /* STYLE */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "style", "_normalizeStyle", "$setup", "sceneStyles", "_createCommentVNode", "$props", "showIndicator", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_normalizeClass", "getSceneIcon", "_hoisted_3", "_toDisplayString", "currentScene", "name", "_hoisted_4", "description", "_hoisted_5", "onClick", "_cache", "args", "toggleSceneSelector", "toggleAutoSwitch", "active", "autoSwitchEnabled", "showSceneSelector", "_hoisted_6", "_hoisted_7", "closeSceneSelector", "_hoisted_8", "_Fragment", "_renderList", "availableScenes", "scene", "key", "id", "$event", "selectScene", "_hoisted_10", "getSceneIconByType", "_hoisted_11", "_hoisted_12", "backgroundColor", "getPriorityColor", "priority", "autoSwitch", "_hoisted_13", "recommendedScenes", "length", "_hoisted_14", "_hoisted_15", "recommendation", "sceneId", "_hoisted_17", "_hoisted_18", "_hoisted_19", "score", "layout", "_renderSlot", "_ctx", "$slots", "_hoisted_20", "_hoisted_21", "_createVNode", "_component_DefaultCard", "themeColors", "_hoisted_22", "cards", "includes", "_createBlock", "_component_KidEducationCard", "position", "x", "y", "theme", "onCardClick", "handleCardClick", "onModeChanged", "handleEducationModeChanged", "onLessonCompleted", "handleLessonCompleted", "_hoisted_23", "_component_AIPediaCard", "size", "onQuestionAsked", "handleQuestionAsked", "onAnswerReceived", "handleAnswerReceived", "onCategoryExplored", "handleCategoryExplored", "onKnowledgeShared", "handleKnowledgeShared", "_hoisted_24", "_component_VPAAvatarWidget", "onAvatarClick", "handleVpaClick", "handleVpaModeChanged", "onAnimationChanged", "handleVpaAnimationChanged", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_component_MusicControlCard", "onSongChanged", "handleSongChanged", "onPlayStateChanged", "handlePlayStateChanged", "_hoisted_28", "_component_AIScheduleAssistantCard", "onScheduleUpdated", "handleScheduleUpdated", "onReminderSet", "handleReminderSet", "onOptimizationApplied", "handleOptimizationApplied", "_hoisted_29", "_component_AIOrderCard", "onOrderPlaced", "handleOrderPlaced", "onOrderTracked", "handleOrderTracked", "onRecommendationSelected", "handleRecommendationSelected", "_hoisted_30", "_component_SmartHomeControlCard", "onDeviceControlled", "handleDeviceControlled", "onSceneModeChanged", "handleSceneModeChanged", "onSuggestionApplied", "handleSuggestionApplied", "_hoisted_31", "_component_NavigationCard", "onNavigationStarted", "handleNavigationStarted", "onRouteChanged", "handleRouteChanged", "onNavigationStopped", "handleNavigationStopped", "_hoisted_32", "_hoisted_33", "_component_ImmersiveWallpaperInterface", "onWallpaperPromptReady", "handleWallpaperPrompt", "_hoisted_34", "_hoisted_35", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "_hoisted_43", "_hoisted_44", "_hoisted_45", "_hoisted_46", "_hoisted_47", "card", "index", "_Transition", "isTransitioning", "_hoisted_48", "_component_VoiceInteractionManager", "onSceneSwitchRequested", "handleVoiceSceneSwitch"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\SceneManager.vue"], "sourcesContent": ["<template>\r\n  <div class=\"scene-manager\" :style=\"sceneStyles\">\r\n    <!-- 场景指示器 -->\r\n    <div class=\"scene-indicator\" v-if=\"showIndicator\">\r\n      <div class=\"scene-info\">\r\n        <i :class=\"getSceneIcon\"></i>\r\n        <span class=\"scene-name\">{{ currentScene.name }}</span>\r\n        <span class=\"scene-description\">{{ currentScene.description }}</span>\r\n      </div>\r\n      <div class=\"scene-controls\">\r\n        <button @click=\"toggleSceneSelector\" class=\"control-btn\">\r\n          <i class=\"fas fa-th-large\"></i>\r\n        </button>\r\n        <button @click=\"toggleAutoSwitch\" class=\"control-btn\" :class=\"{ active: autoSwitchEnabled }\">\r\n          <i class=\"fas fa-magic\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 场景选择器 -->\r\n    <div v-if=\"showSceneSelector\" class=\"scene-selector\">\r\n      <div class=\"selector-header\">\r\n        <h3>选择场景</h3>\r\n        <button @click=\"closeSceneSelector\" class=\"close-btn\">\r\n          <i class=\"fas fa-times\"></i>\r\n        </button>\r\n      </div>\r\n      \r\n      <div class=\"scene-grid\">\r\n        <div \r\n          v-for=\"scene in availableScenes\" \r\n          :key=\"scene.id\"\r\n          :class=\"['scene-card', { active: currentScene.id === scene.id }]\"\r\n          @click=\"selectScene(scene.id)\"\r\n        >\r\n          <div class=\"scene-card-header\">\r\n            <i :class=\"getSceneIconByType(scene.id)\"></i>\r\n            <h4>{{ scene.name }}</h4>\r\n          </div>\r\n          <p class=\"scene-description\">{{ scene.description }}</p>\r\n          <div class=\"scene-meta\">\r\n            <span class=\"scene-priority\" :style=\"{ backgroundColor: getPriorityColor(scene.priority) }\">\r\n              优先级 {{ scene.priority }}\r\n            </span>\r\n            <span class=\"scene-auto-switch\" v-if=\"scene.autoSwitch\">\r\n              <i class=\"fas fa-magic\"></i> 自动\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 推荐场景 -->\r\n      <div v-if=\"recommendedScenes.length > 0\" class=\"recommended-scenes\">\r\n        <h4>推荐场景</h4>\r\n        <div class=\"recommendation-list\">\r\n          <div \r\n            v-for=\"recommendation in recommendedScenes\" \r\n            :key=\"recommendation.sceneId\"\r\n            class=\"recommendation-item\"\r\n            @click=\"selectScene(recommendation.sceneId)\"\r\n          >\r\n            <div class=\"recommendation-info\">\r\n              <i :class=\"getSceneIconByType(recommendation.sceneId)\"></i>\r\n              <div>\r\n                <div class=\"recommendation-name\">{{ recommendation.scene.name }}</div>\r\n                <div class=\"recommendation-score\">匹配度: {{ recommendation.score }}</div>\r\n              </div>\r\n            </div>\r\n            <button class=\"apply-btn\">\r\n              <i class=\"fas fa-check\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 场景内容区域 -->\r\n    <div class=\"scene-content\" :class=\"currentScene.layout\">\r\n      <slot :scene=\"currentScene\" :layout=\"currentScene.layout\">\r\n        <!-- 场景布局内容 -->\r\n        <div :class=\"['scene-layout', `layout-${currentScene.layout}`]\">\r\n          <!-- 根据场景文档的ASCII图来布局卡片 -->\r\n          <div v-if=\"currentScene.layout === 'family'\" class=\"family-layout\">\r\n            <!-- 灵动岛 16x1 -->\r\n            <div class=\"dynamic-island\">\r\n              <DefaultCard\r\n                :scene=\"currentScene\"\r\n                card-type=\"dynamicIsland\"\r\n                :theme-colors=\"themeColors\"\r\n              />\r\n            </div>\r\n\r\n            <!-- 儿童教育卡片 8x8 -->\r\n            <div class=\"kid-education-card\">\r\n              <KidEducationCard\r\n                v-if=\"currentScene.cards.includes('kidEducation')\"\r\n                :position=\"{ x: 1, y: 2 }\"\r\n                :theme=\"'glass'\"\r\n                :theme-colors=\"themeColors\"\r\n                @card-click=\"handleCardClick\"\r\n                @mode-changed=\"handleEducationModeChanged\"\r\n                @lesson-completed=\"handleLessonCompleted\"\r\n              />\r\n            </div>\r\n\r\n            <!-- 百科问答卡片 8x3 -->\r\n            <div class=\"pedia-card\">\r\n              <AIPediaCard\r\n                v-if=\"currentScene.cards.includes('pedia')\"\r\n                :position=\"{ x: 9, y: 2 }\"\r\n                :size=\"'medium'\"\r\n                :theme=\"'glass'\"\r\n                :theme-colors=\"themeColors\"\r\n                @question-asked=\"handleQuestionAsked\"\r\n                @answer-received=\"handleAnswerReceived\"\r\n                @category-explored=\"handleCategoryExplored\"\r\n                @knowledge-shared=\"handleKnowledgeShared\"\r\n              />\r\n            </div>\r\n\r\n            <!-- VPA小窗 2x2 -->\r\n            <div class=\"vpa-widget\">\r\n              <VPAAvatarWidget\r\n                :size=\"'small'\"\r\n                :position=\"{ x: 15, y: 7 }\"\r\n                :theme=\"'glass'\"\r\n                :theme-colors=\"themeColors\"\r\n                @avatar-click=\"handleVpaClick\"\r\n                @mode-changed=\"handleVpaModeChanged\"\r\n                @animation-changed=\"handleVpaAnimationChanged\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div v-else-if=\"currentScene.layout === 'focus'\" class=\"focus-layout\">\r\n            <!-- 灵动岛 16x1 -->\r\n            <div class=\"dynamic-island\">\r\n              <DefaultCard\r\n                :scene=\"currentScene\"\r\n                card-type=\"dynamicIsland\"\r\n              />\r\n            </div>\r\n\r\n            <!-- 音乐控制卡片 8x8 -->\r\n            <div class=\"music-card\">\r\n              <MusicControlCard\r\n                v-if=\"currentScene.cards.includes('music')\"\r\n                :position=\"{ x: 1, y: 2 }\"\r\n                :theme=\"'glass'\"\r\n                :theme-colors=\"themeColors\"\r\n                @card-click=\"handleCardClick\"\r\n                @song-changed=\"handleSongChanged\"\r\n                @play-state-changed=\"handlePlayStateChanged\"\r\n              />\r\n            </div>\r\n\r\n            <!-- AI日程助手卡片 8x3 -->\r\n            <div class=\"schedule-card\">\r\n              <AIScheduleAssistantCard\r\n                v-if=\"currentScene.cards.includes('schedule')\"\r\n                :position=\"{ x: 9, y: 2 }\"\r\n                :size=\"'medium'\"\r\n                :theme=\"'glass'\"\r\n                :theme-colors=\"themeColors\"\r\n                @schedule-updated=\"handleScheduleUpdated\"\r\n                @reminder-set=\"handleReminderSet\"\r\n                @optimization-applied=\"handleOptimizationApplied\"\r\n              />\r\n            </div>\r\n\r\n            <!-- AI订单助手卡片 4x2 -->\r\n            <div class=\"order-card\">\r\n              <AIOrderCard\r\n                v-if=\"currentScene.cards.includes('order')\"\r\n                :position=\"{ x: 9, y: 6 }\"\r\n                :size=\"'small'\"\r\n                :theme=\"'glass'\"\r\n                :theme-colors=\"themeColors\"\r\n                @order-placed=\"handleOrderPlaced\"\r\n                @order-tracked=\"handleOrderTracked\"\r\n                @recommendation-selected=\"handleRecommendationSelected\"\r\n              />\r\n            </div>\r\n\r\n            <!-- 智能家居控制卡片 4x4 -->\r\n            <div class=\"smarthome-card\">\r\n              <SmartHomeControlCard\r\n                v-if=\"currentScene.cards.includes('smarthome')\"\r\n                :position=\"{ x: 13, y: 2 }\"\r\n                :size=\"'small'\"\r\n                :theme=\"'glass'\"\r\n                :theme-colors=\"themeColors\"\r\n                @device-controlled=\"handleDeviceControlled\"\r\n                @scene-mode-changed=\"handleSceneModeChanged\"\r\n                @suggestion-applied=\"handleSuggestionApplied\"\r\n              />\r\n            </div>\r\n\r\n            <!-- 导航卡片 4x3 -->\r\n            <div class=\"navigation-card\">\r\n              <NavigationCard\r\n                v-if=\"currentScene.cards.includes('navigation')\"\r\n                :position=\"{ x: 13, y: 6 }\"\r\n                :size=\"'small'\"\r\n                :theme=\"'glass'\"\r\n                :theme-colors=\"themeColors\"\r\n                @navigation-started=\"handleNavigationStarted\"\r\n                @route-changed=\"handleRouteChanged\"\r\n                @navigation-stopped=\"handleNavigationStopped\"\r\n              />\r\n            </div>\r\n\r\n            <!-- VPA小窗 2x2 -->\r\n            <div class=\"vpa-widget\">\r\n              <DefaultCard\r\n                :scene=\"currentScene\"\r\n                card-type=\"vpaWidget\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 沉浸式桌面壁纸界面 -->\r\n          <div v-else-if=\"currentScene.layout === 'immersive'\" class=\"immersive-layout\">\r\n            <ImmersiveWallpaperInterface @wallpaper-prompt-ready=\"handleWallpaperPrompt\" />\r\n          </div>\r\n\r\n          <div v-else-if=\"currentScene.layout === 'entertainment'\" class=\"entertainment-layout\">\r\n            <!-- 等待/娱乐模式布局 -->\r\n            <div class=\"layout-row video-row\">\r\n              <div class=\"card-video\">\r\n                <DefaultCard\r\n                  v-if=\"currentScene.cards.includes('videoPlayer')\"\r\n                  :scene=\"currentScene\"\r\n                  card-type=\"videoPlayer\"\r\n                  :theme-colors=\"themeColors\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <!-- VPA小窗 -->\r\n            <div class=\"vpa-widget\">\r\n              <DefaultCard\r\n                v-if=\"currentScene.cards.includes('vpaWidget')\"\r\n                :scene=\"currentScene\"\r\n                card-type=\"vpaWidget\"\r\n                :theme-colors=\"themeColors\"\r\n              />\r\n            </div>\r\n\r\n            <div class=\"layout-row bottom-row\">\r\n              <div class=\"card-small\">\r\n                <DefaultCard\r\n                  v-if=\"currentScene.cards.includes('news')\"\r\n                  :scene=\"currentScene\"\r\n                  card-type=\"news\"\r\n                  :theme-colors=\"themeColors\"\r\n                />\r\n              </div>\r\n              <div class=\"card-small\">\r\n                <DefaultCard\r\n                  v-if=\"currentScene.cards.includes('ambientSound')\"\r\n                  :scene=\"currentScene\"\r\n                  card-type=\"ambientSound\"\r\n                  :theme-colors=\"themeColors\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div v-else-if=\"currentScene.layout === 'minimal'\" class=\"minimal-layout\">\r\n            <!-- 雨夜模式极简布局 -->\r\n            <!-- 灵动岛 -->\r\n            <div class=\"dynamic-island-minimal\">\r\n              <DefaultCard\r\n                v-if=\"currentScene.cards.includes('navigation')\"\r\n                :scene=\"currentScene\"\r\n                card-type=\"dynamicIsland\"\r\n                :theme-colors=\"themeColors\"\r\n              />\r\n            </div>\r\n\r\n            <!-- 底部组件区域 -->\r\n            <div class=\"bottom-components\">\r\n              <!-- VPA组件 (左下角) -->\r\n              <div class=\"vpa-minimal\">\r\n                <DefaultCard\r\n                  v-if=\"currentScene.cards.includes('vpaWidget')\"\r\n                  :scene=\"currentScene\"\r\n                  card-type=\"vpaWidget\"\r\n                  :theme-colors=\"themeColors\"\r\n                />\r\n              </div>\r\n\r\n              <!-- 音乐控制 (右下角) -->\r\n              <div class=\"card-minimal\">\r\n                <DefaultCard\r\n                  v-if=\"currentScene.cards.includes('music')\"\r\n                  :scene=\"currentScene\"\r\n                  card-type=\"music\"\r\n                  :theme-colors=\"themeColors\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div v-else class=\"default-layout\">\r\n            <!-- 默认网格布局 -->\r\n            <div class=\"layout-grid\">\r\n              <div v-for=\"(card, index) in currentScene.cards\" :key=\"index\" class=\"scene-card-slot\">\r\n                <!-- VPA组件使用专用组件 -->\r\n                <VPAAvatarWidget\r\n                  v-if=\"card === 'vpaWidget'\"\r\n                  :size=\"'small'\"\r\n                  :position=\"{ x: 1, y: 1 }\"\r\n                  :theme=\"'glass'\"\r\n                  :theme-colors=\"themeColors\"\r\n                  @avatar-click=\"handleVpaClick\"\r\n                  @mode-changed=\"handleVpaModeChanged\"\r\n                  @animation-changed=\"handleVpaAnimationChanged\"\r\n                />\r\n                <!-- 其他卡片使用DefaultCard -->\r\n                <DefaultCard \r\n                  v-else\r\n                  :scene=\"currentScene\" \r\n                  :card-type=\"card\" \r\n                  :theme-colors=\"themeColors\" \r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </slot>\r\n    </div>\r\n\r\n    <!-- 场景切换动画 -->\r\n    <transition name=\"scene-transition\">\r\n      <div v-if=\"isTransitioning\" class=\"scene-transition-overlay\">\r\n        <div class=\"transition-content\">\r\n          <i class=\"fas fa-spinner fa-spin\"></i>\r\n          <span>正在切换场景...</span>\r\n        </div>\r\n      </div>\r\n    </transition>\r\n\r\n    <!-- 语音交互管理器 -->\r\n    <VoiceInteractionManager \r\n      @scene-switch-requested=\"handleVoiceSceneSwitch\"\r\n      @wallpaper-prompt-ready=\"handleWallpaperPrompt\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, computed, onMounted, onUnmounted, watch } from 'vue'\r\nimport SceneManager from '@/utils/SceneManager'\r\nimport EmotionalPromptGenerator from '@/services/EmotionalPromptGenerator'\r\nimport SceneContextManager from '@/services/SceneContextManager'\r\n\r\nimport DefaultCard from './cards/DefaultCard.vue'\r\nimport KidEducationCard from './cards/KidEducationCard.vue'\r\nimport MusicControlCard from './cards/MusicControlCard.vue'\r\nimport AIScheduleAssistantCard from './cards/AIScheduleAssistantCard.vue'\r\nimport SmartHomeControlCard from './cards/SmartHomeControlCard.vue'\r\nimport NavigationCard from './cards/NavigationCard.vue'\r\nimport AIOrderCard from './cards/AIOrderCard.vue'\r\nimport AIPediaCard from './cards/AIPediaCard.vue'\r\nimport VPAAvatarWidget from './vpa/VPAAvatarWidget.vue'\r\nimport ImmersiveWallpaperInterface from './ImmersiveWallpaperInterface.vue'\r\nimport VoiceInteractionManager from './VoiceInteractionManager.vue'\r\n\r\nexport default {\r\n  name: 'SceneManager',\r\n  components: {\r\n    DefaultCard,\r\n    KidEducationCard,\r\n    MusicControlCard,\r\n    AIScheduleAssistantCard,\r\n    SmartHomeControlCard,\r\n    NavigationCard,\r\n    AIOrderCard,\r\n    AIPediaCard,\r\n    VPAAvatarWidget,\r\n    ImmersiveWallpaperInterface,\r\n    VoiceInteractionManager\r\n  },\r\n  props: {\r\n    initialScene: {\r\n      type: String,\r\n      default: 'default'\r\n    },\r\n    showIndicator: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    autoSwitch: {\r\n      type: Boolean,\r\n      default: false  // 默认禁用自动切换，避免意外的场景切换\r\n    },\r\n    themeColors: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  emits: ['scene-changed', 'context-updated', 'wallpaper-prompt-ready'],\r\n  setup(props, { emit }) {\r\n    const sceneManager = new SceneManager()\r\n    const emotionalPromptGenerator = new EmotionalPromptGenerator()\r\n    const contextManager = new SceneContextManager()\r\n    \r\n    const showSceneSelector = ref(false)\r\n    const autoSwitchEnabled = ref(props.autoSwitch)\r\n    const isTransitioning = ref(false)\r\n    \r\n    // 当前场景\r\n    const currentScene = ref(sceneManager.getCurrentScene())\r\n    \r\n    // 可用场景\r\n    const availableScenes = computed(() => sceneManager.getAllScenes())\r\n    \r\n    // 推荐场景\r\n    const recommendedScenes = ref([])\r\n    \r\n    // 场景样式\r\n    const sceneStyles = computed(() => {\r\n      const scene = currentScene.value\r\n\r\n      // 移除背景样式，让DynamicWallpaperManager管理背景\r\n      let backgroundStyle = {}\r\n\r\n      return {\r\n        '--scene-primary-color': getThemeColor(scene.theme, 'primary'),\r\n        '--scene-secondary-color': getThemeColor(scene.theme, 'secondary'),\r\n        '--scene-background': 'transparent', /* 使用透明背景，让动态壁纸显示 */\r\n        '--scene-text-color': getThemeColor(scene.theme, 'text'),\r\n        ...backgroundStyle\r\n      }\r\n    })\r\n    \r\n    // 获取场景图标\r\n    const getSceneIcon = computed(() => {\r\n      return getSceneIconByType(currentScene.value.id)\r\n    })\r\n    \r\n    // 选择场景\r\n    const selectScene = async (sceneId) => {\r\n      if (sceneId === currentScene.value.id) return\r\n\r\n      isTransitioning.value = true\r\n\r\n      try {\r\n        // 触发场景切换\r\n        const success = sceneManager.switchScene(sceneId, 'manual')\r\n\r\n        if (success) {\r\n          // 更新当前场景的响应式状态\r\n          currentScene.value = sceneManager.getCurrentScene()\r\n\r\n          // 更新上下文管理器\r\n          contextManager.updateContext({\r\n            recentScenes: [...contextManager.context.recentScenes, sceneId].slice(-5),\r\n            sceneSwitchCount: contextManager.context.sceneSwitchCount + 1\r\n          })\r\n\r\n          // 生成场景对应的壁纸\r\n          await generateSceneWallpaper(currentScene.value)\r\n\r\n          // 发送事件\r\n          emit('scene-changed', {\r\n            from: sceneManager.sceneHistory[0]?.from,\r\n            to: sceneId,\r\n            scene: currentScene.value,\r\n            context: contextManager.getContext()\r\n          })\r\n\r\n          // 关闭选择器\r\n          showSceneSelector.value = false\r\n        }\r\n      } catch (error) {\r\n        console.error('场景切换失败:', error)\r\n      } finally {\r\n        setTimeout(() => {\r\n          isTransitioning.value = false\r\n        }, 1000)\r\n      }\r\n    }\r\n    \r\n    // 生成场景壁纸\r\n    const generateSceneWallpaper = async (scene) => {\r\n      if (!scene.wallpaper || scene.wallpaper.startsWith('/')) {\r\n        // 使用默认壁纸\r\n        return scene.wallpaper\r\n      }\r\n      \r\n      try {\r\n        console.log('🎨 开始生成情感化壁纸提示词...')\r\n        \r\n        // 获取当前上下文信息\r\n        const promptContext = contextManager.getPromptGenerationContext()\r\n        console.log('📋 当前上下文:', promptContext)\r\n        \r\n        // 生成情感化提示词\r\n        const emotionalPrompt = await emotionalPromptGenerator.generateEmotionalPrompt(\r\n          scene,\r\n          promptContext\r\n        )\r\n        \r\n        console.log('🎭 情感化提示词生成成功:', emotionalPrompt)\r\n        \r\n        // 触发壁纸生成事件\r\n        emit('wallpaper-prompt-ready', {\r\n          prompt: emotionalPrompt,\r\n          scene: scene,\r\n          context: promptContext\r\n        })\r\n        \r\n        return emotionalPrompt\r\n        \r\n      } catch (error) {\r\n        console.error('情感化壁纸生成失败:', error)\r\n        \r\n        // 使用增强的场景描述作为降级方案\r\n        const fallbackPrompt = emotionalPromptGenerator.getFallbackPrompt(\r\n          scene,\r\n          contextManager.getPromptGenerationContext()\r\n        )\r\n        \r\n        console.log('🔄 使用降级提示词:', fallbackPrompt)\r\n        \r\n        // 触发壁纸生成事件\r\n        emit('wallpaper-prompt-ready', {\r\n          prompt: fallbackPrompt,\r\n          scene: scene,\r\n          context: contextManager.getPromptGenerationContext()\r\n        })\r\n        \r\n        return fallbackPrompt\r\n      }\r\n    }\r\n    \r\n    // 切换场景选择器\r\n    const toggleSceneSelector = () => {\r\n      showSceneSelector.value = !showSceneSelector.value\r\n      if (showSceneSelector.value) {\r\n        updateRecommendations()\r\n      }\r\n    }\r\n    \r\n    // 关闭场景选择器\r\n    const closeSceneSelector = () => {\r\n      showSceneSelector.value = false\r\n    }\r\n    \r\n    // 切换自动切换\r\n    const toggleAutoSwitch = () => {\r\n      autoSwitchEnabled.value = !autoSwitchEnabled.value\r\n    }\r\n    \r\n    // 更新推荐场景\r\n    const updateRecommendations = () => {\r\n      const context = getCurrentContext()\r\n      recommendedScenes.value = sceneManager.getRecommendedScenes(context)\r\n    }\r\n    \r\n    // 获取当前上下文\r\n    const getCurrentContext = () => {\r\n      const now = new Date()\r\n      return {\r\n        time: now.getHours(),\r\n        day: now.getDay(),\r\n        isWeekend: now.getDay() === 0 || now.getDay() === 6,\r\n        passengers: ['driver'], // 可以通过传感器获取\r\n        passengerCount: 1,\r\n        destination: '', // 可以通过导航获取\r\n        gear: 'D', // 可以通过车辆系统获取\r\n        drivingDuration: 0, // 可以通过车辆系统获取\r\n        weather: 'clear', // 可以通过天气API获取\r\n        roadType: 'city', // 可以通过GPS获取\r\n        batteryLevel: 85, // 可以通过车辆系统获取\r\n        locationType: 'city', // 可以通过GPS获取\r\n        fatigueDetected: false, // 可以通过疲劳检测系统获取\r\n        manualTrigger: null,\r\n        calendarEvents: [], // 可以通过日历API获取\r\n        newUserDetected: false,\r\n        accidentDetected: false,\r\n        airbagDeployed: false\r\n      }\r\n    }\r\n    \r\n    // 模拟上下文更新\r\n    const simulateContextUpdate = () => {\r\n      contextManager.simulateDrivingUpdate()\r\n    }\r\n    \r\n    // 获取卡片组件\r\n    const getCardComponent = (card) => {\r\n      // 根据卡片类型返回对应的组件名称\r\n      const cardComponents = {\r\n        // 导航相关\r\n        'navigation': 'NavigationCard',\r\n        'tempNavigation': 'NavigationCard',\r\n\r\n        // 音乐相关\r\n        'music': 'MusicCard',\r\n        'basicMusic': 'MusicCard',\r\n        'romanticMusic': 'MusicCard',\r\n\r\n        // 待办事项\r\n        'todo': 'TodoCard',\r\n\r\n        // 儿童教育\r\n        'kidEducation': 'KidEducationCard',\r\n        'pedia': 'PediaCard',\r\n\r\n        // 娱乐相关\r\n        'videoPlayer': 'VideoPlayerCard',\r\n        'news': 'NewsCard',\r\n        'ambientSound': 'AmbientSoundCard',\r\n\r\n        // 智能家居\r\n        'smartHome': 'SmartHomeCard',\r\n\r\n        // 家庭出行\r\n        'rearSeatControl': 'RearSeatControlCard',\r\n        'facilityFinder': 'FacilityFinderCard',\r\n        'tripReminder': 'TripReminderCard',\r\n\r\n        // 长途驾驶\r\n        'serviceArea': 'ServiceAreaCard',\r\n        'driverStatus': 'DriverStatusCard',\r\n        'vehicleStatus': 'VehicleStatusCard',\r\n\r\n        // 充电相关\r\n        'chargingStatus': 'ChargingStatusCard',\r\n        'entertainment': 'EntertainmentCard',\r\n        'nearbyShops': 'NearbyShopsCard',\r\n\r\n        // 其他功能\r\n        'orderStatus': 'OrderStatusCard',\r\n        'basicControl': 'BasicControlCard',\r\n        'petInfo': 'PetInfoCard',\r\n        'climateControl': 'ClimateControlCard',\r\n        'carWashChecklist': 'CarWashChecklistCard',\r\n        'ambientLight': 'AmbientLightCard',\r\n\r\n        // 安全相关\r\n        'fatigueWarning': 'FatigueWarningCard',\r\n        'restArea': 'RestAreaCard',\r\n        'refreshment': 'RefreshmentCard',\r\n        'emergencyContact': 'EmergencyContactCard',\r\n        'emergencyInfo': 'EmergencyInfoCard',\r\n        'firstAid': 'FirstAidCard',\r\n\r\n        // 用户管理\r\n        'userSelector': 'UserSelectorCard',\r\n        'userPreferences': 'UserPreferencesCard',\r\n        'privacySettings': 'PrivacySettingsCard',\r\n\r\n        // 泊车相关\r\n        'parkingSearch': 'ParkingSearchCard',\r\n        'parkingAssist': 'ParkingAssistCard',\r\n        'costInfo': 'CostInfoCard'\r\n      }\r\n\r\n      return cardComponents[card] || 'DefaultCard'\r\n    }\r\n\r\n    // 获取场景图标\r\n    const getSceneIconByType = (sceneId) => {\r\n      const iconMap = {\r\n        default: 'fas fa-home',\r\n        morningCommuteFamily: 'fas fa-child',\r\n        morningCommuteFocus: 'fas fa-briefcase',\r\n        eveningCommute: 'fas fa-sun',\r\n        waitingMode: 'fas fa-couch',\r\n        rainyNight: 'fas fa-cloud-rain',\r\n        familyTrip: 'fas fa-car',\r\n        longDistance: 'fas fa-road',\r\n        guestMode: 'fas fa-user-shield',\r\n        petMode: 'fas fa-paw',\r\n        carWashMode: 'fas fa-car-side',\r\n        romanticMode: 'fas fa-heart',\r\n        chargingMode: 'fas fa-charging-station',\r\n        fatigueDetection: 'fas fa-exclamation-triangle',\r\n        userSwitch: 'fas fa-users',\r\n        parkingMode: 'fas fa-parking',\r\n        emergencyMode: 'fas fa-ambulance'\r\n      }\r\n      return iconMap[sceneId] || 'fas fa-circle'\r\n    }\r\n\r\n    // 获取优先级颜色\r\n    const getPriorityColor = (priority) => {\r\n      const colorMap = {\r\n        1: '#e74c3c', // 红色 - 最高优先级\r\n        2: '#f39c12', // 橙色 - 高优先级\r\n        3: '#f1c40f', // 黄色 - 中优先级\r\n        4: '#2ecc71', // 绿色 - 低优先级\r\n        5: '#95a5a6'  // 灰色 - 最低优先级\r\n      }\r\n      return colorMap[priority] || '#95a5a6'\r\n    }\r\n\r\n    // 获取主题颜色\r\n    const getThemeColor = (theme, type) => {\r\n      const themeColors = {\r\n        light: {\r\n          primary: '#4a90e2',\r\n          secondary: '#7ed321',\r\n          background: 'rgba(255, 255, 255, 0.1)',\r\n          text: '#333333'\r\n        },\r\n        dark: {\r\n          primary: '#2c3e50',\r\n          secondary: '#3498db',\r\n          background: 'rgba(0, 0, 0, 0.3)',\r\n          text: '#ffffff'\r\n        },\r\n        warm: {\r\n          primary: '#e74c3c',\r\n          secondary: '#f39c12',\r\n          background: 'rgba(255, 193, 7, 0.1)',\r\n          text: '#2c3e50'\r\n        },\r\n        calm: {\r\n          primary: '#3498db',\r\n          secondary: '#2ecc71',\r\n          background: 'rgba(52, 152, 219, 0.1)',\r\n          text: '#2c3e50'\r\n        },\r\n        evening: {\r\n          primary: '#8e44ad',\r\n          secondary: '#e67e22',\r\n          background: 'rgba(142, 68, 173, 0.1)',\r\n          text: '#ffffff'\r\n        },\r\n        relax: {\r\n          primary: '#27ae60',\r\n          secondary: '#2ecc71',\r\n          background: 'rgba(39, 174, 96, 0.1)',\r\n          text: '#ffffff'\r\n        },\r\n        bright: {\r\n          primary: '#f39c12',\r\n          secondary: '#e67e22',\r\n          background: 'rgba(243, 156, 18, 0.1)',\r\n          text: '#2c3e50'\r\n        }\r\n      }\r\n      return themeColors[theme]?.[type] || themeColors.light[type]\r\n    }\r\n    \r\n    // 监听自动切换\r\n    let autoSwitchInterval\r\n    const startAutoSwitch = () => {\r\n      if (!autoSwitchEnabled.value) return\r\n      \r\n      autoSwitchInterval = setInterval(() => {\r\n        updateRecommendations()\r\n        \r\n        // 如果有高优先级的推荐场景，自动切换\r\n        const topRecommendation = recommendedScenes.value[0]\r\n        if (topRecommendation && topRecommendation.score > 8) {\r\n          selectScene(topRecommendation.sceneId)\r\n        }\r\n      }, 30000) // 每30秒检查一次\r\n    }\r\n    \r\n    const stopAutoSwitch = () => {\r\n      if (autoSwitchInterval) {\r\n        clearInterval(autoSwitchInterval)\r\n        autoSwitchInterval = null\r\n      }\r\n    }\r\n    \r\n    // 监听自动切换状态\r\n    watch(autoSwitchEnabled, (newValue) => {\r\n      if (newValue) {\r\n        startAutoSwitch()\r\n      } else {\r\n        stopAutoSwitch()\r\n      }\r\n    })\r\n    \r\n    // 初始化\r\n    onMounted(() => {\r\n      // 设置初始场景\r\n      if (props.initialScene !== 'default') {\r\n        sceneManager.switchScene(props.initialScene, 'initial')\r\n      }\r\n      \r\n      // 初始化上下文\r\n      contextManager.updateContext({\r\n        isDriving: true, // 假设车辆在行驶中\r\n        recentScenes: [props.initialScene === 'default' ? 'default' : props.initialScene]\r\n      })\r\n      \r\n      // 启动自动切换\r\n      if (autoSwitchEnabled.value) {\r\n        startAutoSwitch()\r\n      }\r\n      \r\n      // 启动上下文更新定时器\r\n      setInterval(simulateContextUpdate, 60000) // 每分钟更新一次\r\n      \r\n      // 监听键盘事件\r\n      window.addEventListener('keydown', handleKeyDown)\r\n    })\r\n    \r\n    onUnmounted(() => {\r\n      stopAutoSwitch()\r\n      window.removeEventListener('keydown', handleKeyDown)\r\n    })\r\n    \r\n    // 键盘事件处理\r\n    const handleKeyDown = (event) => {\r\n      if (event.key === 'Escape') {\r\n        closeSceneSelector()\r\n      } else if (event.ctrlKey && event.key === 's') {\r\n        event.preventDefault()\r\n        toggleSceneSelector()\r\n      }\r\n    }\r\n\r\n    // 处理语音场景切换请求\r\n    const handleVoiceSceneSwitch = async (sceneRequest) => {\r\n      console.log('收到语音场景切换请求:', sceneRequest)\r\n      \r\n      if (sceneRequest.sceneId && sceneRequest.confidence > 0.6) {\r\n        try {\r\n          await selectScene(sceneRequest.sceneId)\r\n          console.log(`语音场景切换成功: ${sceneRequest.sceneId} (置信度: ${sceneRequest.confidence})`)\r\n        } catch (error) {\r\n          console.error('语音场景切换失败:', error)\r\n        }\r\n      } else {\r\n        console.log(`语音场景切换置信度过低: ${sceneRequest.confidence}`)\r\n      }\r\n    }\r\n\r\n    // 处理壁纸生成请求\r\n    function handleWallpaperPrompt(prompt) {\r\n      console.log('收到壁纸生成请求:', prompt)\r\n      \r\n      // 如果是直接字符串，包装为对象格式\r\n      const promptData = typeof prompt === 'string' ? {\r\n        prompt,\r\n        scene: currentScene.value,\r\n        context: contextManager.getPromptGenerationContext()\r\n      } : prompt\r\n      \r\n      // 触发壁纸生成事件，传递给父组件\r\n      emit('wallpaper-prompt-ready', promptData)\r\n    }\r\n\r\n    // 新组件事件处理方法\r\n    const handleCardClick = (cardType) => {\r\n      console.log('卡片被点击:', cardType)\r\n      // 可以在这里处理卡片点击事件，比如打开详细界面\r\n    }\r\n\r\n    const handleEducationModeChanged = (mode) => {\r\n      console.log('教育模式变更:', mode)\r\n      // 可以在这里处理教育模式变更\r\n    }\r\n\r\n    const handleLessonCompleted = (lessonType) => {\r\n      console.log('课程完成:', lessonType)\r\n      // 可以在这里处理课程完成事件，比如更新进度\r\n    }\r\n\r\n    const handleSongChanged = (song) => {\r\n      console.log('歌曲变更:', song)\r\n      // 可以在这里处理歌曲变更事件\r\n    }\r\n\r\n    const handlePlayStateChanged = (state) => {\r\n      console.log('播放状态变更:', state)\r\n      // 可以在这里处理播放状态变更\r\n    }\r\n\r\n    const handleVpaClick = (data) => {\r\n      console.log('VPA头像被点击:', data)\r\n      // 可以在这里处理VPA交互\r\n    }\r\n\r\n    const handleVpaModeChanged = (mode) => {\r\n      console.log('VPA模式变更:', mode)\r\n      // 可以在这里处理VPA模式变更\r\n    }\r\n\r\n    const handleVpaAnimationChanged = (animationData) => {\r\n      console.log('VPA动画状态变更:', animationData)\r\n      // 可以在这里处理VPA动画状态变更\r\n    }\r\n\r\n    // 处理AI百科卡片事件\r\n    const handleQuestionAsked = (question) => {\r\n      console.log('Question asked:', question)\r\n      // 可以记录用户提问历史\r\n    }\r\n\r\n    const handleAnswerReceived = (answer) => {\r\n      console.log('Answer received:', answer)\r\n      // 可以分析答案质量，优化推荐\r\n    }\r\n\r\n    const handleCategoryExplored = (category) => {\r\n      console.log('Category explored:', category)\r\n      // 可以根据用户兴趣调整推荐\r\n    }\r\n\r\n    const handleKnowledgeShared = (knowledge) => {\r\n      console.log('Knowledge shared:', knowledge)\r\n      // 可以记录分享行为\r\n    }\r\n\r\n    // 处理AI日程助手卡片事件\r\n    const handleScheduleUpdated = (schedule) => {\r\n      console.log('Schedule updated:', schedule)\r\n      // 可以同步到其他系统\r\n    }\r\n\r\n    const handleReminderSet = (reminder) => {\r\n      console.log('Reminder set:', reminder)\r\n      // 可以设置系统提醒\r\n    }\r\n\r\n    const handleOptimizationApplied = (optimization) => {\r\n      console.log('Optimization applied:', optimization)\r\n      // 可以记录优化效果\r\n    }\r\n\r\n    // 处理AI订单助手卡片事件\r\n    const handleOrderPlaced = (order) => {\r\n      console.log('Order placed:', order)\r\n      // 可以跟踪订单状态\r\n    }\r\n\r\n    const handleOrderTracked = (tracking) => {\r\n      console.log('Order tracked:', tracking)\r\n      // 可以更新订单信息\r\n    }\r\n\r\n    const handleRecommendationSelected = (recommendation) => {\r\n      console.log('Recommendation selected:', recommendation)\r\n      // 可以优化推荐算法\r\n    }\r\n\r\n    // 处理智能家居控制卡片事件\r\n    const handleDeviceControlled = (device) => {\r\n      console.log('Device controlled:', device)\r\n      // 可以同步设备状态\r\n    }\r\n\r\n    const handleSceneModeChanged = (mode) => {\r\n      console.log('Scene mode changed:', mode)\r\n      // 可以调整车内环境\r\n    }\r\n\r\n    const handleSuggestionApplied = (suggestion) => {\r\n      console.log('Suggestion applied:', suggestion)\r\n      // 可以学习用户偏好\r\n    }\r\n\r\n    // 处理导航卡片事件\r\n    const handleNavigationStarted = (destination) => {\r\n      console.log('Navigation started:', destination)\r\n      // 可以切换到导航模式\r\n    }\r\n\r\n    const handleRouteChanged = (route) => {\r\n      console.log('Route changed:', route)\r\n      // 可以更新ETA和路况\r\n    }\r\n\r\n    const handleNavigationStopped = () => {\r\n      console.log('Navigation stopped')\r\n      // 可以切换回其他模式\r\n    }\r\n\r\n    return {\r\n      currentScene,\r\n      availableScenes,\r\n      recommendedScenes,\r\n      showSceneSelector,\r\n      autoSwitchEnabled,\r\n      isTransitioning,\r\n      sceneStyles,\r\n      getSceneIcon,\r\n      getSceneIconByType,\r\n      getPriorityColor,\r\n      getThemeColor,\r\n      selectScene,\r\n      toggleSceneSelector,\r\n      closeSceneSelector,\r\n      toggleAutoSwitch,\r\n      getCardComponent,\r\n      handleWallpaperPrompt,\r\n      handleVoiceSceneSwitch,\r\n      // 新组件事件处理方法\r\n      handleCardClick,\r\n      handleEducationModeChanged,\r\n      handleLessonCompleted,\r\n      handleSongChanged,\r\n      handlePlayStateChanged,\r\n      handleVpaClick,\r\n      handleVpaModeChanged,\r\n      handleVpaAnimationChanged,\r\n      // AI百科卡片事件处理方法\r\n      handleQuestionAsked,\r\n      handleAnswerReceived,\r\n      handleCategoryExplored,\r\n      handleKnowledgeShared,\r\n      // AI日程助手卡片事件处理方法\r\n      handleScheduleUpdated,\r\n      handleReminderSet,\r\n      handleOptimizationApplied,\r\n      // AI订单助手卡片事件处理方法\r\n      handleOrderPlaced,\r\n      handleOrderTracked,\r\n      handleRecommendationSelected,\r\n      // 智能家居控制卡片事件处理方法\r\n      handleDeviceControlled,\r\n      handleSceneModeChanged,\r\n      handleSuggestionApplied,\r\n      // 导航卡片事件处理方法\r\n      handleNavigationStarted,\r\n      handleRouteChanged,\r\n      handleNavigationStopped,\r\n      // 暴露给模板的上下文信息\r\n      contextStats: computed(() => contextManager.getStatistics())\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.scene-manager {\r\n  width: 100%;\r\n  height: 100vh;\r\n  position: relative;\r\n  background: transparent; /* 透明背景，让动态壁纸显示 */\r\n  color: var(--scene-text-color);\r\n  transition: all 0.5s ease;\r\n}\r\n\r\n.scene-indicator {\r\n  position: fixed;\r\n  top: 20px;\r\n  right: 20px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  border-radius: 15px;\r\n  padding: 15px 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  gap: 20px;\r\n  z-index: 1000;\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.scene-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.scene-info i {\r\n  font-size: 18px;\r\n  color: var(--scene-primary-color);\r\n}\r\n\r\n.scene-name {\r\n  font-weight: 600;\r\n  font-size: 14px;\r\n}\r\n\r\n.scene-description {\r\n  font-size: 12px;\r\n  opacity: 0.8;\r\n  margin-left: 10px;\r\n}\r\n\r\n.scene-controls {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.control-btn {\r\n  width: 36px;\r\n  height: 36px;\r\n  border-radius: 50%;\r\n  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));\r\n  background: var(--button-bg, rgba(74, 144, 226, 0.8));\r\n  color: var(--button-color, white);\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n  text-shadow: var(--button-text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.control-btn:hover {\r\n  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));\r\n  transform: scale(1.1);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.control-btn.active {\r\n  background: var(--scene-primary-color);\r\n  color: white;\r\n}\r\n\r\n.scene-selector {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  backdrop-filter: blur(10px);\r\n  z-index: 2000;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.selector-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  padding: 20px;\r\n  border-radius: 15px;\r\n}\r\n\r\n.selector-header h3 {\r\n  margin: 0;\r\n  color: white;\r\n  font-size: 24px;\r\n}\r\n\r\n.close-btn {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));\r\n  background: var(--button-bg, rgba(74, 144, 226, 0.8));\r\n  color: var(--button-color, white);\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n  text-shadow: var(--button-text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.close-btn:hover {\r\n  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));\r\n  transform: scale(1.1);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.scene-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\r\n  gap: 20px;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.scene-card {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  border-radius: 15px;\r\n  padding: 20px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.scene-card:hover {\r\n  background: rgba(255, 255, 255, 0.15);\r\n  transform: translateY(-5px);\r\n}\r\n\r\n.scene-card.active {\r\n  background: var(--scene-primary-color);\r\n  border-color: var(--scene-primary-color);\r\n}\r\n\r\n.scene-card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.scene-card-header i {\r\n  font-size: 20px;\r\n  color: var(--scene-primary-color);\r\n}\r\n\r\n.scene-card.active .scene-card-header i {\r\n  color: white;\r\n}\r\n\r\n.scene-card-header h4 {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  color: white;\r\n}\r\n\r\n.scene-card .scene-description {\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n  margin-bottom: 15px;\r\n  color: white;\r\n}\r\n\r\n.scene-meta {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.scene-priority {\r\n  padding: 4px 8px;\r\n  border-radius: 12px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  color: white;\r\n}\r\n\r\n.scene-auto-switch {\r\n  font-size: 12px;\r\n  color: var(--scene-primary-color);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n}\r\n\r\n.scene-card.active .scene-auto-switch {\r\n  color: white;\r\n}\r\n\r\n.recommended-scenes {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  border-radius: 15px;\r\n  padding: 20px;\r\n}\r\n\r\n.recommended-scenes h4 {\r\n  margin: 0 0 15px 0;\r\n  color: white;\r\n  font-size: 18px;\r\n}\r\n\r\n.recommendation-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n}\r\n\r\n.recommendation-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.recommendation-item:hover {\r\n  background: rgba(255, 255, 255, 0.15);\r\n}\r\n\r\n.recommendation-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.recommendation-info i {\r\n  font-size: 16px;\r\n  color: var(--scene-primary-color);\r\n}\r\n\r\n.recommendation-name {\r\n  font-weight: 600;\r\n  color: white;\r\n  font-size: 14px;\r\n}\r\n\r\n.recommendation-score {\r\n  font-size: 12px;\r\n  opacity: 0.8;\r\n  color: white;\r\n}\r\n\r\n.apply-btn {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));\r\n  background: var(--button-bg, rgba(74, 144, 226, 0.8));\r\n  color: var(--button-color, white);\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n  text-shadow: var(--button-text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.apply-btn:hover {\r\n  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));\r\n  transform: scale(1.1);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.scene-content {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 20px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* 布局样式 */\r\n.scene-content.standard {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  grid-template-rows: 1fr 1fr;\r\n  gap: 20px;\r\n}\r\n\r\n.scene-content.family {\r\n  display: grid;\r\n  grid-template-columns: 2fr 1fr;\r\n  grid-template-rows: 2fr 1fr;\r\n  gap: 20px;\r\n}\r\n\r\n.scene-content.focus {\r\n  display: grid;\r\n  grid-template-columns: 2fr 1fr;\r\n  grid-template-rows: 1fr 1fr;\r\n  gap: 20px;\r\n}\r\n\r\n.scene-content.relax {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n.scene-content.entertainment {\r\n  display: grid;\r\n  grid-template-columns: 2fr 1fr;\r\n  grid-template-rows: 1fr 1fr;\r\n  gap: 20px;\r\n}\r\n\r\n.scene-content.minimal {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.scene-content.driving {\r\n  display: grid;\r\n  grid-template-columns: repeat(3, 1fr);\r\n  gap: 20px;\r\n}\r\n\r\n.scene-content.basic {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 20px;\r\n}\r\n\r\n.scene-content.userSelection {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 40px;\r\n}\r\n\r\n.scene-content.parking {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr 1fr;\r\n  gap: 20px;\r\n}\r\n\r\n.scene-content.emergency {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 20px;\r\n}\r\n\r\n/* 场景布局容器 */\r\n.scene-layout {\r\n  width: 100%;\r\n  height: 100vh;\r\n  min-height: 600px;\r\n  box-sizing: border-box;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n/* 家庭出行模式布局 - 16x9网格 */\r\n.family-layout {\r\n  display: grid;\r\n  grid-template-columns: repeat(16, 1fr);\r\n  grid-template-rows: repeat(9, 1fr);\r\n  gap: min(1vw, 10px);\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: min(1vw, 10px);\r\n}\r\n\r\n/* 灵动岛 16x1 */\r\n.family-layout .dynamic-island {\r\n  grid-column: 1 / 17;\r\n  grid-row: 1 / 2;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 儿童教育卡片 8x8 (调整为给灵动岛让出空间) */\r\n.family-layout .kid-education-card {\r\n  grid-column: 1 / 9;\r\n  grid-row: 2 / 10;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 百科问答卡片 8x3 */\r\n.family-layout .pedia-card {\r\n  grid-column: 9 / 17;\r\n  grid-row: 2 / 5;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* VPA小窗 2x2 */\r\n.family-layout .vpa-widget {\r\n  grid-column: 15 / 17;\r\n  grid-row: 7 / 9;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 专注通勤模式布局 - 16x9网格 */\r\n.focus-layout {\r\n  display: grid;\r\n  grid-template-columns: repeat(16, 1fr);\r\n  grid-template-rows: repeat(9, 1fr);\r\n  gap: min(1vw, 10px);\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: min(1vw, 10px);\r\n}\r\n\r\n/* 灵动岛 16x1 */\r\n.focus-layout .dynamic-island {\r\n  grid-column: 1 / 17;\r\n  grid-row: 1 / 2;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 音乐控制卡片 8x8 (调整为给灵动岛让出空间) */\r\n.focus-layout .music-card {\r\n  grid-column: 1 / 9;\r\n  grid-row: 2 / 10;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 今日待办卡片 8x3 */\r\n.focus-layout .todo-card {\r\n  grid-column: 9 / 17;\r\n  grid-row: 2 / 5;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 订单状态卡片 4x2 */\r\n.focus-layout .order-card {\r\n  grid-column: 9 / 13;\r\n  grid-row: 5 / 7;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 智能家居控制卡片 4x4 */\r\n.focus-layout .smarthome-card {\r\n  grid-column: 13 / 17;\r\n  grid-row: 2 / 6;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 导航卡片 4x3 */\r\n.focus-layout .navigation-card {\r\n  grid-column: 13 / 17;\r\n  grid-row: 6 / 9;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* VPA小窗 2x2 */\r\n.focus-layout .vpa-widget {\r\n  grid-column: 15 / 17;\r\n  grid-row: 7 / 9;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 沉浸式桌面壁纸布局 */\r\n.immersive-layout {\r\n  width: 100%;\r\n  height: 100vh;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 娱乐模式布局 (16x5 视频 + 底部卡片) */\r\n.entertainment-layout {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n  height: 100%;\r\n}\r\n\r\n.entertainment-layout .video-row {\r\n  flex: 3;\r\n}\r\n\r\n.entertainment-layout .card-video {\r\n  width: 100%;\r\n  height: 100%;\r\n  min-height: 250px;\r\n}\r\n\r\n.entertainment-layout .bottom-row {\r\n  flex: 1;\r\n  display: flex;\r\n  gap: 20px;\r\n}\r\n\r\n.entertainment-layout .card-small {\r\n  flex: 1;\r\n  min-height: 120px;\r\n}\r\n\r\n/* 极简模式布局 (雨夜模式) */\r\n.minimal-layout {\r\n  position: relative;\r\n  height: 100%;\r\n  padding: 20px;\r\n}\r\n\r\n.minimal-layout .dynamic-island-minimal {\r\n  position: absolute;\r\n  top: 20px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 400px;\r\n  height: 60px;\r\n}\r\n\r\n.minimal-layout .bottom-components {\r\n  position: absolute;\r\n  bottom: 40px;\r\n  left: 40px;\r\n  right: 40px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-end;\r\n}\r\n\r\n.minimal-layout .vpa-minimal {\r\n  width: 120px;\r\n  height: 120px;\r\n}\r\n\r\n.minimal-layout .card-minimal {\r\n  width: 300px;\r\n  height: 120px;\r\n}\r\n\r\n/* 默认网格布局 */\r\n.default-layout .layout-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n  gap: 20px;\r\n  padding: 20px 0;\r\n}\r\n\r\n.scene-card-slot {\r\n  min-height: 200px;\r\n}\r\n\r\n.scene-transition-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  backdrop-filter: blur(10px);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 3000;\r\n}\r\n\r\n.transition-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 20px;\r\n  color: white;\r\n  font-size: 18px;\r\n}\r\n\r\n.transition-content i {\r\n  font-size: 48px;\r\n  color: var(--scene-primary-color);\r\n}\r\n\r\n/* 场景切换动画 */\r\n.scene-transition-enter-active,\r\n.scene-transition-leave-active {\r\n  transition: all 0.5s ease;\r\n}\r\n\r\n.scene-transition-enter-from {\r\n  opacity: 0;\r\n  transform: scale(0.8);\r\n}\r\n\r\n.scene-transition-leave-to {\r\n  opacity: 0;\r\n  transform: scale(1.2);\r\n}\r\n\r\n.scene-transition-enter-to,\r\n.scene-transition-leave-from {\r\n  opacity: 1;\r\n  transform: scale(1);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .scene-indicator {\r\n    top: 10px;\r\n    right: 10px;\r\n    padding: 10px 15px;\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n  \r\n  .scene-info {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n  \r\n  .scene-description {\r\n    margin-left: 0;\r\n  }\r\n  \r\n  .scene-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .scene-content.standard,\r\n  .scene-content.family,\r\n  .scene-content.focus,\r\n  .scene-content.entertainment,\r\n  .scene-content.driving,\r\n  .scene-content.parking {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n</style>"], "mappings": ";;;EAGSA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAY;;EAEfA,KAAK,EAAC;AAAY;;EAClBA,KAAK,EAAC;AAAmB;;EAE5BA,KAAK,EAAC;AAAgB;;;EAWCA,KAAK,EAAC;;;EAC7BA,KAAK,EAAC;AAAiB;;EAOvBA,KAAK,EAAC;AAAY;;;EAOdA,KAAK,EAAC;AAAmB;;EAI3BA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAY;;;EAIfA,KAAK,EAAC;;;;EAQuBA,KAAK,EAAC;;;EAExCA,KAAK,EAAC;AAAqB;;;EAOvBA,KAAK,EAAC;AAAqB;;EAGvBA,KAAK,EAAC;AAAqB;;EAC3BA,KAAK,EAAC;AAAsB;;;EAiBMA,KAAK,EAAC;;;EAE5CA,KAAK,EAAC;AAAgB;;EAStBA,KAAK,EAAC;AAAoB;;EAa1BA,KAAK,EAAC;AAAY;;EAelBA,KAAK,EAAC;AAAY;;;EAawBA,KAAK,EAAC;;;EAEhDA,KAAK,EAAC;AAAgB;;EAQtBA,KAAK,EAAC;AAAY;;EAalBA,KAAK,EAAC;AAAe;;EAcrBA,KAAK,EAAC;AAAY;;EAclBA,KAAK,EAAC;AAAgB;;EActBA,KAAK,EAAC;AAAiB;;EAcvBA,KAAK,EAAC;AAAY;;EAS4BA,KAAK,EAAC;AAAkB;;;EAIpBA,KAAK,EAAC;;;EAExDA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAY;;EAWpBA,KAAK,EAAC;AAAY;;EASlBA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAY;;EAQlBA,KAAK,EAAC;AAAY;;;EAWwBA,KAAK,EAAC;;;EAGlDA,KAAK,EAAC;AAAwB;;EAU9BA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAa;;EAUnBA,KAAK,EAAC;AAAc;;;EAWjBA,KAAK,EAAC;;;EAEXA,KAAK,EAAC;AAAa;;;EA6BFA,KAAK,EAAC;;;;;;;;;;;;;;uBA/UtCC,mBAAA,CA4VM;IA5VDD,KAAK,EAAC,eAAe;IAAEE,KAAK,EAAAC,eAAA,CAAEC,MAAA,CAAAC,WAAW;MAC5CC,mBAAA,WAAc,EACqBC,MAAA,CAAAC,aAAa,I,cAAhDP,mBAAA,CAcM,OAdNQ,UAcM,GAbJC,mBAAA,CAIM,OAJNC,UAIM,GAHJD,mBAAA,CAA6B;IAAzBV,KAAK,EAAAY,eAAA,CAAER,MAAA,CAAAS,YAAY;2BACvBH,mBAAA,CAAuD,QAAvDI,UAAuD,EAAAC,gBAAA,CAA3BX,MAAA,CAAAY,YAAY,CAACC,IAAI,kBAC7CP,mBAAA,CAAqE,QAArEQ,UAAqE,EAAAH,gBAAA,CAAlCX,MAAA,CAAAY,YAAY,CAACG,WAAW,iB,GAE7DT,mBAAA,CAOM,OAPNU,UAOM,GANJV,mBAAA,CAES;IAFAW,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEnB,MAAA,CAAAoB,mBAAA,IAAApB,MAAA,CAAAoB,mBAAA,IAAAD,IAAA,CAAmB;IAAEvB,KAAK,EAAC;gCACzCU,mBAAA,CAA+B;IAA5BV,KAAK,EAAC;EAAiB,0B,IAE5BU,mBAAA,CAES;IAFAW,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEnB,MAAA,CAAAqB,gBAAA,IAAArB,MAAA,CAAAqB,gBAAA,IAAAF,IAAA,CAAgB;IAAEvB,KAAK,EAAAY,eAAA,EAAC,aAAa;MAAAc,MAAA,EAAmBtB,MAAA,CAAAuB;IAAiB;gCACvFjB,mBAAA,CAA4B;IAAzBV,KAAK,EAAC;EAAc,0B,4DAK7BM,mBAAA,WAAc,EACHF,MAAA,CAAAwB,iBAAiB,I,cAA5B3B,mBAAA,CAsDM,OAtDN4B,UAsDM,GArDJnB,mBAAA,CAKM,OALNoB,UAKM,G,0BAJJpB,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAES;IAFAW,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEnB,MAAA,CAAA2B,kBAAA,IAAA3B,MAAA,CAAA2B,kBAAA,IAAAR,IAAA,CAAkB;IAAEvB,KAAK,EAAC;gCACxCU,mBAAA,CAA4B;IAAzBV,KAAK,EAAC;EAAc,0B,MAI3BU,mBAAA,CAqBM,OArBNsB,UAqBM,I,kBApBJ/B,mBAAA,CAmBMgC,SAAA,QAAAC,WAAA,CAlBY9B,MAAA,CAAA+B,eAAe,EAAxBC,KAAK;yBADdnC,mBAAA,CAmBM;MAjBHoC,GAAG,EAAED,KAAK,CAACE,EAAE;MACbtC,KAAK,EAAAY,eAAA;QAAAc,MAAA,EAA2BtB,MAAA,CAAAY,YAAY,CAACsB,EAAE,KAAKF,KAAK,CAACE;MAAE;MAC5DjB,OAAK,EAAAkB,MAAA,IAAEnC,MAAA,CAAAoC,WAAW,CAACJ,KAAK,CAACE,EAAE;QAE5B5B,mBAAA,CAGM,OAHN+B,WAGM,GAFJ/B,mBAAA,CAA6C;MAAzCV,KAAK,EAAAY,eAAA,CAAER,MAAA,CAAAsC,kBAAkB,CAACN,KAAK,CAACE,EAAE;6BACtC5B,mBAAA,CAAyB,YAAAK,gBAAA,CAAlBqB,KAAK,CAACnB,IAAI,iB,GAEnBP,mBAAA,CAAwD,KAAxDiC,WAAwD,EAAA5B,gBAAA,CAAxBqB,KAAK,CAACjB,WAAW,kBACjDT,mBAAA,CAOM,OAPNkC,WAOM,GANJlC,mBAAA,CAEO;MAFDV,KAAK,EAAC,gBAAgB;MAAEE,KAAK,EAAAC,eAAA;QAAA0C,eAAA,EAAqBzC,MAAA,CAAA0C,gBAAgB,CAACV,KAAK,CAACW,QAAQ;MAAA;OAAK,OACtF,GAAAhC,gBAAA,CAAGqB,KAAK,CAACW,QAAQ,yBAEeX,KAAK,CAACY,UAAU,I,cAAtD/C,mBAAA,CAEO,QAFPgD,WAEO,OAAA3B,MAAA,QAAAA,MAAA,OADLZ,mBAAA,CAA4B;MAAzBV,KAAK,EAAC;IAAc,2B,iBAAK,MAC9B,mB;oCAKNM,mBAAA,UAAa,EACFF,MAAA,CAAA8C,iBAAiB,CAACC,MAAM,Q,cAAnClD,mBAAA,CAqBM,OArBNmD,WAqBM,G,0BApBJ1C,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAkBM,OAlBN2C,WAkBM,I,kBAjBJpD,mBAAA,CAgBMgC,SAAA,QAAAC,WAAA,CAfqB9B,MAAA,CAAA8C,iBAAiB,EAAnCI,cAAc;yBADvBrD,mBAAA,CAgBM;MAdHoC,GAAG,EAAEiB,cAAc,CAACC,OAAO;MAC5BvD,KAAK,EAAC,qBAAqB;MAC1BqB,OAAK,EAAAkB,MAAA,IAAEnC,MAAA,CAAAoC,WAAW,CAACc,cAAc,CAACC,OAAO;QAE1C7C,mBAAA,CAMM,OANN8C,WAMM,GALJ9C,mBAAA,CAA2D;MAAvDV,KAAK,EAAAY,eAAA,CAAER,MAAA,CAAAsC,kBAAkB,CAACY,cAAc,CAACC,OAAO;6BACpD7C,mBAAA,CAGM,cAFJA,mBAAA,CAAsE,OAAtE+C,WAAsE,EAAA1C,gBAAA,CAAlCuC,cAAc,CAAClB,KAAK,CAACnB,IAAI,kBAC7DP,mBAAA,CAAuE,OAAvEgD,WAAuE,EAArC,OAAK,GAAA3C,gBAAA,CAAGuC,cAAc,CAACK,KAAK,iB,+BAGlEjD,mBAAA,CAES;MAFDV,KAAK,EAAC;IAAW,IACvBU,mBAAA,CAA4B;MAAzBV,KAAK,EAAC;IAAc,G;kHAOjCM,mBAAA,YAAe,EACfI,mBAAA,CA+PM;IA/PDV,KAAK,EAAAY,eAAA,EAAC,eAAe,EAASR,MAAA,CAAAY,YAAY,CAAC4C,MAAM;MACpDC,WAAA,CA6POC,IAAA,CAAAC,MAAA;IA7PA3B,KAAK,EAAEhC,MAAA,CAAAY,YAAY;IAAG4C,MAAM,EAAExD,MAAA,CAAAY,YAAY,CAAC4C;KAAlD,MA6PO,CA5PLtD,mBAAA,YAAe,EACfI,mBAAA,CA0PM;IA1PAV,KAAK,EAAAY,eAAA,4BAA6BR,MAAA,CAAAY,YAAY,CAAC4C,MAAM;MACzDtD,mBAAA,wBAA2B,EAChBF,MAAA,CAAAY,YAAY,CAAC4C,MAAM,iB,cAA9B3D,mBAAA,CAkDM,OAlDN+D,WAkDM,GAjDJ1D,mBAAA,cAAiB,EACjBI,mBAAA,CAMM,OANNuD,WAMM,GALJC,YAAA,CAIEC,sBAAA;IAHC/B,KAAK,EAAEhC,MAAA,CAAAY,YAAY;IACpB,WAAS,EAAC,eAAe;IACxB,cAAY,EAAET,MAAA,CAAA6D;wDAInB9D,mBAAA,gBAAmB,EACnBI,mBAAA,CAUM,OAVN2D,WAUM,GARIjE,MAAA,CAAAY,YAAY,CAACsD,KAAK,CAACC,QAAQ,oB,cADnCC,YAAA,CAQEC,2BAAA;;IANCC,QAAQ,EAAE;MAAAC,CAAA;MAAAC,CAAA;IAAA,CAAc;IACxBC,KAAK,EAAE,OAAO;IACd,cAAY,EAAEtE,MAAA,CAAA6D,WAAW;IACzBU,WAAU,EAAE1E,MAAA,CAAA2E,eAAe;IAC3BC,aAAY,EAAE5E,MAAA,CAAA6E,0BAA0B;IACxCC,iBAAgB,EAAE9E,MAAA,CAAA+E;yIAIvB7E,mBAAA,gBAAmB,EACnBI,mBAAA,CAYM,OAZN0E,WAYM,GAVIhF,MAAA,CAAAY,YAAY,CAACsD,KAAK,CAACC,QAAQ,a,cADnCC,YAAA,CAUEa,sBAAA;;IARCX,QAAQ,EAAE;MAAAC,CAAA;MAAAC,CAAA;IAAA,CAAc;IACxBU,IAAI,EAAE,QAAQ;IACdT,KAAK,EAAE,OAAO;IACd,cAAY,EAAEtE,MAAA,CAAA6D,WAAW;IACzBmB,eAAc,EAAEnF,MAAA,CAAAoF,mBAAmB;IACnCC,gBAAe,EAAErF,MAAA,CAAAsF,oBAAoB;IACrCC,kBAAiB,EAAEvF,MAAA,CAAAwF,sBAAsB;IACzCC,iBAAgB,EAAEzF,MAAA,CAAA0F;sKAIvBxF,mBAAA,eAAkB,EAClBI,mBAAA,CAUM,OAVNqF,WAUM,GATJ7B,YAAA,CAQE8B,0BAAA;IAPCV,IAAI,EAAE,OAAO;IACbZ,QAAQ,EAAE;MAAAC,CAAA;MAAAC,CAAA;IAAA,CAAe;IACzBC,KAAK,EAAE,OAAO;IACd,cAAY,EAAEtE,MAAA,CAAA6D,WAAW;IACzB6B,aAAY,EAAE7F,MAAA,CAAA8F,cAAc;IAC5BlB,aAAY,EAAE5E,MAAA,CAAA+F,oBAAoB;IAClCC,kBAAiB,EAAEhG,MAAA,CAAAiG;2GAKVjG,MAAA,CAAAY,YAAY,CAAC4C,MAAM,gB,cAAnC3D,mBAAA,CAqFM,OArFNqG,WAqFM,GApFJhG,mBAAA,cAAiB,EACjBI,mBAAA,CAKM,OALN6F,WAKM,GAJJrC,YAAA,CAGEC,sBAAA;IAFC/B,KAAK,EAAEhC,MAAA,CAAAY,YAAY;IACpB,WAAS,EAAC;wCAIdV,mBAAA,gBAAmB,EACnBI,mBAAA,CAUM,OAVN8F,WAUM,GARIpG,MAAA,CAAAY,YAAY,CAACsD,KAAK,CAACC,QAAQ,a,cADnCC,YAAA,CAQEiC,2BAAA;;IANC/B,QAAQ,EAAE;MAAAC,CAAA;MAAAC,CAAA;IAAA,CAAc;IACxBC,KAAK,EAAE,OAAO;IACd,cAAY,EAAEtE,MAAA,CAAA6D,WAAW;IACzBU,WAAU,EAAE1E,MAAA,CAAA2E,eAAe;IAC3B2B,aAAY,EAAEtG,MAAA,CAAAuG,iBAAiB;IAC/BC,kBAAkB,EAAExG,MAAA,CAAAyG;0IAIzBvG,mBAAA,kBAAqB,EACrBI,mBAAA,CAWM,OAXNoG,WAWM,GATI1G,MAAA,CAAAY,YAAY,CAACsD,KAAK,CAACC,QAAQ,gB,cADnCC,YAAA,CASEuC,kCAAA;;IAPCrC,QAAQ,EAAE;MAAAC,CAAA;MAAAC,CAAA;IAAA,CAAc;IACxBU,IAAI,EAAE,QAAQ;IACdT,KAAK,EAAE,OAAO;IACd,cAAY,EAAEtE,MAAA,CAAA6D,WAAW;IACzB4C,iBAAgB,EAAE5G,MAAA,CAAA6G,qBAAqB;IACvCC,aAAY,EAAE9G,MAAA,CAAA+G,iBAAiB;IAC/BC,qBAAoB,EAAEhH,MAAA,CAAAiH;mJAI3B/G,mBAAA,kBAAqB,EACrBI,mBAAA,CAWM,OAXN4G,WAWM,GATIlH,MAAA,CAAAY,YAAY,CAACsD,KAAK,CAACC,QAAQ,a,cADnCC,YAAA,CASE+C,sBAAA;;IAPC7C,QAAQ,EAAE;MAAAC,CAAA;MAAAC,CAAA;IAAA,CAAc;IACxBU,IAAI,EAAE,OAAO;IACbT,KAAK,EAAE,OAAO;IACd,cAAY,EAAEtE,MAAA,CAAA6D,WAAW;IACzBoD,aAAY,EAAEpH,MAAA,CAAAqH,iBAAiB;IAC/BC,cAAa,EAAEtH,MAAA,CAAAuH,kBAAkB;IACjCC,wBAAuB,EAAExH,MAAA,CAAAyH;mJAI9BvH,mBAAA,kBAAqB,EACrBI,mBAAA,CAWM,OAXNoH,WAWM,GATI1H,MAAA,CAAAY,YAAY,CAACsD,KAAK,CAACC,QAAQ,iB,cADnCC,YAAA,CASEuD,+BAAA;;IAPCrD,QAAQ,EAAE;MAAAC,CAAA;MAAAC,CAAA;IAAA,CAAe;IACzBU,IAAI,EAAE,OAAO;IACbT,KAAK,EAAE,OAAO;IACd,cAAY,EAAEtE,MAAA,CAAA6D,WAAW;IACzB4D,kBAAiB,EAAE5H,MAAA,CAAA6H,sBAAsB;IACzCC,kBAAkB,EAAE9H,MAAA,CAAA+H,sBAAsB;IAC1CC,mBAAkB,EAAEhI,MAAA,CAAAiI;uJAIzB/H,mBAAA,cAAiB,EACjBI,mBAAA,CAWM,OAXN4H,WAWM,GATIlI,MAAA,CAAAY,YAAY,CAACsD,KAAK,CAACC,QAAQ,kB,cADnCC,YAAA,CASE+D,yBAAA;;IAPC7D,QAAQ,EAAE;MAAAC,CAAA;MAAAC,CAAA;IAAA,CAAe;IACzBU,IAAI,EAAE,OAAO;IACbT,KAAK,EAAE,OAAO;IACd,cAAY,EAAEtE,MAAA,CAAA6D,WAAW;IACzBoE,mBAAkB,EAAEpI,MAAA,CAAAqI,uBAAuB;IAC3CC,cAAa,EAAEtI,MAAA,CAAAuI,kBAAkB;IACjCC,mBAAkB,EAAExI,MAAA,CAAAyI;oJAIzBvI,mBAAA,eAAkB,EAClBI,mBAAA,CAKM,OALNoI,WAKM,GAJJ5E,YAAA,CAGEC,sBAAA;IAFC/B,KAAK,EAAEhC,MAAA,CAAAY,YAAY;IACpB,WAAS,EAAC;4CAMAZ,MAAA,CAAAY,YAAY,CAAC4C,MAAM,oB,cAAnC3D,mBAAA,CAEMgC,SAAA;IAAAI,GAAA;EAAA,IAHN/B,mBAAA,eAAkB,EAClBI,mBAAA,CAEM,OAFNqI,WAEM,GADJ7E,YAAA,CAA+E8E,sCAAA;IAAjDC,sBAAsB,EAAE7I,MAAA,CAAA8I;EAAqB,oD,sDAG7D9I,MAAA,CAAAY,YAAY,CAAC4C,MAAM,wB,cAAnC3D,mBAAA,CAyCM,OAzCNkJ,WAyCM,GAxCJ7I,mBAAA,eAAkB,EAClBI,mBAAA,CASM,OATN0I,WASM,GARJ1I,mBAAA,CAOM,OAPN2I,WAOM,GALIjJ,MAAA,CAAAY,YAAY,CAACsD,KAAK,CAACC,QAAQ,mB,cADnCC,YAAA,CAKEL,sBAAA;;IAHC/B,KAAK,EAAEhC,MAAA,CAAAY,YAAY;IACpB,WAAS,EAAC,aAAa;IACtB,cAAY,EAAET,MAAA,CAAA6D;+FAKrB9D,mBAAA,WAAc,EACdI,mBAAA,CAOM,OAPN4I,WAOM,GALIlJ,MAAA,CAAAY,YAAY,CAACsD,KAAK,CAACC,QAAQ,iB,cADnCC,YAAA,CAKEL,sBAAA;;IAHC/B,KAAK,EAAEhC,MAAA,CAAAY,YAAY;IACpB,WAAS,EAAC,WAAW;IACpB,cAAY,EAAET,MAAA,CAAA6D;6FAInB1D,mBAAA,CAiBM,OAjBN6I,WAiBM,GAhBJ7I,mBAAA,CAOM,OAPN8I,WAOM,GALIpJ,MAAA,CAAAY,YAAY,CAACsD,KAAK,CAACC,QAAQ,Y,cADnCC,YAAA,CAKEL,sBAAA;;IAHC/B,KAAK,EAAEhC,MAAA,CAAAY,YAAY;IACpB,WAAS,EAAC,MAAM;IACf,cAAY,EAAET,MAAA,CAAA6D;6FAGnB1D,mBAAA,CAOM,OAPN+I,WAOM,GALIrJ,MAAA,CAAAY,YAAY,CAACsD,KAAK,CAACC,QAAQ,oB,cADnCC,YAAA,CAKEL,sBAAA;;IAHC/B,KAAK,EAAEhC,MAAA,CAAAY,YAAY;IACpB,WAAS,EAAC,cAAc;IACvB,cAAY,EAAET,MAAA,CAAA6D;mGAMPhE,MAAA,CAAAY,YAAY,CAAC4C,MAAM,kB,cAAnC3D,mBAAA,CAkCM,OAlCNyJ,WAkCM,GAjCJpJ,mBAAA,cAAiB,EACjBA,mBAAA,SAAY,EACZI,mBAAA,CAOM,OAPNiJ,WAOM,GALIvJ,MAAA,CAAAY,YAAY,CAACsD,KAAK,CAACC,QAAQ,kB,cADnCC,YAAA,CAKEL,sBAAA;;IAHC/B,KAAK,EAAEhC,MAAA,CAAAY,YAAY;IACpB,WAAS,EAAC,eAAe;IACxB,cAAY,EAAET,MAAA,CAAA6D;6FAInB9D,mBAAA,YAAe,EACfI,mBAAA,CAoBM,OApBNkJ,WAoBM,GAnBJtJ,mBAAA,iBAAoB,EACpBI,mBAAA,CAOM,OAPNmJ,WAOM,GALIzJ,MAAA,CAAAY,YAAY,CAACsD,KAAK,CAACC,QAAQ,iB,cADnCC,YAAA,CAKEL,sBAAA;;IAHC/B,KAAK,EAAEhC,MAAA,CAAAY,YAAY;IACpB,WAAS,EAAC,WAAW;IACpB,cAAY,EAAET,MAAA,CAAA6D;6FAInB9D,mBAAA,gBAAmB,EACnBI,mBAAA,CAOM,OAPNoJ,WAOM,GALI1J,MAAA,CAAAY,YAAY,CAACsD,KAAK,CAACC,QAAQ,a,cADnCC,YAAA,CAKEL,sBAAA;;IAHC/B,KAAK,EAAEhC,MAAA,CAAAY,YAAY;IACpB,WAAS,EAAC,OAAO;IAChB,cAAY,EAAET,MAAA,CAAA6D;kHAMvBnE,mBAAA,CAwBM,OAxBN8J,WAwBM,GAvBJzJ,mBAAA,YAAe,EACfI,mBAAA,CAqBM,OArBNsJ,WAqBM,I,kBApBJ/J,mBAAA,CAmBMgC,SAAA,QAAAC,WAAA,CAnBuB9B,MAAA,CAAAY,YAAY,CAACsD,KAAK,GAAlC2F,IAAI,EAAEC,KAAK;yBAAxBjK,mBAAA,CAmBM;MAnB4CoC,GAAG,EAAE6H,KAAK;MAAElK,KAAK,EAAC;QAClEM,mBAAA,iBAAoB,EAEZ2J,IAAI,oB,cADZzF,YAAA,CASEwB,0BAAA;;MAPCV,IAAI,EAAE,OAAO;MACbZ,QAAQ,EAAE;QAAAC,CAAA;QAAAC,CAAA;MAAA,CAAc;MACxBC,KAAK,EAAE,OAAO;MACd,cAAY,EAAEtE,MAAA,CAAA6D,WAAW;MACzB6B,aAAY,EAAE7F,MAAA,CAAA8F,cAAc;MAC5BlB,aAAY,EAAE5E,MAAA,CAAA+F,oBAAoB;MAClCC,kBAAiB,EAAEhG,MAAA,CAAAiG;wHAGtBpG,mBAAA,CAKEgC,SAAA;MAAAI,GAAA;IAAA,IANF/B,mBAAA,uBAA0B,EAC1B4D,YAAA,CAKEC,sBAAA;MAHC/B,KAAK,EAAEhC,MAAA,CAAAY,YAAY;MACnB,WAAS,EAAEiJ,IAAI;MACf,cAAY,EAAE1J,MAAA,CAAA6D;;iFAS7B9D,mBAAA,YAAe,EACf4D,YAAA,CAOaiG,WAAA;IAPDlJ,IAAI,EAAC;EAAkB;sBACjC,MAKM,CALKb,MAAA,CAAAgK,eAAe,I,cAA1BnK,mBAAA,CAKM,OALNoK,WAKM,EAAA/I,MAAA,SAAAA,MAAA,QAJJZ,mBAAA,CAGM;MAHDV,KAAK,EAAC;IAAoB,IAC7BU,mBAAA,CAAsC;MAAnCV,KAAK,EAAC;IAAwB,IACjCU,mBAAA,CAAsB,cAAhB,WAAS,E;;MAKrBJ,mBAAA,aAAgB,EAChB4D,YAAA,CAGEoG,kCAAA;IAFCC,sBAAsB,EAAEnK,MAAA,CAAAoK,sBAAsB;IAC9CvB,sBAAsB,EAAE7I,MAAA,CAAA8I", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}