# AI-HMI 场景生成服务 API 接口文档

- **文档版本:** 1.0
- **状态:** 正式版
- **负责人:** Gemini Agent

---

## 1. 概述 (Overview)

本接口是 AI-HMI 系统的核心,负责接收用户的自然语言场景描述,并返回一个完整的、可直接用于前端渲染的UI布局JSON对象。该过程由后台的AI Agent调用大语言模型(LLM)并结合本地知识库完成。

## 2. 接口定义 (API Definition)

### **端点 (Endpoint)**

```
POST /api/v1/scene/generate
```

- **方法:** `POST`
- **描述:** 根据用户提供的场景文本,动态生成一个完整的HMI界面布局方案。

---

## 3. 请求格式 (Request Format)

- **Content-Type:** `application/json`

### **请求体 (Request Body)**

```json
{
  "scene_text": "用户的自然语言场景描述",
  "user_preferences": {
    "preferred_theme": "dark",
    "language": "zh-CN"
  }
}
```

### **请求体字段说明**

| 字段名 | 类型 | 是否必须 | 描述 |
| :--- | :--- | :--- | :--- |
| `scene_text` | `string` | **是** | 用户的自然语言输入,例如: "下班开车回家,外面在下雨,想听点轻松的音乐"。 |
| `user_preferences` | `object` | 否 | (可选) 用户的个人偏好设置,用于未来功能扩展。 |
| `preferred_theme` | `string` | 否 | 用户偏好的主题模式,如 `dark` 或 `light`。 |
| `language` | `string` | 否 | 用户偏好的语言,如 `zh-CN` 或 `en-US`。 |

---

## 4. 响应格式 (Response Format)

### **4.1. 成功响应 (Success Response)**

- **HTTP Status Code:** `200 OK`
- **Content-Type:** `application/json`

#### **响应体 (Response Body)**

```json
{
  "scene_analysis": { ... },
  "design_system": { ... },
  "background": { ... },
  "layout": { ... }
}
```

#### **响应体字段详细说明**

| 字段路径 | 类型 | 描述 |
| :--- | :--- | :--- |
| **`scene_analysis`** | `object` | 对用户输入场景的解析结果。 |
| `scene_analysis.source_text` | `string` | 用户输入的原始文本。 |
| `scene_analysis.tags` | `object` | 从文本中提取的标准化标签,以键值对形式存在。 |
| **`design_system`** | `object` | 所选用的设计系统信息。 |
| `design_system.style_theme` | `string` | 从样式知识库中选出的主题名称,例如 `theme-dark-blue`。 |
| **`background`** | `object` | 背景/壁纸的生成信息。 |
| `background.prompt` | `string` | 用于AI文生图模型的英文提示词(Prompt)。 |
| **`layout`** | `object` | 核心的布局方案。 |
| `layout.template_name` | `string` | 本次布局所采用的布局模板名称,例如 `focus_left_sidebar_right`。 |
| `layout.grid_template_columns`| `string` | CSS网格布局的列定义,固定为 `"repeat(16, 1fr)"`。 |
| `layout.grid_template_rows` | `string` | CSS网格布局的行定义,固定为 `"repeat(9, 1fr)"`。 |
| `layout.components` | `array` | 包含所有被布局的组件对象的数组。 |

#### **`components` 数组中每个对象的结构**

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `component_name` | `string` | 组件的唯一名称,与前端组件名对应,例如 `NavigationDetailCard`。 |
| `grid_area` | `string` | 该组件在16x9网格中的位置和大小,格式为 `row-start / col-start / row-end / col-end`。 |
| `content` | `object` | 为该组件动态生成的文本和数据内容。其内部结构因组件类型而异。 |

--- 

### **4.2. 失败响应 (Error Response)**

在请求失败或处理异常时,API将返回一个标准的错误信息对象。

- **HTTP Status Code:** `400 Bad Request` / `500 Internal Server Error`
- **Content-Type:** `application/json`

#### **响应体 (Response Body)**

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "An error description."
  }
}
```

#### **错误码 (Error Codes) 示例**

| `code` | `message` | HTTP Status | 描述 |
| :--- | :--- | :--- | :--- |
| `INVALID_REQUEST` | `scene_text field is required.` | 400 | 请求体验证失败,缺少必要字段。 |
| `LLM_UNAVAILABLE` | `The language model service is currently unavailable.` | 503 | 无法连接到下游的大语言模型服务。 |
| `INTERNAL_ERROR` | `An unexpected error occurred while processing the scene.` | 500 | 服务器内部发生未知错误。 |

---

## 5. 附录: 完整成功响应示例

```json
{
  "scene_analysis": {
    "source_text": "下班开车回家,外面在下雨,想听点轻松的音乐",
    "tags": {
      "activity": "driving",
      "event": "commuting",
      "weather": "rainy",
      "media": "music",
      "mood": "relaxing"
    }
  },
  "design_system": {
    "style_theme": "theme-dark-blue"
  },
  "background": {
    "prompt": "A photorealistic wallpaper of a car's dashboard, looking through a rain-streaked windshield at blurry city night lights, creating a melancholic and lonely atmosphere, cinematic lighting, 8k."
  },
  "layout": {
    "template_name": "focus_left_sidebar_right",
    "grid_template_columns": "repeat(16, 1fr)",
    "grid_template_rows": "repeat(9, 1fr)",
    "components": [
      {
        "component_name": "NavigationDetailCard",
        "grid_area": "1 / 1 / 10 / 9",
        "content": {
          "destination": "家",
          "eta_minutes": 22,
          "distance_km": 7.8,
          "current_road": "科技大道",
          "next_turn": "前方800米后右转"
        }
      },
      {
        "component_name": "WeatherCard",
        "grid_area": "1 / 9 / 5 / 17",
        "content": {
          "status": "中雨",
          "temperature_celsius": 19
        }
      },
      {
        "component_name": "MusicControlCard",
        "grid_area": "5 / 9 / 10 / 17",
        "content": {
          "title": "雨夜的浪漫",
          "artist": "Lo-fi Beats Radio"
        }
      }
    ]
  }
}
```