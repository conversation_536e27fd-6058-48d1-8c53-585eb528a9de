<template>
  <div class="glass-card" :style="cardStyles">
    <div class="card-header">
      <i :class="icon" class="card-icon"></i>
      <h3 class="card-title">{{ title }}</h3>
    </div>
    <div class="card-content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'GlassC<PERSON>',
  props: {
    title: {
      type: String,
      required: true
    },
    icon: {
      type: String,
      default: 'fas fa-cube'
    },
    style: {
      type: Object,
      default: () => ({})
    },
    themeColors: {
      type: Object,
      default: null
    }
  },

  setup(props) {
    const cardStyles = computed(() => {
      // 使用主题颜色或默认值
      const colors = props.themeColors || {
        glassBackground: 'rgba(255, 255, 255, 0.15)',
        glassBorder: 'rgba(255, 255, 255, 0.2)',
        text: '#ffffff',
        cardTitleColor: '#ffffff',
        cardContentColor: '#ecf0f1',
        buttonBackground: 'rgba(74, 144, 226, 0.8)',
        buttonColor: '#FFFFFF',
        buttonBorder: 'rgba(255, 255, 255, 0.6)',
        buttonHoverBackground: 'rgba(104, 174, 256, 0.9)',
        buttonTextShadow: '0 1px 2px rgba(0, 0, 0, 0.8)',
        backgroundBrightness: 128
      }

      // 检查是否有AI增强的智能样式
      const useIntelligentStyles = colors.isAIEnhanced && colors.intelligentCardStyles

      // 根据背景亮度调整阴影
      const shadowIntensity = colors.backgroundBrightness > 150 ? 0.2 : 0.1
      const textShadowColor = colors.backgroundBrightness > 150 ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.8)'

      // 如果有AI智能样式，优先使用
      if (useIntelligentStyles) {
        const intelligentStyles = colors.intelligentCardStyles
        const intelligentTextColors = colors.intelligentTextColors || {}
        const intelligentButtonStyles = colors.intelligentButtonStyles || {}

        return {
          // AI智能样式
          borderRadius: intelligentStyles.borderRadius || props.style.borderRadius || '16px',
          backdropFilter: intelligentStyles.backdropFilter || props.style.backdropFilter || 'blur(12px)',
          background: intelligentStyles.background || props.style.background || colors.glassBackground,
          border: intelligentStyles.border || props.style.border || `1px solid ${colors.glassBorder}`,
          boxShadow: intelligentStyles.boxShadow || props.style.boxShadow || `0 8px 32px rgba(0, 0, 0, ${shadowIntensity})`,
          color: intelligentTextColors.content || colors.cardContentColor || colors.text,
          // AI增强的CSS变量
          '--button-bg': intelligentButtonStyles.background || colors.buttonBackground,
          '--button-color': intelligentButtonStyles.color || colors.buttonColor,
          '--button-border': intelligentButtonStyles.border || colors.buttonBorder,
          '--button-hover-bg': intelligentButtonStyles.hoverBackground || colors.buttonHoverBackground,
          '--button-text-shadow': intelligentButtonStyles.textShadow || colors.buttonTextShadow,
          '--card-title-color': intelligentTextColors.title || colors.cardTitleColor || colors.text,
          '--card-content-color': intelligentTextColors.content || colors.cardContentColor || colors.text,
          '--text-shadow': intelligentTextColors.textShadow || `0 1px 2px ${textShadowColor}`,
          // AI分析信息
          '--ai-mood': colors.aiAnalysis?.mood || 'default',
          '--ai-brightness': colors.aiAnalysis?.brightness || 'medium'
        }
      }

      // 传统样式（降级方案）
      return {
        borderRadius: props.style.borderRadius || '16px',
        backdropFilter: props.style.backdropFilter || 'blur(12px)',
        background: props.style.background || colors.glassBackground,
        border: props.style.border || `1px solid ${colors.glassBorder}`,
        boxShadow: props.style.boxShadow || `0 8px 32px rgba(0, 0, 0, ${shadowIntensity})`,
        color: colors.cardContentColor || colors.text,
        // 传统CSS变量
        '--button-bg': colors.buttonBackground,
        '--button-color': colors.buttonColor,
        '--button-border': colors.buttonBorder,
        '--button-hover-bg': colors.buttonHoverBackground,
        '--button-text-shadow': colors.buttonTextShadow,
        '--card-title-color': colors.cardTitleColor || colors.text,
        '--card-content-color': colors.cardContentColor || colors.text,
        '--text-shadow': `0 1px 2px ${textShadowColor}`
      }
    })

    return {
      cardStyles
    }
  }
}
</script>

<style scoped>
.glass-card {
  padding: 20px;
  color: white;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.glass-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-icon {
  font-size: 18px;
  opacity: 0.8;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: var(--card-title-color, #ffffff);
  text-shadow: var(--text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));
}

.card-content {
  flex: 1;
  color: var(--card-content-color, #ecf0f1);
  text-shadow: var(--text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));
}

/* 玻璃态效果增强 */
.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.3), 
    transparent
  );
}

.glass-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 1px;
  background: linear-gradient(180deg, 
    transparent, 
    rgba(255, 255, 255, 0.3), 
    transparent
  );
}
</style>