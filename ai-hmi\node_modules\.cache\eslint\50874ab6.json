[{"D:\\code\\pythonWork\\theme\\ai-hmi\\src\\main.js": "1", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\App.vue": "2", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\TestSceneGeneration.vue": "3", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\SceneManager.vue": "4", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\DynamicWallpaperManager.vue": "5", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\ImmersiveWallpaperInterface.vue": "6", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\VoiceInteractionManager.vue": "7", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\DefaultCard.vue": "8", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\AsrService.js": "9", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\ImageGenerationService.js": "10", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\LlmService.js": "11", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\EmotionalPromptGenerator.js": "12", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\utils\\SceneManager.js": "13", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\DynamicWallpaperService.js": "14", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\utils\\AIColorAnalyzer.js": "15", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\utils\\ColorExtractor.js": "16", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\SceneContextManager.js": "17", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\GlassCard.vue": "18", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\TtsService.js": "19", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\KidEducationCard.vue": "20", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\MusicControlCard.vue": "21", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\vpa\\VPAAvatarWidget.vue": "22", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\BaseCard.vue": "23", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\store\\modules\\vpa.js": "24", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\store\\index.js": "25", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\store\\modules\\layout.js": "26", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\SmartHomeControlCard.vue": "27", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\NavigationCard.vue": "28", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\AIOrderCard.vue": "29", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\AIPediaCard.vue": "30", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\AIScheduleAssistantCard.vue": "31", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\MockDataService.js": "32", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\BaseCard.vue": "33"}, {"size": 195, "mtime": 1754277400431, "results": "34", "hashOfConfig": "35"}, {"size": 5387, "mtime": 1754299983496, "results": "36", "hashOfConfig": "35"}, {"size": 12769, "mtime": 1754277149555, "results": "37", "hashOfConfig": "35"}, {"size": 50153, "mtime": 1754301213049, "results": "38", "hashOfConfig": "35"}, {"size": 20551, "mtime": 1754277149554, "results": "39", "hashOfConfig": "35"}, {"size": 6604, "mtime": 1754301115439, "results": "40", "hashOfConfig": "35"}, {"size": 12055, "mtime": 1754277149555, "results": "41", "hashOfConfig": "35"}, {"size": 90134, "mtime": 1754301197537, "results": "42", "hashOfConfig": "35"}, {"size": 1683, "mtime": 1754277149558, "results": "43", "hashOfConfig": "35"}, {"size": 9603, "mtime": 1754280503786, "results": "44", "hashOfConfig": "35"}, {"size": 11733, "mtime": 1754299072225, "results": "45", "hashOfConfig": "35"}, {"size": 12006, "mtime": 1754277149559, "results": "46", "hashOfConfig": "35"}, {"size": 14637, "mtime": 1754300967845, "results": "47", "hashOfConfig": "35"}, {"size": 8519, "mtime": 1754277149559, "results": "48", "hashOfConfig": "35"}, {"size": 12102, "mtime": 1754277149562, "results": "49", "hashOfConfig": "35"}, {"size": 15105, "mtime": 1754277149562, "results": "50", "hashOfConfig": "35"}, {"size": 15166, "mtime": 1754277149560, "results": "51", "hashOfConfig": "35"}, {"size": 6069, "mtime": 1754277149554, "results": "52", "hashOfConfig": "35"}, {"size": 4501, "mtime": 1754277149560, "results": "53", "hashOfConfig": "35"}, {"size": 14226, "mtime": 1754277400429, "results": "54", "hashOfConfig": "35"}, {"size": 16128, "mtime": 1754277400429, "results": "55", "hashOfConfig": "35"}, {"size": 11618, "mtime": 1754293638362, "results": "56", "hashOfConfig": "35"}, {"size": 8359, "mtime": 1754277400428, "results": "57", "hashOfConfig": "35"}, {"size": 6880, "mtime": 1754277400432, "results": "58", "hashOfConfig": "35"}, {"size": 320, "mtime": 1754277400431, "results": "59", "hashOfConfig": "35"}, {"size": 5303, "mtime": 1754277400431, "results": "60", "hashOfConfig": "35"}, {"size": 29929, "mtime": 1754300803729, "results": "61", "hashOfConfig": "35"}, {"size": 31785, "mtime": 1754300803729, "results": "62", "hashOfConfig": "35"}, {"size": 33416, "mtime": 1754300803872, "results": "63", "hashOfConfig": "35"}, {"size": 34769, "mtime": 1754300803882, "results": "64", "hashOfConfig": "35"}, {"size": 19371, "mtime": 1754300803824, "results": "65", "hashOfConfig": "35"}, {"size": 17651, "mtime": 1754300803882, "results": "66", "hashOfConfig": "35"}, {"size": 5884, "mtime": 1754300803729, "results": "67", "hashOfConfig": "35"}, {"filePath": "68", "messages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, "17wmy<PERSON>", {"filePath": "71", "messages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "76", "messages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "80", "messages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "84", "messages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "88", "messages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "90", "messages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "92", "messages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "94", "messages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "98", "messages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "100", "messages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "102", "messages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "104", "messages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "106", "messages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "108", "messages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "110", "messages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "112", "messages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "114", "messages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "116", "messages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "118", "messages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "120", "messages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "122", "messages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "124", "messages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "126", "messages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "128", "messages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "130", "messages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "132", "messages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "134", "messages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\main.js", [], [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\App.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\TestSceneGeneration.vue", [], [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\SceneManager.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\DynamicWallpaperManager.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\ImmersiveWallpaperInterface.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\VoiceInteractionManager.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\DefaultCard.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\AsrService.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\ImageGenerationService.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\LlmService.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\EmotionalPromptGenerator.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\utils\\SceneManager.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\DynamicWallpaperService.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\utils\\AIColorAnalyzer.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\utils\\ColorExtractor.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\SceneContextManager.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\GlassCard.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\TtsService.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\KidEducationCard.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\MusicControlCard.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\vpa\\VPAAvatarWidget.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\BaseCard.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\store\\modules\\vpa.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\store\\index.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\store\\modules\\layout.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\SmartHomeControlCard.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\NavigationCard.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\AIOrderCard.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\AIPediaCard.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\AIScheduleAssistantCard.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\MockDataService.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\BaseCard.vue", []]