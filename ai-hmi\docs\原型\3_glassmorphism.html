<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI HMI - Glassmorphism Theme</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            overflow: hidden;
            font-family: 'Inter', sans-serif;
        }
        .grid-container {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            grid-template-rows: repeat(4, 1fr);
            gap: 20px;
            width: 100vw;
            height: 100vh;
            padding: 20px;
            box-sizing: border-box;
        }
        .card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            transition: all 0.3s ease;
        }
        .card:hover {
            background: rgba(255, 255, 255, 0.25);
            box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.45);
        }
        .vpa-container { grid-column: span 2; grid-row: span 4; }
        .island-container { grid-column: 3 / span 4; grid-row: 1; display: flex; align-items: center; justify-content: center; }
        .small-card-container { grid-column: 3 / span 4; grid-row: 2; display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; }
        .large-card-container { grid-column: 7 / span 2; grid-row: span 4; }
        .temp-interaction-container { position: absolute; bottom: 40px; left: 50%; transform: translateX(-50%); z-index: 50; }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
</head>
<body class="bg-cover bg-center bg-no-repeat" style="background-image: url('https://images.unsplash.com/photo-1557682250-33bd709cbe85?q=80&w=2670&auto=format&fit=crop');">
    <div class="grid-container">
        <!-- VPA 数字人 -->
        <div class="vpa-container card flex flex-col items-center justify-center p-6 text-white">
            <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=2487&auto=format&fit=crop" alt="VPA Avatar" class="w-36 h-36 rounded-full border-4 border-white/50 shadow-lg mb-5 object-cover">
            <h2 class="text-2xl font-bold">Aura</h2>
            <p class="text-white/80 text-center mt-2">Good evening. How may I assist you?</p>
        </div>

        <!-- 灵动岛 -->
        <div class="island-container card p-3">
            <div class="flex items-center space-x-4 text-white">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                <div>
                    <p class="font-semibold">AI News Update</p>
                    <p class="text-sm text-white/70">New breakthrough in neural networks.</p>
                </div>
            </div>
        </div>

        <!-- 横向小卡片 -->
        <div class="small-card-container">
            <div class="card p-5 flex flex-col justify-between text-white">
                <h3 class="font-bold text-lg">Traffic</h3>
                <div class="text-center">
                    <p class="text-4xl font-bold">25 <span class="text-lg">min</span></p>
                    <p class="text-white/70">Clear roads ahead</p>
                </div>
            </div>
            <div class="card p-5 flex flex-col justify-between text-white">
                <h3 class="font-bold text-lg">Calendar</h3>
                <div class="text-center">
                    <p class="text-xl font-semibold">Dinner with Alex</p>
                    <p class="text-white/70">7:30 PM</p>
                </div>
            </div>
        </div>

        <!-- 纵向大卡片 -->
        <div class="large-card-container card p-5 flex flex-col text-white">
            <h3 class="font-bold text-lg mb-3">My Playlist</h3>
            <div class="flex-grow space-y-3 overflow-y-auto">
                <div class="flex items-center space-x-3 p-2 rounded-lg hover:bg-white/10">
                    <img src="https://images.unsplash.com/photo-1511379938547-c1f69419868d?q=80&w=2670&auto=format&fit=crop" class="w-12 h-12 rounded-md object-cover">
                    <div><p class="font-semibold">Starlight</p><p class="text-sm text-white/70">Muse</p></div>
                </div>
                <div class="flex items-center space-x-3 p-2 rounded-lg hover:bg-white/10">
                    <img src="https://images.unsplash.com/photo-1470225620780-dba8ba36b745?q=80&w=2670&auto=format&fit=crop" class="w-12 h-12 rounded-md object-cover">
                    <div><p class="font-semibold">Midnight City</p><p class="text-sm text-white/70">M83</p></div>
                </div>
            </div>
        </div>

        <!-- 临时交互组件 -->
        <div class="temp-interaction-container card p-4 flex items-center space-x-4 text-white">
            <p class="font-semibold">System Alert</p>
            <p class="text-white/70">Low tire pressure detected.</p>
            <button class="px-4 py-2 bg-blue-500/50 text-white rounded-lg border border-blue-300 hover:bg-blue-500">View Details</button>
        </div>

        <div style="grid-column: 3 / span 4; grid-row: 3 / span 2;"></div>

    </div>
</body>
</html>