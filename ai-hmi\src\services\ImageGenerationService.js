import LlmService from './LlmService'

class ImageGenerationService {
  constructor() {
    this.apiBaseUrl = process.env.VUE_APP_API_BASE_URL || 'http://localhost:8000'
    console.log('ImageGenerationService initialized with API URL:', this.apiBaseUrl)
  }

  async generateWallpaper(prompt, taskId = null) {
    if (!taskId) {
      taskId = `wallpaper_${Date.now()}`
    }

    try {
      console.log('Starting wallpaper generation with prompt:', prompt)
      
      // 使用LLM增强提示词
      const llmService = new LlmService()
      console.log('Calling LLM service to enhance prompt...')
      const enhancedPrompt = await llmService.generateResponse(prompt)
      console.log('Enhanced prompt:', enhancedPrompt)
      
      // 调用简化版的文生图接口（用于AI-HMI测试）
      const apiUrl = `${this.apiBaseUrl}/api/v1/kolors-simple/text-to-image`
      console.log('Calling wallpaper generation API:', apiUrl)
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          prompt: enhancedPrompt, 
          task_id: taskId
        })
      })

      console.log('API response status:', response.status)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      console.log('API response data:', data)

      // 下载并保存图片到本地
      const localImagePath = await this.saveImageToLocal(data.image_url, taskId)

      const result = {
        imageUrl: data.image_url, // 保存Kolors生成的图片URL
        imagePath: localImagePath, // 保存本地图片路径
        taskId: taskId,
        prompt: enhancedPrompt
      }

      // 保存当前壁纸路径到全局状态，供动态壁纸生成使用
      this.saveCurrentWallpaperPath(localImagePath, data.image_url)

      return result
    } catch (error) {
      console.error('壁纸生成失败:', error)
      return this.getFallbackWallpaper()
    }
  }

  getFallbackWallpaper() {
    return {
      imageUrl: '/images/default-glass-wallpaper.svg',
      taskId: 'fallback',
      prompt: 'default glassmorphism'
    }
  }

  // 检查图片是否加载成功
  async checkImageLoad(imageUrl) {
    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => resolve(true)
      img.onerror = () => resolve(false)
      img.src = imageUrl
    })
  }

  // 预加载图片
  async preloadImage(imageUrl) {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => resolve(img)
      img.onerror = () => reject(new Error('图片加载失败'))
      img.src = imageUrl
    })
  }

  // 批量生成壁纸
  async generateMultipleWallpapers(prompts, options = {}) {
    const { concurrent = 3 } = options
    const results = []
    
    // 分批处理以避免过多的并发请求
    for (let i = 0; i < prompts.length; i += concurrent) {
      const batch = prompts.slice(i, i + concurrent)
      const batchPromises = batch.map(async (prompt, index) => {
        const taskId = `wallpaper_${Date.now()}_${i + index}`
        return this.generateWallpaper(prompt, taskId)
      })
      
      const batchResults = await Promise.allSettled(batchPromises)
      results.push(...batchResults.map(result => 
        result.status === 'fulfilled' ? result.value : null
      ).filter(Boolean))
    }
    
    return results
  }

  // 获取生成历史
  getGenerationHistory() {
    const history = localStorage.getItem('wallpaper_generation_history')
    return history ? JSON.parse(history) : []
  }

  // 保存生成历史
  saveToHistory(wallpaperData) {
    const history = this.getGenerationHistory()
    history.unshift({
      ...wallpaperData,
      timestamp: new Date().toISOString()
    })
    
    // 只保留最近50条记录
    const trimmedHistory = history.slice(0, 50)
    localStorage.setItem('wallpaper_generation_history', JSON.stringify(trimmedHistory))
  }

  // 清除历史记录
  clearHistory() {
    localStorage.removeItem('wallpaper_generation_history')
  }

  // 获取统计信息
  getStatistics() {
    const history = this.getGenerationHistory()
    return {
      totalGenerated: history.length,
      todayGenerated: history.filter(item => {
        const today = new Date().toDateString()
        return new Date(item.timestamp).toDateString() === today
      }).length,
      successRate: history.filter(item => item.imageUrl && !item.imageUrl.includes('default')).length / history.length * 100
    }
  }

  // 保存图片到本地
  async saveImageToLocal(imageUrl, taskId) {
    try {
      console.log('开始保存图片到本地:', imageUrl)

      // 下载图片
      const response = await fetch(imageUrl)
      if (!response.ok) {
        throw new Error(`下载图片失败: ${response.status}`)
      }

      const blob = await response.blob()

      // 生成本地文件名
      const fileName = `wallpaper_${taskId || Date.now()}.jpg`
      const localPath = `/images/${fileName}`

      // 创建本地URL并保存到public/images目录
      const file = new File([blob], fileName, { type: blob.type })
      await this.saveFileToPublicImages(file, fileName)

      console.log('图片已保存到本地:', localPath)
      return localPath
    } catch (error) {
      console.error('保存图片到本地失败:', error)
      return null
    }
  }

  // 保存文件到public/images目录
  async saveFileToPublicImages(file, fileName) {
    try {
      // 使用FormData上传文件到后端保存
      const formData = new FormData()
      formData.append('file', file)
      formData.append('fileName', fileName)

      const response = await fetch(`${process.env.VUE_APP_API_BASE_URL || 'http://localhost:8000'}/api/save-image`, {
        method: 'POST',
        body: formData
      })

      if (response.ok) {
        const result = await response.json()
        console.log('文件已保存到服务器:', result.path)
        return result.path
      } else {
        // 如果后端保存失败，回退到浏览器下载方式
        console.warn('后端保存失败，使用浏览器下载方式')
        const url = URL.createObjectURL(file)
        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        console.log('文件已通过下载保存:', fileName)
        return `/images/${fileName}`
      }
    } catch (error) {
      console.error('保存文件失败:', error)
      // 回退到浏览器下载方式
      try {
        const url = URL.createObjectURL(file)
        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        console.log('文件已通过下载保存:', fileName)
        return `/images/${fileName}`
      } catch (downloadError) {
        console.error('下载保存也失败:', downloadError)
        throw error
      }
    }
  }

  // 保存当前壁纸路径
  saveCurrentWallpaperPath(imagePath, imageUrl) {
    try {
      const wallpaperInfo = {
        imagePath: imagePath,
        imageUrl: imageUrl,
        timestamp: new Date().toISOString()
      }

      // 保存到localStorage
      localStorage.setItem('currentWallpaperInfo', JSON.stringify(wallpaperInfo))

      console.log('当前壁纸信息已保存:', wallpaperInfo)
    } catch (error) {
      console.error('保存壁纸路径失败:', error)
    }
  }

  // 获取当前壁纸路径
  getCurrentWallpaperPath() {
    try {
      const wallpaperInfo = localStorage.getItem('currentWallpaperInfo')
      if (wallpaperInfo) {
        const info = JSON.parse(wallpaperInfo)
        return info.imagePath
      }
      return null
    } catch (error) {
      console.error('获取壁纸路径失败:', error)
      return null
    }
  }

  // 获取当前壁纸信息
  getCurrentWallpaperInfo() {
    try {
      const wallpaperInfo = localStorage.getItem('currentWallpaperInfo')
      if (wallpaperInfo) {
        return JSON.parse(wallpaperInfo)
      }
      return null
    } catch (error) {
      console.error('获取壁纸信息失败:', error)
      return null
    }
  }

  // 从本地路径创建File对象
  async loadFileFromPath(filePath) {
    try {
      // 通过fetch获取本地文件并转换为File对象
      const response = await fetch(filePath)
      if (!response.ok) {
        throw new Error(`无法加载文件: ${response.status}`)
      }

      const blob = await response.blob()
      const fileName = filePath.split('/').pop()
      const file = new File([blob], fileName, { type: blob.type })

      console.log('成功从路径加载文件:', filePath)
      return file
    } catch (error) {
      console.error('从路径加载文件失败:', error)
      return null
    }
  }
}

export default ImageGenerationService