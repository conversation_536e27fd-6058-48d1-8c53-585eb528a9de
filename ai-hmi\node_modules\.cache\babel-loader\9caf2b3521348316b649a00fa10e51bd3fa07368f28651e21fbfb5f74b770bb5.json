{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { ref, reactive, computed, onMounted, onUnmounted } from 'vue';\nimport BaseCard from '@/components/cards/BaseCard.vue';\nimport mockDataService from '@/services/MockDataService.js';\nexport default {\n  name: 'SmartHomeControlCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    size: {\n      type: String,\n      default: 'large',\n      validator: value => ['small', 'medium', 'large'].includes(value)\n    },\n    position: {\n      type: Object,\n      default: () => ({\n        x: 0,\n        y: 0\n      })\n    },\n    theme: {\n      type: String,\n      default: 'glassmorphism'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({})\n    },\n    displayMode: {\n      type: String,\n      default: 'full',\n      validator: value => ['compact', 'standard', 'full'].includes(value)\n    },\n    showSceneModes: {\n      type: Boolean,\n      default: true\n    },\n    deviceFilter: {\n      type: String,\n      default: 'all',\n      validator: value => ['all', 'lights', 'security', 'climate', 'entertainment'].includes(value)\n    },\n    autoRefresh: {\n      type: Boolean,\n      default: true\n    },\n    refreshInterval: {\n      type: Number,\n      default: 30000 // 30秒\n    }\n  },\n  emits: ['device-control', 'scene-activated', 'suggestion-applied'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式数据\n    const isLoading = ref(false);\n    const isProcessing = ref(false);\n    const viewMode = ref('grid');\n    const activeSceneId = ref(null);\n    const homeInfo = ref({\n      name: '我的家',\n      address: '北京市朝阳区'\n    });\n    const homeStats = ref({\n      temperature: 22,\n      humidity: 45,\n      activeDevices: 12,\n      energyUsage: 1250\n    });\n    const devices = ref([]);\n    const sceneModes = ref([]);\n    const aiSuggestions = ref([]);\n    const colorPresets = ref([{\n      name: '暖白',\n      value: '#FFF8DC'\n    }, {\n      name: '冷白',\n      value: '#F0F8FF'\n    }, {\n      name: '红色',\n      value: '#FF6B6B'\n    }, {\n      name: '绿色',\n      value: '#4ECDC4'\n    }, {\n      name: '蓝色',\n      value: '#45B7D1'\n    }, {\n      name: '紫色',\n      value: '#96CEB4'\n    }]);\n\n    // 计算属性\n    const cardTitle = computed(() => {\n      const onlineDevices = devices.value.filter(d => d.status === 'online').length;\n      return `智能家居 (${onlineDevices}/${devices.value.length})`;\n    });\n    const overallStatus = computed(() => {\n      const onlineDevices = devices.value.filter(d => d.status === 'online').length;\n      const totalDevices = devices.value.length;\n      if (totalDevices === 0) {\n        return {\n          type: 'unknown',\n          icon: 'fas fa-question',\n          text: '未知'\n        };\n      }\n      const onlinePercentage = onlineDevices / totalDevices * 100;\n      if (onlinePercentage >= 90) {\n        return {\n          type: 'excellent',\n          icon: 'fas fa-check-circle',\n          text: '运行良好'\n        };\n      } else if (onlinePercentage >= 70) {\n        return {\n          type: 'good',\n          icon: 'fas fa-exclamation-triangle',\n          text: '基本正常'\n        };\n      } else {\n        return {\n          type: 'warning',\n          icon: 'fas fa-exclamation-circle',\n          text: '需要关注'\n        };\n      }\n    });\n    const filteredDevices = computed(() => {\n      if (props.deviceFilter === 'all') {\n        return devices.value;\n      }\n      return devices.value.filter(device => device.category === props.deviceFilter);\n    });\n\n    // 方法\n    const loadSmartHomeData = async () => {\n      try {\n        isLoading.value = true;\n        const smartHomeData = await mockDataService.getSmartHomeData();\n        devices.value = smartHomeData.devices || [];\n        sceneModes.value = smartHomeData.scenes || [];\n        aiSuggestions.value = smartHomeData.aiSuggestions || [];\n        activeSceneId.value = smartHomeData.activeScene || null;\n\n        // 更新家庭统计信息\n        updateHomeStats();\n      } catch (error) {\n        console.error('Failed to load smart home data:', error);\n      } finally {\n        isLoading.value = false;\n      }\n    };\n    const updateHomeStats = () => {\n      const thermostat = devices.value.find(d => d.type === 'thermostat');\n      if (thermostat) {\n        homeStats.value.temperature = thermostat.state.currentTemp;\n        homeStats.value.humidity = thermostat.state.humidity || 45;\n      }\n      homeStats.value.activeDevices = devices.value.filter(d => d.status === 'online').length;\n\n      // 计算总功耗\n      const totalPower = devices.value.reduce((sum, device) => {\n        return sum + (device.state.powerUsage || 0);\n      }, 0);\n      homeStats.value.energyUsage = totalPower;\n    };\n    const handleDeviceClick = device => {\n      device.showDetails = !device.showDetails;\n    };\n    const toggleDevice = async device => {\n      try {\n        isProcessing.value = true;\n        const newState = !device.state.on;\n        await mockDataService.controlDevice(device.id, {\n          on: newState\n        });\n        device.state.on = newState;\n        device.status = newState ? 'online' : 'offline';\n        emit('device-control', {\n          deviceId: device.id,\n          action: 'toggle',\n          state: {\n            on: newState\n          }\n        });\n        updateHomeStats();\n      } catch (error) {\n        console.error('Failed to toggle device:', error);\n      } finally {\n        isProcessing.value = false;\n      }\n    };\n    const adjustBrightness = async (device, brightness) => {\n      try {\n        const brightnessValue = parseInt(brightness);\n        await mockDataService.controlDevice(device.id, {\n          brightness: brightnessValue\n        });\n        device.state.brightness = brightnessValue;\n        emit('device-control', {\n          deviceId: device.id,\n          action: 'brightness',\n          state: {\n            brightness: brightnessValue\n          }\n        });\n      } catch (error) {\n        console.error('Failed to adjust brightness:', error);\n      }\n    };\n    const setLightColor = async (device, color) => {\n      try {\n        await mockDataService.controlDevice(device.id, {\n          color: color.value\n        });\n        device.state.color = color.value;\n        emit('device-control', {\n          deviceId: device.id,\n          action: 'color',\n          state: {\n            color: color.value\n          }\n        });\n      } catch (error) {\n        console.error('Failed to set light color:', error);\n      }\n    };\n    const adjustTemperature = async (device, delta) => {\n      try {\n        const newTemp = device.state.targetTemp + delta;\n        if (newTemp < 16 || newTemp > 30) return;\n        await mockDataService.controlDevice(device.id, {\n          targetTemp: newTemp\n        });\n        device.state.targetTemp = newTemp;\n        emit('device-control', {\n          deviceId: device.id,\n          action: 'temperature',\n          state: {\n            targetTemp: newTemp\n          }\n        });\n      } catch (error) {\n        console.error('Failed to adjust temperature:', error);\n      }\n    };\n    const toggleSecurity = async device => {\n      try {\n        isProcessing.value = true;\n        const newArmedState = !device.state.armed;\n        await mockDataService.controlDevice(device.id, {\n          armed: newArmedState\n        });\n        device.state.armed = newArmedState;\n        emit('device-control', {\n          deviceId: device.id,\n          action: 'security',\n          state: {\n            armed: newArmedState\n          }\n        });\n      } catch (error) {\n        console.error('Failed to toggle security:', error);\n      } finally {\n        isProcessing.value = false;\n      }\n    };\n    const activateScene = async scene => {\n      try {\n        isProcessing.value = true;\n        await mockDataService.activateScene(scene.id);\n        activeSceneId.value = scene.id;\n\n        // 应用场景设置到设备\n        scene.deviceSettings.forEach(setting => {\n          const device = devices.value.find(d => d.id === setting.deviceId);\n          if (device) {\n            Object.assign(device.state, setting.state);\n          }\n        });\n        emit('scene-activated', scene);\n        updateHomeStats();\n      } catch (error) {\n        console.error('Failed to activate scene:', error);\n      } finally {\n        isProcessing.value = false;\n      }\n    };\n    const applySuggestion = async suggestion => {\n      try {\n        isProcessing.value = true;\n\n        // 应用AI建议\n        await mockDataService.applySuggestion(suggestion.id);\n\n        // 从建议列表中移除\n        const index = aiSuggestions.value.findIndex(s => s.id === suggestion.id);\n        if (index > -1) {\n          aiSuggestions.value.splice(index, 1);\n        }\n        emit('suggestion-applied', suggestion);\n\n        // 重新加载数据以反映变化\n        await loadSmartHomeData();\n      } catch (error) {\n        console.error('Failed to apply suggestion:', error);\n      } finally {\n        isProcessing.value = false;\n      }\n    };\n    const getDeviceStatusText = device => {\n      switch (device.status) {\n        case 'online':\n          return '在线';\n        case 'offline':\n          return '离线';\n        case 'error':\n          return '故障';\n        default:\n          return '未知';\n      }\n    };\n\n    // 生命周期\n    let refreshTimer = null;\n    onMounted(async () => {\n      await mockDataService.initialize();\n      await loadSmartHomeData();\n\n      // 设置自动刷新\n      if (props.autoRefresh) {\n        refreshTimer = setInterval(() => {\n          loadSmartHomeData();\n        }, props.refreshInterval);\n      }\n    });\n    onUnmounted(() => {\n      if (refreshTimer) {\n        clearInterval(refreshTimer);\n      }\n    });\n    return {\n      // 响应式数据\n      isLoading,\n      isProcessing,\n      viewMode,\n      activeSceneId,\n      homeInfo,\n      homeStats,\n      devices,\n      sceneModes,\n      aiSuggestions,\n      colorPresets,\n      // 计算属性\n      cardTitle,\n      overallStatus,\n      filteredDevices,\n      // 方法\n      handleDeviceClick,\n      toggleDevice,\n      adjustBrightness,\n      setLightColor,\n      adjustTemperature,\n      toggleSecurity,\n      activateScene,\n      applySuggestion,\n      getDeviceStatusText\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "onUnmounted", "BaseCard", "mockDataService", "name", "components", "props", "size", "type", "String", "default", "validator", "value", "includes", "position", "Object", "x", "y", "theme", "themeColors", "displayMode", "showSceneModes", "Boolean", "deviceFilter", "autoRefresh", "refreshInterval", "Number", "emits", "setup", "emit", "isLoading", "isProcessing", "viewMode", "activeSceneId", "homeInfo", "address", "homeStats", "temperature", "humidity", "activeDevices", "energyUsage", "devices", "sceneModes", "aiSuggestions", "colorPresets", "cardTitle", "onlineDevices", "filter", "d", "status", "length", "overallStatus", "totalDevices", "icon", "text", "onlinePercentage", "filteredDevices", "device", "category", "loadSmartHomeData", "smartHomeData", "getSmartHomeData", "scenes", "activeScene", "updateHomeStats", "error", "console", "thermostat", "find", "state", "currentTemp", "totalPower", "reduce", "sum", "powerUsage", "handleDeviceClick", "showDetails", "toggleDevice", "newState", "on", "controlDevice", "id", "deviceId", "action", "adjustBrightness", "brightness", "brightnessValue", "parseInt", "setLightColor", "color", "adjustTemperature", "delta", "newTemp", "targetTemp", "toggleSecurity", "newArmedState", "armed", "activateScene", "scene", "deviceSettings", "for<PERSON>ach", "setting", "assign", "applySuggestion", "suggestion", "index", "findIndex", "s", "splice", "getDeviceStatusText", "refreshTimer", "initialize", "setInterval", "clearInterval"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\SmartHomeControlCard.vue"], "sourcesContent": ["<template>\n  <BaseCard\n    :card-type=\"'smart-home'\"\n    :size=\"size\"\n    :position=\"position\"\n    :theme=\"theme\"\n    :theme-colors=\"themeColors\"\n    :show-header=\"true\"\n    :show-footer=\"false\"\n    :title=\"cardTitle\"\n    :icon=\"'fas fa-home'\"\n    class=\"smart-home-control-card\"\n  >\n    <div class=\"smart-home-container\" :class=\"[`size-${size}`, `mode-${displayMode}`]\">\n      <!-- 家庭状态概览 -->\n      <div class=\"home-status-overview\">\n        <div class=\"status-header\">\n          <div class=\"home-info\">\n            <h3 class=\"home-name\">{{ homeInfo.name }}</h3>\n            <div class=\"home-address\">{{ homeInfo.address }}</div>\n          </div>\n          <div class=\"overall-status\" :class=\"overallStatus.type\">\n            <i :class=\"overallStatus.icon\"></i>\n            <span>{{ overallStatus.text }}</span>\n          </div>\n        </div>\n        \n        <div class=\"quick-stats\">\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ homeStats.temperature }}°C</div>\n            <div class=\"stat-label\">室内温度</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ homeStats.humidity }}%</div>\n            <div class=\"stat-label\">湿度</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ homeStats.activeDevices }}</div>\n            <div class=\"stat-label\">活跃设备</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-value\">{{ homeStats.energyUsage }}W</div>\n            <div class=\"stat-label\">功耗</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 场景模式快捷控制 -->\n      <div class=\"scene-modes\" v-if=\"showSceneModes\">\n        <div class=\"modes-header\">\n          <i class=\"fas fa-magic\"></i>\n          <span>场景模式</span>\n        </div>\n        <div class=\"modes-grid\">\n          <button\n            v-for=\"scene in sceneModes\"\n            :key=\"scene.id\"\n            @click=\"activateScene(scene)\"\n            :class=\"['scene-btn', { active: scene.id === activeSceneId }]\"\n            :disabled=\"isProcessing\"\n          >\n            <div class=\"scene-icon\">\n              <i :class=\"scene.icon\"></i>\n            </div>\n            <div class=\"scene-info\">\n              <div class=\"scene-name\">{{ scene.name }}</div>\n              <div class=\"scene-desc\">{{ scene.description }}</div>\n            </div>\n          </button>\n        </div>\n      </div>\n\n      <!-- 设备控制区域 -->\n      <div class=\"device-controls\">\n        <div class=\"controls-header\">\n          <span>设备控制</span>\n          <div class=\"view-toggle\">\n            <button \n              @click=\"viewMode = 'grid'\"\n              :class=\"['toggle-btn', { active: viewMode === 'grid' }]\"\n            >\n              <i class=\"fas fa-th\"></i>\n            </button>\n            <button \n              @click=\"viewMode = 'list'\"\n              :class=\"['toggle-btn', { active: viewMode === 'list' }]\"\n            >\n              <i class=\"fas fa-list\"></i>\n            </button>\n          </div>\n        </div>\n        \n        <div :class=\"['devices-container', `view-${viewMode}`]\">\n          <div\n            v-for=\"device in filteredDevices\"\n            :key=\"device.id\"\n            :class=\"['device-item', `type-${device.type}`, `status-${device.status}`]\"\n            @click=\"handleDeviceClick(device)\"\n          >\n            <div class=\"device-header\">\n              <div class=\"device-icon\">\n                <i :class=\"device.icon\"></i>\n              </div>\n              <div class=\"device-info\">\n                <div class=\"device-name\">{{ device.name }}</div>\n                <div class=\"device-room\">{{ device.room }}</div>\n              </div>\n              <div class=\"device-status-indicator\" :class=\"device.status\">\n                <div class=\"status-dot\"></div>\n              </div>\n            </div>\n            \n            <div class=\"device-controls-area\">\n              <!-- 开关控制 -->\n              <div v-if=\"device.type === 'switch'\" class=\"switch-control\">\n                <label class=\"switch\">\n                  <input \n                    type=\"checkbox\" \n                    :checked=\"device.state.on\"\n                    @change=\"toggleDevice(device)\"\n                  >\n                  <span class=\"slider\"></span>\n                </label>\n              </div>\n              \n              <!-- 调光控制 -->\n              <div v-else-if=\"device.type === 'light'\" class=\"light-control\">\n                <div class=\"brightness-control\">\n                  <input\n                    type=\"range\"\n                    :value=\"device.state.brightness\"\n                    @input=\"adjustBrightness(device, $event.target.value)\"\n                    min=\"0\"\n                    max=\"100\"\n                    class=\"brightness-slider\"\n                  >\n                  <span class=\"brightness-value\">{{ device.state.brightness }}%</span>\n                </div>\n                <div class=\"color-control\" v-if=\"device.state.colorSupport\">\n                  <div class=\"color-presets\">\n                    <button\n                      v-for=\"color in colorPresets\"\n                      :key=\"color.name\"\n                      @click=\"setLightColor(device, color)\"\n                      :class=\"['color-preset', { active: device.state.color === color.value }]\"\n                      :style=\"{ backgroundColor: color.value }\"\n                    ></button>\n                  </div>\n                </div>\n              </div>\n              \n              <!-- 温控器控制 -->\n              <div v-else-if=\"device.type === 'thermostat'\" class=\"thermostat-control\">\n                <div class=\"temperature-display\">\n                  <span class=\"current-temp\">{{ device.state.currentTemp }}°C</span>\n                  <span class=\"target-temp\">目标: {{ device.state.targetTemp }}°C</span>\n                </div>\n                <div class=\"temp-controls\">\n                  <button @click=\"adjustTemperature(device, -1)\" class=\"temp-btn\">\n                    <i class=\"fas fa-minus\"></i>\n                  </button>\n                  <button @click=\"adjustTemperature(device, 1)\" class=\"temp-btn\">\n                    <i class=\"fas fa-plus\"></i>\n                  </button>\n                </div>\n              </div>\n              \n              <!-- 安防设备控制 -->\n              <div v-else-if=\"device.type === 'security'\" class=\"security-control\">\n                <div class=\"security-status\">\n                  <span :class=\"['status-text', device.state.armed ? 'armed' : 'disarmed']\">\n                    {{ device.state.armed ? '已布防' : '已撤防' }}\n                  </span>\n                </div>\n                <button \n                  @click=\"toggleSecurity(device)\"\n                  :class=\"['security-toggle', device.state.armed ? 'disarm' : 'arm']\"\n                >\n                  {{ device.state.armed ? '撤防' : '布防' }}\n                </button>\n              </div>\n              \n              <!-- 通用状态显示 -->\n              <div v-else class=\"generic-status\">\n                <span class=\"status-text\">{{ getDeviceStatusText(device) }}</span>\n              </div>\n            </div>\n            \n            <!-- 设备详细信息 -->\n            <div v-if=\"device.showDetails\" class=\"device-details\">\n              <div class=\"detail-item\" v-for=\"(value, key) in device.details\" :key=\"key\">\n                <span class=\"detail-label\">{{ key }}:</span>\n                <span class=\"detail-value\">{{ value }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- AI智能建议 -->\n      <div class=\"ai-suggestions\" v-if=\"aiSuggestions.length > 0\">\n        <div class=\"suggestions-header\">\n          <i class=\"fas fa-lightbulb\"></i>\n          <span>AI智能建议</span>\n        </div>\n        <div class=\"suggestions-list\">\n          <div\n            v-for=\"suggestion in aiSuggestions\"\n            :key=\"suggestion.id\"\n            class=\"suggestion-item\"\n          >\n            <div class=\"suggestion-content\">\n              <div class=\"suggestion-text\">{{ suggestion.text }}</div>\n              <div class=\"suggestion-meta\">\n                <span class=\"energy-saving\">节能 {{ suggestion.energySaving }}</span>\n                <span class=\"confidence\">可信度 {{ Math.round(suggestion.confidence * 100) }}%</span>\n              </div>\n            </div>\n            <button \n              @click=\"applySuggestion(suggestion)\"\n              class=\"apply-btn\"\n              :disabled=\"isProcessing\"\n            >\n              <i class=\"fas fa-check\"></i>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 加载状态 -->\n      <div v-if=\"isLoading\" class=\"loading-overlay\">\n        <div class=\"loading-spinner\"></div>\n        <div class=\"loading-text\">正在连接智能设备...</div>\n      </div>\n    </div>\n  </BaseCard>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, onUnmounted } from 'vue'\nimport BaseCard from '@/components/cards/BaseCard.vue'\nimport mockDataService from '@/services/MockDataService.js'\n\nexport default {\n  name: 'SmartHomeControlCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    size: {\n      type: String,\n      default: 'large',\n      validator: (value) => ['small', 'medium', 'large'].includes(value)\n    },\n    position: {\n      type: Object,\n      default: () => ({ x: 0, y: 0 })\n    },\n    theme: {\n      type: String,\n      default: 'glassmorphism'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({})\n    },\n    displayMode: {\n      type: String,\n      default: 'full',\n      validator: (value) => ['compact', 'standard', 'full'].includes(value)\n    },\n    showSceneModes: {\n      type: Boolean,\n      default: true\n    },\n    deviceFilter: {\n      type: String,\n      default: 'all',\n      validator: (value) => ['all', 'lights', 'security', 'climate', 'entertainment'].includes(value)\n    },\n    autoRefresh: {\n      type: Boolean,\n      default: true\n    },\n    refreshInterval: {\n      type: Number,\n      default: 30000 // 30秒\n    }\n  },\n  emits: ['device-control', 'scene-activated', 'suggestion-applied'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const isLoading = ref(false)\n    const isProcessing = ref(false)\n    const viewMode = ref('grid')\n    const activeSceneId = ref(null)\n    \n    const homeInfo = ref({\n      name: '我的家',\n      address: '北京市朝阳区'\n    })\n    \n    const homeStats = ref({\n      temperature: 22,\n      humidity: 45,\n      activeDevices: 12,\n      energyUsage: 1250\n    })\n    \n    const devices = ref([])\n    const sceneModes = ref([])\n    const aiSuggestions = ref([])\n    \n    const colorPresets = ref([\n      { name: '暖白', value: '#FFF8DC' },\n      { name: '冷白', value: '#F0F8FF' },\n      { name: '红色', value: '#FF6B6B' },\n      { name: '绿色', value: '#4ECDC4' },\n      { name: '蓝色', value: '#45B7D1' },\n      { name: '紫色', value: '#96CEB4' }\n    ])\n\n    // 计算属性\n    const cardTitle = computed(() => {\n      const onlineDevices = devices.value.filter(d => d.status === 'online').length\n      return `智能家居 (${onlineDevices}/${devices.value.length})`\n    })\n    \n    const overallStatus = computed(() => {\n      const onlineDevices = devices.value.filter(d => d.status === 'online').length\n      const totalDevices = devices.value.length\n      \n      if (totalDevices === 0) {\n        return { type: 'unknown', icon: 'fas fa-question', text: '未知' }\n      }\n      \n      const onlinePercentage = (onlineDevices / totalDevices) * 100\n      \n      if (onlinePercentage >= 90) {\n        return { type: 'excellent', icon: 'fas fa-check-circle', text: '运行良好' }\n      } else if (onlinePercentage >= 70) {\n        return { type: 'good', icon: 'fas fa-exclamation-triangle', text: '基本正常' }\n      } else {\n        return { type: 'warning', icon: 'fas fa-exclamation-circle', text: '需要关注' }\n      }\n    })\n    \n    const filteredDevices = computed(() => {\n      if (props.deviceFilter === 'all') {\n        return devices.value\n      }\n      return devices.value.filter(device => device.category === props.deviceFilter)\n    })\n\n    // 方法\n    const loadSmartHomeData = async () => {\n      try {\n        isLoading.value = true\n        const smartHomeData = await mockDataService.getSmartHomeData()\n        \n        devices.value = smartHomeData.devices || []\n        sceneModes.value = smartHomeData.scenes || []\n        aiSuggestions.value = smartHomeData.aiSuggestions || []\n        activeSceneId.value = smartHomeData.activeScene || null\n        \n        // 更新家庭统计信息\n        updateHomeStats()\n        \n      } catch (error) {\n        console.error('Failed to load smart home data:', error)\n      } finally {\n        isLoading.value = false\n      }\n    }\n    \n    const updateHomeStats = () => {\n      const thermostat = devices.value.find(d => d.type === 'thermostat')\n      if (thermostat) {\n        homeStats.value.temperature = thermostat.state.currentTemp\n        homeStats.value.humidity = thermostat.state.humidity || 45\n      }\n      \n      homeStats.value.activeDevices = devices.value.filter(d => d.status === 'online').length\n      \n      // 计算总功耗\n      const totalPower = devices.value.reduce((sum, device) => {\n        return sum + (device.state.powerUsage || 0)\n      }, 0)\n      homeStats.value.energyUsage = totalPower\n    }\n    \n    const handleDeviceClick = (device) => {\n      device.showDetails = !device.showDetails\n    }\n    \n    const toggleDevice = async (device) => {\n      try {\n        isProcessing.value = true\n        \n        const newState = !device.state.on\n        await mockDataService.controlDevice(device.id, { on: newState })\n        \n        device.state.on = newState\n        device.status = newState ? 'online' : 'offline'\n        \n        emit('device-control', {\n          deviceId: device.id,\n          action: 'toggle',\n          state: { on: newState }\n        })\n        \n        updateHomeStats()\n        \n      } catch (error) {\n        console.error('Failed to toggle device:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n    \n    const adjustBrightness = async (device, brightness) => {\n      try {\n        const brightnessValue = parseInt(brightness)\n        await mockDataService.controlDevice(device.id, { brightness: brightnessValue })\n        \n        device.state.brightness = brightnessValue\n        \n        emit('device-control', {\n          deviceId: device.id,\n          action: 'brightness',\n          state: { brightness: brightnessValue }\n        })\n        \n      } catch (error) {\n        console.error('Failed to adjust brightness:', error)\n      }\n    }\n    \n    const setLightColor = async (device, color) => {\n      try {\n        await mockDataService.controlDevice(device.id, { color: color.value })\n        \n        device.state.color = color.value\n        \n        emit('device-control', {\n          deviceId: device.id,\n          action: 'color',\n          state: { color: color.value }\n        })\n        \n      } catch (error) {\n        console.error('Failed to set light color:', error)\n      }\n    }\n    \n    const adjustTemperature = async (device, delta) => {\n      try {\n        const newTemp = device.state.targetTemp + delta\n        if (newTemp < 16 || newTemp > 30) return\n        \n        await mockDataService.controlDevice(device.id, { targetTemp: newTemp })\n        \n        device.state.targetTemp = newTemp\n        \n        emit('device-control', {\n          deviceId: device.id,\n          action: 'temperature',\n          state: { targetTemp: newTemp }\n        })\n        \n      } catch (error) {\n        console.error('Failed to adjust temperature:', error)\n      }\n    }\n    \n    const toggleSecurity = async (device) => {\n      try {\n        isProcessing.value = true\n        \n        const newArmedState = !device.state.armed\n        await mockDataService.controlDevice(device.id, { armed: newArmedState })\n        \n        device.state.armed = newArmedState\n        \n        emit('device-control', {\n          deviceId: device.id,\n          action: 'security',\n          state: { armed: newArmedState }\n        })\n        \n      } catch (error) {\n        console.error('Failed to toggle security:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n    \n    const activateScene = async (scene) => {\n      try {\n        isProcessing.value = true\n        \n        await mockDataService.activateScene(scene.id)\n        \n        activeSceneId.value = scene.id\n        \n        // 应用场景设置到设备\n        scene.deviceSettings.forEach(setting => {\n          const device = devices.value.find(d => d.id === setting.deviceId)\n          if (device) {\n            Object.assign(device.state, setting.state)\n          }\n        })\n        \n        emit('scene-activated', scene)\n        updateHomeStats()\n        \n      } catch (error) {\n        console.error('Failed to activate scene:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n    \n    const applySuggestion = async (suggestion) => {\n      try {\n        isProcessing.value = true\n        \n        // 应用AI建议\n        await mockDataService.applySuggestion(suggestion.id)\n        \n        // 从建议列表中移除\n        const index = aiSuggestions.value.findIndex(s => s.id === suggestion.id)\n        if (index > -1) {\n          aiSuggestions.value.splice(index, 1)\n        }\n        \n        emit('suggestion-applied', suggestion)\n        \n        // 重新加载数据以反映变化\n        await loadSmartHomeData()\n        \n      } catch (error) {\n        console.error('Failed to apply suggestion:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n    \n    const getDeviceStatusText = (device) => {\n      switch (device.status) {\n        case 'online':\n          return '在线'\n        case 'offline':\n          return '离线'\n        case 'error':\n          return '故障'\n        default:\n          return '未知'\n      }\n    }\n\n    // 生命周期\n    let refreshTimer = null\n    \n    onMounted(async () => {\n      await mockDataService.initialize()\n      await loadSmartHomeData()\n      \n      // 设置自动刷新\n      if (props.autoRefresh) {\n        refreshTimer = setInterval(() => {\n          loadSmartHomeData()\n        }, props.refreshInterval)\n      }\n    })\n\n    onUnmounted(() => {\n      if (refreshTimer) {\n        clearInterval(refreshTimer)\n      }\n    })\n\n    return {\n      // 响应式数据\n      isLoading,\n      isProcessing,\n      viewMode,\n      activeSceneId,\n      homeInfo,\n      homeStats,\n      devices,\n      sceneModes,\n      aiSuggestions,\n      colorPresets,\n      \n      // 计算属性\n      cardTitle,\n      overallStatus,\n      filteredDevices,\n      \n      // 方法\n      handleDeviceClick,\n      toggleDevice,\n      adjustBrightness,\n      setLightColor,\n      adjustTemperature,\n      toggleSecurity,\n      activateScene,\n      applySuggestion,\n      getDeviceStatusText\n    }\n  }\n}\n</script>\n\n<style scoped>\n.smart-home-control-card {\n  height: 100%;\n}\n\n.smart-home-container {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  padding: 16px;\n  position: relative;\n}\n\n/* 家庭状态概览 */\n.home-status-overview {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.status-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 16px;\n}\n\n.home-info h3 {\n  margin: 0 0 4px 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.home-address {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.overall-status {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 6px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.overall-status.excellent {\n  background: rgba(16, 185, 129, 0.2);\n  color: #10b981;\n}\n\n.overall-status.good {\n  background: rgba(245, 158, 11, 0.2);\n  color: #f59e0b;\n}\n\n.overall-status.warning {\n  background: rgba(239, 68, 68, 0.2);\n  color: #ef4444;\n}\n\n.quick-stats {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 12px;\n}\n\n.stat-item {\n  text-align: center;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 4px;\n}\n\n.stat-label {\n  font-size: 11px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n/* 场景模式 */\n.scene-modes {\n  background: rgba(0, 0, 0, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.modes-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 12px;\n}\n\n.modes-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n  gap: 8px;\n}\n\n.scene-btn {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border: none;\n  border-radius: 8px;\n  color: rgba(255, 255, 255, 0.8);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-align: left;\n}\n\n.scene-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n  transform: translateY(-1px);\n}\n\n.scene-btn.active {\n  background: rgba(74, 144, 226, 0.3);\n  color: #4a90e2;\n}\n\n.scene-icon {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n}\n\n.scene-info {\n  flex: 1;\n}\n\n.scene-name {\n  font-size: 12px;\n  font-weight: 500;\n  margin-bottom: 2px;\n}\n\n.scene-desc {\n  font-size: 10px;\n  opacity: 0.7;\n}\n\n/* 设备控制 */\n.device-controls {\n  flex: 1;\n  min-height: 0;\n}\n\n.controls-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.controls-header span {\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.view-toggle {\n  display: flex;\n  gap: 4px;\n}\n\n.toggle-btn {\n  width: 28px;\n  height: 28px;\n  border: none;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0.1);\n  color: rgba(255, 255, 255, 0.6);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n}\n\n.toggle-btn:hover,\n.toggle-btn.active {\n  background: rgba(255, 255, 255, 0.2);\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.devices-container {\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.devices-container.view-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 12px;\n}\n\n.devices-container.view-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.device-item {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 12px;\n  padding: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border-left: 4px solid transparent;\n}\n\n.device-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateY(-1px);\n}\n\n.device-item.status-online {\n  border-left-color: #10b981;\n}\n\n.device-item.status-offline {\n  border-left-color: #6b7280;\n}\n\n.device-item.status-error {\n  border-left-color: #ef4444;\n}\n\n.device-header {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  margin-bottom: 12px;\n}\n\n.device-icon {\n  width: 32px;\n  height: 32px;\n  border-radius: 8px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.device-info {\n  flex: 1;\n}\n\n.device-name {\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n}\n\n.device-room {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.device-status-indicator {\n  width: 12px;\n  height: 12px;\n  position: relative;\n}\n\n.status-dot {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  background: #6b7280;\n}\n\n.device-status-indicator.online .status-dot {\n  background: #10b981;\n  animation: pulse 2s infinite;\n}\n\n.device-status-indicator.error .status-dot {\n  background: #ef4444;\n}\n\n/* 设备控制区域 */\n.device-controls-area {\n  margin-bottom: 8px;\n}\n\n/* 开关控制 */\n.switch-control {\n  display: flex;\n  justify-content: center;\n}\n\n.switch {\n  position: relative;\n  display: inline-block;\n  width: 44px;\n  height: 24px;\n}\n\n.switch input {\n  opacity: 0;\n  width: 0;\n  height: 0;\n}\n\n.slider {\n  position: absolute;\n  cursor: pointer;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(255, 255, 255, 0.2);\n  transition: 0.3s;\n  border-radius: 24px;\n}\n\n.slider:before {\n  position: absolute;\n  content: \"\";\n  height: 18px;\n  width: 18px;\n  left: 3px;\n  bottom: 3px;\n  background-color: white;\n  transition: 0.3s;\n  border-radius: 50%;\n}\n\ninput:checked + .slider {\n  background-color: #4a90e2;\n}\n\ninput:checked + .slider:before {\n  transform: translateX(20px);\n}\n\n/* 调光控制 */\n.light-control {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.brightness-control {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.brightness-slider {\n  flex: 1;\n  height: 4px;\n  border-radius: 2px;\n  background: rgba(255, 255, 255, 0.2);\n  outline: none;\n  -webkit-appearance: none;\n}\n\n.brightness-slider::-webkit-slider-thumb {\n  -webkit-appearance: none;\n  appearance: none;\n  width: 16px;\n  height: 16px;\n  border-radius: 50%;\n  background: #4a90e2;\n  cursor: pointer;\n}\n\n.brightness-value {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.8);\n  min-width: 30px;\n  text-align: right;\n}\n\n.color-presets {\n  display: flex;\n  gap: 4px;\n  justify-content: center;\n}\n\n.color-preset {\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  border: 2px solid transparent;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.color-preset:hover,\n.color-preset.active {\n  border-color: rgba(255, 255, 255, 0.8);\n  transform: scale(1.1);\n}\n\n/* 温控器控制 */\n.thermostat-control {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  align-items: center;\n}\n\n.temperature-display {\n  text-align: center;\n}\n\n.current-temp {\n  font-size: 18px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n  display: block;\n}\n\n.target-temp {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.temp-controls {\n  display: flex;\n  gap: 8px;\n}\n\n.temp-btn {\n  width: 32px;\n  height: 32px;\n  border: none;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.2);\n  color: rgba(255, 255, 255, 0.8);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.temp-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: scale(1.05);\n}\n\n/* 安防控制 */\n.security-control {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  align-items: center;\n}\n\n.security-status .status-text.armed {\n  color: #ef4444;\n}\n\n.security-status .status-text.disarmed {\n  color: #10b981;\n}\n\n.security-toggle {\n  padding: 6px 12px;\n  border: none;\n  border-radius: 6px;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.security-toggle.arm {\n  background: rgba(239, 68, 68, 0.2);\n  color: #ef4444;\n}\n\n.security-toggle.disarm {\n  background: rgba(16, 185, 129, 0.2);\n  color: #10b981;\n}\n\n/* 通用状态 */\n.generic-status {\n  text-align: center;\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.7);\n}\n\n/* 设备详细信息 */\n.device-details {\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n  padding-top: 8px;\n  margin-top: 8px;\n}\n\n.detail-item {\n  display: flex;\n  justify-content: space-between;\n  font-size: 11px;\n  margin-bottom: 4px;\n}\n\n.detail-label {\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.detail-value {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* AI建议 */\n.ai-suggestions {\n  background: rgba(126, 211, 33, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.suggestions-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  color: #7ed321;\n  margin-bottom: 12px;\n}\n\n.suggestions-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.suggestion-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n}\n\n.suggestion-content {\n  flex: 1;\n}\n\n.suggestion-text {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 4px;\n}\n\n.suggestion-meta {\n  display: flex;\n  gap: 12px;\n  font-size: 10px;\n}\n\n.energy-saving {\n  color: #7ed321;\n}\n\n.confidence {\n  color: #4a90e2;\n}\n\n.apply-btn {\n  width: 28px;\n  height: 28px;\n  border: none;\n  border-radius: 6px;\n  background: rgba(126, 211, 33, 0.3);\n  color: #7ed321;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.apply-btn:hover {\n  background: rgba(126, 211, 33, 0.5);\n  transform: scale(1.05);\n}\n\n/* 加载状态 */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.3);\n  backdrop-filter: blur(5px);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 12px;\n  border-radius: 12px;\n}\n\n.loading-spinner {\n  width: 32px;\n  height: 32px;\n  border: 3px solid rgba(255, 255, 255, 0.2);\n  border-top: 3px solid #4a90e2;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.loading-text {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* 动画 */\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n}\n\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* 响应式设计 */\n.size-small .smart-home-container {\n  padding: 12px;\n  gap: 12px;\n}\n\n.size-small .quick-stats {\n  grid-template-columns: repeat(2, 1fr);\n}\n\n.size-small .modes-grid {\n  grid-template-columns: repeat(2, 1fr);\n}\n\n.mode-compact .scene-modes,\n.mode-compact .ai-suggestions {\n  display: none;\n}\n\n.mode-compact .devices-container {\n  max-height: 250px;\n}\n</style>"], "mappings": ";;;;;AA+OA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAU,QAAS,KAAI;AACpE,OAAOC,QAAO,MAAO,iCAAgC;AACrD,OAAOC,eAAc,MAAO,+BAA8B;AAE1D,eAAe;EACbC,IAAI,EAAE,sBAAsB;EAC5BC,UAAU,EAAE;IACVH;EACF,CAAC;EACDI,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAGC,KAAK,IAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAACC,QAAQ,CAACD,KAAK;IACnE,CAAC;IACDE,QAAQ,EAAE;MACRN,IAAI,EAAEO,MAAM;MACZL,OAAO,EAAEA,CAAA,MAAO;QAAEM,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;IAChC,CAAC;IACDC,KAAK,EAAE;MACLV,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDS,WAAW,EAAE;MACXX,IAAI,EAAEO,MAAM;MACZL,OAAO,EAAEA,CAAA,MAAO,CAAC,CAAC;IACpB,CAAC;IACDU,WAAW,EAAE;MACXZ,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAGC,KAAK,IAAK,CAAC,SAAS,EAAE,UAAU,EAAE,MAAM,CAAC,CAACC,QAAQ,CAACD,KAAK;IACtE,CAAC;IACDS,cAAc,EAAE;MACdb,IAAI,EAAEc,OAAO;MACbZ,OAAO,EAAE;IACX,CAAC;IACDa,YAAY,EAAE;MACZf,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAGC,KAAK,IAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,eAAe,CAAC,CAACC,QAAQ,CAACD,KAAK;IAChG,CAAC;IACDY,WAAW,EAAE;MACXhB,IAAI,EAAEc,OAAO;MACbZ,OAAO,EAAE;IACX,CAAC;IACDe,eAAe,EAAE;MACfjB,IAAI,EAAEkB,MAAM;MACZhB,OAAO,EAAE,KAAI,CAAE;IACjB;EACF,CAAC;EACDiB,KAAK,EAAE,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,oBAAoB,CAAC;EAClEC,KAAKA,CAACtB,KAAK,EAAE;IAAEuB;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,SAAQ,GAAIjC,GAAG,CAAC,KAAK;IAC3B,MAAMkC,YAAW,GAAIlC,GAAG,CAAC,KAAK;IAC9B,MAAMmC,QAAO,GAAInC,GAAG,CAAC,MAAM;IAC3B,MAAMoC,aAAY,GAAIpC,GAAG,CAAC,IAAI;IAE9B,MAAMqC,QAAO,GAAIrC,GAAG,CAAC;MACnBO,IAAI,EAAE,KAAK;MACX+B,OAAO,EAAE;IACX,CAAC;IAED,MAAMC,SAAQ,GAAIvC,GAAG,CAAC;MACpBwC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE;IACf,CAAC;IAED,MAAMC,OAAM,GAAI5C,GAAG,CAAC,EAAE;IACtB,MAAM6C,UAAS,GAAI7C,GAAG,CAAC,EAAE;IACzB,MAAM8C,aAAY,GAAI9C,GAAG,CAAC,EAAE;IAE5B,MAAM+C,YAAW,GAAI/C,GAAG,CAAC,CACvB;MAAEO,IAAI,EAAE,IAAI;MAAEQ,KAAK,EAAE;IAAU,CAAC,EAChC;MAAER,IAAI,EAAE,IAAI;MAAEQ,KAAK,EAAE;IAAU,CAAC,EAChC;MAAER,IAAI,EAAE,IAAI;MAAEQ,KAAK,EAAE;IAAU,CAAC,EAChC;MAAER,IAAI,EAAE,IAAI;MAAEQ,KAAK,EAAE;IAAU,CAAC,EAChC;MAAER,IAAI,EAAE,IAAI;MAAEQ,KAAK,EAAE;IAAU,CAAC,EAChC;MAAER,IAAI,EAAE,IAAI;MAAEQ,KAAK,EAAE;IAAU,EAChC;;IAED;IACA,MAAMiC,SAAQ,GAAI9C,QAAQ,CAAC,MAAM;MAC/B,MAAM+C,aAAY,GAAIL,OAAO,CAAC7B,KAAK,CAACmC,MAAM,CAACC,CAAA,IAAKA,CAAC,CAACC,MAAK,KAAM,QAAQ,CAAC,CAACC,MAAK;MAC5E,OAAO,SAASJ,aAAa,IAAIL,OAAO,CAAC7B,KAAK,CAACsC,MAAM,GAAE;IACzD,CAAC;IAED,MAAMC,aAAY,GAAIpD,QAAQ,CAAC,MAAM;MACnC,MAAM+C,aAAY,GAAIL,OAAO,CAAC7B,KAAK,CAACmC,MAAM,CAACC,CAAA,IAAKA,CAAC,CAACC,MAAK,KAAM,QAAQ,CAAC,CAACC,MAAK;MAC5E,MAAME,YAAW,GAAIX,OAAO,CAAC7B,KAAK,CAACsC,MAAK;MAExC,IAAIE,YAAW,KAAM,CAAC,EAAE;QACtB,OAAO;UAAE5C,IAAI,EAAE,SAAS;UAAE6C,IAAI,EAAE,iBAAiB;UAAEC,IAAI,EAAE;QAAK;MAChE;MAEA,MAAMC,gBAAe,GAAKT,aAAY,GAAIM,YAAY,GAAI,GAAE;MAE5D,IAAIG,gBAAe,IAAK,EAAE,EAAE;QAC1B,OAAO;UAAE/C,IAAI,EAAE,WAAW;UAAE6C,IAAI,EAAE,qBAAqB;UAAEC,IAAI,EAAE;QAAO;MACxE,OAAO,IAAIC,gBAAe,IAAK,EAAE,EAAE;QACjC,OAAO;UAAE/C,IAAI,EAAE,MAAM;UAAE6C,IAAI,EAAE,6BAA6B;UAAEC,IAAI,EAAE;QAAO;MAC3E,OAAO;QACL,OAAO;UAAE9C,IAAI,EAAE,SAAS;UAAE6C,IAAI,EAAE,2BAA2B;UAAEC,IAAI,EAAE;QAAO;MAC5E;IACF,CAAC;IAED,MAAME,eAAc,GAAIzD,QAAQ,CAAC,MAAM;MACrC,IAAIO,KAAK,CAACiB,YAAW,KAAM,KAAK,EAAE;QAChC,OAAOkB,OAAO,CAAC7B,KAAI;MACrB;MACA,OAAO6B,OAAO,CAAC7B,KAAK,CAACmC,MAAM,CAACU,MAAK,IAAKA,MAAM,CAACC,QAAO,KAAMpD,KAAK,CAACiB,YAAY;IAC9E,CAAC;;IAED;IACA,MAAMoC,iBAAgB,GAAI,MAAAA,CAAA,KAAY;MACpC,IAAI;QACF7B,SAAS,CAAClB,KAAI,GAAI,IAAG;QACrB,MAAMgD,aAAY,GAAI,MAAMzD,eAAe,CAAC0D,gBAAgB,CAAC;QAE7DpB,OAAO,CAAC7B,KAAI,GAAIgD,aAAa,CAACnB,OAAM,IAAK,EAAC;QAC1CC,UAAU,CAAC9B,KAAI,GAAIgD,aAAa,CAACE,MAAK,IAAK,EAAC;QAC5CnB,aAAa,CAAC/B,KAAI,GAAIgD,aAAa,CAACjB,aAAY,IAAK,EAAC;QACtDV,aAAa,CAACrB,KAAI,GAAIgD,aAAa,CAACG,WAAU,IAAK,IAAG;;QAEtD;QACAC,eAAe,CAAC;MAElB,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK;MACxD,UAAU;QACRnC,SAAS,CAAClB,KAAI,GAAI,KAAI;MACxB;IACF;IAEA,MAAMoD,eAAc,GAAIA,CAAA,KAAM;MAC5B,MAAMG,UAAS,GAAI1B,OAAO,CAAC7B,KAAK,CAACwD,IAAI,CAACpB,CAAA,IAAKA,CAAC,CAACxC,IAAG,KAAM,YAAY;MAClE,IAAI2D,UAAU,EAAE;QACd/B,SAAS,CAACxB,KAAK,CAACyB,WAAU,GAAI8B,UAAU,CAACE,KAAK,CAACC,WAAU;QACzDlC,SAAS,CAACxB,KAAK,CAAC0B,QAAO,GAAI6B,UAAU,CAACE,KAAK,CAAC/B,QAAO,IAAK,EAAC;MAC3D;MAEAF,SAAS,CAACxB,KAAK,CAAC2B,aAAY,GAAIE,OAAO,CAAC7B,KAAK,CAACmC,MAAM,CAACC,CAAA,IAAKA,CAAC,CAACC,MAAK,KAAM,QAAQ,CAAC,CAACC,MAAK;;MAEtF;MACA,MAAMqB,UAAS,GAAI9B,OAAO,CAAC7B,KAAK,CAAC4D,MAAM,CAAC,CAACC,GAAG,EAAEhB,MAAM,KAAK;QACvD,OAAOgB,GAAE,IAAKhB,MAAM,CAACY,KAAK,CAACK,UAAS,IAAK,CAAC;MAC5C,CAAC,EAAE,CAAC;MACJtC,SAAS,CAACxB,KAAK,CAAC4B,WAAU,GAAI+B,UAAS;IACzC;IAEA,MAAMI,iBAAgB,GAAKlB,MAAM,IAAK;MACpCA,MAAM,CAACmB,WAAU,GAAI,CAACnB,MAAM,CAACmB,WAAU;IACzC;IAEA,MAAMC,YAAW,GAAI,MAAOpB,MAAM,IAAK;MACrC,IAAI;QACF1B,YAAY,CAACnB,KAAI,GAAI,IAAG;QAExB,MAAMkE,QAAO,GAAI,CAACrB,MAAM,CAACY,KAAK,CAACU,EAAC;QAChC,MAAM5E,eAAe,CAAC6E,aAAa,CAACvB,MAAM,CAACwB,EAAE,EAAE;UAAEF,EAAE,EAAED;QAAS,CAAC;QAE/DrB,MAAM,CAACY,KAAK,CAACU,EAAC,GAAID,QAAO;QACzBrB,MAAM,CAACR,MAAK,GAAI6B,QAAO,GAAI,QAAO,GAAI,SAAQ;QAE9CjD,IAAI,CAAC,gBAAgB,EAAE;UACrBqD,QAAQ,EAAEzB,MAAM,CAACwB,EAAE;UACnBE,MAAM,EAAE,QAAQ;UAChBd,KAAK,EAAE;YAAEU,EAAE,EAAED;UAAS;QACxB,CAAC;QAEDd,eAAe,CAAC;MAElB,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK;MACjD,UAAU;QACRlC,YAAY,CAACnB,KAAI,GAAI,KAAI;MAC3B;IACF;IAEA,MAAMwE,gBAAe,GAAI,MAAAA,CAAO3B,MAAM,EAAE4B,UAAU,KAAK;MACrD,IAAI;QACF,MAAMC,eAAc,GAAIC,QAAQ,CAACF,UAAU;QAC3C,MAAMlF,eAAe,CAAC6E,aAAa,CAACvB,MAAM,CAACwB,EAAE,EAAE;UAAEI,UAAU,EAAEC;QAAgB,CAAC;QAE9E7B,MAAM,CAACY,KAAK,CAACgB,UAAS,GAAIC,eAAc;QAExCzD,IAAI,CAAC,gBAAgB,EAAE;UACrBqD,QAAQ,EAAEzB,MAAM,CAACwB,EAAE;UACnBE,MAAM,EAAE,YAAY;UACpBd,KAAK,EAAE;YAAEgB,UAAU,EAAEC;UAAgB;QACvC,CAAC;MAEH,EAAE,OAAOrB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK;MACrD;IACF;IAEA,MAAMuB,aAAY,GAAI,MAAAA,CAAO/B,MAAM,EAAEgC,KAAK,KAAK;MAC7C,IAAI;QACF,MAAMtF,eAAe,CAAC6E,aAAa,CAACvB,MAAM,CAACwB,EAAE,EAAE;UAAEQ,KAAK,EAAEA,KAAK,CAAC7E;QAAM,CAAC;QAErE6C,MAAM,CAACY,KAAK,CAACoB,KAAI,GAAIA,KAAK,CAAC7E,KAAI;QAE/BiB,IAAI,CAAC,gBAAgB,EAAE;UACrBqD,QAAQ,EAAEzB,MAAM,CAACwB,EAAE;UACnBE,MAAM,EAAE,OAAO;UACfd,KAAK,EAAE;YAAEoB,KAAK,EAAEA,KAAK,CAAC7E;UAAM;QAC9B,CAAC;MAEH,EAAE,OAAOqD,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK;MACnD;IACF;IAEA,MAAMyB,iBAAgB,GAAI,MAAAA,CAAOjC,MAAM,EAAEkC,KAAK,KAAK;MACjD,IAAI;QACF,MAAMC,OAAM,GAAInC,MAAM,CAACY,KAAK,CAACwB,UAAS,GAAIF,KAAI;QAC9C,IAAIC,OAAM,GAAI,EAAC,IAAKA,OAAM,GAAI,EAAE,EAAE;QAElC,MAAMzF,eAAe,CAAC6E,aAAa,CAACvB,MAAM,CAACwB,EAAE,EAAE;UAAEY,UAAU,EAAED;QAAQ,CAAC;QAEtEnC,MAAM,CAACY,KAAK,CAACwB,UAAS,GAAID,OAAM;QAEhC/D,IAAI,CAAC,gBAAgB,EAAE;UACrBqD,QAAQ,EAAEzB,MAAM,CAACwB,EAAE;UACnBE,MAAM,EAAE,aAAa;UACrBd,KAAK,EAAE;YAAEwB,UAAU,EAAED;UAAQ;QAC/B,CAAC;MAEH,EAAE,OAAO3B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK;MACtD;IACF;IAEA,MAAM6B,cAAa,GAAI,MAAOrC,MAAM,IAAK;MACvC,IAAI;QACF1B,YAAY,CAACnB,KAAI,GAAI,IAAG;QAExB,MAAMmF,aAAY,GAAI,CAACtC,MAAM,CAACY,KAAK,CAAC2B,KAAI;QACxC,MAAM7F,eAAe,CAAC6E,aAAa,CAACvB,MAAM,CAACwB,EAAE,EAAE;UAAEe,KAAK,EAAED;QAAc,CAAC;QAEvEtC,MAAM,CAACY,KAAK,CAAC2B,KAAI,GAAID,aAAY;QAEjClE,IAAI,CAAC,gBAAgB,EAAE;UACrBqD,QAAQ,EAAEzB,MAAM,CAACwB,EAAE;UACnBE,MAAM,EAAE,UAAU;UAClBd,KAAK,EAAE;YAAE2B,KAAK,EAAED;UAAc;QAChC,CAAC;MAEH,EAAE,OAAO9B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK;MACnD,UAAU;QACRlC,YAAY,CAACnB,KAAI,GAAI,KAAI;MAC3B;IACF;IAEA,MAAMqF,aAAY,GAAI,MAAOC,KAAK,IAAK;MACrC,IAAI;QACFnE,YAAY,CAACnB,KAAI,GAAI,IAAG;QAExB,MAAMT,eAAe,CAAC8F,aAAa,CAACC,KAAK,CAACjB,EAAE;QAE5ChD,aAAa,CAACrB,KAAI,GAAIsF,KAAK,CAACjB,EAAC;;QAE7B;QACAiB,KAAK,CAACC,cAAc,CAACC,OAAO,CAACC,OAAM,IAAK;UACtC,MAAM5C,MAAK,GAAIhB,OAAO,CAAC7B,KAAK,CAACwD,IAAI,CAACpB,CAAA,IAAKA,CAAC,CAACiC,EAAC,KAAMoB,OAAO,CAACnB,QAAQ;UAChE,IAAIzB,MAAM,EAAE;YACV1C,MAAM,CAACuF,MAAM,CAAC7C,MAAM,CAACY,KAAK,EAAEgC,OAAO,CAAChC,KAAK;UAC3C;QACF,CAAC;QAEDxC,IAAI,CAAC,iBAAiB,EAAEqE,KAAK;QAC7BlC,eAAe,CAAC;MAElB,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK;MAClD,UAAU;QACRlC,YAAY,CAACnB,KAAI,GAAI,KAAI;MAC3B;IACF;IAEA,MAAM2F,eAAc,GAAI,MAAOC,UAAU,IAAK;MAC5C,IAAI;QACFzE,YAAY,CAACnB,KAAI,GAAI,IAAG;;QAExB;QACA,MAAMT,eAAe,CAACoG,eAAe,CAACC,UAAU,CAACvB,EAAE;;QAEnD;QACA,MAAMwB,KAAI,GAAI9D,aAAa,CAAC/B,KAAK,CAAC8F,SAAS,CAACC,CAAA,IAAKA,CAAC,CAAC1B,EAAC,KAAMuB,UAAU,CAACvB,EAAE;QACvE,IAAIwB,KAAI,GAAI,CAAC,CAAC,EAAE;UACd9D,aAAa,CAAC/B,KAAK,CAACgG,MAAM,CAACH,KAAK,EAAE,CAAC;QACrC;QAEA5E,IAAI,CAAC,oBAAoB,EAAE2E,UAAU;;QAErC;QACA,MAAM7C,iBAAiB,CAAC;MAE1B,EAAE,OAAOM,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK;MACpD,UAAU;QACRlC,YAAY,CAACnB,KAAI,GAAI,KAAI;MAC3B;IACF;IAEA,MAAMiG,mBAAkB,GAAKpD,MAAM,IAAK;MACtC,QAAQA,MAAM,CAACR,MAAM;QACnB,KAAK,QAAQ;UACX,OAAO,IAAG;QACZ,KAAK,SAAS;UACZ,OAAO,IAAG;QACZ,KAAK,OAAO;UACV,OAAO,IAAG;QACZ;UACE,OAAO,IAAG;MACd;IACF;;IAEA;IACA,IAAI6D,YAAW,GAAI,IAAG;IAEtB9G,SAAS,CAAC,YAAY;MACpB,MAAMG,eAAe,CAAC4G,UAAU,CAAC;MACjC,MAAMpD,iBAAiB,CAAC;;MAExB;MACA,IAAIrD,KAAK,CAACkB,WAAW,EAAE;QACrBsF,YAAW,GAAIE,WAAW,CAAC,MAAM;UAC/BrD,iBAAiB,CAAC;QACpB,CAAC,EAAErD,KAAK,CAACmB,eAAe;MAC1B;IACF,CAAC;IAEDxB,WAAW,CAAC,MAAM;MAChB,IAAI6G,YAAY,EAAE;QAChBG,aAAa,CAACH,YAAY;MAC5B;IACF,CAAC;IAED,OAAO;MACL;MACAhF,SAAS;MACTC,YAAY;MACZC,QAAQ;MACRC,aAAa;MACbC,QAAQ;MACRE,SAAS;MACTK,OAAO;MACPC,UAAU;MACVC,aAAa;MACbC,YAAY;MAEZ;MACAC,SAAS;MACTM,aAAa;MACbK,eAAe;MAEf;MACAmB,iBAAiB;MACjBE,YAAY;MACZO,gBAAgB;MAChBI,aAAa;MACbE,iBAAiB;MACjBI,cAAc;MACdG,aAAa;MACbM,eAAe;MACfM;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}