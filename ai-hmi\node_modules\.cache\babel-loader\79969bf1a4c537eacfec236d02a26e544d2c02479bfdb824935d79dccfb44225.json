{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.find.js\";\n/**\r\n * 模拟数据服务 - 为AI-HMI项目提供仿真数据\r\n * 专为模拟座舱显示设计，无需真实API集成\r\n */\n\nclass MockDataService {\n  constructor() {\n    this.isInitialized = false;\n    this.currentScene = 'commute';\n    this.currentUser = null;\n    this.mockData = {\n      navigation: {\n        currentRoute: {\n          destination: '公司',\n          estimatedTime: '25分钟',\n          distance: '12.5公里',\n          traffic: 'moderate',\n          routePoints: [{\n            lat: 39.9042,\n            lng: 116.4074,\n            name: '家'\n          }, {\n            lat: 39.9142,\n            lng: 116.4174,\n            name: '幼儿园'\n          }, {\n            lat: 39.9242,\n            lng: 116.4274,\n            name: '公司'\n          }]\n        },\n        trafficStatus: {\n          level: 'moderate',\n          description: '路况一般，比平时拥堵',\n          delay: '10分钟'\n        }\n      },\n      music: {\n        currentSong: {\n          title: '日落大道',\n          artist: 'AI推荐',\n          album: '通勤放松歌单',\n          cover: '/assets/music/sunset-road.jpg',\n          duration: 240,\n          currentTime: 45\n        },\n        playlist: [{\n          title: '日落大道',\n          artist: 'AI推荐',\n          mood: 'relaxing'\n        }, {\n          title: '晨光序曲',\n          artist: 'AI生成',\n          mood: 'energetic'\n        }, {\n          title: '城市节拍',\n          artist: 'AI混音',\n          mood: 'focused'\n        }],\n        isPlaying: true,\n        aiRecommendation: {\n          reason: '基于您的通勤时间和当前情绪推荐',\n          mood: 'focused',\n          scene: 'commute'\n        }\n      },\n      schedule: {\n        todayEvents: [{\n          id: 1,\n          title: '重要会议',\n          time: '10:00',\n          location: '会议室A',\n          priority: 'high',\n          status: 'upcoming',\n          aiSuggestion: '建议提前5分钟到达，已为您准备会议资料摘要'\n        }, {\n          id: 2,\n          title: '项目评审',\n          time: '14:30',\n          location: '会议室B',\n          priority: 'medium',\n          status: 'scheduled'\n        }, {\n          id: 3,\n          title: '接毛毛放学',\n          time: '17:00',\n          location: '幼儿园',\n          priority: 'high',\n          status: 'scheduled'\n        }],\n        aiOptimization: {\n          suggestion: '直达公司，咖啡已预订',\n          timeSaved: '8分钟',\n          confidence: 0.95\n        }\n      },\n      smartHome: {\n        devices: [{\n          id: 1,\n          name: '客厅灯',\n          type: 'light',\n          status: 'on',\n          icon: 'fas fa-lightbulb',\n          brightness: 80\n        }, {\n          id: 2,\n          name: '空调',\n          type: 'climate',\n          status: 'off',\n          icon: 'fas fa-snowflake',\n          temperature: 24\n        }, {\n          id: 3,\n          name: '安防系统',\n          type: 'security',\n          status: 'armed',\n          icon: 'fas fa-shield-alt'\n        }, {\n          id: 4,\n          name: '扫地机器人',\n          type: 'cleaning',\n          status: 'cleaning',\n          icon: 'fas fa-robot',\n          progress: 65\n        }],\n        homeStatus: {\n          temperature: 22,\n          humidity: 45,\n          airQuality: 'good',\n          energyUsage: 'normal'\n        }\n      },\n      orders: [{\n        id: 1,\n        type: 'coffee',\n        vendor: '瑞幸咖啡',\n        item: '美式咖啡 + 三明治',\n        status: 'confirmed',\n        pickupTime: '09:35',\n        location: '公司楼下',\n        estimatedReady: '5分钟',\n        aiOptimized: true\n      }],\n      vpa: {\n        currentState: 'companion',\n        mood: 'helpful',\n        lastInteraction: Date.now() - 30000,\n        conversationHistory: [{\n          id: 1,\n          type: 'vpa',\n          content: 'Hello，主人和毛毛你好。按往常一样先送毛毛上学吧？',\n          timestamp: Date.now() - 300000\n        }, {\n          id: 2,\n          type: 'user',\n          content: '是的，今天路况怎么样？',\n          timestamp: Date.now() - 280000\n        }, {\n          id: 3,\n          type: 'vpa',\n          content: '今天路况比平时拥堵一些，预计会多花10分钟。我已经为您优化了路线。',\n          timestamp: Date.now() - 260000\n        }],\n        quickActions: [{\n          id: 1,\n          label: '播放音乐',\n          action: 'playMusic'\n        }, {\n          id: 2,\n          label: '查看日程',\n          action: 'showSchedule'\n        }, {\n          id: 3,\n          label: '导航回家',\n          action: 'navigateHome'\n        }, {\n          id: 4,\n          label: '控制家居',\n          action: 'controlHome'\n        }]\n      },\n      kidEducation: {\n        currentContent: {\n          type: 'video',\n          title: '小猪佩奇学数学',\n          duration: '15分钟',\n          progress: 35,\n          ageAppropriate: true\n        },\n        aiQA: {\n          lastQuestion: '地球为什么是圆的？',\n          answer: '因为呀，有一个叫\"万有引力\"的大力士，它从地球的中心把所有东西都紧紧地抱住，抱得时间太久了，就把地球抱成了一个圆滚滚的胖子啦！',\n          difficulty: 'child-friendly'\n        }\n      },\n      dynamicIsland: {\n        currentInfo: {\n          primary: '前往: 公司',\n          secondary: '预计: 25分钟',\n          status: '智能方案: 直达+咖啡预订',\n          icon: '🚀',\n          priority: 'high'\n        },\n        notifications: [{\n          id: 1,\n          type: 'navigation',\n          message: '路线已优化',\n          priority: 'medium'\n        }, {\n          id: 2,\n          type: 'order',\n          message: '咖啡预订成功',\n          priority: 'low'\n        }]\n      }\n    };\n  }\n\n  // 初始化服务\n  async initialize() {\n    if (this.isInitialized) return;\n\n    // 模拟初始化延迟\n    await new Promise(resolve => setTimeout(resolve, 500));\n    this.isInitialized = true;\n    console.log('MockDataService initialized');\n  }\n\n  // 获取导航数据\n  getNavigationData() {\n    return {\n      ...this.mockData.navigation,\n      timestamp: Date.now()\n    };\n  }\n\n  // 获取音乐数据\n  getMusicData() {\n    return {\n      ...this.mockData.music,\n      timestamp: Date.now()\n    };\n  }\n\n  // 获取日程数据\n  getScheduleData() {\n    return {\n      ...this.mockData.schedule,\n      timestamp: Date.now()\n    };\n  }\n\n  // 获取智能家居数据\n  getSmartHomeData() {\n    return {\n      ...this.mockData.smartHome,\n      timestamp: Date.now()\n    };\n  }\n\n  // 获取订单数据\n  getOrdersData() {\n    return {\n      orders: this.mockData.orders,\n      timestamp: Date.now()\n    };\n  }\n\n  // 获取VPA数据\n  getVPAData() {\n    return {\n      ...this.mockData.vpa,\n      timestamp: Date.now()\n    };\n  }\n\n  // 获取儿童教育数据\n  getKidEducationData() {\n    return {\n      ...this.mockData.kidEducation,\n      timestamp: Date.now()\n    };\n  }\n\n  // 获取灵动岛数据\n  getDynamicIslandData() {\n    return {\n      ...this.mockData.dynamicIsland,\n      timestamp: Date.now()\n    };\n  }\n\n  // AI百科数据\n  getPediaData() {\n    return {\n      searchSuggestions: [{\n        id: 1,\n        text: '什么是人工智能？',\n        icon: 'fas fa-robot'\n      }, {\n        id: 2,\n        text: '如何保持健康的生活方式？',\n        icon: 'fas fa-heart'\n      }, {\n        id: 3,\n        text: '太阳系有几颗行星？',\n        icon: 'fas fa-globe'\n      }, {\n        id: 4,\n        text: '如何学习编程？',\n        icon: 'fas fa-code'\n      }, {\n        id: 5,\n        text: '气候变化的影响',\n        icon: 'fas fa-leaf'\n      }],\n      knowledgeCategories: [{\n        id: 1,\n        name: '科学技术',\n        description: '探索科技前沿知识',\n        count: 156,\n        icon: 'fas fa-flask'\n      }, {\n        id: 2,\n        name: '健康生活',\n        description: '健康养生知识分享',\n        count: 89,\n        icon: 'fas fa-heartbeat'\n      }, {\n        id: 3,\n        name: '历史文化',\n        description: '人文历史知识宝库',\n        count: 234,\n        icon: 'fas fa-landmark'\n      }, {\n        id: 4,\n        name: '自然科学',\n        description: '物理化学生物知识',\n        count: 178,\n        icon: 'fas fa-atom'\n      }, {\n        id: 5,\n        name: '艺术文学',\n        description: '文学艺术欣赏',\n        count: 92,\n        icon: 'fas fa-palette'\n      }, {\n        id: 6,\n        name: '生活技能',\n        description: '实用生活小技巧',\n        count: 145,\n        icon: 'fas fa-tools'\n      }],\n      dailyRecommendations: [{\n        id: 1,\n        title: '人工智能的发展历程',\n        summary: '从图灵测试到ChatGPT，探索AI技术的演进之路和未来发展趋势',\n        category: '科技',\n        readTime: 5,\n        views: 1234,\n        icon: 'fas fa-robot',\n        badge: '热门'\n      }, {\n        id: 2,\n        title: '健康饮食的科学原理',\n        summary: '了解营养学基础，掌握科学饮食搭配方法',\n        category: '健康',\n        readTime: 3,\n        views: 856,\n        icon: 'fas fa-apple-alt',\n        badge: '推荐'\n      }, {\n        id: 3,\n        title: '量子计算的基本概念',\n        summary: '深入浅出解释量子计算原理和应用前景',\n        category: '科学',\n        readTime: 8,\n        views: 567,\n        icon: 'fas fa-atom',\n        badge: '新'\n      }],\n      searchHistory: [],\n      learningStats: {\n        questionsAsked: 12,\n        knowledgeGained: 45,\n        readingTime: '2小时30分',\n        streak: 7,\n        achievements: [{\n          id: 1,\n          name: '好奇宝宝',\n          description: '连续提问7天',\n          icon: 'fas fa-star'\n        }, {\n          id: 2,\n          name: '知识达人',\n          description: '学习45个知识点',\n          icon: 'fas fa-graduation-cap'\n        }]\n      },\n      timestamp: Date.now()\n    };\n  }\n\n  // 搜索知识库\n  async searchKnowledge(query) {\n    // 模拟搜索延迟\n    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));\n    const knowledgeBase = {\n      '人工智能': {\n        content: '人工智能（Artificial Intelligence，AI）是指由人制造出来的机器所表现出来的智能。通常人工智能是指通过普通计算机程序来呈现人类智能的技术。\\n\\n**主要特征：**\\n- 学习能力：能够从数据中学习和改进\\n- 推理能力：能够进行逻辑推理和决策\\n- 感知能力：能够理解和处理感官信息\\n- 语言处理：能够理解和生成自然语言',\n        confidence: 0.95,\n        images: [{\n          url: '/images/ai-brain.jpg',\n          caption: 'AI神经网络示意图'\n        }, {\n          url: '/images/ai-robot.jpg',\n          caption: '智能机器人'\n        }],\n        links: [{\n          id: 1,\n          title: 'AI发展史详解',\n          url: 'https://example.com/ai-history'\n        }, {\n          id: 2,\n          title: '机器学习入门',\n          url: 'https://example.com/ml-intro'\n        }]\n      },\n      '健康': {\n        content: '健康的生活方式包括均衡饮食、规律运动、充足睡眠和良好的心理状态。\\n\\n**核心要素：**\\n- **饮食**：多吃蔬菜水果，少吃加工食品\\n- **运动**：每周至少150分钟中等强度运动\\n- **睡眠**：成人每晚7-9小时优质睡眠\\n- **心理**：保持积极心态，学会压力管理',\n        confidence: 0.92,\n        images: [{\n          url: '/images/healthy-food.jpg',\n          caption: '健康饮食搭配'\n        }, {\n          url: '/images/exercise.jpg',\n          caption: '规律运动'\n        }],\n        links: [{\n          id: 1,\n          title: '营养学指南',\n          url: 'https://example.com/nutrition'\n        }, {\n          id: 2,\n          title: '运动健身计划',\n          url: 'https://example.com/fitness'\n        }]\n      },\n      '太阳系': {\n        content: '太阳系包含8颗行星：水星、金星、地球、火星、木星、土星、天王星、海王星。\\n\\n**行星分类：**\\n- **类地行星**：水星、金星、地球、火星\\n- **气态巨行星**：木星、土星\\n- **冰巨行星**：天王星、海王星\\n\\n冥王星在2006年被重新分类为矮行星。',\n        confidence: 0.98,\n        images: [{\n          url: '/images/solar-system.jpg',\n          caption: '太阳系示意图'\n        }, {\n          url: '/images/planets.jpg',\n          caption: '八大行星'\n        }],\n        links: [{\n          id: 1,\n          title: 'NASA太阳系探索',\n          url: 'https://example.com/nasa-solar'\n        }, {\n          id: 2,\n          title: '行星科学研究',\n          url: 'https://example.com/planetary-science'\n        }]\n      },\n      '编程': {\n        content: '学习编程需要循序渐进，从基础概念开始，逐步掌握编程思维和实践技能。\\n\\n**学习路径：**\\n1. **选择语言**：Python适合初学者\\n2. **基础语法**：变量、函数、控制结构\\n3. **数据结构**：数组、链表、树等\\n4. **算法思维**：解决问题的逻辑方法\\n5. **项目实践**：通过实际项目巩固知识',\n        confidence: 0.89,\n        images: [{\n          url: '/images/coding.jpg',\n          caption: '编程学习'\n        }, {\n          url: '/images/algorithms.jpg',\n          caption: '算法可视化'\n        }],\n        links: [{\n          id: 1,\n          title: 'Python入门教程',\n          url: 'https://example.com/python-tutorial'\n        }, {\n          id: 2,\n          title: '算法学习指南',\n          url: 'https://example.com/algorithms'\n        }]\n      }\n    };\n\n    // 简单的关键词匹配\n    for (const [key, value] of Object.entries(knowledgeBase)) {\n      if (query.includes(key) || key.includes(query)) {\n        return value;\n      }\n    }\n\n    // 默认回答\n    return {\n      content: `关于\"${query}\"的问题很有趣！这是一个值得深入探讨的话题。\\n\\n虽然我目前没有详细的相关信息，但我建议您可以：\\n- 查阅相关的专业资料\\n- 咨询领域专家\\n- 通过实践来加深理解\\n\\n如果您有更具体的问题，我很乐意为您提供更有针对性的帮助。`,\n      confidence: 0.65,\n      images: [],\n      links: []\n    };\n  }\n\n  // 模拟场景切换\n  switchScene(sceneId) {\n    this.currentScene = sceneId;\n\n    // 根据场景更新数据\n    switch (sceneId) {\n      case 'family':\n        this.mockData.navigation.currentRoute.destination = '幼儿园';\n        this.mockData.dynamicIsland.currentInfo.primary = '前往: 幼儿园';\n        break;\n      case 'focus':\n        this.mockData.navigation.currentRoute.destination = '公司';\n        this.mockData.dynamicIsland.currentInfo.primary = '前往: 公司';\n        break;\n      case 'entertainment':\n        this.mockData.navigation.currentRoute.destination = '电影院';\n        this.mockData.dynamicIsland.currentInfo.primary = '前往: 电影院';\n        break;\n      default:\n        break;\n    }\n    return this.getAllSceneData();\n  }\n\n  // 获取所有场景数据\n  getAllSceneData() {\n    return {\n      navigation: this.getNavigationData(),\n      music: this.getMusicData(),\n      schedule: this.getScheduleData(),\n      smartHome: this.getSmartHomeData(),\n      orders: this.getOrdersData(),\n      vpa: this.getVPAData(),\n      kidEducation: this.getKidEducationData(),\n      dynamicIsland: this.getDynamicIslandData(),\n      pedia: this.getPediaData(),\n      currentScene: this.currentScene,\n      timestamp: Date.now()\n    };\n  }\n\n  // 模拟设备控制\n  async controlDevice(deviceId, action, value) {\n    const device = this.mockData.smartHome.devices.find(d => d.id === deviceId);\n    if (!device) return false;\n\n    // 模拟控制延迟\n    await new Promise(resolve => setTimeout(resolve, 200));\n    switch (action) {\n      case 'toggle':\n        device.status = device.status === 'on' ? 'off' : 'on';\n        break;\n      case 'setBrightness':\n        if (device.type === 'light') {\n          device.brightness = value;\n        }\n        break;\n      case 'setTemperature':\n        if (device.type === 'climate') {\n          device.temperature = value;\n        }\n        break;\n    }\n    return true;\n  }\n\n  // 模拟VPA对话\n  async sendVPAMessage(message) {\n    // 模拟AI处理时间\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: message,\n      timestamp: Date.now()\n    };\n\n    // 简单的模拟回复逻辑\n    let vpaResponse = '';\n    if (message.includes('音乐')) {\n      vpaResponse = '好的，我为您播放适合当前场景的音乐。';\n    } else if (message.includes('导航')) {\n      vpaResponse = '已为您规划最优路线，预计节省8分钟。';\n    } else if (message.includes('家居')) {\n      vpaResponse = '正在为您控制智能家居设备。';\n    } else {\n      vpaResponse = '我明白了，让我为您处理这个请求。';\n    }\n    const vpaMessage = {\n      id: Date.now() + 1,\n      type: 'vpa',\n      content: vpaResponse,\n      timestamp: Date.now()\n    };\n    this.mockData.vpa.conversationHistory.push(userMessage, vpaMessage);\n    return vpaMessage;\n  }\n}\n\n// 创建单例实例\nconst mockDataService = new MockDataService();\nexport default mockDataService;", "map": {"version": 3, "names": ["MockDataService", "constructor", "isInitialized", "currentScene", "currentUser", "mockData", "navigation", "currentRoute", "destination", "estimatedTime", "distance", "traffic", "routePoints", "lat", "lng", "name", "trafficStatus", "level", "description", "delay", "music", "currentSong", "title", "artist", "album", "cover", "duration", "currentTime", "playlist", "mood", "isPlaying", "aiRecommendation", "reason", "scene", "schedule", "todayEvents", "id", "time", "location", "priority", "status", "aiSuggestion", "aiOptimization", "suggestion", "timeSaved", "confidence", "smartHome", "devices", "type", "icon", "brightness", "temperature", "progress", "homeStatus", "humidity", "airQuality", "energyUsage", "orders", "vendor", "item", "pickupTime", "estimatedReady", "aiOptimized", "vpa", "currentState", "lastInteraction", "Date", "now", "conversationHistory", "content", "timestamp", "quickActions", "label", "action", "kidEducation", "currentC<PERSON>nt", "ageAppropriate", "aiQA", "lastQuestion", "answer", "difficulty", "dynamicIsland", "currentInfo", "primary", "secondary", "notifications", "message", "initialize", "Promise", "resolve", "setTimeout", "console", "log", "getNavigationData", "getMusicData", "getScheduleData", "getSmartHomeData", "getOrdersData", "getVPAData", "getKidEducationData", "getDynamicIslandData", "getPediaData", "searchSuggestions", "text", "knowledgeCategories", "count", "dailyRecommendations", "summary", "category", "readTime", "views", "badge", "searchHistory", "learningStats", "questionsAsked", "knowledgeGained", "readingTime", "streak", "achievements", "searchKnowledge", "query", "Math", "random", "knowledgeBase", "images", "url", "caption", "links", "key", "value", "Object", "entries", "includes", "switchScene", "sceneId", "getAllSceneData", "pedia", "controlDevice", "deviceId", "device", "find", "d", "sendVPAMessage", "userMessage", "vpaResponse", "vpaMessage", "push", "mockDataService"], "sources": ["D:/code/pythonWork/theme/ai-hmi/src/services/MockDataService.js"], "sourcesContent": ["/**\r\n * 模拟数据服务 - 为AI-HMI项目提供仿真数据\r\n * 专为模拟座舱显示设计，无需真实API集成\r\n */\r\n\r\nclass MockDataService {\r\n  constructor() {\r\n    this.isInitialized = false;\r\n    this.currentScene = 'commute';\r\n    this.currentUser = null;\r\n    this.mockData = {\r\n      navigation: {\r\n        currentRoute: {\r\n          destination: '公司',\r\n          estimatedTime: '25分钟',\r\n          distance: '12.5公里',\r\n          traffic: 'moderate',\r\n          routePoints: [\r\n            { lat: 39.9042, lng: 116.4074, name: '家' },\r\n            { lat: 39.9142, lng: 116.4174, name: '幼儿园' },\r\n            { lat: 39.9242, lng: 116.4274, name: '公司' }\r\n          ]\r\n        },\r\n        trafficStatus: {\r\n          level: 'moderate',\r\n          description: '路况一般，比平时拥堵',\r\n          delay: '10分钟'\r\n        }\r\n      },\r\n      music: {\r\n        currentSong: {\r\n          title: '日落大道',\r\n          artist: 'AI推荐',\r\n          album: '通勤放松歌单',\r\n          cover: '/assets/music/sunset-road.jpg',\r\n          duration: 240,\r\n          currentTime: 45\r\n        },\r\n        playlist: [\r\n          { title: '日落大道', artist: 'AI推荐', mood: 'relaxing' },\r\n          { title: '晨光序曲', artist: 'AI生成', mood: 'energetic' },\r\n          { title: '城市节拍', artist: 'AI混音', mood: 'focused' }\r\n        ],\r\n        isPlaying: true,\r\n        aiRecommendation: {\r\n          reason: '基于您的通勤时间和当前情绪推荐',\r\n          mood: 'focused',\r\n          scene: 'commute'\r\n        }\r\n      },\r\n      schedule: {\r\n        todayEvents: [\r\n          {\r\n            id: 1,\r\n            title: '重要会议',\r\n            time: '10:00',\r\n            location: '会议室A',\r\n            priority: 'high',\r\n            status: 'upcoming',\r\n            aiSuggestion: '建议提前5分钟到达，已为您准备会议资料摘要'\r\n          },\r\n          {\r\n            id: 2,\r\n            title: '项目评审',\r\n            time: '14:30',\r\n            location: '会议室B',\r\n            priority: 'medium',\r\n            status: 'scheduled'\r\n          },\r\n          {\r\n            id: 3,\r\n            title: '接毛毛放学',\r\n            time: '17:00',\r\n            location: '幼儿园',\r\n            priority: 'high',\r\n            status: 'scheduled'\r\n          }\r\n        ],\r\n        aiOptimization: {\r\n          suggestion: '直达公司，咖啡已预订',\r\n          timeSaved: '8分钟',\r\n          confidence: 0.95\r\n        }\r\n      },\r\n      smartHome: {\r\n        devices: [\r\n          {\r\n            id: 1,\r\n            name: '客厅灯',\r\n            type: 'light',\r\n            status: 'on',\r\n            icon: 'fas fa-lightbulb',\r\n            brightness: 80\r\n          },\r\n          {\r\n            id: 2,\r\n            name: '空调',\r\n            type: 'climate',\r\n            status: 'off',\r\n            icon: 'fas fa-snowflake',\r\n            temperature: 24\r\n          },\r\n          {\r\n            id: 3,\r\n            name: '安防系统',\r\n            type: 'security',\r\n            status: 'armed',\r\n            icon: 'fas fa-shield-alt'\r\n          },\r\n          {\r\n            id: 4,\r\n            name: '扫地机器人',\r\n            type: 'cleaning',\r\n            status: 'cleaning',\r\n            icon: 'fas fa-robot',\r\n            progress: 65\r\n          }\r\n        ],\r\n        homeStatus: {\r\n          temperature: 22,\r\n          humidity: 45,\r\n          airQuality: 'good',\r\n          energyUsage: 'normal'\r\n        }\r\n      },\r\n      orders: [\r\n        {\r\n          id: 1,\r\n          type: 'coffee',\r\n          vendor: '瑞幸咖啡',\r\n          item: '美式咖啡 + 三明治',\r\n          status: 'confirmed',\r\n          pickupTime: '09:35',\r\n          location: '公司楼下',\r\n          estimatedReady: '5分钟',\r\n          aiOptimized: true\r\n        }\r\n      ],\r\n      vpa: {\r\n        currentState: 'companion',\r\n        mood: 'helpful',\r\n        lastInteraction: Date.now() - 30000,\r\n        conversationHistory: [\r\n          {\r\n            id: 1,\r\n            type: 'vpa',\r\n            content: 'Hello，主人和毛毛你好。按往常一样先送毛毛上学吧？',\r\n            timestamp: Date.now() - 300000\r\n          },\r\n          {\r\n            id: 2,\r\n            type: 'user',\r\n            content: '是的，今天路况怎么样？',\r\n            timestamp: Date.now() - 280000\r\n          },\r\n          {\r\n            id: 3,\r\n            type: 'vpa',\r\n            content: '今天路况比平时拥堵一些，预计会多花10分钟。我已经为您优化了路线。',\r\n            timestamp: Date.now() - 260000\r\n          }\r\n        ],\r\n        quickActions: [\r\n          { id: 1, label: '播放音乐', action: 'playMusic' },\r\n          { id: 2, label: '查看日程', action: 'showSchedule' },\r\n          { id: 3, label: '导航回家', action: 'navigateHome' },\r\n          { id: 4, label: '控制家居', action: 'controlHome' }\r\n        ]\r\n      },\r\n      kidEducation: {\r\n        currentContent: {\r\n          type: 'video',\r\n          title: '小猪佩奇学数学',\r\n          duration: '15分钟',\r\n          progress: 35,\r\n          ageAppropriate: true\r\n        },\r\n        aiQA: {\r\n          lastQuestion: '地球为什么是圆的？',\r\n          answer: '因为呀，有一个叫\"万有引力\"的大力士，它从地球的中心把所有东西都紧紧地抱住，抱得时间太久了，就把地球抱成了一个圆滚滚的胖子啦！',\r\n          difficulty: 'child-friendly'\r\n        }\r\n      },\r\n      dynamicIsland: {\r\n        currentInfo: {\r\n          primary: '前往: 公司',\r\n          secondary: '预计: 25分钟',\r\n          status: '智能方案: 直达+咖啡预订',\r\n          icon: '🚀',\r\n          priority: 'high'\r\n        },\r\n        notifications: [\r\n          {\r\n            id: 1,\r\n            type: 'navigation',\r\n            message: '路线已优化',\r\n            priority: 'medium'\r\n          },\r\n          {\r\n            id: 2,\r\n            type: 'order',\r\n            message: '咖啡预订成功',\r\n            priority: 'low'\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  }\r\n\r\n  // 初始化服务\r\n  async initialize() {\r\n    if (this.isInitialized) return;\r\n    \r\n    // 模拟初始化延迟\r\n    await new Promise(resolve => setTimeout(resolve, 500));\r\n    \r\n    this.isInitialized = true;\r\n    console.log('MockDataService initialized');\r\n  }\r\n\r\n  // 获取导航数据\r\n  getNavigationData() {\r\n    return {\r\n      ...this.mockData.navigation,\r\n      timestamp: Date.now()\r\n    };\r\n  }\r\n\r\n  // 获取音乐数据\r\n  getMusicData() {\r\n    return {\r\n      ...this.mockData.music,\r\n      timestamp: Date.now()\r\n    };\r\n  }\r\n\r\n  // 获取日程数据\r\n  getScheduleData() {\r\n    return {\r\n      ...this.mockData.schedule,\r\n      timestamp: Date.now()\r\n    };\r\n  }\r\n\r\n  // 获取智能家居数据\r\n  getSmartHomeData() {\r\n    return {\r\n      ...this.mockData.smartHome,\r\n      timestamp: Date.now()\r\n    };\r\n  }\r\n\r\n  // 获取订单数据\r\n  getOrdersData() {\r\n    return {\r\n      orders: this.mockData.orders,\r\n      timestamp: Date.now()\r\n    };\r\n  }\r\n\r\n  // 获取VPA数据\r\n  getVPAData() {\r\n    return {\r\n      ...this.mockData.vpa,\r\n      timestamp: Date.now()\r\n    };\r\n  }\r\n\r\n  // 获取儿童教育数据\r\n  getKidEducationData() {\r\n    return {\r\n      ...this.mockData.kidEducation,\r\n      timestamp: Date.now()\r\n    };\r\n  }\r\n\r\n  // 获取灵动岛数据\r\n  getDynamicIslandData() {\r\n    return {\r\n      ...this.mockData.dynamicIsland,\r\n      timestamp: Date.now()\r\n    };\r\n  }\r\n\r\n  // AI百科数据\r\n  getPediaData() {\r\n    return {\r\n      searchSuggestions: [\r\n        { id: 1, text: '什么是人工智能？', icon: 'fas fa-robot' },\r\n        { id: 2, text: '如何保持健康的生活方式？', icon: 'fas fa-heart' },\r\n        { id: 3, text: '太阳系有几颗行星？', icon: 'fas fa-globe' },\r\n        { id: 4, text: '如何学习编程？', icon: 'fas fa-code' },\r\n        { id: 5, text: '气候变化的影响', icon: 'fas fa-leaf' }\r\n      ],\r\n      knowledgeCategories: [\r\n        { id: 1, name: '科学技术', description: '探索科技前沿知识', count: 156, icon: 'fas fa-flask' },\r\n        { id: 2, name: '健康生活', description: '健康养生知识分享', count: 89, icon: 'fas fa-heartbeat' },\r\n        { id: 3, name: '历史文化', description: '人文历史知识宝库', count: 234, icon: 'fas fa-landmark' },\r\n        { id: 4, name: '自然科学', description: '物理化学生物知识', count: 178, icon: 'fas fa-atom' },\r\n        { id: 5, name: '艺术文学', description: '文学艺术欣赏', count: 92, icon: 'fas fa-palette' },\r\n        { id: 6, name: '生活技能', description: '实用生活小技巧', count: 145, icon: 'fas fa-tools' }\r\n      ],\r\n      dailyRecommendations: [\r\n        {\r\n          id: 1,\r\n          title: '人工智能的发展历程',\r\n          summary: '从图灵测试到ChatGPT，探索AI技术的演进之路和未来发展趋势',\r\n          category: '科技',\r\n          readTime: 5,\r\n          views: 1234,\r\n          icon: 'fas fa-robot',\r\n          badge: '热门'\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '健康饮食的科学原理',\r\n          summary: '了解营养学基础，掌握科学饮食搭配方法',\r\n          category: '健康',\r\n          readTime: 3,\r\n          views: 856,\r\n          icon: 'fas fa-apple-alt',\r\n          badge: '推荐'\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '量子计算的基本概念',\r\n          summary: '深入浅出解释量子计算原理和应用前景',\r\n          category: '科学',\r\n          readTime: 8,\r\n          views: 567,\r\n          icon: 'fas fa-atom',\r\n          badge: '新'\r\n        }\r\n      ],\r\n      searchHistory: [],\r\n      learningStats: {\r\n        questionsAsked: 12,\r\n        knowledgeGained: 45,\r\n        readingTime: '2小时30分',\r\n        streak: 7,\r\n        achievements: [\r\n          { id: 1, name: '好奇宝宝', description: '连续提问7天', icon: 'fas fa-star' },\r\n          { id: 2, name: '知识达人', description: '学习45个知识点', icon: 'fas fa-graduation-cap' }\r\n        ]\r\n      },\r\n      timestamp: Date.now()\r\n    };\r\n  }\r\n\r\n  // 搜索知识库\r\n  async searchKnowledge(query) {\r\n    // 模拟搜索延迟\r\n    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));\r\n    \r\n    const knowledgeBase = {\r\n      '人工智能': {\r\n        content: '人工智能（Artificial Intelligence，AI）是指由人制造出来的机器所表现出来的智能。通常人工智能是指通过普通计算机程序来呈现人类智能的技术。\\n\\n**主要特征：**\\n- 学习能力：能够从数据中学习和改进\\n- 推理能力：能够进行逻辑推理和决策\\n- 感知能力：能够理解和处理感官信息\\n- 语言处理：能够理解和生成自然语言',\r\n        confidence: 0.95,\r\n        images: [\r\n          { url: '/images/ai-brain.jpg', caption: 'AI神经网络示意图' },\r\n          { url: '/images/ai-robot.jpg', caption: '智能机器人' }\r\n        ],\r\n        links: [\r\n          { id: 1, title: 'AI发展史详解', url: 'https://example.com/ai-history' },\r\n          { id: 2, title: '机器学习入门', url: 'https://example.com/ml-intro' }\r\n        ]\r\n      },\r\n      '健康': {\r\n        content: '健康的生活方式包括均衡饮食、规律运动、充足睡眠和良好的心理状态。\\n\\n**核心要素：**\\n- **饮食**：多吃蔬菜水果，少吃加工食品\\n- **运动**：每周至少150分钟中等强度运动\\n- **睡眠**：成人每晚7-9小时优质睡眠\\n- **心理**：保持积极心态，学会压力管理',\r\n        confidence: 0.92,\r\n        images: [\r\n          { url: '/images/healthy-food.jpg', caption: '健康饮食搭配' },\r\n          { url: '/images/exercise.jpg', caption: '规律运动' }\r\n        ],\r\n        links: [\r\n          { id: 1, title: '营养学指南', url: 'https://example.com/nutrition' },\r\n          { id: 2, title: '运动健身计划', url: 'https://example.com/fitness' }\r\n        ]\r\n      },\r\n      '太阳系': {\r\n        content: '太阳系包含8颗行星：水星、金星、地球、火星、木星、土星、天王星、海王星。\\n\\n**行星分类：**\\n- **类地行星**：水星、金星、地球、火星\\n- **气态巨行星**：木星、土星\\n- **冰巨行星**：天王星、海王星\\n\\n冥王星在2006年被重新分类为矮行星。',\r\n        confidence: 0.98,\r\n        images: [\r\n          { url: '/images/solar-system.jpg', caption: '太阳系示意图' },\r\n          { url: '/images/planets.jpg', caption: '八大行星' }\r\n        ],\r\n        links: [\r\n          { id: 1, title: 'NASA太阳系探索', url: 'https://example.com/nasa-solar' },\r\n          { id: 2, title: '行星科学研究', url: 'https://example.com/planetary-science' }\r\n        ]\r\n      },\r\n      '编程': {\r\n        content: '学习编程需要循序渐进，从基础概念开始，逐步掌握编程思维和实践技能。\\n\\n**学习路径：**\\n1. **选择语言**：Python适合初学者\\n2. **基础语法**：变量、函数、控制结构\\n3. **数据结构**：数组、链表、树等\\n4. **算法思维**：解决问题的逻辑方法\\n5. **项目实践**：通过实际项目巩固知识',\r\n        confidence: 0.89,\r\n        images: [\r\n          { url: '/images/coding.jpg', caption: '编程学习' },\r\n          { url: '/images/algorithms.jpg', caption: '算法可视化' }\r\n        ],\r\n        links: [\r\n          { id: 1, title: 'Python入门教程', url: 'https://example.com/python-tutorial' },\r\n          { id: 2, title: '算法学习指南', url: 'https://example.com/algorithms' }\r\n        ]\r\n      }\r\n    };\r\n    \r\n    // 简单的关键词匹配\r\n    for (const [key, value] of Object.entries(knowledgeBase)) {\r\n      if (query.includes(key) || key.includes(query)) {\r\n        return value;\r\n      }\r\n    }\r\n    \r\n    // 默认回答\r\n    return {\r\n      content: `关于\"${query}\"的问题很有趣！这是一个值得深入探讨的话题。\\n\\n虽然我目前没有详细的相关信息，但我建议您可以：\\n- 查阅相关的专业资料\\n- 咨询领域专家\\n- 通过实践来加深理解\\n\\n如果您有更具体的问题，我很乐意为您提供更有针对性的帮助。`,\r\n      confidence: 0.65,\r\n      images: [],\r\n      links: []\r\n    };\r\n  }\r\n\r\n  // 模拟场景切换\r\n  switchScene(sceneId) {\r\n    this.currentScene = sceneId;\r\n    \r\n    // 根据场景更新数据\r\n    switch (sceneId) {\r\n      case 'family':\r\n        this.mockData.navigation.currentRoute.destination = '幼儿园';\r\n        this.mockData.dynamicIsland.currentInfo.primary = '前往: 幼儿园';\r\n        break;\r\n      case 'focus':\r\n        this.mockData.navigation.currentRoute.destination = '公司';\r\n        this.mockData.dynamicIsland.currentInfo.primary = '前往: 公司';\r\n        break;\r\n      case 'entertainment':\r\n        this.mockData.navigation.currentRoute.destination = '电影院';\r\n        this.mockData.dynamicIsland.currentInfo.primary = '前往: 电影院';\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n    \r\n    return this.getAllSceneData();\r\n  }\r\n\r\n  // 获取所有场景数据\r\n  getAllSceneData() {\r\n    return {\r\n      navigation: this.getNavigationData(),\r\n      music: this.getMusicData(),\r\n      schedule: this.getScheduleData(),\r\n      smartHome: this.getSmartHomeData(),\r\n      orders: this.getOrdersData(),\r\n      vpa: this.getVPAData(),\r\n      kidEducation: this.getKidEducationData(),\r\n      dynamicIsland: this.getDynamicIslandData(),\r\n      pedia: this.getPediaData(),\r\n      currentScene: this.currentScene,\r\n      timestamp: Date.now()\r\n    };\r\n  }\r\n\r\n  // 模拟设备控制\r\n  async controlDevice(deviceId, action, value) {\r\n    const device = this.mockData.smartHome.devices.find(d => d.id === deviceId);\r\n    if (!device) return false;\r\n\r\n    // 模拟控制延迟\r\n    await new Promise(resolve => setTimeout(resolve, 200));\r\n\r\n    switch (action) {\r\n      case 'toggle':\r\n        device.status = device.status === 'on' ? 'off' : 'on';\r\n        break;\r\n      case 'setBrightness':\r\n        if (device.type === 'light') {\r\n          device.brightness = value;\r\n        }\r\n        break;\r\n      case 'setTemperature':\r\n        if (device.type === 'climate') {\r\n          device.temperature = value;\r\n        }\r\n        break;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  // 模拟VPA对话\r\n  async sendVPAMessage(message) {\r\n    // 模拟AI处理时间\r\n    await new Promise(resolve => setTimeout(resolve, 1000));\r\n\r\n    const userMessage = {\r\n      id: Date.now(),\r\n      type: 'user',\r\n      content: message,\r\n      timestamp: Date.now()\r\n    };\r\n\r\n    // 简单的模拟回复逻辑\r\n    let vpaResponse = '';\r\n    if (message.includes('音乐')) {\r\n      vpaResponse = '好的，我为您播放适合当前场景的音乐。';\r\n    } else if (message.includes('导航')) {\r\n      vpaResponse = '已为您规划最优路线，预计节省8分钟。';\r\n    } else if (message.includes('家居')) {\r\n      vpaResponse = '正在为您控制智能家居设备。';\r\n    } else {\r\n      vpaResponse = '我明白了，让我为您处理这个请求。';\r\n    }\r\n\r\n    const vpaMessage = {\r\n      id: Date.now() + 1,\r\n      type: 'vpa',\r\n      content: vpaResponse,\r\n      timestamp: Date.now()\r\n    };\r\n\r\n    this.mockData.vpa.conversationHistory.push(userMessage, vpaMessage);\r\n    \r\n    return vpaMessage;\r\n  }\r\n}\r\n\r\n// 创建单例实例\r\nconst mockDataService = new MockDataService();\r\n\r\nexport default mockDataService;"], "mappings": ";;;AAAA;AACA;AACA;AACA;;AAEA,MAAMA,eAAe,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,YAAY,GAAG,SAAS;IAC7B,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,QAAQ,GAAG;MACdC,UAAU,EAAE;QACVC,YAAY,EAAE;UACZC,WAAW,EAAE,IAAI;UACjBC,aAAa,EAAE,MAAM;UACrBC,QAAQ,EAAE,QAAQ;UAClBC,OAAO,EAAE,UAAU;UACnBC,WAAW,EAAE,CACX;YAAEC,GAAG,EAAE,OAAO;YAAEC,GAAG,EAAE,QAAQ;YAAEC,IAAI,EAAE;UAAI,CAAC,EAC1C;YAAEF,GAAG,EAAE,OAAO;YAAEC,GAAG,EAAE,QAAQ;YAAEC,IAAI,EAAE;UAAM,CAAC,EAC5C;YAAEF,GAAG,EAAE,OAAO;YAAEC,GAAG,EAAE,QAAQ;YAAEC,IAAI,EAAE;UAAK,CAAC;QAE/C,CAAC;QACDC,aAAa,EAAE;UACbC,KAAK,EAAE,UAAU;UACjBC,WAAW,EAAE,YAAY;UACzBC,KAAK,EAAE;QACT;MACF,CAAC;MACDC,KAAK,EAAE;QACLC,WAAW,EAAE;UACXC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,KAAK,EAAE,QAAQ;UACfC,KAAK,EAAE,+BAA+B;UACtCC,QAAQ,EAAE,GAAG;UACbC,WAAW,EAAE;QACf,CAAC;QACDC,QAAQ,EAAE,CACR;UAAEN,KAAK,EAAE,MAAM;UAAEC,MAAM,EAAE,MAAM;UAAEM,IAAI,EAAE;QAAW,CAAC,EACnD;UAAEP,KAAK,EAAE,MAAM;UAAEC,MAAM,EAAE,MAAM;UAAEM,IAAI,EAAE;QAAY,CAAC,EACpD;UAAEP,KAAK,EAAE,MAAM;UAAEC,MAAM,EAAE,MAAM;UAAEM,IAAI,EAAE;QAAU,CAAC,CACnD;QACDC,SAAS,EAAE,IAAI;QACfC,gBAAgB,EAAE;UAChBC,MAAM,EAAE,iBAAiB;UACzBH,IAAI,EAAE,SAAS;UACfI,KAAK,EAAE;QACT;MACF,CAAC;MACDC,QAAQ,EAAE;QACRC,WAAW,EAAE,CACX;UACEC,EAAE,EAAE,CAAC;UACLd,KAAK,EAAE,MAAM;UACbe,IAAI,EAAE,OAAO;UACbC,QAAQ,EAAE,MAAM;UAChBC,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE,UAAU;UAClBC,YAAY,EAAE;QAChB,CAAC,EACD;UACEL,EAAE,EAAE,CAAC;UACLd,KAAK,EAAE,MAAM;UACbe,IAAI,EAAE,OAAO;UACbC,QAAQ,EAAE,MAAM;UAChBC,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE;QACV,CAAC,EACD;UACEJ,EAAE,EAAE,CAAC;UACLd,KAAK,EAAE,OAAO;UACde,IAAI,EAAE,OAAO;UACbC,QAAQ,EAAE,KAAK;UACfC,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE;QACV,CAAC,CACF;QACDE,cAAc,EAAE;UACdC,UAAU,EAAE,YAAY;UACxBC,SAAS,EAAE,KAAK;UAChBC,UAAU,EAAE;QACd;MACF,CAAC;MACDC,SAAS,EAAE;QACTC,OAAO,EAAE,CACP;UACEX,EAAE,EAAE,CAAC;UACLrB,IAAI,EAAE,KAAK;UACXiC,IAAI,EAAE,OAAO;UACbR,MAAM,EAAE,IAAI;UACZS,IAAI,EAAE,kBAAkB;UACxBC,UAAU,EAAE;QACd,CAAC,EACD;UACEd,EAAE,EAAE,CAAC;UACLrB,IAAI,EAAE,IAAI;UACViC,IAAI,EAAE,SAAS;UACfR,MAAM,EAAE,KAAK;UACbS,IAAI,EAAE,kBAAkB;UACxBE,WAAW,EAAE;QACf,CAAC,EACD;UACEf,EAAE,EAAE,CAAC;UACLrB,IAAI,EAAE,MAAM;UACZiC,IAAI,EAAE,UAAU;UAChBR,MAAM,EAAE,OAAO;UACfS,IAAI,EAAE;QACR,CAAC,EACD;UACEb,EAAE,EAAE,CAAC;UACLrB,IAAI,EAAE,OAAO;UACbiC,IAAI,EAAE,UAAU;UAChBR,MAAM,EAAE,UAAU;UAClBS,IAAI,EAAE,cAAc;UACpBG,QAAQ,EAAE;QACZ,CAAC,CACF;QACDC,UAAU,EAAE;UACVF,WAAW,EAAE,EAAE;UACfG,QAAQ,EAAE,EAAE;UACZC,UAAU,EAAE,MAAM;UAClBC,WAAW,EAAE;QACf;MACF,CAAC;MACDC,MAAM,EAAE,CACN;QACErB,EAAE,EAAE,CAAC;QACLY,IAAI,EAAE,QAAQ;QACdU,MAAM,EAAE,MAAM;QACdC,IAAI,EAAE,YAAY;QAClBnB,MAAM,EAAE,WAAW;QACnBoB,UAAU,EAAE,OAAO;QACnBtB,QAAQ,EAAE,MAAM;QAChBuB,cAAc,EAAE,KAAK;QACrBC,WAAW,EAAE;MACf,CAAC,CACF;MACDC,GAAG,EAAE;QACHC,YAAY,EAAE,WAAW;QACzBnC,IAAI,EAAE,SAAS;QACfoC,eAAe,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,KAAK;QACnCC,mBAAmB,EAAE,CACnB;UACEhC,EAAE,EAAE,CAAC;UACLY,IAAI,EAAE,KAAK;UACXqB,OAAO,EAAE,6BAA6B;UACtCC,SAAS,EAAEJ,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG;QAC1B,CAAC,EACD;UACE/B,EAAE,EAAE,CAAC;UACLY,IAAI,EAAE,MAAM;UACZqB,OAAO,EAAE,aAAa;UACtBC,SAAS,EAAEJ,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG;QAC1B,CAAC,EACD;UACE/B,EAAE,EAAE,CAAC;UACLY,IAAI,EAAE,KAAK;UACXqB,OAAO,EAAE,mCAAmC;UAC5CC,SAAS,EAAEJ,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG;QAC1B,CAAC,CACF;QACDI,YAAY,EAAE,CACZ;UAAEnC,EAAE,EAAE,CAAC;UAAEoC,KAAK,EAAE,MAAM;UAAEC,MAAM,EAAE;QAAY,CAAC,EAC7C;UAAErC,EAAE,EAAE,CAAC;UAAEoC,KAAK,EAAE,MAAM;UAAEC,MAAM,EAAE;QAAe,CAAC,EAChD;UAAErC,EAAE,EAAE,CAAC;UAAEoC,KAAK,EAAE,MAAM;UAAEC,MAAM,EAAE;QAAe,CAAC,EAChD;UAAErC,EAAE,EAAE,CAAC;UAAEoC,KAAK,EAAE,MAAM;UAAEC,MAAM,EAAE;QAAc,CAAC;MAEnD,CAAC;MACDC,YAAY,EAAE;QACZC,cAAc,EAAE;UACd3B,IAAI,EAAE,OAAO;UACb1B,KAAK,EAAE,SAAS;UAChBI,QAAQ,EAAE,MAAM;UAChB0B,QAAQ,EAAE,EAAE;UACZwB,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAE;UACJC,YAAY,EAAE,WAAW;UACzBC,MAAM,EAAE,iEAAiE;UACzEC,UAAU,EAAE;QACd;MACF,CAAC;MACDC,aAAa,EAAE;QACbC,WAAW,EAAE;UACXC,OAAO,EAAE,QAAQ;UACjBC,SAAS,EAAE,UAAU;UACrB5C,MAAM,EAAE,eAAe;UACvBS,IAAI,EAAE,IAAI;UACVV,QAAQ,EAAE;QACZ,CAAC;QACD8C,aAAa,EAAE,CACb;UACEjD,EAAE,EAAE,CAAC;UACLY,IAAI,EAAE,YAAY;UAClBsC,OAAO,EAAE,OAAO;UAChB/C,QAAQ,EAAE;QACZ,CAAC,EACD;UACEH,EAAE,EAAE,CAAC;UACLY,IAAI,EAAE,OAAO;UACbsC,OAAO,EAAE,QAAQ;UACjB/C,QAAQ,EAAE;QACZ,CAAC;MAEL;IACF,CAAC;EACH;;EAEA;EACA,MAAMgD,UAAUA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACrF,aAAa,EAAE;;IAExB;IACA,MAAM,IAAIsF,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,IAAI,CAACvF,aAAa,GAAG,IAAI;IACzByF,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;EAC5C;;EAEA;EACAC,iBAAiBA,CAAA,EAAG;IAClB,OAAO;MACL,GAAG,IAAI,CAACxF,QAAQ,CAACC,UAAU;MAC3BgE,SAAS,EAAEJ,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;EACH;;EAEA;EACA2B,YAAYA,CAAA,EAAG;IACb,OAAO;MACL,GAAG,IAAI,CAACzF,QAAQ,CAACe,KAAK;MACtBkD,SAAS,EAAEJ,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;EACH;;EAEA;EACA4B,eAAeA,CAAA,EAAG;IAChB,OAAO;MACL,GAAG,IAAI,CAAC1F,QAAQ,CAAC6B,QAAQ;MACzBoC,SAAS,EAAEJ,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;EACH;;EAEA;EACA6B,gBAAgBA,CAAA,EAAG;IACjB,OAAO;MACL,GAAG,IAAI,CAAC3F,QAAQ,CAACyC,SAAS;MAC1BwB,SAAS,EAAEJ,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;EACH;;EAEA;EACA8B,aAAaA,CAAA,EAAG;IACd,OAAO;MACLxC,MAAM,EAAE,IAAI,CAACpD,QAAQ,CAACoD,MAAM;MAC5Ba,SAAS,EAAEJ,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;EACH;;EAEA;EACA+B,UAAUA,CAAA,EAAG;IACX,OAAO;MACL,GAAG,IAAI,CAAC7F,QAAQ,CAAC0D,GAAG;MACpBO,SAAS,EAAEJ,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;EACH;;EAEA;EACAgC,mBAAmBA,CAAA,EAAG;IACpB,OAAO;MACL,GAAG,IAAI,CAAC9F,QAAQ,CAACqE,YAAY;MAC7BJ,SAAS,EAAEJ,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;EACH;;EAEA;EACAiC,oBAAoBA,CAAA,EAAG;IACrB,OAAO;MACL,GAAG,IAAI,CAAC/F,QAAQ,CAAC4E,aAAa;MAC9BX,SAAS,EAAEJ,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;EACH;;EAEA;EACAkC,YAAYA,CAAA,EAAG;IACb,OAAO;MACLC,iBAAiB,EAAE,CACjB;QAAElE,EAAE,EAAE,CAAC;QAAEmE,IAAI,EAAE,UAAU;QAAEtD,IAAI,EAAE;MAAe,CAAC,EACjD;QAAEb,EAAE,EAAE,CAAC;QAAEmE,IAAI,EAAE,cAAc;QAAEtD,IAAI,EAAE;MAAe,CAAC,EACrD;QAAEb,EAAE,EAAE,CAAC;QAAEmE,IAAI,EAAE,WAAW;QAAEtD,IAAI,EAAE;MAAe,CAAC,EAClD;QAAEb,EAAE,EAAE,CAAC;QAAEmE,IAAI,EAAE,SAAS;QAAEtD,IAAI,EAAE;MAAc,CAAC,EAC/C;QAAEb,EAAE,EAAE,CAAC;QAAEmE,IAAI,EAAE,SAAS;QAAEtD,IAAI,EAAE;MAAc,CAAC,CAChD;MACDuD,mBAAmB,EAAE,CACnB;QAAEpE,EAAE,EAAE,CAAC;QAAErB,IAAI,EAAE,MAAM;QAAEG,WAAW,EAAE,UAAU;QAAEuF,KAAK,EAAE,GAAG;QAAExD,IAAI,EAAE;MAAe,CAAC,EAClF;QAAEb,EAAE,EAAE,CAAC;QAAErB,IAAI,EAAE,MAAM;QAAEG,WAAW,EAAE,UAAU;QAAEuF,KAAK,EAAE,EAAE;QAAExD,IAAI,EAAE;MAAmB,CAAC,EACrF;QAAEb,EAAE,EAAE,CAAC;QAAErB,IAAI,EAAE,MAAM;QAAEG,WAAW,EAAE,UAAU;QAAEuF,KAAK,EAAE,GAAG;QAAExD,IAAI,EAAE;MAAkB,CAAC,EACrF;QAAEb,EAAE,EAAE,CAAC;QAAErB,IAAI,EAAE,MAAM;QAAEG,WAAW,EAAE,UAAU;QAAEuF,KAAK,EAAE,GAAG;QAAExD,IAAI,EAAE;MAAc,CAAC,EACjF;QAAEb,EAAE,EAAE,CAAC;QAAErB,IAAI,EAAE,MAAM;QAAEG,WAAW,EAAE,QAAQ;QAAEuF,KAAK,EAAE,EAAE;QAAExD,IAAI,EAAE;MAAiB,CAAC,EACjF;QAAEb,EAAE,EAAE,CAAC;QAAErB,IAAI,EAAE,MAAM;QAAEG,WAAW,EAAE,SAAS;QAAEuF,KAAK,EAAE,GAAG;QAAExD,IAAI,EAAE;MAAe,CAAC,CAClF;MACDyD,oBAAoB,EAAE,CACpB;QACEtE,EAAE,EAAE,CAAC;QACLd,KAAK,EAAE,WAAW;QAClBqF,OAAO,EAAE,kCAAkC;QAC3CC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE,IAAI;QACX7D,IAAI,EAAE,cAAc;QACpB8D,KAAK,EAAE;MACT,CAAC,EACD;QACE3E,EAAE,EAAE,CAAC;QACLd,KAAK,EAAE,WAAW;QAClBqF,OAAO,EAAE,oBAAoB;QAC7BC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE,GAAG;QACV7D,IAAI,EAAE,kBAAkB;QACxB8D,KAAK,EAAE;MACT,CAAC,EACD;QACE3E,EAAE,EAAE,CAAC;QACLd,KAAK,EAAE,WAAW;QAClBqF,OAAO,EAAE,mBAAmB;QAC5BC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE,GAAG;QACV7D,IAAI,EAAE,aAAa;QACnB8D,KAAK,EAAE;MACT,CAAC,CACF;MACDC,aAAa,EAAE,EAAE;MACjBC,aAAa,EAAE;QACbC,cAAc,EAAE,EAAE;QAClBC,eAAe,EAAE,EAAE;QACnBC,WAAW,EAAE,QAAQ;QACrBC,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,CACZ;UAAElF,EAAE,EAAE,CAAC;UAAErB,IAAI,EAAE,MAAM;UAAEG,WAAW,EAAE,QAAQ;UAAE+B,IAAI,EAAE;QAAc,CAAC,EACnE;UAAEb,EAAE,EAAE,CAAC;UAAErB,IAAI,EAAE,MAAM;UAAEG,WAAW,EAAE,UAAU;UAAE+B,IAAI,EAAE;QAAwB,CAAC;MAEnF,CAAC;MACDqB,SAAS,EAAEJ,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;EACH;;EAEA;EACA,MAAMoD,eAAeA,CAACC,KAAK,EAAE;IAC3B;IACA,MAAM,IAAIhC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,GAAGgC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IAE9E,MAAMC,aAAa,GAAG;MACpB,MAAM,EAAE;QACNtD,OAAO,EAAE,+KAA+K;QACxLxB,UAAU,EAAE,IAAI;QAChB+E,MAAM,EAAE,CACN;UAAEC,GAAG,EAAE,sBAAsB;UAAEC,OAAO,EAAE;QAAY,CAAC,EACrD;UAAED,GAAG,EAAE,sBAAsB;UAAEC,OAAO,EAAE;QAAQ,CAAC,CAClD;QACDC,KAAK,EAAE,CACL;UAAE3F,EAAE,EAAE,CAAC;UAAEd,KAAK,EAAE,SAAS;UAAEuG,GAAG,EAAE;QAAiC,CAAC,EAClE;UAAEzF,EAAE,EAAE,CAAC;UAAEd,KAAK,EAAE,QAAQ;UAAEuG,GAAG,EAAE;QAA+B,CAAC;MAEnE,CAAC;MACD,IAAI,EAAE;QACJxD,OAAO,EAAE,iJAAiJ;QAC1JxB,UAAU,EAAE,IAAI;QAChB+E,MAAM,EAAE,CACN;UAAEC,GAAG,EAAE,0BAA0B;UAAEC,OAAO,EAAE;QAAS,CAAC,EACtD;UAAED,GAAG,EAAE,sBAAsB;UAAEC,OAAO,EAAE;QAAO,CAAC,CACjD;QACDC,KAAK,EAAE,CACL;UAAE3F,EAAE,EAAE,CAAC;UAAEd,KAAK,EAAE,OAAO;UAAEuG,GAAG,EAAE;QAAgC,CAAC,EAC/D;UAAEzF,EAAE,EAAE,CAAC;UAAEd,KAAK,EAAE,QAAQ;UAAEuG,GAAG,EAAE;QAA8B,CAAC;MAElE,CAAC;MACD,KAAK,EAAE;QACLxD,OAAO,EAAE,yIAAyI;QAClJxB,UAAU,EAAE,IAAI;QAChB+E,MAAM,EAAE,CACN;UAAEC,GAAG,EAAE,0BAA0B;UAAEC,OAAO,EAAE;QAAS,CAAC,EACtD;UAAED,GAAG,EAAE,qBAAqB;UAAEC,OAAO,EAAE;QAAO,CAAC,CAChD;QACDC,KAAK,EAAE,CACL;UAAE3F,EAAE,EAAE,CAAC;UAAEd,KAAK,EAAE,WAAW;UAAEuG,GAAG,EAAE;QAAiC,CAAC,EACpE;UAAEzF,EAAE,EAAE,CAAC;UAAEd,KAAK,EAAE,QAAQ;UAAEuG,GAAG,EAAE;QAAwC,CAAC;MAE5E,CAAC;MACD,IAAI,EAAE;QACJxD,OAAO,EAAE,sKAAsK;QAC/KxB,UAAU,EAAE,IAAI;QAChB+E,MAAM,EAAE,CACN;UAAEC,GAAG,EAAE,oBAAoB;UAAEC,OAAO,EAAE;QAAO,CAAC,EAC9C;UAAED,GAAG,EAAE,wBAAwB;UAAEC,OAAO,EAAE;QAAQ,CAAC,CACpD;QACDC,KAAK,EAAE,CACL;UAAE3F,EAAE,EAAE,CAAC;UAAEd,KAAK,EAAE,YAAY;UAAEuG,GAAG,EAAE;QAAsC,CAAC,EAC1E;UAAEzF,EAAE,EAAE,CAAC;UAAEd,KAAK,EAAE,QAAQ;UAAEuG,GAAG,EAAE;QAAiC,CAAC;MAErE;IACF,CAAC;;IAED;IACA,KAAK,MAAM,CAACG,GAAG,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACR,aAAa,CAAC,EAAE;MACxD,IAAIH,KAAK,CAACY,QAAQ,CAACJ,GAAG,CAAC,IAAIA,GAAG,CAACI,QAAQ,CAACZ,KAAK,CAAC,EAAE;QAC9C,OAAOS,KAAK;MACd;IACF;;IAEA;IACA,OAAO;MACL5D,OAAO,EAAE,MAAMmD,KAAK,uHAAuH;MAC3I3E,UAAU,EAAE,IAAI;MAChB+E,MAAM,EAAE,EAAE;MACVG,KAAK,EAAE;IACT,CAAC;EACH;;EAEA;EACAM,WAAWA,CAACC,OAAO,EAAE;IACnB,IAAI,CAACnI,YAAY,GAAGmI,OAAO;;IAE3B;IACA,QAAQA,OAAO;MACb,KAAK,QAAQ;QACX,IAAI,CAACjI,QAAQ,CAACC,UAAU,CAACC,YAAY,CAACC,WAAW,GAAG,KAAK;QACzD,IAAI,CAACH,QAAQ,CAAC4E,aAAa,CAACC,WAAW,CAACC,OAAO,GAAG,SAAS;QAC3D;MACF,KAAK,OAAO;QACV,IAAI,CAAC9E,QAAQ,CAACC,UAAU,CAACC,YAAY,CAACC,WAAW,GAAG,IAAI;QACxD,IAAI,CAACH,QAAQ,CAAC4E,aAAa,CAACC,WAAW,CAACC,OAAO,GAAG,QAAQ;QAC1D;MACF,KAAK,eAAe;QAClB,IAAI,CAAC9E,QAAQ,CAACC,UAAU,CAACC,YAAY,CAACC,WAAW,GAAG,KAAK;QACzD,IAAI,CAACH,QAAQ,CAAC4E,aAAa,CAACC,WAAW,CAACC,OAAO,GAAG,SAAS;QAC3D;MACF;QACE;IACJ;IAEA,OAAO,IAAI,CAACoD,eAAe,CAAC,CAAC;EAC/B;;EAEA;EACAA,eAAeA,CAAA,EAAG;IAChB,OAAO;MACLjI,UAAU,EAAE,IAAI,CAACuF,iBAAiB,CAAC,CAAC;MACpCzE,KAAK,EAAE,IAAI,CAAC0E,YAAY,CAAC,CAAC;MAC1B5D,QAAQ,EAAE,IAAI,CAAC6D,eAAe,CAAC,CAAC;MAChCjD,SAAS,EAAE,IAAI,CAACkD,gBAAgB,CAAC,CAAC;MAClCvC,MAAM,EAAE,IAAI,CAACwC,aAAa,CAAC,CAAC;MAC5BlC,GAAG,EAAE,IAAI,CAACmC,UAAU,CAAC,CAAC;MACtBxB,YAAY,EAAE,IAAI,CAACyB,mBAAmB,CAAC,CAAC;MACxClB,aAAa,EAAE,IAAI,CAACmB,oBAAoB,CAAC,CAAC;MAC1CoC,KAAK,EAAE,IAAI,CAACnC,YAAY,CAAC,CAAC;MAC1BlG,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BmE,SAAS,EAAEJ,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;EACH;;EAEA;EACA,MAAMsE,aAAaA,CAACC,QAAQ,EAAEjE,MAAM,EAAEwD,KAAK,EAAE;IAC3C,MAAMU,MAAM,GAAG,IAAI,CAACtI,QAAQ,CAACyC,SAAS,CAACC,OAAO,CAAC6F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzG,EAAE,KAAKsG,QAAQ,CAAC;IAC3E,IAAI,CAACC,MAAM,EAAE,OAAO,KAAK;;IAEzB;IACA,MAAM,IAAInD,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,QAAQhB,MAAM;MACZ,KAAK,QAAQ;QACXkE,MAAM,CAACnG,MAAM,GAAGmG,MAAM,CAACnG,MAAM,KAAK,IAAI,GAAG,KAAK,GAAG,IAAI;QACrD;MACF,KAAK,eAAe;QAClB,IAAImG,MAAM,CAAC3F,IAAI,KAAK,OAAO,EAAE;UAC3B2F,MAAM,CAACzF,UAAU,GAAG+E,KAAK;QAC3B;QACA;MACF,KAAK,gBAAgB;QACnB,IAAIU,MAAM,CAAC3F,IAAI,KAAK,SAAS,EAAE;UAC7B2F,MAAM,CAACxF,WAAW,GAAG8E,KAAK;QAC5B;QACA;IACJ;IAEA,OAAO,IAAI;EACb;;EAEA;EACA,MAAMa,cAAcA,CAACxD,OAAO,EAAE;IAC5B;IACA,MAAM,IAAIE,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;IAEvD,MAAMsD,WAAW,GAAG;MAClB3G,EAAE,EAAE8B,IAAI,CAACC,GAAG,CAAC,CAAC;MACdnB,IAAI,EAAE,MAAM;MACZqB,OAAO,EAAEiB,OAAO;MAChBhB,SAAS,EAAEJ,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;;IAED;IACA,IAAI6E,WAAW,GAAG,EAAE;IACpB,IAAI1D,OAAO,CAAC8C,QAAQ,CAAC,IAAI,CAAC,EAAE;MAC1BY,WAAW,GAAG,oBAAoB;IACpC,CAAC,MAAM,IAAI1D,OAAO,CAAC8C,QAAQ,CAAC,IAAI,CAAC,EAAE;MACjCY,WAAW,GAAG,oBAAoB;IACpC,CAAC,MAAM,IAAI1D,OAAO,CAAC8C,QAAQ,CAAC,IAAI,CAAC,EAAE;MACjCY,WAAW,GAAG,eAAe;IAC/B,CAAC,MAAM;MACLA,WAAW,GAAG,kBAAkB;IAClC;IAEA,MAAMC,UAAU,GAAG;MACjB7G,EAAE,EAAE8B,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;MAClBnB,IAAI,EAAE,KAAK;MACXqB,OAAO,EAAE2E,WAAW;MACpB1E,SAAS,EAAEJ,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;IAED,IAAI,CAAC9D,QAAQ,CAAC0D,GAAG,CAACK,mBAAmB,CAAC8E,IAAI,CAACH,WAAW,EAAEE,UAAU,CAAC;IAEnE,OAAOA,UAAU;EACnB;AACF;;AAEA;AACA,MAAME,eAAe,GAAG,IAAInJ,eAAe,CAAC,CAAC;AAE7C,eAAemJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}