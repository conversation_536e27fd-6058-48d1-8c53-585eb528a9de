/**
 * 动态壁纸服务
 * 提供智能的动态壁纸生成功能，支持文件上传和URL两种模式
 */
class DynamicWallpaperService {
  constructor() {
    this.apiBaseUrl = process.env.VUE_APP_API_BASE_URL || 'http://localhost:8000'
    this.currentPromptId = null
    this.isGenerating = false
  }

  /**
   * 生成动态壁纸（智能选择接口）
   * @param {Object} options - 生成选项
   * @param {File} options.imageFile - 图片文件（优先）
   * @param {string} options.imageUrl - Kolors生成的图片URL（备用）
   * @param {string} options.taskId - 任务ID
   * @param {Function} options.onProgress - 进度回调
   * @returns {Promise<Object>} 生成结果
   */
  async generateDynamicWallpaper(options) {
    const { imageFile, imageUrl, taskId, onProgress } = options
    
    // 优先使用文件上传接口
    if (imageFile) {
      console.log('使用文件上传模式生成动态壁纸')
      return await this.generateFromFile(imageFile, taskId, onProgress)
    }
    
    // 备用URL接口
    if (imageUrl) {
      console.log('使用URL模式生成动态壁纸')
      return await this.generateFromUrl(imageUrl, taskId, onProgress)
    }
    
    throw new Error('必须提供imageFile或imageUrl参数')
  }

  /**
   * 通过文件上传生成动态壁纸（推荐方式）
   */
  async generateFromFile(imageFile, taskId, onProgress) {
    try {
      this.isGenerating = true
      this.currentPromptId = null
      
      // 进度回调
      if (onProgress) {
        onProgress({
          percentage: 10,
          message: '正在上传图片文件...',
          status: 'uploading'
        })
      }
      
      // 创建表单数据
      const formData = new FormData()
      formData.append('file', imageFile)
      formData.append('task_id', taskId)
      
      // 调用文件上传接口
      const response = await fetch(`${this.apiBaseUrl}/api/v1/dynamic-wallpaper/dynamic-wallpaper`, {
        method: 'POST',
        body: formData
      })
      
      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }
      
      const result = await response.json()
      this.currentPromptId = result.prompt_id
      
      // 进度回调
      if (onProgress) {
        onProgress({
          percentage: 30,
          message: '文件上传成功，正在生成动态效果...',
          status: 'processing',
          promptId: result.prompt_id
        })
      }
      
      // 模拟进度更新（实际项目中可以通过WebSocket获取真实进度）
      await this.simulateProgress(onProgress, 30, 100)
      
      return {
        promptId: result.prompt_id,
        url: result.video_url,
        taskId: result.task_id,
        type: 'video'
      }
      
    } catch (error) {
      console.error('文件上传模式生成失败:', error)
      throw new Error(`文件上传生成失败: ${error.message}`)
    } finally {
      this.isGenerating = false
    }
  }

  /**
   * 通过URL生成动态壁纸（备用方式）
   */
  async generateFromUrl(imageUrl, taskId, onProgress) {
    try {
      this.isGenerating = true
      this.currentPromptId = null
      
      // 进度回调
      if (onProgress) {
        onProgress({
          percentage: 10,
          message: '正在下载图片...',
          status: 'downloading'
        })
      }
      
      // 调用URL接口
      const response = await fetch(`${this.apiBaseUrl}/api/v1/dynamic-wallpaper/generate-from-url`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          image_url: imageUrl,
          task_id: taskId
        })
      })
      
      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }
      
      const result = await response.json()
      this.currentPromptId = result.prompt_id
      
      // 进度回调
      if (onProgress) {
        onProgress({
          percentage: 30,
          message: '图片下载成功，正在生成动态效果...',
          status: 'processing',
          promptId: result.prompt_id
        })
      }
      
      // 模拟进度更新
      await this.simulateProgress(onProgress, 30, 100)
      
      return {
        promptId: result.prompt_id,
        url: result.video_url,
        taskId: result.task_id,
        sourceImageUrl: result.source_image_url,
        type: 'video'
      }
      
    } catch (error) {
      console.error('URL模式生成失败:', error)
      throw new Error(`URL生成失败: ${error.message}`)
    } finally {
      this.isGenerating = false
    }
  }

  /**
   * 模拟进度更新（实际项目中应该通过WebSocket获取真实进度）
   */
  async simulateProgress(onProgress, startPercentage, endPercentage) {
    if (!onProgress) return
    
    const steps = [
      { percentage: 40, message: '正在处理图片...' },
      { percentage: 60, message: '正在生成动态效果...' },
      { percentage: 80, message: '正在渲染视频...' },
      { percentage: 95, message: '正在优化输出...' }
    ]
    
    for (const step of steps) {
      if (step.percentage <= endPercentage) {
        await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟延迟
        onProgress({
          percentage: step.percentage,
          message: step.message,
          status: 'processing',
          promptId: this.currentPromptId
        })
      }
    }
  }

  /**
   * 检查生成状态
   */
  async checkGenerationStatus(promptId) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/v1/dynamic-wallpaper/status/${promptId}`)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error('检查生成状态失败:', error)
      return { status: 'error', error: error.message }
    }
  }

  /**
   * 取消生成任务
   */
  async cancelGeneration(promptId) {
    try {
      if (!promptId) {
        promptId = this.currentPromptId
      }
      
      if (!promptId) {
        console.warn('没有可取消的任务')
        return
      }
      
      // 注意：ComfyUI可能不支持取消任务，这里只是重置本地状态
      this.isGenerating = false
      this.currentPromptId = null
      
      console.log(`已取消生成任务: ${promptId}`)
      
      // 如果后端支持取消接口，可以在这里调用
      // const response = await fetch(`${this.apiBaseUrl}/api/v1/dynamic-wallpaper/cancel/${promptId}`, {
      //   method: 'POST'
      // })
      
    } catch (error) {
      console.error('取消生成任务失败:', error)
    }
  }

  /**
   * 获取当前生成状态
   */
  getGenerationStatus() {
    return {
      isGenerating: this.isGenerating,
      currentPromptId: this.currentPromptId
    }
  }

  /**
   * 生成任务ID
   */
  generateTaskId() {
    return `dynamic_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 验证图片URL
   */
  validateImageUrl(imageUrl) {
    if (!imageUrl || typeof imageUrl !== 'string') {
      return false
    }
    
    try {
      const url = new URL(imageUrl)
      return url.protocol === 'http:' || url.protocol === 'https:'
    } catch {
      return false
    }
  }

  /**
   * 验证图片文件
   */
  validateImageFile(imageFile) {
    if (!imageFile || !(imageFile instanceof File)) {
      return { valid: false, error: '无效的文件对象' }
    }
    
    // 检查文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(imageFile.type)) {
      return { valid: false, error: '不支持的文件格式，请使用 JPG、PNG 或 WebP 格式' }
    }
    
    // 检查文件大小（10MB限制）
    const maxSize = 10 * 1024 * 1024
    if (imageFile.size > maxSize) {
      return { valid: false, error: '文件过大，请选择小于10MB的图片' }
    }
    
    return { valid: true }
  }
}

export default DynamicWallpaperService
