// 情感化壁纸提示词生成器
// 专为车载HMI场景设计的动态提示词生成服务
import LlmService from '@/services/LlmService'

export default class EmotionalPromptGenerator {
  constructor() {
    this.llmService = new LlmService()
    this.cache = new Map()
    this.maxCacheSize = 50
  }

  /**
   * 生成情感化的壁纸提示词
   * @param {Object} sceneInfo - 场景信息
   * @param {Object} context - 用户上下文信息
   * @returns {Promise<string>} 生成的提示词
   */
  async generateEmotionalPrompt(sceneInfo, context = {}) {
    const cacheKey = this.getCacheKey(sceneInfo, context)
    
    // 检查缓存
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    try {
      // 构建情感化提示词生成请求
      const emotionalPrompt = this.buildEmotionalPrompt(sceneInfo, context)
      
      // 调用LLM生成提示词
      const generatedPrompt = await this.llmService.generateResponse(emotionalPrompt)
      
      // 缓存结果
      this.cacheResult(cacheKey, generatedPrompt)
      
      console.log('🎨 情感化提示词生成成功:', generatedPrompt)
      return generatedPrompt
      
    } catch (error) {
      console.error('情感化提示词生成失败:', error)
      // 返回增强的默认提示词
      return this.getFallbackPrompt(sceneInfo, context)
    }
  }

  /**
   * 构建情感化提示词生成请求
   */
  buildEmotionalPrompt(sceneInfo, context) {
    const { 
      id: sceneId, 
      name: sceneName, 
      description: sceneDescription,
      theme: sceneTheme 
    } = sceneInfo

    const {
      timeOfDay,
      weather,
      mood,
      userRole,
      passengers,
      destination,
      isWeekend,
      drivingDuration
    } = context

    return `# 角色
你是一个专业的车载HMI壁纸提示词设计师，专门为汽车智能座舱系统生成动漫卡通风格的温馨壁纸描述提示词。

## 任务
根据提供的场景信息和用户上下文，生成一个能够体现用户真实感受和心理状态的动漫卡通风格壁纸描述提示词。

## 场景信息
- 场景ID: ${sceneId}
- 场景名称: ${sceneName}
- 场景描述: ${sceneDescription}
- 主题风格: ${sceneTheme}

## 用户上下文
- 时间: ${timeOfDay || '未知'}
- 天气: ${weather || '晴朗'}
- 用户情绪: ${mood || '平静'}
- 用户角色: ${userRole || '车主'}
- 同行人员: ${passengers || '独自'}
- 目的地: ${destination || '未知'}
- 是否周末: ${isWeekend ? '是' : '否'}
- 驾驶时长: ${drivingDuration || '刚开始'}

## 生成要求
1. **动漫卡通风格**: 必须使用动漫、卡通、插画风格，避免写实风格
2. **温馨氛围**: 营造温馨、舒适、治愈的视觉氛围
3. **避免人物**: 不要生成任何人物形象，专注于场景和环境
4. **情感共鸣**: 从车主的真实感受出发，体现当前场景下的心理状态
5. **场景贴合**: 紧密结合车载环境和驾驶场景
6. **视觉描述**: 提供具体、生动的视觉元素描述
7. **氛围营造**: 通过光影、色彩、构图营造相应的氛围

## 艺术风格要求
- 🎨 **风格**: 动漫卡通、插画风格、手绘风格
- 🌈 **色彩**: 温暖柔和的色调，避免过于强烈的对比
- 🏠 **场景**: 可爱的小房子、温馨的车厢、自然风光等
- 🚗 **车辆**: 卡通化的汽车设计，圆润可爱的造型
- 🌸 **元素**: 花朵、云朵、星星、心形等温馨装饰元素

## 严格禁止
- ❌ 写实风格、照片级真实感
- ❌ 任何人物形象（包括卡通人物）
- ❌ 高楼大厦、现代都市建筑
- ❌ 机械化的模板描述
- ❌ 过于抽象或概念化的表达

## 输出格式
直接输出壁纸描述提示词，不需要额外说明。提示词应该：
- 专注于动漫卡通风格的场景描述
- 体现温馨治愈的情感氛围
- 适合作为文生图模型的输入
- 长度控制在100-200字之间

请根据以上信息，生成一个动漫卡通风格的温馨壁纸描述提示词：`
  }

  /**
   * 获取场景特定的情感增强词
   */
  getSceneEmotionalEnhancements(sceneId) {
    const enhancements = {
      morningCommuteFamily: {
        emotions: ['温馨', '关爱', '期待', '家庭温暖'],
        keywords: ['晨光', '童趣', '亲子时光', '温暖守护', '卡通车厢'],
        atmosphere: '动漫风格的温馨车厢，柔和的晨光透过车窗，可爱的玩具和书包散落其间'
      },
      morningCommuteFocus: {
        emotions: ['专注', '效率', '清醒', '目标导向'],
        keywords: ['晨光', '清新', '专注力', '简约', '卡通道路'],
        atmosphere: '动漫风格的简约驾驶环境，清新的晨光，可爱的道路标示和简约仪表盘'
      },
      eveningCommute: {
        emotions: ['放松', '满足', '期待', '归家心切'],
        keywords: ['夕阳', '归途', '温暖', '疲劳缓解', '卡通小屋'],
        atmosphere: '动漫风格的黄昏归途，温暖的夕阳，道路两旁可爱的卡通小屋和温暖的灯光'
      },
      waitingMode: {
        emotions: ['悠闲', '放松', '享受', '宁静'],
        keywords: ['休憩', '舒适', '轻松', '冥想', '卡通云朵'],
        atmosphere: '动漫风格的悠闲休息场景，舒适的卡通座椅，飘浮的云朵和轻松的氛围'
      },
      rainyNight: {
        emotions: ['宁静', '沉思', '安全', '温暖'],
        keywords: ['雨滴', '夜色', '温暖灯光', '安全感', '卡通雨伞'],
        atmosphere: '动漫风格的雨夜车厢，车窗上滑落的卡通雨滴，温暖的车内灯光和舒适的环境'
      },
      familyTrip: {
        emotions: ['愉悦', '期待', '亲密', '冒险'],
        keywords: ['阳光', '自然', '欢笑', '自由', '卡通风景'],
        atmosphere: '动漫风格的愉快出游，阳光明媚的自然风光，卡通化的风景和可爱的车辆'
      },
      longDistance: {
        emotions: ['专注', '坚韧', '自由', '思考'],
        keywords: ['公路', '远方', '自由感', '坚持', '卡通地图'],
        atmosphere: '动漫风格的长途驾驶，开阔的卡通道路，可爱的地图元素和自由的氛围'
      },
      romanticMode: {
        emotions: ['浪漫', '亲密', '温馨', '私密'],
        keywords: ['星光', '温暖', '浪漫氛围', '温柔', '卡通星星'],
        atmosphere: '动漫风格的浪漫车厢，满天卡通星星，温暖的光影和温馨的氛围'
      },
      chargingMode: {
        emotions: ['耐心', '期待', '科技感', '环保'],
        keywords: ['充电', '科技', '未来感', '环保', '卡通闪电'],
        atmosphere: '动漫风格的科技充电环境，可爱的充电元素，未来感的卡通设计'
      }
    }

    return enhancements[sceneId] || {
      emotions: ['舒适', '安全', '愉悦'],
      keywords: ['驾驶', '舒适', '安全', '卡通元素'],
      atmosphere: '动漫风格的舒适驾驶环境，可爱的卡通元素和温馨的氛围'
    }
  }

  /**
   * 获取降级提示词
   */
  getFallbackPrompt(sceneInfo, context) {
    const enhancements = this.getSceneEmotionalEnhancements(sceneInfo.id)
    const timeInfo = context.timeOfDay || 'daytime'
    const weatherInfo = context.weather || 'clear'
    
    const basePrompt = `${enhancements.atmosphere}`
    const emotionalWords = enhancements.emotions.join('、')
    const visualKeywords = enhancements.keywords.join('、')
    
    return `${basePrompt}, 体现${emotionalWords}的情感氛围, 包含${visualKeywords}等元素, 动漫卡通风格, 插画风格, 手绘风格, 温馨治愈, 柔和色彩, 可爱设计, 无人物, 纯场景, ${timeInfo}时光, ${weatherInfo}天气, (动漫风格:1.3), (卡通插画:1.2), (温馨治愈:1.1)`
  }

  /**
   * 生成缓存键
   */
  getCacheKey(sceneInfo, context) {
    const contextStr = JSON.stringify({
      sceneId: sceneInfo.id,
      timeOfDay: context.timeOfDay,
      weather: context.weather,
      mood: context.mood,
      isWeekend: context.isWeekend
    })
    return `prompt_${Buffer.from(contextStr).toString('base64').slice(0, 32)}`
  }

  /**
   * 缓存结果
   */
  cacheResult(key, result) {
    if (this.cache.size >= this.maxCacheSize) {
      // 删除最旧的缓存项
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    this.cache.set(key, result)
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache.clear()
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      hitRate: 'N/A' // 可以添加命中率统计
    }
  }

  /**
   * 根据时间获取情感化描述
   */
  getTimeEmotionalContext(hour) {
    if (hour >= 5 && hour < 8) {
      return {
        timeOfDay: '清晨',
        mood: '清新宁静',
        lighting: '柔和的晨光',
        atmosphere: '宁静祥和'
      }
    } else if (hour >= 8 && hour < 12) {
      return {
        timeOfDay: '上午',
        mood: '充满活力',
        lighting: '明亮的日光',
        atmosphere: '活跃向上'
      }
    } else if (hour >= 12 && hour < 14) {
      return {
        timeOfDay: '中午',
        mood: '略带疲惫',
        lighting: '强烈的阳光',
        atmosphere: '热情充沛'
      }
    } else if (hour >= 14 && hour < 17) {
      return {
        timeOfDay: '下午',
        mood: '专注沉稳',
        lighting: '倾斜的阳光',
        atmosphere: '沉稳专注'
      }
    } else if (hour >= 17 && hour < 19) {
      return {
        timeOfDay: '傍晚',
        mood: '期待放松',
        lighting: '温暖的夕阳',
        atmosphere: '温暖归家'
      }
    } else if (hour >= 19 && hour < 22) {
      return {
        timeOfDay: '夜晚',
        mood: '宁静温馨',
        lighting: '温暖的灯光',
        atmosphere: '温馨舒适'
      }
    } else {
      return {
        timeOfDay: '深夜',
        mood: '宁静安详',
        lighting: '柔和的月光',
        atmosphere: '宁静神秘'
      }
    }
  }

  /**
   * 根据天气获取情感化描述
   */
  getWeatherEmotionalContext(weather) {
    const weatherMap = {
      'sunny': {
        description: '晴朗',
        mood: '愉悦开朗',
        lighting: '明媚的阳光',
        atmosphere: '明亮开朗'
      },
      'cloudy': {
        description: '多云',
        mood: '平静舒缓',
        lighting: '柔和的光线',
        atmosphere: '柔和舒适'
      },
      'rainy': {
        description: '下雨',
        mood: '宁静沉思',
        lighting: '雨滴的折射光',
        atmosphere: '清新宁静'
      },
      'night': {
        description: '夜晚',
        mood: '神秘安静',
        lighting: '温暖的灯光',
        atmosphere: '宁静神秘'
      }
    }

    return weatherMap[weather] || weatherMap.sunny
  }

  /**
   * 批量生成提示词
   */
  async generateMultiplePrompts(scenes, context) {
    const results = []
    
    for (const scene of scenes) {
      try {
        const prompt = await this.generateEmotionalPrompt(scene, context)
        results.push({
          sceneId: scene.id,
          prompt,
          success: true
        })
      } catch (error) {
        console.error(`生成场景 ${scene.id} 提示词失败:`, error)
        results.push({
          sceneId: scene.id,
          prompt: this.getFallbackPrompt(scene, context),
          success: false,
          error: error.message
        })
      }
    }
    
    return results
  }
}