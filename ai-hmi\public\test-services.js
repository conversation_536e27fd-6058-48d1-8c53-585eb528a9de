// AI-HMI 服务测试脚本
// 测试各个服务的连通性和功能

const testServices = async () => {
  console.log('🧪 开始测试AI-HMI服务...\n');

  // 1. 测试theme_backend连通性
  console.log('1️⃣ 测试theme_backend连通性...');
  try {
    const response = await fetch('http://localhost:8000/docs');
    if (response.ok) {
      console.log('✅ theme_backend服务正常运行');
    } else {
      console.log('❌ theme_backend服务响应异常:', response.status);
    }
  } catch (error) {
    console.log('❌ theme_backend服务连接失败:', error.message);
  }

  // 2. 测试kolors接口
  console.log('\n2️⃣ 测试kolors接口...');
  try {
    const testPrompt = 'modern glass building, transparent texture, warm lighting';
    const response = await fetch('http://localhost:8000/kolors/text-to-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: testPrompt,
        task_id: 'test_' + Date.now()
      })
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ kolors接口调用成功');
      console.log('📷 生成图片URL:', result.image_url);
    } else {
      console.log('❌ kolors接口调用失败:', response.status);
      const error = await response.text();
      console.log('错误信息:', error);
    }
  } catch (error) {
    console.log('❌ kolors接口连接失败:', error.message);
  }

  // 3. 测试LLM服务
  console.log('\n3️⃣ 测试LLM服务...');
  try {
    const testInput = '温暖的日落场景';
    const response = await fetch('https://open.bigmodel.cn/api/paas/v4/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW'
      },
      body: JSON.stringify({
        model: 'glm-4-flash',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的AI绘画提示词优化专家。请将用户的自然语言描述转换为适合AI绘画的英文提示词，要求包含玻璃态(glassmorphism)设计元素。'
          },
          {
            role: 'user',
            content: `请优化这个提示词用于AI绘画: ${testInput}`
          }
        ],
        temperature: 0.7,
        max_tokens: 200
      })
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ LLM服务调用成功');
      console.log('🤖 优化结果:', result.choices[0].message.content);
    } else {
      console.log('❌ LLM服务调用失败:', response.status);
      const error = await response.text();
      console.log('错误信息:', error);
    }
  } catch (error) {
    console.log('❌ LLM服务连接失败:', error.message);
  }

  // 4. 测试浏览器语音API
  console.log('\n4️⃣ 测试浏览器语音API...');
  if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
    console.log('✅ 浏览器支持语音识别');
  } else {
    console.log('❌ 浏览器不支持语音识别');
  }

  if ('speechSynthesis' in window) {
    console.log('✅ 浏览器支持语音合成');
  } else {
    console.log('❌ 浏览器不支持语音合成');
  }

  console.log('\n🎉 服务测试完成！');
};

// 在浏览器控制台中运行测试
if (typeof window !== 'undefined') {
  window.testServices = testServices;
  console.log('💡 在浏览器控制台中运行 testServices() 来测试所有服务');
}

// 在Node.js环境中运行测试
if (typeof module !== 'undefined' && module.exports) {
  module.exports = testServices;
}
