# AI-HMI 玻璃态主题系统技术方案

## 项目概述

基于现有AI-HMI项目，实现一个简化版的玻璃态主题系统。用户通过自然语言描述需求，系统调用文生图接口生成壁纸，并从壁纸中提取颜色来动态调整玻璃态卡片的样式（圆角、背景色、透明度等）。

## 系统架构

### 1. 核心模块

```
ai-hmi/
├── src/
│   ├── components/
│   │   ├── GlassThemeManager.vue      # 玻璃态主题管理器
│   │   ├── VoiceInput.vue            # 语音输入组件
│   │   ├── WallpaperService.js       # 壁纸服务（集成现有接口）
│   │   ├── ColorExtractor.js         # 颜色提取器
│   │   └── GlassCard.vue             # 玻璃态卡片组件
│   ├── services/
│   │   ├── AiService.js             # AI服务接口
│   │   └── ImageGenerationService.js # 文生图服务（集成现有API）
│   └── utils/
│       ├── colorUtils.js            # 颜色工具函数
│       └── glassStyleUtils.js       # 玻璃态样式工具
```

### 2. 数据流

```
用户自然语言输入 
    ↓
文生图接口调用 (生成壁纸)
    ↓
颜色提取器 (从壁纸提取主色调)
    ↓
玻璃态样式计算器 (计算卡片样式参数)
    ↓
玻璃态主题管理器 (应用样式到所有卡片)
    ↓
渲染最终玻璃态页面
```

## 技术实现方案

### 1. 文生图服务集成（基于现有API）

#### 1.1 壁纸生成服务

```javascript
// ImageGenerationService.js
class ImageGenerationService {
  constructor() {
    this.apiBaseUrl = process.env.VUE_APP_API_BASE_URL || 'http://localhost:8000'
  }

  async generateWallpaper(prompt, taskId = null) {
    if (!taskId) {
      taskId = `wallpaper_${Date.now()}`
    }

    try {
      // 调用现有的文生图接口
      const response = await fetch(`${this.apiBaseUrl}/kolors/text-to-image`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          prompt: this.buildGlassmorphismPrompt(prompt),
          task_id: taskId
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      return {
        imageUrl: data.image_url,
        taskId: taskId,
        prompt: prompt
      }
    } catch (error) {
      console.error('壁纸生成失败:', error)
      return this.getFallbackWallpaper()
    }
  }

  buildGlassmorphismPrompt(userInput) {
    // 为玻璃态主题优化的提示词
    const glassmorphismPrompts = [
      `现代玻璃建筑，透明质感，模糊背景，${userInput}，简洁商务风格，适合车载显示`,
      `玻璃幕墙建筑，现代都市，透明材质，${userInput}，高清质感，适合车载界面`,
      `透明玻璃材质，现代简约风格，${userInput}，柔和光线，适合车载显示`,
      `玻璃反射效果，城市景观，${userInput}，现代设计，适合车载界面`
    ]

    // 根据用户输入选择最合适的提示词
    return glassmorphismPrompts[0] // 默认使用第一个提示词
  }

  getFallbackWallpaper() {
    return {
      imageUrl: '/images/default-glass-wallpaper.jpg',
      taskId: 'fallback',
      prompt: 'default glassmorphism'
    }
  }
}
```

### 2. 颜色提取与玻璃态样式计算

#### 2.1 颜色提取器

```javascript
// ColorExtractor.js
class ColorExtractor {
  static async extractColors(imageUrl) {
    return new Promise((resolve) => {
      const img = new Image()
      img.crossOrigin = 'anonymous'
      
      img.onload = () => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        
        canvas.width = img.width
        canvas.height = img.height
        ctx.drawImage(img, 0, 0)

        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
        const colors = this.analyzeColors(imageData)
        
        resolve(colors)
      }
      
      img.onerror = () => {
        resolve(this.getDefaultGlassmorphismColors())
      }
      
      img.src = imageUrl
    })
  }

  static analyzeColors(imageData) {
    const data = imageData.data
    const colorMap = {}
    
    // 采样像素点
    for (let i = 0; i < data.length; i += 16) {
      const r = data[i]
      const g = data[i + 1]
      const b = data[i + 2]
      const a = data[i + 3]
      
      if (a < 128) continue
      
      const color = this.quantizeColor(r, g, b)
      const key = `${color.r},${color.g},${color.b}`
      
      colorMap[key] = (colorMap[key] || 0) + 1
    }
    
    const sortedColors = Object.entries(colorMap)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([key]) => {
        const [r, g, b] = key.split(',').map(Number)
        return { r, g, b }
      })
    
    return this.generateGlassmorphismPalette(sortedColors)
  }

  static generateGlassmorphismPalette(colors) {
    if (colors.length === 0) return this.getDefaultGlassmorphismColors()
    
    const primaryColor = colors[0]
    const secondaryColor = colors[1] || colors[0]
    
    return {
      primary: this.rgbToHex(primaryColor),
      secondary: this.rgbToHex(secondaryColor),
      glassBackground: this.getGlassBackground(primaryColor),
      glassBorder: this.getGlassBorder(primaryColor),
      text: this.getContrastColor(primaryColor)
    }
  }

  static getGlassBackground(color) {
    const brightness = (color.r + color.g + color.b) / 3
    const alpha = brightness > 128 ? 0.15 : 0.25
    return `rgba(${color.r}, ${color.g}, ${color.b}, ${alpha})`
  }

  static getGlassBorder(color) {
    const brightness = (color.r + color.g + color.b) / 3
    const alpha = brightness > 128 ? 0.2 : 0.3
    return `rgba(255, 255, 255, ${alpha})`
  }

  static getDefaultGlassmorphismColors() {
    return {
      primary: '#4A90E2',
      secondary: '#7ED321',
      glassBackground: 'rgba(255, 255, 255, 0.15)',
      glassBorder: 'rgba(255, 255, 255, 0.2)',
      text: '#FFFFFF'
    }
  }
}
```

#### 2.2 玻璃态样式计算器

```javascript
// glassStyleUtils.js
class GlassStyleUtils {
  static calculateGlassStyles(colors) {
    const brightness = this.getColorBrightness(colors.primary)
    
    return {
      borderRadius: this.calculateBorderRadius(brightness),
      backdropFilter: 'blur(12px)',
      background: colors.glassBackground,
      border: `1px solid ${colors.glassBorder}`,
      boxShadow: this.calculateBoxShadow(brightness),
      textShadow: this.calculateTextShadow(brightness)
    }
  }

  static calculateBorderRadius(brightness) {
    // 根据颜色亮度计算圆角大小
    if (brightness > 200) return '8px'      // 高亮度 - 小圆角
    if (brightness > 150) return '12px'     // 中高亮度 - 中圆角
    if (brightness > 100) return '16px'     // 中亮度 - 大圆角
    return '20px'                           // 低亮度 - 最大圆角
  }

  static calculateBoxShadow(brightness) {
    const opacity = brightness > 128 ? 0.1 : 0.2
    return `0 8px 32px rgba(0, 0, 0, ${opacity})`
  }

  static calculateTextShadow(brightness) {
    if (brightness > 128) {
      return '0 1px 2px rgba(0, 0, 0, 0.5)'
    }
    return '0 1px 2px rgba(255, 255, 255, 0.3)'
  }

  static getColorBrightness(hexColor) {
    const rgb = this.hexToRgb(hexColor)
    return (rgb.r + rgb.g + rgb.b) / 3
  }

  static hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null
  }
}
```

### 3. 玻璃态主题管理器

#### 3.1 主题管理组件

```vue
<!-- GlassThemeManager.vue -->
<template>
  <div class="glass-theme-manager" :style="themeStyles">
    <!-- 玻璃态卡片容器 -->
    <div class="glass-cards-container">
      <!-- 导航卡片 -->
      <GlassCard 
        title="导航"
        :icon="'fas fa-route'"
        :style="glassStyles"
      >
        <div class="navigation-content">
          <div class="destination">前往公司</div>
          <div class="route-info">
            <span class="distance">12.5 公里</span>
            <span class="duration">25 分钟</span>
          </div>
          <div class="route-preview">
            <img src="/assets/map-preview.jpg" alt="路线预览">
          </div>
        </div>
      </GlassCard>

      <!-- 音乐控制卡片 -->
      <GlassCard 
        title="音乐"
        :icon="'fas fa-music'"
        :style="glassStyles"
      >
        <div class="music-content">
          <div class="song-info">
            <div class="song-title">Morning Coffee</div>
            <div class="artist">Jazz Collective</div>
          </div>
          <div class="music-controls">
            <button class="control-btn"><i class="fas fa-backward"></i></button>
            <button class="control-btn play-pause"><i class="fas fa-play"></i></button>
            <button class="control-btn"><i class="fas fa-forward"></i></button>
          </div>
        </div>
      </GlassCard>

      <!-- 待办事项卡片 -->
      <GlassCard 
        title="今日待办"
        :icon="'fas fa-tasks'"
        :style="glassStyles"
      >
        <div class="todo-content">
          <ul class="todo-list">
            <li class="todo-item">
              <input type="checkbox" id="todo1">
              <label for="todo1">团队会议 10:00</label>
            </li>
            <li class="todo-item">
              <input type="checkbox" id="todo2">
              <label for="todo2">项目汇报 14:00</label>
            </li>
            <li class="todo-item">
              <input type="checkbox" id="todo3">
              <label for="todo3">客户电话 16:30</label>
            </li>
          </ul>
        </div>
      </GlassCard>
    </div>

    <!-- VPA助手 -->
    <div class="vpa-assistant" :style="vpaStyles">
      <div class="vpa-avatar">🤖</div>
      <div class="vpa-message">
        早上好！今天天气不错，祝您一路平安！
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-content">
        <i class="fas fa-spinner fa-spin"></i>
        <span>正在生成壁纸...</span>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import ImageGenerationService from '@/services/ImageGenerationService'
import ColorExtractor from '@/utils/ColorExtractor'
import GlassStyleUtils from '@/utils/glassStyleUtils'
import GlassCard from './GlassCard.vue'

export default {
  name: 'GlassThemeManager',
  components: {
    GlassCard
  },

  props: {
    userInput: {
      type: String,
      default: '现代玻璃建筑，简洁商务风格'
    }
  },

  setup(props) {
    const currentWallpaper = ref('')
    const currentColors = ref({})
    const glassStyles = ref({})
    const isLoading = ref(false)

    const themeStyles = computed(() => {
      return {
        backgroundImage: currentWallpaper.value ? 
          `url(${currentWallpaper.value})` : 'none',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }
    })

    const vpaStyles = computed(() => {
      return {
        ...glassStyles.value,
        background: currentColors.value.glassBackground || 'rgba(255, 255, 255, 0.15)',
        border: `1px solid ${currentColors.value.glassBorder || 'rgba(255, 255, 255, 0.2)'}`
      }
    })

    const generateTheme = async (userInput) => {
      isLoading.value = true
      
      try {
        // 1. 生成壁纸
        const imageService = new ImageGenerationService()
        const wallpaperResult = await imageService.generateWallpaper(userInput)
        
        currentWallpaper.value = wallpaperResult.imageUrl
        
        // 2. 提取颜色
        const colors = await ColorExtractor.extractColors(wallpaperResult.imageUrl)
        currentColors.value = colors
        
        // 3. 计算玻璃态样式
        const styles = GlassStyleUtils.calculateGlassStyles(colors)
        glassStyles.value = styles
        
        console.log('主题生成完成:', { wallpaperResult, colors, styles })
        
      } catch (error) {
        console.error('主题生成失败:', error)
        // 使用默认样式
        currentColors.value = ColorExtractor.getDefaultGlassmorphismColors()
        glassStyles.value = GlassStyleUtils.calculateGlassStyles(currentColors.value)
      } finally {
        isLoading.value = false
      }
    }

    // 监听用户输入变化
    watch(() => props.userInput, (newInput) => {
      if (newInput && newInput.trim()) {
        generateTheme(newInput)
      }
    })

    onMounted(() => {
      generateTheme(props.userInput)
    })

    return {
      currentWallpaper,
      currentColors,
      glassStyles,
      themeStyles,
      vpaStyles,
      isLoading,
      generateTheme
    }
  }
}
</script>

<style scoped>
.glass-theme-manager {
  width: 100%;
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.glass-cards-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 20px;
  height: calc(100vh - 140px);
  max-width: 1200px;
  margin: 0 auto;
}

.vpa-assistant {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  border-radius: 25px;
  min-width: 200px;
  backdrop-filter: blur(12px);
  transition: all 0.3s ease;
}

.vpa-avatar {
  font-size: 24px;
}

.vpa-message {
  font-size: 14px;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: white;
  font-size: 18px;
}

.loading-content i {
  font-size: 32px;
}

/* 卡片内容样式 */
.navigation-content,
.music-content,
.todo-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.destination {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

.route-info {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.route-preview img {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
}

.song-info {
  text-align: center;
  margin-bottom: 15px;
}

.song-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}

.artist {
  font-size: 14px;
  opacity: 0.8;
}

.music-controls {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.control-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.control-btn.play-pause {
  width: 50px;
  height: 50px;
  font-size: 18px;
}

.todo-list {
  list-style: none;
  padding: 0;
}

.todo-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.todo-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

.todo-item label {
  font-size: 14px;
  cursor: pointer;
}
</style>
```

### 4. 玻璃态卡片组件

#### 4.1 玻璃态卡片组件

```vue
<!-- GlassCard.vue -->
<template>
  <div class="glass-card" :style="cardStyles">
    <div class="card-header">
      <i :class="icon" class="card-icon"></i>
      <h3 class="card-title">{{ title }}</h3>
    </div>
    <div class="card-content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'GlassCard',
  props: {
    title: {
      type: String,
      required: true
    },
    icon: {
      type: String,
      default: 'fas fa-cube'
    },
    style: {
      type: Object,
      default: () => ({})
    }
  },

  setup(props) {
    const cardStyles = computed(() => {
      return {
        borderRadius: props.style.borderRadius || '16px',
        backdropFilter: props.style.backdropFilter || 'blur(12px)',
        background: props.style.background || 'rgba(255, 255, 255, 0.15)',
        border: props.style.border || '1px solid rgba(255, 255, 255, 0.2)',
        boxShadow: props.style.boxShadow || '0 8px 32px rgba(0, 0, 0, 0.1)',
        textShadow: props.style.textShadow || '0 1px 2px rgba(0, 0, 0, 0.5)'
      }
    })

    return {
      cardStyles
    }
  }
}
</script>

<style scoped>
.glass-card {
  padding: 20px;
  color: white;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.glass-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-icon {
  font-size: 18px;
  opacity: 0.8;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.card-content {
  flex: 1;
}

/* 玻璃态效果增强 */
.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.3), 
    transparent
  );
}

.glass-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 1px;
  background: linear-gradient(180deg, 
    transparent, 
    rgba(255, 255, 255, 0.3), 
    transparent
  );
}
</style>
```

### 5. 语音交互系统

#### 5.1 ASR语音识别服务

```javascript
// services/AsrService.js
class AsrService {
  constructor() {
    // 使用系统自带的ASR服务
    this.recognition = null
    this.isSupported = 'webkitSpeechRecognition' in window
  }

  initRecognition() {
    if (!this.isSupported) {
      console.warn('浏览器不支持语音识别')
      return null
    }

    this.recognition = new webkitSpeechRecognition()
    this.recognition.continuous = false
    this.recognition.interimResults = true
    this.recognition.lang = 'zh-CN'

    return this.recognition
  }

  async startRecognition() {
    return new Promise((resolve, reject) => {
      if (!this.isSupported) {
        reject(new Error('浏览器不支持语音识别'))
        return
      }

      const recognition = this.initRecognition()
      
      recognition.onstart = () => {
        console.log('语音识别开始')
      }

      recognition.onresult = (event) => {
        const last = event.results.length - 1
        const transcript = event.results[last][0].transcript
        
        if (event.results[last].isFinal) {
          resolve(transcript.trim())
        }
      }

      recognition.onerror = (event) => {
        console.error('语音识别错误:', event.error)
        reject(new Error(`语音识别失败: ${event.error}`))
      }

      recognition.onend = () => {
        if (recognition.finalResult === undefined) {
          reject(new Error('语音识别超时'))
        }
      }

      recognition.start()
    })
  }

  stopRecognition() {
    if (this.recognition) {
      this.recognition.stop()
    }
  }
}
```

#### 5.2 TTS语音合成服务

```javascript
// services/TtsService.js
class TtsService {
  constructor() {
    // 使用系统自带的TTS服务
    this.synthesis = window.speechSynthesis
    this.isSupported = 'speechSynthesis' in window
  }

  async speak(text, options = {}) {
    if (!this.isSupported) {
      console.warn('浏览器不支持语音合成')
      return
    }

    // 停止当前播放
    this.synthesis.cancel()

    const utterance = new SpeechSynthesisUtterance(text)
    
    // 设置语音参数
    utterance.lang = options.lang || 'zh-CN'
    utterance.rate = options.rate || 1.0
    utterance.pitch = options.pitch || 1.0
    utterance.volume = options.volume || 1.0

    // 选择中文语音
    const voices = this.synthesis.getVoices()
    const chineseVoice = voices.find(voice => 
      voice.lang.includes('zh') || voice.name.includes('Chinese')
    )
    
    if (chineseVoice) {
      utterance.voice = chineseVoice
    }

    return new Promise((resolve, reject) => {
      utterance.onend = () => {
        resolve()
      }

      utterance.onerror = (event) => {
        reject(new Error(`语音合成失败: ${event.error}`))
      }

      this.synthesis.speak(utterance)
    })
  }

  stop() {
    if (this.synthesis) {
      this.synthesis.cancel()
    }
  }

  getVoices() {
    return this.synthesis.getVoices()
  }
}
```

#### 5.3 LLM对话服务

```javascript
// services/LlmService.js
class LlmService {
  constructor() {
    // 从配置文件加载LLM设置
    this.config = {
      model: "glm-4-flash",
      base_url: "https://open.bigmodel.cn/api/paas/v4/",
      api_key: process.env.VUE_APP_LLM_API_KEY || "d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW"
    }
  }

  async generateResponse(userInput, context = {}) {
    try {
      // 构建对话上下文
      const messages = [
        {
          role: "system",
          content: "你是一个智能车载助手，帮助用户生成适合的壁纸描述。用户会描述他们想要的场景，你需要将其转换为更适合AI图像生成的描述。"
        },
        {
          role: "user", 
          content: userInput
        }
      ]

      const response = await fetch(`${this.config.base_url}chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.api_key}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: this.config.model,
          messages: messages,
          temperature: 0.7,
          max_tokens: 500
        })
      })

      if (!response.ok) {
        throw new Error(`LLM API调用失败: ${response.status}`)
      }

      const data = await response.json()
      return data.choices[0].message.content

    } catch (error) {
      console.error('LLM调用失败:', error)
      // 返回增强后的用户输入作为降级方案
      return this.enhancePrompt(userInput)
    }
  }

  enhancePrompt(userInput) {
    // 为玻璃态主题优化的提示词增强
    const glassmorphismEnhancements = [
      "现代玻璃建筑，透明质感，模糊背景",
      "玻璃幕墙，简洁商务风格",
      "透明材质，现代都市",
      "玻璃反射效果，高清质感"
    ]

    const randomEnhancement = glassmorphismEnhancements[
      Math.floor(Math.random() * glassmorphismEnhancements.length)
    ]

    return `${randomEnhancement}，${userInput}，适合车载显示的壁纸`
  }
}
```

#### 5.4 语音交互管理器

```vue
<!-- VoiceInteractionManager.vue -->
<template>
  <div class="voice-interaction-manager">
    <!-- 语音输入按钮 -->
    <button 
      @click="toggleVoiceInteraction"
      :class="['voice-btn', { 
        listening: isListening, 
        processing: isProcessing,
        speaking: isSpeaking
      }]"
    >
      <i :class="getVoiceButtonIcon"></i>
    </button>

    <!-- 识别结果显示 -->
    <div v-if="recognitionResult" class="recognition-result">
      <div class="result-text">{{ recognitionResult }}</div>
      <div class="result-actions">
        <button @click="acceptResult" class="action-btn accept">
          <i class="fas fa-check"></i>
        </button>
        <button @click="rejectResult" class="action-btn reject">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- 处理状态显示 -->
    <div v-if="statusMessage" class="status-message">
      <i class="fas fa-info-circle"></i>
      {{ statusMessage }}
    </div>

    <!-- 文本输入备用方案 -->
    <div class="text-input-fallback">
      <input 
        v-model="textInput"
        @keyup.enter="submitTextInput"
        placeholder="或直接输入描述..."
        class="text-input-field"
      />
      <button @click="submitTextInput" class="submit-btn">
        <i class="fas fa-paper-plane"></i>
      </button>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import AsrService from '@/services/AsrService'
import TtsService from '@/services/TtsService'
import LlmService from '@/services/LlmService'

export default {
  name: 'VoiceInteractionManager',
  emits: ['wallpaper-prompt-ready'],

  setup(props, { emit }) {
    const asrService = new AsrService()
    const ttsService = new TtsService()
    const llmService = new LlmService()

    const isListening = ref(false)
    const isProcessing = ref(false)
    const isSpeaking = ref(false)
    const recognitionResult = ref('')
    const textInput = ref('')
    const statusMessage = ref('')

    const voiceState = computed(() => {
      if (isSpeaking.value) return 'speaking'
      if (isProcessing.value) return 'processing'
      if (isListening.value) return 'listening'
      return 'idle'
    })

    const getVoiceButtonIcon = computed(() => {
      switch (voiceState.value) {
        case 'speaking':
          return 'fas fa-volume-up'
        case 'processing':
          return 'fas fa-spinner fa-spin'
        case 'listening':
          return 'fas fa-stop'
        default:
          return 'fas fa-microphone'
      }
    })

    const toggleVoiceInteraction = async () => {
      if (isListening.value) {
        stopVoiceInteraction()
      } else {
        startVoiceInteraction()
      }
    }

    const startVoiceInteraction = async () => {
      if (isListening.value || isProcessing.value) return

      try {
        isListening.value = true
        statusMessage.value = '正在聆听...'
        
        // 使用ASR服务进行语音识别
        const result = await asrService.startRecognition()
        recognitionResult.value = result
        
        // 自动接受结果并处理
        await processVoiceResult(result)
        
      } catch (error) {
        console.error('语音识别失败:', error)
        statusMessage.value = '语音识别失败，请重试'
        
        // 语音反馈
        await ttsService.speak('语音识别失败，请重试或使用文本输入')
      } finally {
        isListening.value = false
        statusMessage.value = ''
      }
    }

    const stopVoiceInteraction = () => {
      if (asrService) {
        asrService.stopRecognition()
      }
      if (ttsService) {
        ttsService.stop()
      }
      isListening.value = false
      statusMessage.value = ''
    }

    const processVoiceResult = async (userInput) => {
      isProcessing.value = true
      statusMessage.value = '正在处理...'

      try {
        // 使用LLM服务优化用户输入
        statusMessage.value = '正在理解您的需求...'
        const enhancedPrompt = await llmService.generateResponse(userInput)
        
        // 语音反馈处理结果
        statusMessage.value = '正在生成壁纸...'
        await ttsService.speak('正在为您生成壁纸，请稍候')
        
        // 发送最终提示词给主题管理器
        emit('wallpaper-prompt-ready', enhancedPrompt)
        
        // 完成反馈
        setTimeout(async () => {
          await ttsService.speak('壁纸生成完成')
          statusMessage.value = ''
        }, 3000)
        
      } catch (error) {
        console.error('处理语音输入失败:', error)
        statusMessage.value = '处理失败，请重试'
        
        await ttsService.speak('处理失败，请重试')
        
        // 降级方案：直接使用原始输入
        emit('wallpaper-prompt-ready', userInput)
      } finally {
        isProcessing.value = false
        recognitionResult.value = ''
      }
    }

    const acceptResult = async () => {
      if (recognitionResult.value.trim()) {
        await processVoiceResult(recognitionResult.value)
      }
    }

    const rejectResult = () => {
      recognitionResult.value = ''
      statusMessage.value = ''
      ttsService.speak('请重新描述')
    }

    const submitTextInput = async () => {
      if (textInput.value.trim()) {
        await processVoiceResult(textInput.value)
        textInput.value = ''
      }
    }

    // 初始化TTS语音
    onMounted(() => {
      // 等待语音列表加载
      if (ttsService.isSupported) {
        setTimeout(() => {
          ttsService.getVoices()
        }, 100)
      }
    })

    return {
      isListening,
      isProcessing,
      isSpeaking,
      recognitionResult,
      textInput,
      statusMessage,
      voiceState,
      getVoiceButtonIcon,
      toggleVoiceInteraction,
      acceptResult,
      rejectResult,
      submitTextInput
    }
  }
}
</script>

<style scoped>
.voice-interaction-manager {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-width: 420px;
}

.voice-btn {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.voice-btn:hover {
  transform: scale(1.1);
  background: rgba(255, 255, 255, 0.3);
}

.voice-btn.listening {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  animation: pulse 1.5s infinite;
}

.voice-btn.processing {
  background: linear-gradient(45deg, #4834d4, #686de0);
  animation: pulse 2s infinite;
}

.voice-btn.speaking {
  background: linear-gradient(45deg, #00d2d3, #54a0ff);
  animation: wave 2s infinite;
}

.recognition-result {
  padding: 15px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.result-text {
  font-size: 14px;
  color: #333;
  margin-bottom: 10px;
  line-height: 1.4;
}

.result-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.action-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn.accept {
  background: #2ed573;
  color: white;
}

.action-btn.reject {
  background: #ff4757;
  color: white;
}

.action-btn:hover {
  transform: scale(1.1);
}

.status-message {
  padding: 12px 15px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  font-size: 13px;
  color: #333;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 8px;
}

.text-input-fallback {
  display: flex;
  gap: 10px;
}

.text-input-field {
  flex: 1;
  padding: 12px 18px;
  border: none;
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  outline: none;
  backdrop-filter: blur(10px);
  color: #333;
}

.submit-btn {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.submit-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes wave {
  0%, 100% { transform: scale(1); }
  25% { transform: scale(1.02); }
  75% { transform: scale(1.02); }
}
</style>
```

### 6. 主应用入口

#### 6.1 主应用组件

```vue
<!-- App.vue -->
<template>
  <div id="app">
    <GlassThemeManager 
      :user-input="currentInput"
      @input-complete="handleInputComplete"
    />
    
    <VoiceInteractionManager 
      @wallpaper-prompt-ready="handleWallpaperPrompt"
    />
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import GlassThemeManager from './components/GlassThemeManager.vue'
import VoiceInteractionManager from './components/VoiceInteractionManager.vue'

export default {
  name: 'App',
  components: {
    GlassThemeManager,
    VoiceInteractionManager
  },

  setup() {
    const currentInput = ref('现代玻璃建筑，简洁商务风格')

    const handleWallpaperPrompt = (prompt) => {
      console.log('收到壁纸提示词:', prompt)
      currentInput.value = prompt
    }

    const handleInputComplete = (input) => {
      console.log('主题生成完成:', input)
    }

    // 页面加载时的欢迎语音
    const welcomeUser = async () => {
      // 这里可以调用TTS服务播放欢迎语音
      console.log('欢迎使用智能车载壁纸系统')
    }

    onMounted(() => {
      welcomeUser()
    })

    return {
      currentInput,
      handleWallpaperPrompt,
      handleInputComplete
    }
  }
}
</script>

<style>
#app {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  font-family: 'Noto Sans', sans-serif;
  overflow: hidden;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* 全局字体引入 */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

/* 图标库引入 */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');
</style>
```

## 部署和配置

### 1. 环境变量配置

```bash
# .env
# API基础配置
VUE_APP_API_BASE_URL=http://localhost:8000
VUE_APP_IMAGE_API_URL=http://localhost:8000/kolors/text-to-image

# LLM服务配置（来自llm.md）
VUE_APP_LLM_API_KEY=d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW
VUE_APP_LLM_MODEL=glm-4-flash
VUE_APP_LLM_BASE_URL=https://open.bigmodel.cn/api/paas/v4/

# 语音服务配置
VUE_APP_ASR_ENABLED=true
VUE_APP_TTS_ENABLED=true
VUE_APP_VOICE_LANG=zh-CN
```

### 2. 项目文件结构

```
ai-hmi/
├── src/
│   ├── components/
│   │   ├── GlassThemeManager.vue
│   │   ├── GlassCard.vue
│   │   └── VoiceInput.vue
│   ├── services/
│   │   └── ImageGenerationService.js
│   ├── utils/
│   │   ├── ColorExtractor.js
│   │   └── glassStyleUtils.js
│   ├── App.vue
│   └── main.js
├── public/
│   ├── index.html
│   └── images/
│       └── default-glass-wallpaper.jpg
├── package.json
└── docs/
    └── 技术实现方案.md
```

### 3. 构建和部署

```bash
# 安装依赖
npm install

# 开发环境
npm run serve

# 生产构建
npm run build

# 代码检查
npm run lint
```

## 关键特性说明

### 1. 玻璃态主题特性
- **动态圆角**: 根据壁纸颜色亮度自动调整卡片圆角大小
- **颜色适配**: 从壁纸中提取主色调并应用到玻璃态卡片
- **透明度变化**: 根据背景颜色亮度调整玻璃透明度
- **模糊效果**: 统一的12px背景模糊效果

### 2. 文生图集成
- **API集成**: 基于现有的`/kolors/text-to-image`接口
- **提示词优化**: 为玻璃态主题优化的提示词模板
- **错误处理**: 完整的错误处理和降级方案

### 3. 交互方式
- **语音输入**: 支持中文语音识别
- **文本输入**: 备用的文本输入方式
- **实时反馈**: 输入后立即生成新的主题

### 4. 性能优化
- **异步加载**: 壁纸生成和颜色提取都是异步处理
- **缓存机制**: 避免重复生成相同的壁纸
- **降级方案**: 网络错误时使用默认壁纸和颜色

## 扩展性考虑

### 1. 新卡片类型
- 可以轻松添加新的玻璃态卡片组件
- 所有卡片都继承相同的玻璃态样式

### 2. 样式算法优化
- 可以改进颜色提取算法
- 可以添加更多的样式计算规则

### 3. 多主题支持
- 虽然当前专注于玻璃态，但架构支持未来扩展其他主题

## 总结

这个方案提供了一个完整的玻璃态主题系统，专注于：

1. **简单实用**: 基于现有Vue3项目，集成现有的文生图API
2. **动态适配**: 根据生成的壁纸自动调整卡片样式
3. **用户体验**: 支持语音和文本输入，提供实时反馈
4. **可扩展性**: 模块化设计，便于后续功能扩展

系统核心流程是：用户输入 → 调用文生图API → 提取颜色 → 计算玻璃态样式 → 渲染主题页面。

## 部署和配置

### 1. 环境变量配置

```bash
# .env
VUE_APP_IMAGE_API_KEY=your_image_api_key_here
VUE_APP_IMAGE_API_URL=https://api.openai.com/v1/images/generations
VUE_APP_AI_SERVICE_URL=https://your-ai-service.com/api
```

### 2. 构建和部署

```bash
# 安装依赖
npm install

# 开发环境
npm run serve

# 生产构建
npm run build

# 代码检查
npm run lint
```

## 扩展性考虑

### 1. 新主题添加
- 在 `themes/` 目录下创建新的主题组件
- 在 `themeMatcher.js` 中添加关键词映射
- 在 `ThemeManager.vue` 中注册新主题

### 2. 新文生图服务支持
- 实现 `ImageGenerationService` 的不同接口
- 支持多个图片生成服务的切换

### 3. 颜色算法优化
- 改进颜色提取算法
- 添加颜色和谐性检查
- 支持多种配色方案

## 总结

这个方案提供了一个完整的固定主题页面系统，具有以下特点：

1. **简单实用**：基于现有Vue3项目，易于理解和维护
2. **智能匹配**：通过关键词匹配自动选择合适的主题
3. **动态适配**：根据生成的壁纸自动调整UI颜色
4. **可扩展性**：模块化设计，便于添加新主题和功能
5. **用户友好**：支持语音输入，提供良好的交互体验

系统可以快速响应用户的自然语言输入，提供个性化的车载界面体验。