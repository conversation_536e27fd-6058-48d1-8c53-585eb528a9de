/**
 * AI HMI 过渡特效管理器
 * 基于设计文档中的蒙版切换效果规范
 * 使用GSAP和CSS clip-path实现高性能过渡动画
 */

class TransitionManager {
    constructor() {
        this.isTransitioning = false;
        this.currentPage = null;
        this.transitionContainer = null;
        this.init();
    }

    init() {
        // 创建过渡容器
        this.createTransitionContainer();
        
        // 绑定全局事件
        this.bindEvents();
        
        console.log('TransitionManager initialized');
    }

    createTransitionContainer() {
        if (document.getElementById('transition-container')) return;
        
        const container = document.createElement('div');
        container.id = 'transition-container';
        container.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 9999;
            pointer-events: none;
            overflow: hidden;
        `;
        
        document.body.appendChild(container);
        this.transitionContainer = container;
    }

    /**
     * 执行页面过渡
     * @param {string} targetUrl - 目标页面URL
     * @param {string} effectType - 过渡效果类型
     * @param {Object} options - 过渡选项
     */
    async transitionToPage(targetUrl, effectType = 'circle_expand', options = {}) {
        if (this.isTransitioning) {
            console.warn('Transition already in progress');
            return;
        }

        this.isTransitioning = true;
        
        try {
            // 1. 预加载目标页面
            const targetContent = await this.preloadPage(targetUrl);
            
            // 2. 捕获当前页面快照
            const currentSnapshot = await this.capturePageSnapshot();
            
            // 3. 执行过渡动画
            await this.executeTransition(currentSnapshot, targetContent, effectType, options);
            
            // 4. 导航到目标页面
            window.location.href = targetUrl;
            
        } catch (error) {
            console.error('Transition failed:', error);
            // 回退到直接导航
            window.location.href = targetUrl;
        } finally {
            this.isTransitioning = false;
        }
    }

    /**
     * 预加载目标页面内容
     */
    async preloadPage(url) {
        return new Promise((resolve, reject) => {
            const iframe = document.createElement('iframe');
            iframe.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border: none;
                opacity: 0;
                pointer-events: none;
            `;
            
            iframe.onload = () => {
                try {
                    // 克隆iframe内容到新的div
                    const targetDiv = document.createElement('div');
                    targetDiv.innerHTML = iframe.contentDocument.body.innerHTML;
                    
                    // 复制样式
                    const styles = iframe.contentDocument.head.innerHTML;
                    const styleDiv = document.createElement('div');
                    styleDiv.innerHTML = styles;
                    
                    resolve({ content: targetDiv, styles: styleDiv });
                } catch (e) {
                    // 跨域限制，使用简化方案
                    resolve({ content: null, styles: null });
                }
            };
            
            iframe.onerror = () => reject(new Error('Failed to load target page'));
            iframe.src = url;
            
            // 隐藏加载iframe
            iframe.style.display = 'none';
            document.body.appendChild(iframe);
            
            // 清理
            setTimeout(() => {
                if (iframe.parentNode) {
                    iframe.parentNode.removeChild(iframe);
                }
            }, 5000);
        });
    }

    /**
     * 捕获当前页面快照
     */
    async capturePageSnapshot() {
        return new Promise((resolve) => {
            // 使用html2canvas或简化的DOM克隆
            if (window.html2canvas) {
                html2canvas(document.body).then(canvas => {
                    resolve(canvas);
                });
            } else {
                // 简化方案：创建当前页面的静态副本
                const snapshot = document.body.cloneNode(true);
                snapshot.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    overflow: hidden;
                    pointer-events: none;
                `;
                resolve(snapshot);
            }
        });
    }

    /**
     * 执行过渡动画
     */
    async executeTransition(currentSnapshot, targetContent, effectType, options) {
        return new Promise((resolve) => {
            // 设置过渡容器
            this.transitionContainer.style.pointerEvents = 'auto';
            this.transitionContainer.innerHTML = '';
            
            // 添加当前快照
            const snapshotLayer = document.createElement('div');
            snapshotLayer.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 2;
            `;
            
            if (currentSnapshot instanceof HTMLCanvasElement) {
                snapshotLayer.appendChild(currentSnapshot);
            } else {
                snapshotLayer.appendChild(currentSnapshot);
            }
            
            // 添加目标内容层（如果可用）
            const targetLayer = document.createElement('div');
            targetLayer.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 1;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            `;
            
            if (targetContent && targetContent.content) {
                targetLayer.appendChild(targetContent.content);
            } else {
                // 显示加载动画
                targetLayer.innerHTML = `
                    <div style="
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 100%;
                        color: white;
                        font-size: 24px;
                        font-family: -apple-system, BlinkMacSystemFont, sans-serif;
                    ">
                        <div style="text-align: center;">
                            <div style="margin-bottom: 20px;">正在加载...</div>
                            <div style="width: 40px; height: 40px; border: 3px solid rgba(255,255,255,0.3); border-top: 3px solid white; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto;"></div>
                        </div>
                    </div>
                    <style>
                        @keyframes spin {
                            0% { transform: rotate(0deg); }
                            100% { transform: rotate(360deg); }
                        }
                    </style>
                `;
            }
            
            this.transitionContainer.appendChild(targetLayer);
            this.transitionContainer.appendChild(snapshotLayer);
            
            // 执行具体的过渡效果
            this.executeEffect(snapshotLayer, effectType, options, resolve);
        });
    }

    /**
     * 执行具体的过渡效果
     */
    executeEffect(snapshotLayer, effectType, options, onComplete) {
        const effects = {
            // 滑动面板擦除
            sliding_panel: () => {
                gsap.set(snapshotLayer, { clipPath: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)' });
                gsap.to(snapshotLayer, {
                    clipPath: 'polygon(100% 0%, 100% 0%, 100% 100%, 100% 100%)',
                    duration: 1.2,
                    ease: 'power2.inOut',
                    onComplete: this.cleanupTransition.bind(this, onComplete)
                });
            },
            
            // 斜切面板擦除
            angled_panel: () => {
                gsap.set(snapshotLayer, { clipPath: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)' });
                gsap.to(snapshotLayer, {
                    clipPath: 'polygon(100% 0%, 110% 0%, 110% 100%, 100% 100%)',
                    duration: 1.0,
                    ease: 'power3.inOut',
                    onComplete: this.cleanupTransition.bind(this, onComplete)
                });
            },
            
            // 圆形扩展擦除
            circle_expand: () => {
                const centerX = options.centerX || 50;
                const centerY = options.centerY || 50;
                
                gsap.set(snapshotLayer, { clipPath: `circle(150% at ${centerX}% ${centerY}%)` });
                gsap.to(snapshotLayer, {
                    clipPath: `circle(0% at ${centerX}% ${centerY}%)`,
                    duration: 1.5,
                    ease: 'power2.inOut',
                    onComplete: this.cleanupTransition.bind(this, onComplete)
                });
            },
            
            // 幕布开启擦除
            curtain_open: () => {
                gsap.set(snapshotLayer, { clipPath: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)' });
                gsap.to(snapshotLayer, {
                    clipPath: 'polygon(50% 0%, 50% 0%, 50% 100%, 50% 100%)',
                    duration: 1.8,
                    ease: 'power2.inOut',
                    onComplete: this.cleanupTransition.bind(this, onComplete)
                });
            },
            
            // 默认淡出效果
            fade: () => {
                gsap.to(snapshotLayer, {
                    opacity: 0,
                    duration: 0.8,
                    ease: 'power2.inOut',
                    onComplete: this.cleanupTransition.bind(this, onComplete)
                });
            }
        };
        
        const effect = effects[effectType] || effects.fade;
        effect();
    }

    /**
     * 清理过渡效果
     */
    cleanupTransition(onComplete) {
        setTimeout(() => {
            this.transitionContainer.style.pointerEvents = 'none';
            this.transitionContainer.innerHTML = '';
            if (onComplete) onComplete();
        }, 100);
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 监听页面链接点击
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[data-transition]');
            if (link && !this.isTransitioning) {
                e.preventDefault();
                
                const url = link.href;
                const effect = link.dataset.transition || 'circle_expand';
                const options = {};
                
                // 如果是圆形扩展，计算点击位置
                if (effect === 'circle_expand') {
                    const rect = link.getBoundingClientRect();
                    options.centerX = ((rect.left + rect.width / 2) / window.innerWidth) * 100;
                    options.centerY = ((rect.top + rect.height / 2) / window.innerHeight) * 100;
                }
                
                this.transitionToPage(url, effect, options);
            }
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key >= '1' && e.key <= '5' && !this.isTransitioning) {
                e.preventDefault();
                
                const pages = [
                    '1_natural_commute.html',
                    '2_cyberpunk_drive.html',
                    '3_glassmorphism_wait.html',
                    '4_neumorphism_rainy.html',
                    '5_kawaii_family_trip.html'
                ];
                
                const effects = [
                    'sliding_panel',
                    'angled_panel',
                    'circle_expand',
                    'curtain_open',
                    'circle_expand'
                ];
                
                const index = parseInt(e.key) - 1;
                if (pages[index]) {
                    const basePath = window.location.pathname.replace(/\/[^/]*$/, '');
                    const targetUrl = basePath + '/' + pages[index];
                    this.transitionToPage(targetUrl, effects[index]);
                }
            }
        });
    }

    /**
     * 手动触发过渡
     */
    triggerTransition(targetUrl, effectType = 'circle_expand', options = {}) {
        this.transitionToPage(targetUrl, effectType, options);
    }
}

// 自动初始化
if (typeof window !== 'undefined') {
    window.TransitionManager = TransitionManager;
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            window.transitionManager = new TransitionManager();
        });
    } else {
        window.transitionManager = new TransitionManager();
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TransitionManager;
}