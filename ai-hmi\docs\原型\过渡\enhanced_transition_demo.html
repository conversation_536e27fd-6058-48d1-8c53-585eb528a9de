<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI HMI 增强过渡演示 - Enhanced Transition Demo</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            overflow: hidden;
        }

        .main-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }

        .page-frame {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }

        .navigation-overlay {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .navigation-overlay.hidden {
            transform: translateY(-100px);
            opacity: 0;
        }

        .nav-title {
            color: white;
            font-size: 18px;
            font-weight: 600;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-button {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .nav-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .nav-button.active {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .transition-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 9999;
            pointer-events: none;
            overflow: hidden;
        }

        .snapshot-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2;
        }

        .target-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .loading-indicator {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10000;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px 30px;
            border-radius: 10px;
            font-size: 16px;
            display: none;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .effect-preview {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 1001;
        }

        .keyboard-shortcuts {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 11px;
            z-index: 1001;
            max-width: 200px;
        }

        .shortcut-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .shortcut-key {
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 当前页面显示区域 -->
        <iframe id="current-page" class="page-frame" src="../1_natural_commute.html"></iframe>
        
        <!-- 导航覆盖层 -->
        <div class="navigation-overlay" id="nav-overlay">
            <div class="nav-title">AI HMI 过渡效果演示</div>
            <div class="nav-buttons">
                <a href="#" class="nav-button active" data-page="../1_natural_commute.html" data-effect="sliding_panel">自然通勤</a>
                <a href="#" class="nav-button" data-page="../2_cyberpunk_drive.html" data-effect="angled_panel">赛博朋克</a>
                <a href="#" class="nav-button" data-page="../3_glassmorphism_wait.html" data-effect="circle_expand">玻璃拟态</a>
                <a href="#" class="nav-button" data-page="../4_neumorphism_rainy.html" data-effect="curtain_open">新拟态</a>
                <a href="#" class="nav-button" data-page="../5_kawaii_family_trip.html" data-effect="circle_expand">可爱风格</a>
                <button class="nav-button" onclick="toggleNavigation()">隐藏导航</button>
            </div>
        </div>

        <!-- 过渡容器 -->
        <div class="transition-container" id="transition-container"></div>

        <!-- 加载指示器 -->
        <div class="loading-indicator" id="loading-indicator">
            <span class="loading-spinner"></span>
            正在切换页面...
        </div>

        <!-- 效果预览 -->
        <div class="effect-preview" id="effect-preview">
            当前效果: 滑动面板
        </div>

        <!-- 键盘快捷键说明 -->
        <div class="keyboard-shortcuts">
            <div style="font-weight: bold; margin-bottom: 8px;">快捷键</div>
            <div class="shortcut-item">
                <span>自然通勤</span>
                <span class="shortcut-key">Ctrl+1</span>
            </div>
            <div class="shortcut-item">
                <span>赛博朋克</span>
                <span class="shortcut-key">Ctrl+2</span>
            </div>
            <div class="shortcut-item">
                <span>玻璃拟态</span>
                <span class="shortcut-key">Ctrl+3</span>
            </div>
            <div class="shortcut-item">
                <span>新拟态</span>
                <span class="shortcut-key">Ctrl+4</span>
            </div>
            <div class="shortcut-item">
                <span>可爱风格</span>
                <span class="shortcut-key">Ctrl+5</span>
            </div>
            <div class="shortcut-item">
                <span>隐藏导航</span>
                <span class="shortcut-key">H</span>
            </div>
        </div>
    </div>

    <script>
        class EnhancedTransitionManager {
            constructor() {
                this.isTransitioning = false;
                this.currentPageUrl = '../1_natural_commute.html';
                this.currentPageFrame = document.getElementById('current-page');
                this.transitionContainer = document.getElementById('transition-container');
                this.loadingIndicator = document.getElementById('loading-indicator');
                this.effectPreview = document.getElementById('effect-preview');
                this.navOverlay = document.getElementById('nav-overlay');
                this.isNavHidden = false;
                
                this.init();
            }

            init() {
                this.bindEvents();
                console.log('Enhanced Transition Manager initialized');
            }

            async transitionToPage(targetUrl, effectType = 'circle_expand', options = {}) {
                if (this.isTransitioning) {
                    console.warn('Transition already in progress');
                    return;
                }

                this.isTransitioning = true;
                this.showLoading();
                this.updateEffectPreview(effectType);

                try {
                    // 1. 捕获当前页面快照
                    const currentSnapshot = await this.captureCurrentPage();
                    
                    // 2. 预加载目标页面
                    const targetFrame = await this.preloadTargetPage(targetUrl);
                    
                    // 3. 执行过渡动画
                    await this.executeTransition(currentSnapshot, targetFrame, effectType, options);
                    
                    // 4. 切换到目标页面
                    this.currentPageFrame.src = targetUrl;
                    this.currentPageUrl = targetUrl;
                    
                    // 5. 更新导航状态
                    this.updateNavigationState(targetUrl);
                    
                } catch (error) {
                    console.error('Transition failed:', error);
                    // 直接切换页面作为回退
                    this.currentPageFrame.src = targetUrl;
                    this.currentPageUrl = targetUrl;
                } finally {
                    this.hideLoading();
                    this.isTransitioning = false;
                }
            }

            async captureCurrentPage() {
                return new Promise((resolve) => {
                    try {
                        // 尝试使用html2canvas捕获iframe内容
                        const iframe = this.currentPageFrame;
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        
                        if (window.html2canvas && iframeDoc) {
                            html2canvas(iframeDoc.body, {
                                useCORS: true,
                                allowTaint: true,
                                scale: 0.5 // 降低分辨率以提高性能
                            }).then(canvas => {
                                resolve(canvas);
                            }).catch(() => {
                                // 回退到简单的截图方案
                                resolve(this.createFallbackSnapshot());
                            });
                        } else {
                            resolve(this.createFallbackSnapshot());
                        }
                    } catch (error) {
                        resolve(this.createFallbackSnapshot());
                    }
                });
            }

            createFallbackSnapshot() {
                const canvas = document.createElement('canvas');
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
                const ctx = canvas.getContext('2d');
                
                // 创建渐变背景作为快照
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#1e3c72');
                gradient.addColorStop(1, '#2a5298');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 添加文字
                ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                ctx.font = '24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('页面快照', canvas.width / 2, canvas.height / 2);
                
                return canvas;
            }

            async preloadTargetPage(targetUrl) {
                return new Promise((resolve) => {
                    const preloadFrame = document.createElement('iframe');
                    preloadFrame.style.cssText = `
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        border: none;
                        opacity: 0;
                        pointer-events: none;
                        z-index: -1;
                    `;
                    
                    preloadFrame.onload = () => {
                        resolve(preloadFrame);
                    };
                    
                    preloadFrame.onerror = () => {
                        resolve(null);
                    };
                    
                    preloadFrame.src = targetUrl;
                    document.body.appendChild(preloadFrame);
                    
                    // 清理预加载的iframe
                    setTimeout(() => {
                        if (preloadFrame.parentNode) {
                            preloadFrame.parentNode.removeChild(preloadFrame);
                        }
                    }, 5000);
                });
            }

            async executeTransition(currentSnapshot, targetFrame, effectType, options) {
                return new Promise((resolve) => {
                    // 设置过渡容器
                    this.transitionContainer.style.pointerEvents = 'auto';
                    this.transitionContainer.innerHTML = '';
                    
                    // 创建快照层
                    const snapshotLayer = document.createElement('div');
                    snapshotLayer.className = 'snapshot-layer';
                    
                    if (currentSnapshot instanceof HTMLCanvasElement) {
                        currentSnapshot.style.cssText = `
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        `;
                        snapshotLayer.appendChild(currentSnapshot);
                    } else {
                        snapshotLayer.style.background = 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)';
                    }
                    
                    // 创建目标层
                    const targetLayer = document.createElement('div');
                    targetLayer.className = 'target-layer';
                    
                    if (targetFrame) {
                        targetFrame.style.cssText = `
                            width: 100%;
                            height: 100%;
                            border: none;
                            opacity: 1;
                        `;
                        targetLayer.appendChild(targetFrame);
                    } else {
                        targetLayer.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                    }
                    
                    this.transitionContainer.appendChild(targetLayer);
                    this.transitionContainer.appendChild(snapshotLayer);
                    
                    // 执行过渡效果
                    this.executeEffect(snapshotLayer, effectType, options, () => {
                        this.cleanupTransition();
                        resolve();
                    });
                });
            }

            executeEffect(snapshotLayer, effectType, options, onComplete) {
                const effects = {
                    sliding_panel: () => {
                        gsap.set(snapshotLayer, { clipPath: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)' });
                        gsap.to(snapshotLayer, {
                            clipPath: 'polygon(100% 0%, 100% 0%, 100% 100%, 100% 100%)',
                            duration: 1.2,
                            ease: 'power2.inOut',
                            onComplete
                        });
                    },
                    
                    angled_panel: () => {
                        gsap.set(snapshotLayer, { clipPath: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)' });
                        gsap.to(snapshotLayer, {
                            clipPath: 'polygon(100% 0%, 110% 0%, 110% 100%, 100% 100%)',
                            duration: 1.0,
                            ease: 'power3.inOut',
                            onComplete
                        });
                    },
                    
                    circle_expand: () => {
                        const centerX = options.centerX || 50;
                        const centerY = options.centerY || 50;
                        
                        gsap.set(snapshotLayer, { clipPath: `circle(150% at ${centerX}% ${centerY}%)` });
                        gsap.to(snapshotLayer, {
                            clipPath: `circle(0% at ${centerX}% ${centerY}%)`,
                            duration: 1.5,
                            ease: 'power2.inOut',
                            onComplete
                        });
                    },
                    
                    curtain_open: () => {
                        gsap.set(snapshotLayer, { clipPath: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)' });
                        gsap.to(snapshotLayer, {
                            clipPath: 'polygon(50% 0%, 50% 0%, 50% 100%, 50% 100%)',
                            duration: 1.8,
                            ease: 'power2.inOut',
                            onComplete
                        });
                    },
                    
                    fade: () => {
                        gsap.to(snapshotLayer, {
                            opacity: 0,
                            duration: 0.8,
                            ease: 'power2.inOut',
                            onComplete
                        });
                    }
                };
                
                const effect = effects[effectType] || effects.fade;
                effect();
            }

            cleanupTransition() {
                setTimeout(() => {
                    this.transitionContainer.style.pointerEvents = 'none';
                    this.transitionContainer.innerHTML = '';
                }, 100);
            }

            showLoading() {
                this.loadingIndicator.style.display = 'block';
            }

            hideLoading() {
                this.loadingIndicator.style.display = 'none';
            }

            updateEffectPreview(effectType) {
                const effectNames = {
                    sliding_panel: '滑动面板',
                    angled_panel: '斜切面板',
                    circle_expand: '圆形扩展',
                    curtain_open: '幕布开启',
                    fade: '淡入淡出'
                };
                
                this.effectPreview.textContent = `当前效果: ${effectNames[effectType] || effectType}`;
            }

            updateNavigationState(targetUrl) {
                const buttons = document.querySelectorAll('.nav-button[data-page]');
                buttons.forEach(button => {
                    button.classList.remove('active');
                    if (button.dataset.page === targetUrl) {
                        button.classList.add('active');
                    }
                });
            }

            bindEvents() {
                // 导航按钮点击事件
                document.addEventListener('click', (e) => {
                    const button = e.target.closest('.nav-button[data-page]');
                    if (button && !this.isTransitioning) {
                        e.preventDefault();
                        
                        const targetUrl = button.dataset.page;
                        const effectType = button.dataset.effect || 'circle_expand';
                        
                        if (targetUrl !== this.currentPageUrl) {
                            this.transitionToPage(targetUrl, effectType);
                        }
                    }
                });
                
                // 键盘快捷键
                document.addEventListener('keydown', (e) => {
                    if (this.isTransitioning) return;
                    
                    if (e.ctrlKey && e.key >= '1' && e.key <= '5') {
                        e.preventDefault();
                        
                        const pages = [
                            '../1_natural_commute.html',
                            '../2_cyberpunk_drive.html',
                            '../3_glassmorphism_wait.html',
                            '../4_neumorphism_rainy.html',
                            '../5_kawaii_family_trip.html'
                        ];
                        
                        const effects = [
                            'sliding_panel',
                            'angled_panel',
                            'circle_expand',
                            'curtain_open',
                            'circle_expand'
                        ];
                        
                        const index = parseInt(e.key) - 1;
                        if (pages[index] && pages[index] !== this.currentPageUrl) {
                            this.transitionToPage(pages[index], effects[index]);
                        }
                    } else if (e.key.toLowerCase() === 'h') {
                        e.preventDefault();
                        toggleNavigation();
                    }
                });
            }
        }

        // 导航显示/隐藏功能
        function toggleNavigation() {
            const manager = window.transitionManager;
            const navOverlay = document.getElementById('nav-overlay');
            
            manager.isNavHidden = !manager.isNavHidden;
            
            if (manager.isNavHidden) {
                navOverlay.classList.add('hidden');
            } else {
                navOverlay.classList.remove('hidden');
            }
        }

        // 初始化
        window.addEventListener('DOMContentLoaded', () => {
            window.transitionManager = new EnhancedTransitionManager();
        });
    </script>
</body>
</html>