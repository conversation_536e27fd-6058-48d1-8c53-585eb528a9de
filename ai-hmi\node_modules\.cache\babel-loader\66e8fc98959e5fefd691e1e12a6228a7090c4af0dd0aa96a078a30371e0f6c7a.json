{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, resolveComponent as _resolveComponent, createBlock as _createBlock, createVNode as _createVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"vpa-test-page\"\n};\nconst _hoisted_2 = {\n  class: \"mode-controls\"\n};\nconst _hoisted_3 = [\"onClick\"];\nconst _hoisted_4 = {\n  class: \"size-controls\"\n};\nconst _hoisted_5 = [\"onClick\"];\nconst _hoisted_6 = {\n  class: \"animation-controls\"\n};\nconst _hoisted_7 = [\"onClick\"];\nconst _hoisted_8 = {\n  class: \"vpa-display-area\"\n};\nconst _hoisted_9 = {\n  class: \"vpa-container\"\n};\nconst _hoisted_10 = {\n  key: 1,\n  class: \"hidden-message\"\n};\nconst _hoisted_11 = {\n  class: \"status-info\"\n};\nconst _hoisted_12 = {\n  class: \"info-grid\"\n};\nconst _hoisted_13 = {\n  class: \"info-item\"\n};\nconst _hoisted_14 = {\n  class: \"info-item\"\n};\nconst _hoisted_15 = {\n  class: \"info-item\"\n};\nconst _hoisted_16 = {\n  class: \"info-item\"\n};\nconst _hoisted_17 = {\n  class: \"info-item\"\n};\nconst _hoisted_18 = {\n  class: \"scene-test\"\n};\nconst _hoisted_19 = {\n  class: \"scene-grid\"\n};\nconst _hoisted_20 = {\n  class: \"scene-preview\"\n};\nconst _hoisted_21 = {\n  class: \"vpa-widget-preview\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_VPAAvatarWidget = _resolveComponent(\"VPAAvatarWidget\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[10] || (_cache[10] = _createElementVNode(\"h1\", null, \"VPA数字人显示测试\", -1 /* CACHED */)), _createCommentVNode(\" 模式切换控制 \"), _createElementVNode(\"div\", _hoisted_2, [_cache[0] || (_cache[0] = _createElementVNode(\"h3\", null, \"VPA模式切换\", -1 /* CACHED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.availableModes, mode => {\n    return _openBlock(), _createElementBlock(\"button\", {\n      key: mode,\n      onClick: $event => $setup.switchVpaMode(mode),\n      class: _normalizeClass(['mode-btn', {\n        active: $setup.currentMode === mode\n      }])\n    }, _toDisplayString($setup.getModeLabel(mode)), 11 /* TEXT, CLASS, PROPS */, _hoisted_3);\n  }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 尺寸测试 \"), _createElementVNode(\"div\", _hoisted_4, [_cache[1] || (_cache[1] = _createElementVNode(\"h3\", null, \"VPA尺寸测试\", -1 /* CACHED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.availableSizes, size => {\n    return _openBlock(), _createElementBlock(\"button\", {\n      key: size,\n      onClick: $event => $setup.currentSize = size,\n      class: _normalizeClass(['size-btn', {\n        active: $setup.currentSize === size\n      }])\n    }, _toDisplayString(size), 11 /* TEXT, CLASS, PROPS */, _hoisted_5);\n  }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 动画状态测试 \"), _createElementVNode(\"div\", _hoisted_6, [_cache[2] || (_cache[2] = _createElementVNode(\"h3\", null, \"VPA动画状态测试\", -1 /* CACHED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.availableAnimations, animation => {\n    return _openBlock(), _createElementBlock(\"button\", {\n      key: animation,\n      onClick: $event => $setup.setVpaAnimation(animation),\n      class: _normalizeClass(['animation-btn', {\n        active: $setup.currentAnimation === animation\n      }])\n    }, _toDisplayString($setup.getAnimationLabel(animation)), 11 /* TEXT, CLASS, PROPS */, _hoisted_7);\n  }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" VPA组件显示区域 \"), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"h3\", null, \"VPA组件显示 (当前模式: \" + _toDisplayString($setup.getModeLabel($setup.currentMode)) + \")\", 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_9, [$setup.currentMode !== 'hidden' ? (_openBlock(), _createBlock(_component_VPAAvatarWidget, {\n    key: 0,\n    size: $setup.currentSize,\n    position: {\n      x: 1,\n      y: 1\n    },\n    theme: 'glass',\n    \"theme-colors\": $setup.themeColors,\n    onAvatarClick: $setup.handleVpaClick,\n    onModeChanged: $setup.handleVpaModeChanged,\n    onAnimationChanged: $setup.handleVpaAnimationChanged\n  }, null, 8 /* PROPS */, [\"size\", \"theme-colors\", \"onAvatarClick\", \"onModeChanged\", \"onAnimationChanged\"])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_10, \" VPA已隐藏 \"))])]), _createCommentVNode(\" 状态信息显示 \"), _createElementVNode(\"div\", _hoisted_11, [_cache[8] || (_cache[8] = _createElementVNode(\"h3\", null, \"VPA状态信息\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_cache[3] || (_cache[3] = _createElementVNode(\"label\", null, \"当前模式:\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($setup.getModeLabel($setup.currentMode)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_14, [_cache[4] || (_cache[4] = _createElementVNode(\"label\", null, \"当前尺寸:\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($setup.currentSize), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_15, [_cache[5] || (_cache[5] = _createElementVNode(\"label\", null, \"当前动画:\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($setup.getAnimationLabel($setup.currentAnimation)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_16, [_cache[6] || (_cache[6] = _createElementVNode(\"label\", null, \"是否可见:\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($setup.isVisible ? '是' : '否'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_17, [_cache[7] || (_cache[7] = _createElementVNode(\"label\", null, \"是否交互中:\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($setup.isInteracting ? '是' : '否'), 1 /* TEXT */)])])]), _createCommentVNode(\" 场景测试 \"), _createElementVNode(\"div\", _hoisted_18, [_cache[9] || (_cache[9] = _createElementVNode(\"h3\", null, \"场景中的VPA显示测试\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_19, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.testScenes, scene => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"scene-item\",\n      key: scene.id\n    }, [_createElementVNode(\"h4\", null, _toDisplayString(scene.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", {\n      class: _normalizeClass(['scene-layout', `layout-${scene.layout}`])\n    }, [_createElementVNode(\"div\", _hoisted_21, [_createVNode(_component_VPAAvatarWidget, {\n      size: scene.vpaSize || 'small',\n      position: scene.vpaPosition || {\n        x: 1,\n        y: 1\n      },\n      theme: 'glass',\n      \"theme-colors\": $setup.themeColors,\n      onAvatarClick: $setup.handleVpaClick\n    }, null, 8 /* PROPS */, [\"size\", \"position\", \"theme-colors\", \"onAvatarClick\"])])], 2 /* CLASS */)])]);\n  }), 128 /* KEYED_FRAGMENT */))])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_createCommentVNode", "_hoisted_2", "_Fragment", "_renderList", "$setup", "availableModes", "mode", "key", "onClick", "$event", "switchVpaMode", "_normalizeClass", "active", "currentMode", "getModeLabel", "_hoisted_3", "_hoisted_4", "availableSizes", "size", "currentSize", "_hoisted_5", "_hoisted_6", "availableAnimations", "animation", "setVpaAnimation", "currentAnimation", "getAnimationLabel", "_hoisted_7", "_hoisted_8", "_toDisplayString", "_hoisted_9", "_createBlock", "_component_VPAAvatarWidget", "position", "x", "y", "theme", "themeColors", "onAvatarClick", "handleVpaClick", "onModeChanged", "handleVpaModeChanged", "onAnimationChanged", "handleVpaAnimationChanged", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "isVisible", "_hoisted_17", "isInteracting", "_hoisted_18", "_hoisted_19", "testScenes", "scene", "id", "name", "_hoisted_20", "layout", "_hoisted_21", "_createVNode", "vpaSize", "vpaPosition"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\VPATestPage.vue"], "sourcesContent": ["<template>\n  <div class=\"vpa-test-page\">\n    <h1>VPA数字人显示测试</h1>\n    \n    <!-- 模式切换控制 -->\n    <div class=\"mode-controls\">\n      <h3>VPA模式切换</h3>\n      <button \n        v-for=\"mode in availableModes\" \n        :key=\"mode\"\n        @click=\"switchVpaMode(mode)\"\n        :class=\"['mode-btn', { active: currentMode === mode }]\"\n      >\n        {{ getModeLabel(mode) }}\n      </button>\n    </div>\n\n    <!-- 尺寸测试 -->\n    <div class=\"size-controls\">\n      <h3>VPA尺寸测试</h3>\n      <button \n        v-for=\"size in availableSizes\" \n        :key=\"size\"\n        @click=\"currentSize = size\"\n        :class=\"['size-btn', { active: currentSize === size }]\"\n      >\n        {{ size }}\n      </button>\n    </div>\n\n    <!-- 动画状态测试 -->\n    <div class=\"animation-controls\">\n      <h3>VPA动画状态测试</h3>\n      <button \n        v-for=\"animation in availableAnimations\" \n        :key=\"animation\"\n        @click=\"setVpaAnimation(animation)\"\n        :class=\"['animation-btn', { active: currentAnimation === animation }]\"\n      >\n        {{ getAnimationLabel(animation) }}\n      </button>\n    </div>\n\n    <!-- VPA组件显示区域 -->\n    <div class=\"vpa-display-area\">\n      <h3>VPA组件显示 (当前模式: {{ getModeLabel(currentMode) }})</h3>\n      \n      <div class=\"vpa-container\">\n        <VPAAvatarWidget\n          v-if=\"currentMode !== 'hidden'\"\n          :size=\"currentSize\"\n          :position=\"{ x: 1, y: 1 }\"\n          :theme=\"'glass'\"\n          :theme-colors=\"themeColors\"\n          @avatar-click=\"handleVpaClick\"\n          @mode-changed=\"handleVpaModeChanged\"\n          @animation-changed=\"handleVpaAnimationChanged\"\n        />\n        \n        <div v-else class=\"hidden-message\">\n          VPA已隐藏\n        </div>\n      </div>\n    </div>\n\n    <!-- 状态信息显示 -->\n    <div class=\"status-info\">\n      <h3>VPA状态信息</h3>\n      <div class=\"info-grid\">\n        <div class=\"info-item\">\n          <label>当前模式:</label>\n          <span>{{ getModeLabel(currentMode) }}</span>\n        </div>\n        <div class=\"info-item\">\n          <label>当前尺寸:</label>\n          <span>{{ currentSize }}</span>\n        </div>\n        <div class=\"info-item\">\n          <label>当前动画:</label>\n          <span>{{ getAnimationLabel(currentAnimation) }}</span>\n        </div>\n        <div class=\"info-item\">\n          <label>是否可见:</label>\n          <span>{{ isVisible ? '是' : '否' }}</span>\n        </div>\n        <div class=\"info-item\">\n          <label>是否交互中:</label>\n          <span>{{ isInteracting ? '是' : '否' }}</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- 场景测试 -->\n    <div class=\"scene-test\">\n      <h3>场景中的VPA显示测试</h3>\n      <div class=\"scene-grid\">\n        <div class=\"scene-item\" v-for=\"scene in testScenes\" :key=\"scene.id\">\n          <h4>{{ scene.name }}</h4>\n          <div class=\"scene-preview\">\n            <div :class=\"['scene-layout', `layout-${scene.layout}`]\">\n              <div class=\"vpa-widget-preview\">\n                <VPAAvatarWidget\n                  :size=\"scene.vpaSize || 'small'\"\n                  :position=\"scene.vpaPosition || { x: 1, y: 1 }\"\n                  :theme=\"'glass'\"\n                  :theme-colors=\"themeColors\"\n                  @avatar-click=\"handleVpaClick\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted } from 'vue'\nimport { useVpaStore } from '@/store/modules/vpa'\nimport VPAAvatarWidget from './vpa/VPAAvatarWidget.vue'\n\nexport default {\n  name: 'VPATestPage',\n  components: {\n    VPAAvatarWidget\n  },\n  \n  setup() {\n    const vpaStore = useVpaStore()\n    \n    // 响应式数据\n    const currentSize = ref('medium')\n    const availableModes = ['companion', 'interaction', 'hidden']\n    const availableSizes = ['small', 'medium', 'large']\n    const availableAnimations = ['idle', 'talking', 'listening', 'thinking', 'greeting', 'sleeping']\n    \n    // 主题颜色\n    const themeColors = {\n      primary: '#667eea',\n      secondary: '#764ba2',\n      background: 'rgba(102, 126, 234, 0.1)',\n      text: '#ffffff'\n    }\n    \n    // 测试场景\n    const testScenes = [\n      {\n        id: 'family',\n        name: '家庭出行模式',\n        layout: 'family',\n        vpaSize: 'small',\n        vpaPosition: { x: 15, y: 7 }\n      },\n      {\n        id: 'focus',\n        name: '专注通勤模式',\n        layout: 'focus',\n        vpaSize: 'small',\n        vpaPosition: { x: 15, y: 7 }\n      },\n      {\n        id: 'entertainment',\n        name: '娱乐等待模式',\n        layout: 'entertainment',\n        vpaSize: 'small',\n        vpaPosition: { x: 1, y: 1 }\n      },\n      {\n        id: 'minimal',\n        name: '极简模式',\n        layout: 'minimal',\n        vpaSize: 'medium',\n        vpaPosition: { x: 1, y: 1 }\n      }\n    ]\n    \n    // 计算属性\n    const currentMode = computed(() => vpaStore.currentMode)\n    const currentAnimation = computed(() => vpaStore.animationState)\n    const isVisible = computed(() => vpaStore.isVisible)\n    const isInteracting = computed(() => vpaStore.isInteracting)\n    \n    // 方法\n    const switchVpaMode = (mode) => {\n      vpaStore.switchMode(mode)\n    }\n    \n    const setVpaAnimation = (animation) => {\n      vpaStore.setAnimation(animation)\n    }\n    \n    const getModeLabel = (mode) => {\n      const labels = {\n        companion: 'div模式 (陪伴)',\n        interaction: '交互模式',\n        hidden: '隐藏模式'\n      }\n      return labels[mode] || mode\n    }\n    \n    const getAnimationLabel = (animation) => {\n      const labels = {\n        idle: '待机',\n        talking: '说话',\n        listening: '聆听',\n        thinking: '思考',\n        greeting: '问候',\n        sleeping: '休眠'\n      }\n      return labels[animation] || animation\n    }\n    \n    const handleVpaClick = (data) => {\n      console.log('VPA点击事件:', data)\n    }\n    \n    const handleVpaModeChanged = (mode) => {\n      console.log('VPA模式变更:', mode)\n    }\n    \n    const handleVpaAnimationChanged = (animationData) => {\n      console.log('VPA动画变更:', animationData)\n    }\n    \n    // 生命周期\n    onMounted(() => {\n      console.log('VPA测试页面已加载')\n      // 确保VPA处于companion模式\n      vpaStore.switchMode('companion')\n    })\n    \n    return {\n      currentSize,\n      availableModes,\n      availableSizes,\n      availableAnimations,\n      testScenes,\n      themeColors,\n      currentMode,\n      currentAnimation,\n      isVisible,\n      isInteracting,\n      switchVpaMode,\n      setVpaAnimation,\n      getModeLabel,\n      getAnimationLabel,\n      handleVpaClick,\n      handleVpaModeChanged,\n      handleVpaAnimationChanged\n    }\n  }\n}\n</script>\n\n<style scoped>\n.vpa-test-page {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  min-height: 100vh;\n  color: white;\n}\n\n.vpa-test-page h1 {\n  text-align: center;\n  margin-bottom: 30px;\n  font-size: 2.5em;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.vpa-test-page h3 {\n  margin-bottom: 15px;\n  color: #ffffff;\n  font-size: 1.3em;\n}\n\n/* 控制按钮样式 */\n.mode-controls,\n.size-controls,\n.animation-controls {\n  margin-bottom: 30px;\n  padding: 20px;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.mode-btn,\n.size-btn,\n.animation-btn {\n  margin: 5px;\n  padding: 10px 20px;\n  border: none;\n  border-radius: 25px;\n  background: rgba(255, 255, 255, 0.2);\n  color: white;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-weight: 500;\n}\n\n.mode-btn:hover,\n.size-btn:hover,\n.animation-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: translateY(-2px);\n}\n\n.mode-btn.active,\n.size-btn.active,\n.animation-btn.active {\n  background: rgba(255, 255, 255, 0.4);\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\n}\n\n/* VPA显示区域 */\n.vpa-display-area {\n  margin-bottom: 30px;\n  padding: 20px;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.vpa-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 200px;\n  background: rgba(0, 0, 0, 0.1);\n  border-radius: 10px;\n  margin-top: 15px;\n}\n\n.hidden-message {\n  font-size: 1.2em;\n  color: rgba(255, 255, 255, 0.7);\n  text-align: center;\n}\n\n/* 状态信息 */\n.status-info {\n  margin-bottom: 30px;\n  padding: 20px;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 15px;\n  margin-top: 15px;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  padding: 10px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n}\n\n.info-item label {\n  font-weight: 600;\n}\n\n/* 场景测试 */\n.scene-test {\n  padding: 20px;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.scene-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n  margin-top: 15px;\n}\n\n.scene-item {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 10px;\n  padding: 15px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.scene-item h4 {\n  margin-bottom: 10px;\n  text-align: center;\n  color: #ffffff;\n}\n\n.scene-preview {\n  height: 150px;\n  background: rgba(0, 0, 0, 0.2);\n  border-radius: 8px;\n  position: relative;\n  overflow: hidden;\n}\n\n.scene-layout {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n\n.vpa-widget-preview {\n  position: absolute;\n  bottom: 10px;\n  right: 10px;\n}\n\n.layout-family .vpa-widget-preview {\n  bottom: 10px;\n  right: 10px;\n}\n\n.layout-focus .vpa-widget-preview {\n  bottom: 10px;\n  right: 10px;\n}\n\n.layout-entertainment .vpa-widget-preview {\n  top: 50%;\n  right: 10px;\n  transform: translateY(-50%);\n}\n\n.layout-minimal .vpa-widget-preview {\n  bottom: 10px;\n  left: 10px;\n}\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAe;;EAInBA,KAAK,EAAC;AAAe;;;EAarBA,KAAK,EAAC;AAAe;;;EAarBA,KAAK,EAAC;AAAoB;;;EAa1BA,KAAK,EAAC;AAAkB;;EAGtBA,KAAK,EAAC;AAAe;;;EAYZA,KAAK,EAAC;;;EAOjBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAQrBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAY;;EAGdA,KAAK,EAAC;AAAe;;EAEjBA,KAAK,EAAC;AAAoB;;;uBAnG3CC,mBAAA,CAiHM,OAjHNC,UAiHM,G,4BAhHJC,mBAAA,CAAmB,YAAf,YAAU,qBAEdC,mBAAA,YAAe,EACfD,mBAAA,CAUM,OAVNE,UAUM,G,0BATJF,mBAAA,CAAgB,YAAZ,SAAO,sB,kBACXF,mBAAA,CAOSK,SAAA,QAAAC,WAAA,CANQC,MAAA,CAAAC,cAAc,EAAtBC,IAAI;yBADbT,mBAAA,CAOS;MALNU,GAAG,EAAED,IAAI;MACTE,OAAK,EAAAC,MAAA,IAAEL,MAAA,CAAAM,aAAa,CAACJ,IAAI;MACzBV,KAAK,EAAAe,eAAA;QAAAC,MAAA,EAAyBR,MAAA,CAAAS,WAAW,KAAKP;MAAI;wBAEhDF,MAAA,CAAAU,YAAY,CAACR,IAAI,iCAAAS,UAAA;oCAIxBf,mBAAA,UAAa,EACbD,mBAAA,CAUM,OAVNiB,UAUM,G,0BATJjB,mBAAA,CAAgB,YAAZ,SAAO,sB,kBACXF,mBAAA,CAOSK,SAAA,QAAAC,WAAA,CANQC,MAAA,CAAAa,cAAc,EAAtBC,IAAI;yBADbrB,mBAAA,CAOS;MALNU,GAAG,EAAEW,IAAI;MACTV,OAAK,EAAAC,MAAA,IAAEL,MAAA,CAAAe,WAAW,GAAGD,IAAI;MACzBtB,KAAK,EAAAe,eAAA;QAAAC,MAAA,EAAyBR,MAAA,CAAAe,WAAW,KAAKD;MAAI;wBAEhDA,IAAI,gCAAAE,UAAA;oCAIXpB,mBAAA,YAAe,EACfD,mBAAA,CAUM,OAVNsB,UAUM,G,0BATJtB,mBAAA,CAAkB,YAAd,WAAS,sB,kBACbF,mBAAA,CAOSK,SAAA,QAAAC,WAAA,CANaC,MAAA,CAAAkB,mBAAmB,EAAhCC,SAAS;yBADlB1B,mBAAA,CAOS;MALNU,GAAG,EAAEgB,SAAS;MACdf,OAAK,EAAAC,MAAA,IAAEL,MAAA,CAAAoB,eAAe,CAACD,SAAS;MAChC3B,KAAK,EAAAe,eAAA;QAAAC,MAAA,EAA8BR,MAAA,CAAAqB,gBAAgB,KAAKF;MAAS;wBAE/DnB,MAAA,CAAAsB,iBAAiB,CAACH,SAAS,iCAAAI,UAAA;oCAIlC3B,mBAAA,eAAkB,EAClBD,mBAAA,CAmBM,OAnBN6B,UAmBM,GAlBJ7B,mBAAA,CAAwD,YAApD,iBAAe,GAAA8B,gBAAA,CAAGzB,MAAA,CAAAU,YAAY,CAACV,MAAA,CAAAS,WAAW,KAAI,GAAC,iBAEnDd,mBAAA,CAeM,OAfN+B,UAeM,GAbI1B,MAAA,CAAAS,WAAW,iB,cADnBkB,YAAA,CASEC,0BAAA;;IAPCd,IAAI,EAAEd,MAAA,CAAAe,WAAW;IACjBc,QAAQ,EAAE;MAAAC,CAAA;MAAAC,CAAA;IAAA,CAAc;IACxBC,KAAK,EAAE,OAAO;IACd,cAAY,EAAEhC,MAAA,CAAAiC,WAAW;IACzBC,aAAY,EAAElC,MAAA,CAAAmC,cAAc;IAC5BC,aAAY,EAAEpC,MAAA,CAAAqC,oBAAoB;IAClCC,kBAAiB,EAAEtC,MAAA,CAAAuC;8HAGtB9C,mBAAA,CAEM,OAFN+C,WAEM,EAF6B,UAEnC,G,KAIJ5C,mBAAA,YAAe,EACfD,mBAAA,CAwBM,OAxBN8C,WAwBM,G,0BAvBJ9C,mBAAA,CAAgB,YAAZ,SAAO,qBACXA,mBAAA,CAqBM,OArBN+C,WAqBM,GApBJ/C,mBAAA,CAGM,OAHNgD,WAGM,G,0BAFJhD,mBAAA,CAAoB,eAAb,OAAK,qBACZA,mBAAA,CAA4C,cAAA8B,gBAAA,CAAnCzB,MAAA,CAAAU,YAAY,CAACV,MAAA,CAAAS,WAAW,kB,GAEnCd,mBAAA,CAGM,OAHNiD,WAGM,G,0BAFJjD,mBAAA,CAAoB,eAAb,OAAK,qBACZA,mBAAA,CAA8B,cAAA8B,gBAAA,CAArBzB,MAAA,CAAAe,WAAW,iB,GAEtBpB,mBAAA,CAGM,OAHNkD,WAGM,G,0BAFJlD,mBAAA,CAAoB,eAAb,OAAK,qBACZA,mBAAA,CAAsD,cAAA8B,gBAAA,CAA7CzB,MAAA,CAAAsB,iBAAiB,CAACtB,MAAA,CAAAqB,gBAAgB,kB,GAE7C1B,mBAAA,CAGM,OAHNmD,WAGM,G,0BAFJnD,mBAAA,CAAoB,eAAb,OAAK,qBACZA,mBAAA,CAAwC,cAAA8B,gBAAA,CAA/BzB,MAAA,CAAA+C,SAAS,6B,GAEpBpD,mBAAA,CAGM,OAHNqD,WAGM,G,0BAFJrD,mBAAA,CAAqB,eAAd,QAAM,qBACbA,mBAAA,CAA4C,cAAA8B,gBAAA,CAAnCzB,MAAA,CAAAiD,aAAa,6B,OAK5BrD,mBAAA,UAAa,EACbD,mBAAA,CAoBM,OApBNuD,WAoBM,G,0BAnBJvD,mBAAA,CAAoB,YAAhB,aAAW,qBACfA,mBAAA,CAiBM,OAjBNwD,WAiBM,I,kBAhBJ1D,mBAAA,CAeMK,SAAA,QAAAC,WAAA,CAfkCC,MAAA,CAAAoD,UAAU,EAAnBC,KAAK;yBAApC5D,mBAAA,CAeM;MAfDD,KAAK,EAAC,YAAY;MAA8BW,GAAG,EAAEkD,KAAK,CAACC;QAC9D3D,mBAAA,CAAyB,YAAA8B,gBAAA,CAAlB4B,KAAK,CAACE,IAAI,kBACjB5D,mBAAA,CAYM,OAZN6D,WAYM,GAXJ7D,mBAAA,CAUM;MAVAH,KAAK,EAAAe,eAAA,4BAA6B8C,KAAK,CAACI,MAAM;QAClD9D,mBAAA,CAQM,OARN+D,WAQM,GAPJC,YAAA,CAME/B,0BAAA;MALCd,IAAI,EAAEuC,KAAK,CAACO,OAAO;MACnB/B,QAAQ,EAAEwB,KAAK,CAACQ,WAAW;QAAA/B,CAAA;QAAAC,CAAA;MAAA;MAC3BC,KAAK,EAAE,OAAO;MACd,cAAY,EAAEhC,MAAA,CAAAiC,WAAW;MACzBC,aAAY,EAAElC,MAAA,CAAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}