<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>情感化提示词生成测试</title>
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .test-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .test-card h3 {
            margin-top: 0;
            color: #4fc3f7;
        }
        .btn {
            background: linear-gradient(45deg, #4fc3f7, #29b6f6);
            border: none;
            border-radius: 25px;
            color: white;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 195, 247, 0.3);
        }
        .result-box {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .context-info {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
            font-size: 12px;
        }
        .loading {
            display: inline-block;
            margin-left: 10px;
            color: #4fc3f7;
        }
        .success {
            color: #4caf50;
        }
        .error {
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎭 情感化壁纸提示词生成测试</h1>
            <p>测试AI-HMI系统的动漫卡通风格情感化提示词生成功能</p>
            <p><strong>风格要求:</strong> 动漫卡通、温馨治愈、无人物、避免写实风格和高楼大厦</p>
        </div>

        <div class="test-section">
            <h2>📋 当前上下文状态</h2>
            <div id="contextInfo" class="context-info">加载中...</div>
            <button class="btn" onclick="updateContext()">🔄 更新上下文</button>
            <button class="btn" onclick="simulateDriving()">🚗 模拟驾驶状态</button>
        </div>

        <div class="test-section">
            <h2>🎨 场景测试</h2>
            <div class="test-grid">
                <div class="test-card">
                    <h3>👨‍👩‍👧 家庭出行</h3>
                    <p>送孩子上学的温馨场景</p>
                    <button class="btn" onclick="testScene('morningCommuteFamily')">
                        生成家庭出行提示词
                    </button>
                    <div id="result-morningCommuteFamily" class="result-box"></div>
                </div>

                <div class="test-card">
                    <h3>💼 专注通勤</h3>
                    <p>独自上班的专注场景</p>
                    <button class="btn" onclick="testScene('morningCommuteFocus')">
                        生成专注通勤提示词
                    </button>
                    <div id="result-morningCommuteFocus" class="result-box"></div>
                </div>

                <div class="test-card">
                    <h3>🌅 下班归途</h3>
                    <p>工作日下班回家的放松场景</p>
                    <button class="btn" onclick="testScene('eveningCommute')">
                        生成下班归途提示词
                    </button>
                    <div id="result-eveningCommute" class="result-box"></div>
                </div>

                <div class="test-card">
                    <h3>🌧️ 雨夜模式</h3>
                    <p>雨夜驾驶的安全宁静场景</p>
                    <button class="btn" onclick="testScene('rainyNight')">
                        生成雨夜模式提示词
                    </button>
                    <div id="result-rainyNight" class="result-box"></div>
                </div>

                <div class="test-card">
                    <h3>🏞️ 周末出游</h3>
                    <p>家庭周末旅行的愉悦场景</p>
                    <button class="btn" onclick="testScene('familyTrip')">
                        生成周末出游提示词
                    </button>
                    <div id="result-familyTrip" class="result-box"></div>
                </div>

                <div class="test-card">
                    <h3>💕 浪漫模式</h3>
                    <p>二人世界的浪漫场景</p>
                    <button class="btn" onclick="testScene('romanticMode')">
                        生成浪漫模式提示词
                    </button>
                    <div id="result-romanticMode" class="result-box"></div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>⚡ 性能测试</h2>
            <button class="btn" onclick="batchTest()">🔄 批量测试所有场景</button>
            <button class="btn" onclick="clearResults()">🗑️ 清空结果</button>
            <div id="performanceResults" class="result-box"></div>
        </div>
    </div>

    <!-- 引入测试脚本 -->
    <script type="module">
        import EmotionalPromptGenerator from '/src/services/EmotionalPromptGenerator.js'
        import SceneContextManager from '/src/services/SceneContextManager.js'
        import SceneManager from '/src/utils/SceneManager.js'

        // 全局变量
        let emotionalPromptGenerator
        let contextManager
        let sceneManager

        // 初始化
        window.onload = function() {
            emotionalPromptGenerator = new EmotionalPromptGenerator()
            contextManager = new SceneContextManager()
            sceneManager = new SceneManager()
            
            updateContext()
            console.log('🎭 情感化提示词生成测试系统已初始化')
        }

        // 更新上下文显示
        window.updateContext = function() {
            const context = contextManager.getContext()
            const stats = contextManager.getStatistics()
            
            const contextHtml = `
                <strong>时间:</strong> ${context.timeOfDay} | 
                <strong>天气:</strong> ${context.weather} | 
                <strong>情绪:</strong> ${context.mood} | 
                <strong>驾驶时长:</strong> ${stats.drivingDuration} | 
                <strong>场景切换次数:</strong> ${stats.sceneSwitchCount} | 
                <strong>用户角色:</strong> ${context.userPreferences?.musicStyle || 'default'}
            `
            
            document.getElementById('contextInfo').innerHTML = contextHtml
        }

        // 模拟驾驶状态
        window.simulateDriving = function() {
            contextManager.updateContext({
                isDriving: true,
                drivingDuration: contextManager.context.drivingDuration + 300000, // 增加5分钟
                fatigueLevel: Math.min(10, contextManager.context.fatigueLevel + 0.5)
            })
            updateContext()
            console.log('🚗 模拟驾驶状态已更新')
        }

        // 测试单个场景
        window.testScene = async function(sceneId) {
            const resultDiv = document.getElementById(`result-${sceneId}`)
            resultDiv.innerHTML = '<span class="loading">🔄 正在生成情感化提示词...</span>'
            
            try {
                const scene = sceneManager.scenes[sceneId]
                const context = contextManager.getPromptGenerationContext()
                
                console.log(`🎨 测试场景: ${sceneId}`, { scene, context })
                
                const startTime = Date.now()
                const prompt = await emotionalPromptGenerator.generateEmotionalPrompt(scene, context)
                const endTime = Date.now()
                
                const resultText = `✅ <span class="success">生成成功</span> (${endTime - startTime}ms)\n\n📝 生成的提示词:\n${prompt}\n\n📋 生成上下文:\n场景: ${scene.name}\n时间: ${context.timeOfDay}\n天气: ${context.weather}\n情绪: ${context.mood}\n用户角色: ${context.userRole}`
                
                resultDiv.innerHTML = resultText
                
                console.log(`🎭 场景 ${sceneId} 测试成功:`, prompt)
                
            } catch (error) {
                const errorText = `❌ <span class="error">生成失败</span>\n\n错误信息: ${error.message}\n\n请检查:\n1. LLM服务是否正常运行\n2. 网络连接是否正常\n3. API配置是否正确`
                
                resultDiv.innerHTML = errorText
                console.error(`🎭 场景 ${sceneId} 测试失败:`, error)
            }
        }

        // 批量测试
        window.batchTest = async function() {
            const performanceDiv = document.getElementById('performanceResults')
            performanceDiv.innerHTML = '<span class="loading">🔄 正在进行批量测试...</span>'
            
            const results = []
            const scenes = ['morningCommuteFamily', 'morningCommuteFocus', 'eveningCommute', 'rainyNight', 'familyTrip', 'romanticMode']
            
            for (const sceneId of scenes) {
                try {
                    const scene = sceneManager.scenes[sceneId]
                    const context = contextManager.getPromptGenerationContext()
                    
                    const startTime = Date.now()
                    const prompt = await emotionalPromptGenerator.generateEmotionalPrompt(scene, context)
                    const endTime = Date.now()
                    
                    results.push({
                        sceneId,
                        sceneName: scene.name,
                        success: true,
                        time: endTime - startTime,
                        prompt: prompt.substring(0, 100) + '...'
                    })
                    
                    console.log(`✅ ${sceneId} 测试完成`)
                    
                } catch (error) {
                    results.push({
                        sceneId,
                        sceneName: sceneManager.scenes[sceneId].name,
                        success: false,
                        error: error.message
                    })
                    
                    console.error(`❌ ${sceneId} 测试失败:`, error)
                }
            }
            
            // 生成性能报告
            const successCount = results.filter(r => r.success).length
            const totalTime = results.filter(r => r.success).reduce((sum, r) => sum + r.time, 0)
            const avgTime = successCount > 0 ? (totalTime / successCount).toFixed(2) : 0
            
            const reportText = `📊 批量测试完成\n\n成功率: ${successCount}/${results.length} (${(successCount/results.length*100).toFixed(1)}%)\n总耗时: ${totalTime}ms\n平均耗时: ${avgTime}ms\n\n详细结果:\n${results.map(r => 
                `${r.success ? '✅' : '❌'} ${r.sceneName}: ${r.success ? r.time + 'ms' : r.error}`
            ).join('\n')}`
            
            performanceDiv.innerHTML = reportText
        }

        // 清空结果
        window.clearResults = function() {
            const resultBoxes = document.querySelectorAll('.result-box')
            resultBoxes.forEach(box => box.innerHTML = '')
            document.getElementById('performanceResults').innerHTML = ''
            console.log('🗑️ 测试结果已清空')
        }
    </script>
</body>
</html>