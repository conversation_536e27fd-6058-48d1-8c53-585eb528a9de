[{"id": "natural", "name": "自然通勤", "description": "模拟真实世界中的通勤场景，界面元素采用柔和的色彩和自然的材质。", "properties": {"theme-color-primary": "#7D8A6E", "theme-color-secondary": "#B2C2A6", "theme-color-background": "#F0F2EB", "theme-color-text": "#3D403A", "font-family": "'Noto Sans', sans-serif", "border-radius": "12px"}, "layout_hints": {"density": "sparse", "flow": "organic", "spacing_schema": "natural"}}, {"id": "cyberpunk", "name": "赛博朋克", "description": "灵感来自赛博朋克美学，以霓虹灯、高对比度和未来感线条为特征。", "properties": {"theme-color-primary": "#EA00D9", "theme-color-secondary": "#00F0FF", "theme-color-background": "#0A021A", "theme-color-text": "#F0F0F0", "font-family": "'Orbitron', sans-serif", "border-radius": "2px", "text-shadow": "0 0 5px #EA00D9"}, "layout_hints": {"density": "compact", "flow": "rigid", "spacing_schema": "geometric"}}, {"id": "glassmorphism", "name": "玻璃拟物", "description": "通过模糊背景和半透明边框，创造出磨砂玻璃般的质感，界面元素悬浮其上。", "properties": {"background-blur": "15px", "border": "1px solid rgba(255, 255, 255, 0.25)", "background-color": "rgba(255, 255, 255, 0.1)", "border-radius": "16px", "box-shadow": "0 8px 32px 0 rgba(31, 38, 135, 0.37)"}, "layout_hints": {"density": "sparse", "flow": "fluid", "spacing_schema": "organic"}}, {"id": "neumorphism", "name": "新拟物", "description": "界面元素从背景中“挤压”出来，通过微妙的内外阴影营造出柔软、立体的效果。", "properties": {"background-color": "#E0E5EC", "box-shadow-light": "9px 9px 16px #A3B1C6", "box-shadow-dark": "-9px -9px 16px #FFFFFF", "color": "#3D403A", "border-radius": "20px"}, "layout_hints": {"density": "normal", "flow": "fluid", "spacing_schema": "uniform"}}, {"id": "kawaii", "name": "可爱卡通", "description": "采用圆润的形状、柔和的粉彩色调和可爱的图标，营造轻松愉快的氛围。", "properties": {"theme-color-primary": "#FFC0CB", "theme-color-secondary": "#B19CD9", "theme-color-background": "#FFF8E1", "theme-color-text": "#5D4037", "font-family": "'Comic Sans MS', cursive, sans-serif", "border-radius": "50px", "border": "2px solid #5D4037"}, "layout_hints": {"density": "normal", "flow": "organic", "spacing_schema": "playful"}}]