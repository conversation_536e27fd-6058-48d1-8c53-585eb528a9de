<template>
  <BaseCard
    :card-type="'ai-schedule'"
    :size="size"
    :position="position"
    :theme="theme"
    :theme-colors="themeColors"
    :show-header="true"
    :show-footer="false"
    :title="cardTitle"
    :icon="'fas fa-calendar-alt'"
    class="ai-schedule-assistant-card"
  >
    <div class="schedule-container" :class="[`size-${size}`, `mode-${displayMode}`]">
      <!-- AI优化建议横幅 -->
      <div v-if="aiOptimization && aiOptimization.suggestion" class="ai-optimization-banner">
        <div class="optimization-icon">
          <i class="fas fa-magic"></i>
        </div>
        <div class="optimization-content">
          <div class="optimization-title">AI智能建议</div>
          <div class="optimization-text">{{ aiOptimization.suggestion }}</div>
          <div class="optimization-meta">
            <span class="time-saved">节省 {{ aiOptimization.timeSaved }}</span>
            <span class="confidence">可信度 {{ Math.round(aiOptimization.confidence * 100) }}%</span>
          </div>
        </div>
        <button class="apply-suggestion-btn" @click="applySuggestion">
          <i class="fas fa-check"></i>
        </button>
      </div>

      <!-- 今日日程列表 -->
      <div class="schedule-list">
        <div class="schedule-header">
          <h3 class="schedule-title">今日日程</h3>
          <div class="schedule-date">{{ currentDate }}</div>
        </div>
        
        <div class="events-container">
          <div 
            v-for="event in todayEvents" 
            :key="event.id"
            :class="['event-item', `priority-${event.priority}`, `status-${event.status}`]"
            @click="handleEventClick(event)"
          >
            <div class="event-time">
              <div class="time-text">{{ event.time }}</div>
              <div class="time-indicator" :class="getTimeStatus(event)"></div>
            </div>
            
            <div class="event-content">
              <div class="event-title">{{ event.title }}</div>
              <div class="event-location" v-if="event.location">
                <i class="fas fa-map-marker-alt"></i>
                <span>{{ event.location }}</span>
              </div>
              
              <!-- AI建议 -->
              <div v-if="event.aiSuggestion" class="event-ai-suggestion">
                <i class="fas fa-lightbulb"></i>
                <span>{{ event.aiSuggestion }}</span>
              </div>
            </div>
            
            <div class="event-actions">
              <button 
                v-if="event.status === 'upcoming'"
                @click.stop="startNavigation(event)"
                class="nav-btn"
                :disabled="!event.location"
              >
                <i class="fas fa-route"></i>
              </button>
              
              <button 
                @click.stop="toggleEventReminder(event)"
                :class="['reminder-btn', { active: event.reminderEnabled }]"
              >
                <i class="fas fa-bell"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 智能时间管理 -->
      <div class="time-management" v-if="showTimeManagement">
        <div class="management-header">
          <i class="fas fa-clock"></i>
          <span>智能时间管理</span>
        </div>
        
        <div class="management-content">
          <div class="travel-time-analysis">
            <div class="analysis-item">
              <span class="label">当前位置到下个会议</span>
              <span class="value">{{ travelTimeToNext }}</span>
            </div>
            <div class="analysis-item">
              <span class="label">建议出发时间</span>
              <span class="value highlight">{{ suggestedDepartureTime }}</span>
            </div>
          </div>
          
          <div class="time-buffer">
            <div class="buffer-info">
              <span>缓冲时间: {{ timeBuffer }}</span>
              <div class="buffer-bar">
                <div class="buffer-fill" :style="{ width: bufferPercentage + '%' }"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="quick-actions" v-if="showQuickActions">
        <button 
          v-for="action in quickActions" 
          :key="action.id"
          @click="handleQuickAction(action)"
          class="quick-action-btn"
          :disabled="isProcessing"
        >
          <i :class="action.icon"></i>
          <span>{{ action.label }}</span>
        </button>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在优化您的日程...</div>
      </div>
    </div>
  </BaseCard>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import BaseCard from '@/components/cards/BaseCard.vue'
import mockDataService from '@/services/MockDataService.js'

export default {
  name: 'AIScheduleAssistantCard',
  components: {
    BaseCard
  },
  props: {
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    position: {
      type: Object,
      default: () => ({ x: 0, y: 0 })
    },
    theme: {
      type: String,
      default: 'glassmorphism'
    },
    themeColors: {
      type: Object,
      default: () => ({})
    },
    displayMode: {
      type: String,
      default: 'full',
      validator: (value) => ['compact', 'standard', 'full'].includes(value)
    },
    showTimeManagement: {
      type: Boolean,
      default: true
    },
    showQuickActions: {
      type: Boolean,
      default: true
    },
    autoRefresh: {
      type: Boolean,
      default: true
    },
    refreshInterval: {
      type: Number,
      default: 60000 // 1分钟
    }
  },
  emits: ['event-click', 'navigation-start', 'suggestion-applied', 'action-triggered'],
  setup(props, { emit }) {
    // 响应式数据
    const isLoading = ref(false)
    const isProcessing = ref(false)
    const todayEvents = ref([])
    const aiOptimization = ref(null)
    const currentDate = ref('')
    const travelTimeToNext = ref('--')
    const suggestedDepartureTime = ref('--')
    const timeBuffer = ref('5分钟')
    const bufferPercentage = ref(75)
    
    // 快速操作
    const quickActions = ref([
      {
        id: 1,
        label: '添加事件',
        icon: 'fas fa-plus',
        action: 'addEvent'
      },
      {
        id: 2,
        label: '查看周视图',
        icon: 'fas fa-calendar-week',
        action: 'weekView'
      },
      {
        id: 3,
        label: 'AI优化',
        icon: 'fas fa-magic',
        action: 'aiOptimize'
      }
    ])

    // 计算属性
    const cardTitle = computed(() => {
      const upcomingCount = todayEvents.value.filter(e => e.status === 'upcoming').length
      return upcomingCount > 0 ? `日程助手 (${upcomingCount}个待办)` : '日程助手'
    })

    // 方法
    const updateCurrentDate = () => {
      const now = new Date()
      currentDate.value = now.toLocaleDateString('zh-CN', {
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      })
    }

    const getTimeStatus = (event) => {
      const now = new Date()
      const eventTime = new Date()
      const [hours, minutes] = event.time.split(':').map(Number)
      eventTime.setHours(hours, minutes, 0, 0)
      
      const timeDiff = eventTime.getTime() - now.getTime()
      const minutesDiff = Math.floor(timeDiff / (1000 * 60))
      
      if (minutesDiff < 0) return 'past'
      if (minutesDiff <= 15) return 'imminent'
      if (minutesDiff <= 60) return 'soon'
      return 'future'
    }

    const loadScheduleData = async () => {
      try {
        isLoading.value = true
        const scheduleData = await mockDataService.getScheduleData()
        
        todayEvents.value = scheduleData.todayEvents || []
        aiOptimization.value = scheduleData.aiOptimization || null
        
        // 更新时间管理信息
        updateTimeManagement()
        
      } catch (error) {
        console.error('Failed to load schedule data:', error)
      } finally {
        isLoading.value = false
      }
    }

    const updateTimeManagement = () => {
      const upcomingEvents = todayEvents.value.filter(e => e.status === 'upcoming')
      if (upcomingEvents.length > 0) {
        const nextEvent = upcomingEvents[0]
        
        // 模拟计算行程时间
        travelTimeToNext.value = '15分钟'
        
        // 计算建议出发时间
        const eventTime = new Date()
        const [hours, minutes] = nextEvent.time.split(':').map(Number)
        eventTime.setHours(hours, minutes, 0, 0)
        
        const departureTime = new Date(eventTime.getTime() - 20 * 60 * 1000) // 提前20分钟
        suggestedDepartureTime.value = departureTime.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      }
    }

    const handleEventClick = (event) => {
      emit('event-click', event)
    }

    const startNavigation = (event) => {
      if (!event.location) return
      
      emit('navigation-start', {
        destination: event.location,
        event: event
      })
    }

    const toggleEventReminder = (event) => {
      event.reminderEnabled = !event.reminderEnabled
      // 这里可以调用API保存提醒设置
    }

    const applySuggestion = async () => {
      if (!aiOptimization.value) return
      
      try {
        isProcessing.value = true
        
        // 模拟应用建议的过程
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        emit('suggestion-applied', aiOptimization.value)
        
        // 重新加载数据
        await loadScheduleData()
        
      } catch (error) {
        console.error('Failed to apply suggestion:', error)
      } finally {
        isProcessing.value = false
      }
    }

    const handleQuickAction = async (action) => {
      emit('action-triggered', action)
      
      switch (action.action) {
        case 'aiOptimize':
          await performAIOptimization()
          break
        case 'addEvent':
          // 触发添加事件对话框
          break
        case 'weekView':
          // 切换到周视图
          break
      }
    }

    const performAIOptimization = async () => {
      try {
        isProcessing.value = true
        
        // 模拟AI优化过程
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        // 生成新的优化建议
        aiOptimization.value = {
          suggestion: '建议将下午会议提前30分钟，避开交通高峰',
          timeSaved: '15分钟',
          confidence: 0.88
        }
        
      } catch (error) {
        console.error('AI optimization failed:', error)
      } finally {
        isProcessing.value = false
      }
    }

    // 生命周期
    let refreshTimer = null
    
    onMounted(async () => {
      await mockDataService.initialize()
      updateCurrentDate()
      await loadScheduleData()
      
      // 设置自动刷新
      if (props.autoRefresh) {
        refreshTimer = setInterval(() => {
          updateCurrentDate()
          updateTimeManagement()
        }, props.refreshInterval)
      }
    })

    onUnmounted(() => {
      if (refreshTimer) {
        clearInterval(refreshTimer)
      }
    })

    return {
      // 响应式数据
      isLoading,
      isProcessing,
      todayEvents,
      aiOptimization,
      currentDate,
      travelTimeToNext,
      suggestedDepartureTime,
      timeBuffer,
      bufferPercentage,
      quickActions,
      
      // 计算属性
      cardTitle,
      
      // 方法
      getTimeStatus,
      handleEventClick,
      startNavigation,
      toggleEventReminder,
      applySuggestion,
      handleQuickAction
    }
  }
}
</script>

<style scoped>
.ai-schedule-assistant-card {
  height: 100%;
}

.schedule-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  position: relative;
}

/* AI优化建议横幅 */
.ai-optimization-banner {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.2) 0%, rgba(126, 211, 33, 0.2) 100%);
  border-radius: 12px;
  border: 1px solid rgba(74, 144, 226, 0.3);
  backdrop-filter: blur(10px);
}

.optimization-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4a90e2 0%, #7ed321 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  flex-shrink: 0;
}

.optimization-content {
  flex: 1;
}

.optimization-title {
  font-size: 12px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 4px;
}

.optimization-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
  margin-bottom: 6px;
}

.optimization-meta {
  display: flex;
  gap: 12px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
}

.time-saved {
  color: #7ed321;
}

.confidence {
  color: #4a90e2;
}

.apply-suggestion-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: rgba(126, 211, 33, 0.3);
  color: #7ed321;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.apply-suggestion-btn:hover {
  background: rgba(126, 211, 33, 0.5);
  transform: scale(1.05);
}

/* 日程列表 */
.schedule-list {
  flex: 1;
  min-height: 0;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.schedule-title {
  font-size: 16px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

.schedule-date {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.events-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.event-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  border-left: 4px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.event-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(2px);
}

.event-item.priority-high {
  border-left-color: #ef4444;
}

.event-item.priority-medium {
  border-left-color: #f59e0b;
}

.event-item.priority-low {
  border-left-color: #10b981;
}

.event-item.status-past {
  opacity: 0.6;
}

.event-time {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  min-width: 60px;
}

.time-text {
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.time-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
}

.time-indicator.imminent {
  background: #ef4444;
  animation: pulse 1.5s infinite;
}

.time-indicator.soon {
  background: #f59e0b;
}

.time-indicator.future {
  background: #10b981;
}

.time-indicator.past {
  background: rgba(255, 255, 255, 0.2);
}

.event-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.event-title {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.event-location {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.event-ai-suggestion {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #7ed321;
  background: rgba(126, 211, 33, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
  margin-top: 4px;
}

.event-actions {
  display: flex;
  gap: 4px;
}

.nav-btn,
.reminder-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.nav-btn:hover,
.reminder-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.9);
}

.reminder-btn.active {
  background: rgba(74, 144, 226, 0.3);
  color: #4a90e2;
}

/* 智能时间管理 */
.time-management {
  padding: 12px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

.management-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 12px;
}

.management-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.travel-time-analysis {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.analysis-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.analysis-item .label {
  color: rgba(255, 255, 255, 0.6);
}

.analysis-item .value {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.analysis-item .value.highlight {
  color: #7ed321;
}

.time-buffer {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.buffer-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.buffer-bar {
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.buffer-fill {
  height: 100%;
  background: linear-gradient(90deg, #7ed321 0%, #4a90e2 100%);
  transition: width 0.3s ease;
}

/* 快速操作 */
.quick-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.quick-action-btn {
  flex: 1;
  min-width: 80px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.quick-action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.quick-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  border-radius: 12px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-top: 3px solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

/* 动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
.size-small .schedule-container {
  padding: 12px;
  gap: 12px;
}

.size-small .event-item {
  padding: 8px;
}

.size-small .optimization-banner {
  padding: 8px;
}

.size-large .schedule-container {
  padding: 20px;
  gap: 20px;
}

.mode-compact .events-container {
  max-height: 200px;
}

.mode-compact .time-management,
.mode-compact .quick-actions {
  display: none;
}
</style>