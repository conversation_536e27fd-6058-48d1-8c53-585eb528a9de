[{"id": "vpa-interaction-panel", "name": "VPA交互面板", "description": "VPA主交互面板，用于显示对话和结果。当此面板激活时，通常不需要VPA头像小部件。", "tags": ["vpa", "assistant", "voice", "dialog"]}, {"id": "vpa-avatar-widget", "name": "VPA头像小部件", "description": "VPA的悬浮头像，用于实时语音反馈。在没有主交互面板时使用。", "tags": ["vpa", "assistant", "voice"]}, {"id": "dynamic-island", "name": "灵动岛", "description": "位于顶部的状态栏，用于显示关键通知和实时活动。", "tags": ["driving", "notification", "status"]}, {"id": "weather-card", "name": "天气卡片", "description": "显示当前或未来的天气信息。", "tags": ["weather", "info"]}, {"id": "music-control-card", "name": "音乐控制卡片", "description": "提供音乐播放、暂停、切歌等媒体控制功能。", "tags": ["music", "media", "control"]}, {"id": "todo-card", "name": "待办事项卡片", "description": "显示用户的待办事项列表或日历事件。", "tags": ["task", "calendar", "info"]}, {"id": "quick-action-card", "name": "快捷操作卡片", "description": "提供一组常用操作的按钮，如“回家”、“呼叫常用联系人”。", "tags": ["action", "button", "control"]}, {"id": "ai-task-engine-panel", "name": "AI任务引擎面板", "description": "显示后台AI任务的规划、执行和思考过程。", "tags": ["ai", "task", "loading", "status"]}, {"id": "navigation-detail-card", "name": "导航详情卡片", "description": "显示详细的地图和转向导航信息。", "tags": ["navigation", "map", "driving"]}, {"id": "news-feed-card", "name": "新闻源卡片", "description": "展示一个滚动的新闻或信息流。", "tags": ["news", "info", "feed"]}]