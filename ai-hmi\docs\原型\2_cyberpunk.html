<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI HMI - Cyberpunk Theme</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @keyframes glitch {
            2%, 64% { transform: translate(2px, 0) skew(0deg); }
            4%, 60% { transform: translate(-2px, 0) skew(0deg); }
            62% { transform: translate(0, 0) skew(5deg); }
        }
        body {
            overflow: hidden;
            font-family: 'Orbitron', sans-serif; /* A more futuristic font */
            background-color: #0a0a0a;
            color: #00f0ff;
        }
        .grid-container {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            grid-template-rows: repeat(4, 1fr);
            gap: 16px;
            width: 100vw;
            height: 100vh;
            padding: 16px;
            box-sizing: border-box;
            background: linear-gradient(45deg, rgba(0,0,0,0.8), rgba(0,0,0,0.95)), url('https://images.unsplash.com/photo-1534723452862-4c874018d66d?q=80&w=2560&auto=format&fit=crop') center/cover no-repeat;
        }
        .card {
            background: rgba(10, 25, 47, 0.5);
            backdrop-filter: blur(5px);
            border-radius: 8px;
            border: 1px solid #00f0ff;
            box-shadow: 0 0 15px rgba(0, 240, 255, 0.3), 0 0 5px rgba(0, 240, 255, 0.5) inset;
            transition: all 0.3s ease;
            animation: glitch 1s linear infinite reverse;
        }
        .card:hover {
            box-shadow: 0 0 25px rgba(255, 70, 200, 0.5), 0 0 8px rgba(255, 70, 200, 0.7) inset;
            border-color: #ff46c8;
        }
        .vpa-container { grid-column: span 2; grid-row: span 4; }
        .island-container { grid-column: 3 / span 4; grid-row: 1; display: flex; align-items: center; justify-content: center; }
        .small-card-container { grid-column: 3 / span 4; grid-row: 2; display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px; }
        .large-card-container { grid-column: 7 / span 2; grid-row: span 4; }
        .temp-interaction-container { position: absolute; bottom: 40px; left: 50%; transform: translateX(-50%); z-index: 50; }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="grid-container">
        <!-- VPA 数字人 -->
        <div class="vpa-container card flex flex-col items-center justify-center p-4">
            <img src="https://images.unsplash.com/photo-1527430253228-e93688616381?q=80&w=2487&auto=format&fit=crop" alt="VPA Avatar" class="w-32 h-32 rounded-full border-2 border-[#ff46c8] shadow-[0_0_20px_#ff46c8] mb-4 object-cover">
            <h2 class="text-xl font-bold text-[#ff46c8]">Z3R0</h2>
            <p class="text-[#00f0ff] text-center text-sm">System online. Awaiting command.</p>
        </div>

        <!-- 灵动岛 -->
        <div class="island-container card p-2">
            <div class="flex items-center space-x-4">
                <svg class="w-8 h-8 text-[#ff46c8] animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M12 6V3m0 18v-3"></path></svg>
                <div>
                    <p class="font-semibold text-[#00f0ff]">Threat Scan</p>
                    <p class="text-sm text-gray-400">Sector 7 clear. No anomalies.</p>
                </div>
            </div>
        </div>

        <!-- 横向小卡片 -->
        <div class="small-card-container">
            <div class="card p-4 flex flex-col justify-between">
                <h3 class="font-bold text-[#ff46c8]">System Status</h3>
                <div class="text-center">
                    <p class="text-3xl font-bold text-green-400">99.8%</p>
                    <p class="text-gray-400">Core Integrity</p>
                </div>
            </div>
            <div class="card p-4 flex flex-col justify-between">
                <h3 class="font-bold text-[#ff46c8]">Next Jump</h3>
                <div class="text-center">
                    <p class="text-lg font-semibold text-[#00f0ff]">Neo-Kyoto</p>
                    <p class="text-gray-400">ETA: 12 mins</p>
                    <p class="text-sm text-yellow-400">High-Risk Zone</p>
                </div>
            </div>
        </div>

        <!-- 纵向大卡片 -->
        <div class="large-card-container card p-4 flex flex-col">
            <h3 class="font-bold text-[#ff46c8] mb-2">Data Stream</h3>
            <div class="flex-grow rounded-lg overflow-hidden bg-black/50 p-2 text-xs text-green-400 font-mono">
                <p>&gt; Connection established to sat-link 7...</p>
                <p>&gt; Encrypting data packets...</p>
                <p class="text-yellow-400">! WARNING: Unidentified signal detected.</p>
                <p>&gt; Rerouting through proxy...</p>
                <p>&gt; Connection secured.</p>
            </div>
        </div>

        <!-- 临时交互组件 -->
        <div class="temp-interaction-container card p-4 flex items-center space-x-4 border-[#ff46c8]">
            <p class="font-semibold text-[#00f0ff]">Incoming Transmission</p>
            <p class="text-gray-400">Unknown ID</p>
            <button class="px-4 py-2 bg-green-500/50 text-white rounded-md border border-green-400 hover:bg-green-500">Accept</button>
            <button class="px-4 py-2 bg-red-500/50 text-white rounded-md border border-red-400 hover:bg-red-500">Reject</button>
        </div>

        <div style="grid-column: 3 / span 4; grid-row: 3 / span 2;"></div>

    </div>
</body>
</html>