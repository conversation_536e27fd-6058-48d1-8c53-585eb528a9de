"""
启动脚本 - 确保正确识别模块路径
需要在项目根目录下运行: python start.py
"""

import os
import sys

# 添加当前目录到Python路径，确保能找到theme_backend包
sys.path.insert(0, os.path.abspath('.'))

# 导入app应用
from app.main import app

if __name__ == "__main__":
    import uvicorn
    print("正在启动theme_backend服务...")
    uvicorn.run(
        "app.main:app", 
        host="0.0.0.0", 
        port=8000, 
        reload=True,
        reload_dirs=["app", "agent"]
    ) 