<template>
  <div id="app">
    <!-- 动态壁纸管理器 - 作为最底层 -->
    <DynamicWallpaperManager
      :scene-prompt="currentWallpaperPrompt"
      :auto-generate="true"
      :enable-config="true"
      @wallpaper-changed="handleWallpaperChanged"
      @colors-extracted="handleColorsExtracted"
    >
      <!-- 测试模式切换 - 移到右下角 -->
      <div class="mode-toggle">
        <button @click="toggleMode" class="toggle-btn">
          {{ isTestMode ? '🚗 切换到正常模式' : '🧪 切换到测试模式' }}
        </button>
      </div>

      <!-- 测试模式 -->
      <TestSceneGeneration
        v-if="isTestMode"
        :theme-colors="themeColors"
        @colors-extracted="handleColorsExtracted"
      />

      <!-- 正常模式 -->
      <div v-else class="normal-mode">
        <SceneManager
          :initial-scene="initialScene"
          :show-indicator="true"
          :auto-switch="autoSwitchEnabled"
          :theme-colors="themeColors"
          @scene-changed="handleSceneChanged"
          @wallpaper-prompt-ready="handleWallpaperPrompt"
        />
      </div>
    </DynamicWallpaperManager>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import SceneManager from './components/SceneManager.vue'
import TestSceneGeneration from './components/TestSceneGeneration.vue'
import DynamicWallpaperManager from './components/DynamicWallpaperManager.vue'

export default {
  name: 'App',
  components: {
    SceneManager,
    TestSceneGeneration,
    DynamicWallpaperManager
  },

  setup() {
    const isTestMode = ref(false) // 默认启动正常模式
    const initialScene = ref('default')
    const autoSwitchEnabled = ref(false) // 禁用自动切换，避免意外的场景切换
    const currentWallpaperPrompt = ref('动漫卡通风格的温馨小屋，柔和的阳光，可爱的卡通元素，温馨治愈的氛围')
    const themeColors = ref(null)

    const toggleMode = () => {
      isTestMode.value = !isTestMode.value
    }

    const handleWallpaperPrompt = (promptData) => {
      console.log('收到壁纸提示词:', promptData)
      
      // 处理不同格式的提示词数据
      if (typeof promptData === 'string') {
        currentWallpaperPrompt.value = promptData
      } else if (promptData && promptData.prompt) {
        // 使用生成的情感化提示词
        currentWallpaperPrompt.value = promptData.prompt
        
        // 记录详细的生成信息
        console.log('🎨 情感化提示词生成详情:', {
          prompt: promptData.prompt,
          scene: promptData.scene?.name,
          context: promptData.context,
          originalPrompt: promptData.originalPrompt
        })
      }
    }

    const handleSceneChanged = (event) => {
      console.log('场景切换:', event)

      // 场景切换时不再直接设置提示词
      // 等待SceneManager生成情感化提示词并通过wallpaper-prompt-ready事件传递
      console.log('等待情感化提示词生成...')
    }

    const handleWallpaperChanged = (wallpaper) => {
      console.log('壁纸已更换:', wallpaper)
    }

    const handleColorsExtracted = (colors) => {
      console.log('颜色已提取:', colors)
      themeColors.value = colors
    }

    // 页面加载时的欢迎语音
    const welcomeUser = async () => {
      console.log('欢迎使用AI-HMI智能场景系统')
      
      // 根据时间设置初始场景
      const now = new Date()
      const hour = now.getHours()
      
      if (hour >= 7 && hour <= 9) {
        initialScene.value = 'morningCommuteFamily'
      } else if (hour >= 17 && hour <= 19) {
        initialScene.value = 'eveningCommute'
      } else if (hour >= 20 || hour <= 6) {
        initialScene.value = 'rainyNight'
      }
    }

    onMounted(() => {
      welcomeUser()
    })

    return {
      isTestMode,
      toggleMode,
      initialScene,
      autoSwitchEnabled,
      currentWallpaperPrompt,
      themeColors,
      handleWallpaperPrompt,
      handleSceneChanged,
      handleWallpaperChanged,
      handleColorsExtracted
    }
  }
}
</script>

<style>
#app {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  font-family: 'Noto Sans', sans-serif;
  overflow: hidden;
  /* 移除默认背景，由DynamicWallpaperManager管理 */
}

.mode-toggle {
  position: fixed;
  bottom: 20px;
  left: 20px;
  z-index: 9999;
}

.toggle-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  font-weight: 600;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.toggle-btn:hover {
  background: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.normal-mode {
  width: 100%;
  height: 100vh;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* 全局字体引入 */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

/* 图标库引入 */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');
</style>
