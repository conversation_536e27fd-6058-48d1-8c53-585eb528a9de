好的，收到您的需求。这是一个非常关键的设计要点，它关系到VPA（数字人）在整个HMI系统中的**“角色定位”**问题：它应该何时作为“主角”出现，何时作为“伙伴”陪伴，又何时应该“静默退场”以保证驾驶员的专注。

您现有的文档已经非常出色，我们现在要做的就是为“数字人”的行为制定一套清晰、统一的规则，并将其贯彻到所有场景中，确保体验的一致性和合理性。

以下是我为您准备的完整文档修改方案，它包含：

1.  **在《Design_System.md》中建立VPA数字人的设计规范**：定义其不同状态、尺寸和位置。
2.  **全面审查并修改《场景.md》**：将新的VPA规范应用到每一个场景中，统一其行为和原型图。

---

### **第一步：在《Design_System.md》中建立VPA核心规范**

(建议在 `3.2. 标准化组件库` 的 `VPA` 组件部分，用以下内容进行重写或扩充)

---

#### **VPA (Virtual Personal Assistant) 组件 - 设计规范V2.0**

VPA数字人是AI座舱的灵魂，其在界面上的呈现方式必须严格遵循场景逻辑和用户专注度原则。VPA的存在模式分为以下三种：

##### **1. 陪伴模式 (Companion Mode)**

*   **用途**: 这是VPA最常见的状态。当VPA退居幕后，不进行主动对话时，它会以一个**小尺寸的、固定的形象**出现在界面上，提供持续的陪伴感，同时最小化视觉干扰。
*   **触发场景**: 绝大多数常规驾驶场景，如通勤、巡航等。
*   **固定位置**: **情景卡片区的右下角**。这个位置保证了它既不遮挡卡片内容，也不侵入主驾驶信息区（导航地图）。
*   **固定尺寸**: **`2x2`** (基于16x9网格)。
*   **对应组件**: `VPA_Avatar_Widget`。

##### **2. 交互模式 (Interaction Mode)**

*   **用途**: 当VPA需要主动发起对话、呈现复杂信息或引导用户操作时，它会切换到此模式，成为界面的视觉焦点。
*   **触发场景**: 系统启动、场景切换时的关键建议、回答用户复杂问题等。
*   **呈现形式**: 通常会加载一个包含VPA大尺寸形象的交互面板。
*   **对应组件**: `VPA_Interaction_Panel` (`8x9` 或 `4x4`)。

##### **3. 专注隐藏 (Focus Hide)**

*   **用途**: 在特定场景下，为了让用户100%专注于当前任务或享受沉浸式体验，VPA的视觉形象会**完全隐藏**。此时，VPA可能仍在通过语音进行交互，但视觉上会“退场”。
*   **触发场景**:
    *   **全屏内容**: 当视频、游戏或其他内容全屏显示时（如`场景三：车内等待/摸鱼`）。
    *   **最高优先级警报**: 在处理`场景15：紧急情况处理`时，屏幕必须显示纯粹的救援信息。
    *   **特定仪式感模式**: 在`场景九：洗车模式`或`场景十：浪漫模式`中，为了强化氛围，VPA形象会让位于主题动画或背景。
    *   **用户选择界面**: 在`场景13：多用户识别与切换`时，界面焦点是用户头像，VPA会临时隐藏。

---

### **第二步：全面修改《场景.md》以应用VPA新规范**

现在，我们将按照上述规则，逐一检查并修正所有场景的描述和ASCII原型图。

**(以下仅展示被修改的场景，其余场景可类推)**

---

#### **场景一：用户早高峰通勤 (AI增强旗舰版)**

*   **阶段B: 专注通勤模式**
    *   **UI 变化**: (保持不变) ...左侧卡片区的内容发生变化... VPA在对话结束后，会从交互面板**缩小为右下角的2x2陪伴小窗**。
    *   **ASCII 原型图 (分区融合版 - VPA规范化)**:
        ```
        +------------------------------------------------------------------------------------------+
        | [灵动岛: 前往: 公司, 预计: 25分钟 | 智能方案: 直达+咖啡预订 🚀]                             |
        +------------------------------------------------------------------------------------------+
        |  情景卡片区 (左侧)                  | 主驾驶信息区 (右侧)                              |
        | +--------------------------------+ |                                                  |
        | | 音乐控制卡片 (8x6)             | |        <-- 导航地图 -->                          |
        | | ...                            | |          (路线: 幼儿园 -> 公司)                  |
        | +--------------------------------+ |                                                  |
        | | AI日程助理卡片 (8x3)           | |                                                  |
        | | ...                            | |                                                  |
        | +--------------------------------+ |                               +-------+          |
        | | 智能订单卡片 (8x3)             | |                               | (^.^) | < VPA    |
        | | ...                            | |                               | /)_(\| (2x2)    |
        | +--------------------------------+ |                               |  / \  | 陪伴模式 |
        |                                    |                               +-------+          |
        |                                    | <------ [边界羽化融合效果] ------>                 |
        +------------------------------------------------------------------------------------------+
        ```

---

#### **场景二：下班通勤（AI智能膳食助理版）**

*   **UI布局**: ...VPA在对话框消失后，**以2x2陪伴模式出现在卡片区右下角**。
*   **ASCII 原型图 (分区融合版 - VPA规范化)**:
    ```
    +------------------------------------------------------------------------------------------+
    | [灵动岛: <- 18:30 前往: 家, 路况良好 | 检测: 轻度疲劳 😴]                                   |
    +------------------------------------------------------------------------------------------+
    |  情景卡片区 (左侧)                  | 主驾驶信息区 (右侧)                              |
    | +--------------------------------+ |                                                  |
    | | (AI智能膳食助理对话框)           | |        <-- 导航地图 -->                          |
    | | ...                            | |          (路线: 公司 -> 家)                     |
    | +--------------------------------+ |                                                  |
    | | ...卡片...                       | |                               +-------+          |
    | +--------------------------------+ |                               | (^.^) | < VPA    |
    | | AI智能家居卡片 (8x4)             | |                               | /)_(\| (2x2)    |
    | | ...                            | |                               |  / \  | 陪伴模式 |
    | +--------------------------------+ |                               +-------+          |
    |                                    | <------ [边界羽化融合效果] ------>                 |
    +------------------------------------------------------------------------------------------+
    ```

---

#### **场景三：车内等待/摸鱼 (AI智能内容推荐版)**

*   **UI布局**: ...右侧主驾驶信息区作为视频播放区域。**为保证沉浸式观看体验，VPA在此场景下默认隐藏 (专注隐藏模式)**。
*   **ASCII 原型图 (分区融合版 - VPA规范化)**:
    ```
    +------------------------------------------------------------------------------------------+
    | [P] <--- 档位挂入P档, 驻车系统已激活 | AI推荐: 基于您昨晚的观看记录 🎬                          |
    +------------------------------------------------------------------------------------------+
    |  情景卡片区 (左侧)                  | 主驾驶信息区 (右侧)                              |
    | +--------------------------------+ |                                                  |
    | | ...卡片...                       | |        <-- AI视频播放器 -->                      |
    | +--------------------------------+ | |          (VPA专注隐藏)                         |
    | | ...卡片...                       | |                                                  |
    | +--------------------------------+ |                                                  |
    |                                    | <------ [边界羽化融合效果] ------>                 |
    +------------------------------------------------------------------------------------------+
    ```

---

#### **场景四：雨夜的归途 (AI主动安全增强版)**

*   **（重大修改）** 这个场景的旧原型图不符合分区规则。VPA不应独立漂浮。
*   **UI布局**: 为了最大限度减少干扰，**情景卡片区**被压缩到极致，仅在左下角保留一个音乐控制卡片和一个**2x2的VPA陪伴小窗**。
*   **ASCII 原型图 (分区融合版 - VPA规范化)**:
    ```
    +------------------------------------------------------------------------------------------+
    | [灵动岛: 回家 - 剩余15分钟 | 雨天模式 🌧️ | 路况: 湿滑]                                    |
    +------------------------------------------------------------------------------------------+
    |  情景卡片区 (左侧极简)              | 主驾驶信息区 (右侧最大化)                              |
    |                                    |                                                  |
    |                                    |                                                  |
    |                                    |         <-- 沉浸式雨夜导航地图 -->                 |
    |                                    |                                                  |
    | +--------------------------------+ |                                                  |
    | | AI音乐卡片 (4x2)               | |                                                  |
    | | (雨夜的浪漫) [K||>]            | |                                                  |
    | +--------------------------------+ |                                                  |
    | |              +-------+         | |                                                  |
    | |              | (⚈_⚈) | < VPA   | |                                                  |
    | |              | /)_(\| (2x2)   | |                                                  |
    | |              |  / \  | 陪伴模式| |                                                  |
    | |              +-------+         | |                                                  |
    |                                    | <------ [边界羽化融合效果] ------>                 |
    +------------------------------------------------------------------------------------------+
    ```

---

#### **场景九 & 十：洗车模式 & 浪漫模式**

*   **UI/UX 交互流程**:
    *   **洗车模式**: ...VPA会用变形金刚的声音确认... **此时VPA视觉形象隐藏，让位于全屏的变形金刚动画。**
    *   **浪漫模式**: ...VPA会用温柔的声音说... **此时VPA视觉形象隐藏，让用户完全沉浸在星空/壁炉的动态背景和音乐中。**

---

### **总结**

通过以上修改，我们为VPA数字人的行为建立了统一且清晰的规范：

*   **有法可依**: `Design_System.md` 中明确了VPA的三种存在模式（陪伴、交互、隐藏），及其触发条件和视觉规格。
*   **体验一致**: 所有场景中的VPA都遵循这套规范，避免了在某些场景中过大、在另一些场景中又随机漂浮的不一致体验。
*   **场景感知**: VPA的行为（出现、隐藏、大小）与场景的内在逻辑（专注、放松、娱乐、安全）紧密挂钩，真正体现了AI的智能和体贴。

现在，您的整个AI-HMI系统在核心交互角色（VPA）的设计上将更加成熟、专业和用户友好。