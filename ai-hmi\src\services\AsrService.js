class AsrService {
  constructor() {
    // 使用系统自带的ASR服务
    this.recognition = null
    this.isSupported = 'webkitSpeechRecognition' in window
  }

  initRecognition() {
    if (!this.isSupported) {
      console.warn('浏览器不支持语音识别')
      return null
    }

    // eslint-disable-next-line no-undef
    this.recognition = new webkitSpeechRecognition()
    this.recognition.continuous = false
    this.recognition.interimResults = true
    this.recognition.lang = 'zh-CN'

    return this.recognition
  }

  async startRecognition() {
    return new Promise((resolve, reject) => {
      if (!this.isSupported) {
        reject(new Error('浏览器不支持语音识别'))
        return
      }

      const recognition = this.initRecognition()
      
      recognition.onstart = () => {
        console.log('语音识别开始')
      }

      recognition.onresult = (event) => {
        const last = event.results.length - 1
        const transcript = event.results[last][0].transcript
        
        if (event.results[last].isFinal) {
          resolve(transcript.trim())
        }
      }

      recognition.onerror = (event) => {
        console.error('语音识别错误:', event.error)
        reject(new Error(`语音识别失败: ${event.error}`))
      }

      recognition.onend = () => {
        if (recognition.finalResult === undefined) {
          reject(new Error('语音识别超时'))
        }
      }

      recognition.start()
    })
  }

  stopRecognition() {
    if (this.recognition) {
      this.recognition.stop()
    }
  }
}

export default AsrService