# AI HMI 布局沙盒系统 - 设计文档

## 1. 核心理念

本沙盒系统的核心目标是，将抽象的“布局”和“组件”概念，从静态的JSON配置文件，转变为用户（设计师、开发者）可以在Web界面上**可视化、可交互、所见即所得**的实体。

我们不再手动编写复杂的JSON来“描述”布局，而是通过一个沙盒工具来**“构建”**布局。这个工具的产出物，将直接服务于AI，成为AI进行界面布局生成的“高阶知识”。

这套系统将彻底改变我们补充和扩展布局能力的方式，使其更直观、更高效、更富有创造力。

## 2. 系统目标

1.  **可视化管理**：提供一个图形化界面，用于创建、查看、修改和组织所有的“布局容器”和“原子组件”。
2.  **动态知识库**：沙盒的后台直接与 `Component_KB.json` 以及一个**新的`Layout_KB.json`** 文件关联。在沙盒中的所有操作，最终都会被序列化，并更新到这两个知识库文件中。
3.  **实时预览**：在沙盒中进行的任何布局调整或组件修改，都能在预览区域实时看到效果。
4.  **AI集成**：
    *   沙盒能帮助用户**生成结构化的AI提示词**（特别是布局描述的JSON部分）。
    *   AI生成的结果（布局JSON）可以直接**“反向加载”**到沙盒中进行预览和微调。
5.  **易于扩展**：当需要新的组件或布局模式时，可以直接在沙盒中创建原型，保存后即可被AI使用。

## 3. 核心功能模块

### 3.1. 可视化布局编辑器 (Canvas)

这是沙盒的核心。一个拖放式的画布，用户可以在这里：
*   从“组件库面板”拖入“布局容器”或“原子组件”。
*   在容器中嵌套其他容器或组件。
*   通过拖拽调整组件顺序和层级。
*   选择一个元素（容器或组件），在“属性检查器”中编辑其详细信息。

### 3.2. 知识库面板 (Library Panel)

界面左侧的面板，分为两个标签页：
*   **布局 (Layouts):** 读取并展示 `Layout_KB.json` 中所有可用的布局容器（如 `双栏布局`, `网格布局` 等）。
*   **组件 (Components):** 读取并展示 `Component_KB.json` 中所有可用的原子组件（如 `地图`, `按钮`, `音乐播放器` 等）。

用户可以直接从这里拖拽元素到画布上。

### 3.3. 属性检查器 (Property Inspector)

界面右侧的面板。当用户在画布中选中一个元素时，这里会显示该元素的所有可配置属性。
*   **对于容器:**
    *   `id`: 唯一标识符。
    *   `name`: 显示名称。
    *   `description`: 功能描述。
    *   `styles`: 布局相关的CSS属性（如 `display`, `flex-direction`, `gap`）。提供下拉框和输入框进行编辑。
    *   `slots`: 定义该容器可容纳内容的“插槽”数量和ID。
*   **对于组件:**
    *   `id`, `name`, `description`。
    *   `default_styles`: 默认的样式。
    *   `props`: 该组件可接受的自定义参数（如一个`chart`组件的`data_source`属性）。

### 3.4. 知识库同步模块 (KB Sync)

这是后台的核心逻辑。
*   **保存 (Save):** 当用户在沙盒中完成布局或组件的创建/修改后，点击“保存”。系统会将当前画布上的元素结构进行序列化，并更新到对应的 `Layout_KB.json` 或 `Component_KB.json` 文件中。
*   **加载 (Load):** 应用启动时，会自动加载两个JSON知识库，并在“知识库面板”中渲染出来。

### 3.5. AI 提示词生成器 (Prompt Helper)

一个辅助工具，可以是弹窗或是一个专门的视图。
1.  用户在沙盒里搭建好一个理想的布局。
2.  点击“生成AI提示词”。
3.  系统自动分析画布上的布局结构，并生成我们在上一条回复中设计的、结构化的JSON布局描述。
4.  用户可以复制这个JSON，并将其粘贴到完整的提示词中，交给AI去执行。

## 4. 新增知识库：`Layout_KB.json`

为了更好地分离职责，我们创建一个新的知识库文件。

*   `Component_KB.json`: 只存放**原子组件**的定义。这些是构成界面的最小单元，自身不包含复杂的布局逻辑。
*   `Layout_KB.json`: 存放所有**布局容器**的定义。这些是组件的“骨架”，定义了组件如何排列和组织。

**`Layout_KB.json` 示例:**
```json
[
  {
    "id": "two-column-container",
    "name": "双栏布局容器",
    "type": "container",
    "description": "一个水平排列的双栏布局容器，左边窄，右边宽。",
    "styles": {
      "display": "grid",
      "grid-template-columns": "1fr 2fr",
      "gap": "20px",
      "padding": "16px"
    },
    "slots": [
      { "id": "left-panel", "description": "左侧栏" },
      { "id": "main-content", "description": "主内容区" }
    ]
  },
  {
    "id": "vertical-stack-container",
    "name": "垂直堆叠容器",
    "type": "container",
    "description": "将子元素从上到下垂直排列。",
    "styles": {
      "display": "flex",
      "flex-direction": "column",
      "gap": "12px"
    },
    "slots": []
  }
]
```

## 5. 工作流程示例

1.  **启动沙盒:** 开发者或设计师在浏览器中打开沙盒页面。
2.  **创建新布局:**
    *   从“知识库面板”拖入一个 `two-column-container` 到画布。
    *   再拖入一个 `vertical-stack-container` 到右侧的 `main-content` 插槽中。
3.  **填充组件:**
    *   拖一个 `map-component` 到左侧的 `left-panel` 插槽。
    *   拖一个 `music-player` 和两个 `widget` 到右侧的 `vertical-stack-container` 中。
4.  **调整属性:**
    *   选中 `two-column-container`，在“属性检查器”中将其 `gap` 从 `20px` 改为 `30px`。
5.  **保存到知识库:**
    *   点击“保存”，为这个新的组合布局命名，例如 `main-dashboard-layout`。
    *   这个新布局被保存到 `Layout_KB.json` 中。
6.  **生成AI指令:**
    *   点击“生成AI提示词”，获得描述该布局的JSON。
7.  **与AI交互:** 将生成的JSON用于与AI的交互中，让AI基于这个精确的布局结构来生成更多样化的内容和风格。

## 6. 技术选型建议

*   **前端框架:** Vue 3 (与您现有项目 `aiHmi` 技术栈保持一致)。
*   **UI组件库:** Element Plus 或 Ant Design Vue，用于快速搭建沙盒自身的UI界面。
*   **拖放功能:** `VueDraggable` 或 `dnd-kit` 等成熟的拖放库。
*   **状态管理:** Pinia，用于管理画布状态和知识库数据。
*   **后端服务:** 一个轻量的Node.js (Express) 或 Python (FastAPI) 服务器，提供API接口用于读取和写入`..._KB.json`文件。**这是必需的，因为前端JS无法直接修改服务器上的文件系统。**

## 7. 下一步行动

1.  评审并确认这份设计文档。
2.  在 `d:\code\pythonWork\theme\aiHmi\docs` 目录下，手动创建空的 `Layout_KB.json` 文件。
3.  搭建沙盒应用的Web前端项目基本结构。
4.  实现后端API，提供对 `Component_KB.json` 和 `Layout_KB.json` 的读/写能力。
5.  开始开发前端的核心模块，从“知识库面板”和“画布”开始。