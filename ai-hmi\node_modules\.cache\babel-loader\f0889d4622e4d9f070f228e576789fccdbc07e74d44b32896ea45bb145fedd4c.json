{"ast": null, "code": "import { ref, computed, onMounted, onUnmounted } from 'vue';\nimport BaseCard from '@/components/cards/BaseCard.vue';\nimport mockDataService from '@/services/MockDataService.js';\nexport default {\n  name: 'NavigationCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    size: {\n      type: String,\n      default: 'large',\n      validator: value => ['small', 'medium', 'large'].includes(value)\n    },\n    position: {\n      type: Object,\n      default: () => ({\n        x: 0,\n        y: 0\n      })\n    },\n    theme: {\n      type: String,\n      default: 'glassmorphism'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({})\n    },\n    displayMode: {\n      type: String,\n      default: 'full',\n      validator: value => ['compact', 'standard', 'full'].includes(value)\n    },\n    showRoutePreview: {\n      type: Boolean,\n      default: true\n    },\n    showTrafficInfo: {\n      type: Boolean,\n      default: true\n    },\n    showQuickActions: {\n      type: Boolean,\n      default: true\n    },\n    autoRefresh: {\n      type: Boolean,\n      default: true\n    },\n    refreshInterval: {\n      type: Number,\n      default: 30000 // 30秒\n    }\n  },\n  emits: ['navigation-start', 'navigation-end', 'route-changed', 'suggestion-applied'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式数据\n    const isLoading = ref(false);\n    const isProcessing = ref(false);\n    const loadingText = ref('正在规划路线...');\n    const routeViewMode = ref('map');\n    const currentStepIndex = ref(0);\n    const destinationInput = ref('');\n    const currentNavigation = ref(null);\n    const trafficAlerts = ref([]);\n    const alternativeRoutes = ref([]);\n    const aiSuggestions = ref([]);\n    const recentDestinations = ref([]);\n    const quickActions = ref([{\n      id: 1,\n      label: '重新规划',\n      icon: 'fas fa-redo',\n      type: 'primary',\n      action: 'replan'\n    }, {\n      id: 2,\n      label: '语音导航',\n      icon: 'fas fa-volume-up',\n      type: 'secondary',\n      action: 'voice'\n    }, {\n      id: 3,\n      label: '分享位置',\n      icon: 'fas fa-share',\n      type: 'secondary',\n      action: 'share'\n    }, {\n      id: 4,\n      label: '结束导航',\n      icon: 'fas fa-stop',\n      type: 'danger',\n      action: 'stop'\n    }]);\n\n    // 计算属性\n    const cardTitle = computed(() => {\n      if (currentNavigation.value) {\n        return `导航中 - ${currentNavigation.value.destination.name}`;\n      }\n      return '导航';\n    });\n\n    // 方法\n    const loadNavigationData = async () => {\n      try {\n        isLoading.value = true;\n        loadingText.value = '正在获取导航信息...';\n        const navigationData = await mockDataService.getNavigationData();\n        currentNavigation.value = navigationData.currentNavigation || null;\n        trafficAlerts.value = navigationData.trafficAlerts || [];\n        alternativeRoutes.value = navigationData.alternativeRoutes || [];\n        aiSuggestions.value = navigationData.aiSuggestions || [];\n        recentDestinations.value = navigationData.recentDestinations || [];\n      } catch (error) {\n        console.error('Failed to load navigation data:', error);\n      } finally {\n        isLoading.value = false;\n      }\n    };\n    const getModeIcon = mode => {\n      const icons = {\n        driving: 'fas fa-car',\n        walking: 'fas fa-walking',\n        transit: 'fas fa-bus',\n        cycling: 'fas fa-bicycle'\n      };\n      return icons[mode] || 'fas fa-route';\n    };\n    const getModeText = mode => {\n      const texts = {\n        driving: '驾车',\n        walking: '步行',\n        transit: '公交',\n        cycling: '骑行'\n      };\n      return texts[mode] || '导航';\n    };\n    const getDirectionIcon = action => {\n      const icons = {\n        straight: 'fas fa-arrow-up',\n        left: 'fas fa-arrow-left',\n        right: 'fas fa-arrow-right',\n        uturn: 'fas fa-undo',\n        merge: 'fas fa-code-branch',\n        exit: 'fas fa-sign-out-alt'\n      };\n      return icons[action] || 'fas fa-arrow-up';\n    };\n    const getAlertIcon = type => {\n      const icons = {\n        accident: 'fas fa-car-crash',\n        construction: 'fas fa-hard-hat',\n        traffic: 'fas fa-traffic-light',\n        weather: 'fas fa-cloud-rain',\n        closure: 'fas fa-ban'\n      };\n      return icons[type] || 'fas fa-exclamation-triangle';\n    };\n    const getTrafficText = level => {\n      const texts = {\n        light: '畅通',\n        moderate: '缓慢',\n        heavy: '拥堵',\n        severe: '严重拥堵'\n      };\n      return texts[level] || '未知';\n    };\n    const toggleRouteView = () => {\n      routeViewMode.value = routeViewMode.value === 'map' ? 'list' : 'map';\n    };\n    const startNavigation = async () => {\n      if (!destinationInput.value.trim()) return;\n      try {\n        isLoading.value = true;\n        loadingText.value = '正在规划路线...';\n        const destination = {\n          name: destinationInput.value.trim(),\n          address: destinationInput.value.trim()\n        };\n        const navigationResult = await mockDataService.startNavigation(destination);\n        currentNavigation.value = navigationResult;\n        destinationInput.value = '';\n        emit('navigation-start', navigationResult);\n      } catch (error) {\n        console.error('Failed to start navigation:', error);\n      } finally {\n        isLoading.value = false;\n      }\n    };\n    const selectDestination = async destination => {\n      try {\n        isLoading.value = true;\n        loadingText.value = '正在规划路线...';\n        const navigationResult = await mockDataService.startNavigation(destination);\n        currentNavigation.value = navigationResult;\n        emit('navigation-start', navigationResult);\n      } catch (error) {\n        console.error('Failed to start navigation:', error);\n      } finally {\n        isLoading.value = false;\n      }\n    };\n    const selectAlternativeRoute = async route => {\n      try {\n        isProcessing.value = true;\n        const updatedNavigation = await mockDataService.selectRoute(route.id);\n        currentNavigation.value = updatedNavigation;\n        emit('route-changed', route);\n\n        // 重新加载数据以获取新的备选路线\n        await loadNavigationData();\n      } catch (error) {\n        console.error('Failed to select alternative route:', error);\n      } finally {\n        isProcessing.value = false;\n      }\n    };\n    const handleQuickAction = async action => {\n      switch (action.action) {\n        case 'replan':\n          await replanRoute();\n          break;\n        case 'voice':\n          toggleVoiceNavigation();\n          break;\n        case 'share':\n          shareLocation();\n          break;\n        case 'stop':\n          await stopNavigation();\n          break;\n      }\n    };\n    const replanRoute = async () => {\n      if (!currentNavigation.value) return;\n      try {\n        isProcessing.value = true;\n        loadingText.value = '正在重新规划路线...';\n        const updatedNavigation = await mockDataService.replanRoute();\n        currentNavigation.value = updatedNavigation;\n        await loadNavigationData();\n      } catch (error) {\n        console.error('Failed to replan route:', error);\n      } finally {\n        isProcessing.value = false;\n      }\n    };\n    const toggleVoiceNavigation = () => {\n      if (currentNavigation.value) {\n        currentNavigation.value.voiceEnabled = !currentNavigation.value.voiceEnabled;\n      }\n    };\n    const shareLocation = () => {\n      // 模拟分享位置功能\n      console.log('Sharing current location...');\n    };\n    const stopNavigation = async () => {\n      try {\n        isProcessing.value = true;\n        await mockDataService.stopNavigation();\n        const stoppedNavigation = currentNavigation.value;\n        currentNavigation.value = null;\n        currentStepIndex.value = 0;\n        emit('navigation-end', stoppedNavigation);\n      } catch (error) {\n        console.error('Failed to stop navigation:', error);\n      } finally {\n        isProcessing.value = false;\n      }\n    };\n    const applySuggestion = async suggestion => {\n      try {\n        isProcessing.value = true;\n        await mockDataService.applySuggestion(suggestion.id);\n\n        // 从建议列表中移除\n        const index = aiSuggestions.value.findIndex(s => s.id === suggestion.id);\n        if (index > -1) {\n          aiSuggestions.value.splice(index, 1);\n        }\n        emit('suggestion-applied', suggestion);\n\n        // 重新加载导航数据\n        await loadNavigationData();\n      } catch (error) {\n        console.error('Failed to apply suggestion:', error);\n      } finally {\n        isProcessing.value = false;\n      }\n    };\n    const updateNavigationProgress = () => {\n      if (currentNavigation.value && currentNavigation.value.directions) {\n        // 模拟导航进度更新\n        const totalSteps = currentNavigation.value.directions.length;\n        if (currentStepIndex.value < totalSteps - 1) {\n          // 随机推进到下一步（实际应用中基于GPS位置）\n          if (Math.random() < 0.1) {\n            // 10%概率推进\n            currentStepIndex.value++;\n          }\n        }\n      }\n    };\n\n    // 生命周期\n    let refreshTimer = null;\n    let progressTimer = null;\n    onMounted(async () => {\n      await mockDataService.initialize();\n      await loadNavigationData();\n\n      // 设置自动刷新\n      if (props.autoRefresh) {\n        refreshTimer = setInterval(() => {\n          loadNavigationData();\n        }, props.refreshInterval);\n\n        // 设置导航进度更新\n        progressTimer = setInterval(() => {\n          updateNavigationProgress();\n        }, 5000); // 每5秒检查一次进度\n      }\n    });\n    onUnmounted(() => {\n      if (refreshTimer) {\n        clearInterval(refreshTimer);\n      }\n      if (progressTimer) {\n        clearInterval(progressTimer);\n      }\n    });\n    return {\n      // 响应式数据\n      isLoading,\n      isProcessing,\n      loadingText,\n      routeViewMode,\n      currentStepIndex,\n      destinationInput,\n      currentNavigation,\n      trafficAlerts,\n      alternativeRoutes,\n      aiSuggestions,\n      recentDestinations,\n      quickActions,\n      // 计算属性\n      cardTitle,\n      // 方法\n      getModeIcon,\n      getModeText,\n      getDirectionIcon,\n      getAlertIcon,\n      getTrafficText,\n      toggleRouteView,\n      startNavigation,\n      selectDestination,\n      selectAlternativeRoute,\n      handleQuickAction,\n      applySuggestion\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "onUnmounted", "BaseCard", "mockDataService", "name", "components", "props", "size", "type", "String", "default", "validator", "value", "includes", "position", "Object", "x", "y", "theme", "themeColors", "displayMode", "showRoutePreview", "Boolean", "showTrafficInfo", "showQuickActions", "autoRefresh", "refreshInterval", "Number", "emits", "setup", "emit", "isLoading", "isProcessing", "loadingText", "routeViewMode", "currentStepIndex", "destinationInput", "currentNavigation", "trafficAlerts", "alternativeRoutes", "aiSuggestions", "recentDestinations", "quickActions", "id", "label", "icon", "action", "cardTitle", "destination", "loadNavigationData", "navigationData", "getNavigationData", "error", "console", "getModeIcon", "mode", "icons", "driving", "walking", "transit", "cycling", "getModeText", "texts", "getDirectionIcon", "straight", "left", "right", "uturn", "merge", "exit", "getAlertIcon", "accident", "construction", "traffic", "weather", "closure", "getTrafficText", "level", "light", "moderate", "heavy", "severe", "toggleRouteView", "startNavigation", "trim", "address", "navigationResult", "selectDestination", "selectAlternativeRoute", "route", "updatedNavigation", "selectRoute", "handleQuickAction", "replanRoute", "toggleVoiceNavigation", "shareLocation", "stopNavigation", "voiceEnabled", "log", "stoppedNavigation", "applySuggestion", "suggestion", "index", "findIndex", "s", "splice", "updateNavigationProgress", "directions", "totalSteps", "length", "Math", "random", "refreshTimer", "progressTimer", "initialize", "setInterval", "clearInterval"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\NavigationCard.vue"], "sourcesContent": ["<template>\n  <BaseCard\n    :card-type=\"'navigation'\"\n    :size=\"size\"\n    :position=\"position\"\n    :theme=\"theme\"\n    :theme-colors=\"themeColors\"\n    :show-header=\"true\"\n    :show-footer=\"false\"\n    :title=\"cardTitle\"\n    :icon=\"'fas fa-route'\"\n    class=\"navigation-card\"\n  >\n    <div class=\"navigation-container\" :class=\"[`size-${size}`, `mode-${displayMode}`]\">\n      <!-- 当前导航状态 -->\n      <div class=\"navigation-status\" v-if=\"currentNavigation\">\n        <div class=\"status-header\">\n          <div class=\"destination-info\">\n            <h3 class=\"destination-name\">{{ currentNavigation.destination.name }}</h3>\n            <div class=\"destination-address\">{{ currentNavigation.destination.address }}</div>\n          </div>\n          <div class=\"navigation-mode\" :class=\"currentNavigation.mode\">\n            <i :class=\"getModeIcon(currentNavigation.mode)\"></i>\n            <span>{{ getModeText(currentNavigation.mode) }}</span>\n          </div>\n        </div>\n        \n        <div class=\"route-summary\">\n          <div class=\"summary-item\">\n            <div class=\"summary-value\">{{ currentNavigation.eta }}</div>\n            <div class=\"summary-label\">预计到达</div>\n          </div>\n          <div class=\"summary-item\">\n            <div class=\"summary-value\">{{ currentNavigation.distance }}</div>\n            <div class=\"summary-label\">剩余距离</div>\n          </div>\n          <div class=\"summary-item\">\n            <div class=\"summary-value\">{{ currentNavigation.duration }}</div>\n            <div class=\"summary-label\">剩余时间</div>\n          </div>\n          <div class=\"summary-item\">\n            <div class=\"summary-value\">{{ currentNavigation.traffic }}</div>\n            <div class=\"summary-label\">路况</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 路线预览 -->\n      <div class=\"route-preview\" v-if=\"showRoutePreview && currentNavigation\">\n        <div class=\"preview-header\">\n          <span>路线预览</span>\n          <button @click=\"toggleRouteView\" class=\"view-toggle-btn\">\n            <i :class=\"routeViewMode === 'map' ? 'fas fa-list' : 'fas fa-map'\"></i>\n          </button>\n        </div>\n        \n        <!-- 地图视图 -->\n        <div v-if=\"routeViewMode === 'map'\" class=\"map-view\">\n          <div class=\"map-container\">\n            <!-- 模拟地图显示 -->\n            <div class=\"map-placeholder\">\n              <div class=\"route-line\"></div>\n              <div class=\"start-point\">\n                <i class=\"fas fa-circle\"></i>\n                <span>起点</span>\n              </div>\n              <div class=\"end-point\">\n                <i class=\"fas fa-map-marker-alt\"></i>\n                <span>{{ currentNavigation.destination.name }}</span>\n              </div>\n              <div class=\"current-position\">\n                <i class=\"fas fa-location-arrow\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 列表视图 -->\n        <div v-else class=\"directions-list\">\n          <div \n            v-for=\"(step, index) in currentNavigation.directions\" \n            :key=\"index\"\n            :class=\"['direction-step', { current: index === currentStepIndex }]\"\n          >\n            <div class=\"step-icon\">\n              <i :class=\"getDirectionIcon(step.action)\"></i>\n            </div>\n            <div class=\"step-content\">\n              <div class=\"step-instruction\">{{ step.instruction }}</div>\n              <div class=\"step-distance\">{{ step.distance }}</div>\n            </div>\n            <div class=\"step-status\" v-if=\"index === currentStepIndex\">\n              <div class=\"status-indicator\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 交通信息 -->\n      <div class=\"traffic-info\" v-if=\"showTrafficInfo\">\n        <div class=\"traffic-header\">\n          <i class=\"fas fa-traffic-light\"></i>\n          <span>实时路况</span>\n        </div>\n        \n        <div class=\"traffic-alerts\">\n          <div \n            v-for=\"alert in trafficAlerts\" \n            :key=\"alert.id\"\n            :class=\"['traffic-alert', `severity-${alert.severity}`]\"\n          >\n            <div class=\"alert-icon\">\n              <i :class=\"getAlertIcon(alert.type)\"></i>\n            </div>\n            <div class=\"alert-content\">\n              <div class=\"alert-title\">{{ alert.title }}</div>\n              <div class=\"alert-description\">{{ alert.description }}</div>\n              <div class=\"alert-impact\">影响时间: {{ alert.impact }}</div>\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"alternative-routes\" v-if=\"alternativeRoutes.length > 0\">\n          <div class=\"alternatives-header\">备选路线</div>\n          <div \n            v-for=\"route in alternativeRoutes\" \n            :key=\"route.id\"\n            @click=\"selectAlternativeRoute(route)\"\n            class=\"alternative-route\"\n          >\n            <div class=\"route-info\">\n              <div class=\"route-name\">{{ route.name }}</div>\n              <div class=\"route-details\">\n                <span class=\"route-time\">{{ route.duration }}</span>\n                <span class=\"route-distance\">{{ route.distance }}</span>\n                <span :class=\"['route-traffic', route.trafficLevel]\">\n                  {{ getTrafficText(route.trafficLevel) }}\n                </span>\n              </div>\n            </div>\n            <div class=\"route-savings\" v-if=\"route.timeSaved\">\n              <span class=\"savings-text\">节省 {{ route.timeSaved }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 快速操作 -->\n      <div class=\"quick-actions\" v-if=\"showQuickActions\">\n        <button \n          v-for=\"action in quickActions\" \n          :key=\"action.id\"\n          @click=\"handleQuickAction(action)\"\n          :class=\"['quick-action-btn', action.type]\"\n          :disabled=\"isProcessing\"\n        >\n          <i :class=\"action.icon\"></i>\n          <span>{{ action.label }}</span>\n        </button>\n      </div>\n\n      <!-- AI导航建议 -->\n      <div class=\"ai-navigation-suggestions\" v-if=\"aiSuggestions.length > 0\">\n        <div class=\"suggestions-header\">\n          <i class=\"fas fa-magic\"></i>\n          <span>AI导航建议</span>\n        </div>\n        <div class=\"suggestions-list\">\n          <div\n            v-for=\"suggestion in aiSuggestions\"\n            :key=\"suggestion.id\"\n            class=\"suggestion-item\"\n          >\n            <div class=\"suggestion-content\">\n              <div class=\"suggestion-text\">{{ suggestion.text }}</div>\n              <div class=\"suggestion-meta\">\n                <span class=\"time-saved\">节省 {{ suggestion.timeSaved }}</span>\n                <span class=\"confidence\">可信度 {{ Math.round(suggestion.confidence * 100) }}%</span>\n              </div>\n            </div>\n            <button \n              @click=\"applySuggestion(suggestion)\"\n              class=\"apply-suggestion-btn\"\n              :disabled=\"isProcessing\"\n            >\n              <i class=\"fas fa-check\"></i>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 无导航状态 -->\n      <div v-if=\"!currentNavigation\" class=\"no-navigation\">\n        <div class=\"no-nav-icon\">\n          <i class=\"fas fa-map-marked-alt\"></i>\n        </div>\n        <div class=\"no-nav-text\">\n          <h3>开始导航</h3>\n          <p>输入目的地开始您的旅程</p>\n        </div>\n        <div class=\"destination-input\">\n          <input \n            v-model=\"destinationInput\"\n            @keyup.enter=\"startNavigation\"\n            placeholder=\"输入目的地...\"\n            class=\"destination-field\"\n          >\n          <button @click=\"startNavigation\" class=\"start-nav-btn\" :disabled=\"!destinationInput.trim()\">\n            <i class=\"fas fa-search\"></i>\n          </button>\n        </div>\n        \n        <div class=\"recent-destinations\" v-if=\"recentDestinations.length > 0\">\n          <div class=\"recent-header\">最近目的地</div>\n          <div class=\"recent-list\">\n            <button\n              v-for=\"destination in recentDestinations\"\n              :key=\"destination.id\"\n              @click=\"selectDestination(destination)\"\n              class=\"recent-item\"\n            >\n              <div class=\"recent-icon\">\n                <i :class=\"destination.icon\"></i>\n              </div>\n              <div class=\"recent-info\">\n                <div class=\"recent-name\">{{ destination.name }}</div>\n                <div class=\"recent-address\">{{ destination.address }}</div>\n              </div>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 加载状态 -->\n      <div v-if=\"isLoading\" class=\"loading-overlay\">\n        <div class=\"loading-spinner\"></div>\n        <div class=\"loading-text\">{{ loadingText }}</div>\n      </div>\n    </div>\n  </BaseCard>\n</template>\n\n<script>\nimport { ref, computed, onMounted, onUnmounted } from 'vue'\nimport BaseCard from '@/components/cards/BaseCard.vue'\nimport mockDataService from '@/services/MockDataService.js'\n\nexport default {\n  name: 'NavigationCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    size: {\n      type: String,\n      default: 'large',\n      validator: (value) => ['small', 'medium', 'large'].includes(value)\n    },\n    position: {\n      type: Object,\n      default: () => ({ x: 0, y: 0 })\n    },\n    theme: {\n      type: String,\n      default: 'glassmorphism'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({})\n    },\n    displayMode: {\n      type: String,\n      default: 'full',\n      validator: (value) => ['compact', 'standard', 'full'].includes(value)\n    },\n    showRoutePreview: {\n      type: Boolean,\n      default: true\n    },\n    showTrafficInfo: {\n      type: Boolean,\n      default: true\n    },\n    showQuickActions: {\n      type: Boolean,\n      default: true\n    },\n    autoRefresh: {\n      type: Boolean,\n      default: true\n    },\n    refreshInterval: {\n      type: Number,\n      default: 30000 // 30秒\n    }\n  },\n  emits: ['navigation-start', 'navigation-end', 'route-changed', 'suggestion-applied'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const isLoading = ref(false)\n    const isProcessing = ref(false)\n    const loadingText = ref('正在规划路线...')\n    const routeViewMode = ref('map')\n    const currentStepIndex = ref(0)\n    const destinationInput = ref('')\n    \n    const currentNavigation = ref(null)\n    const trafficAlerts = ref([])\n    const alternativeRoutes = ref([])\n    const aiSuggestions = ref([])\n    const recentDestinations = ref([])\n    \n    const quickActions = ref([\n      {\n        id: 1,\n        label: '重新规划',\n        icon: 'fas fa-redo',\n        type: 'primary',\n        action: 'replan'\n      },\n      {\n        id: 2,\n        label: '语音导航',\n        icon: 'fas fa-volume-up',\n        type: 'secondary',\n        action: 'voice'\n      },\n      {\n        id: 3,\n        label: '分享位置',\n        icon: 'fas fa-share',\n        type: 'secondary',\n        action: 'share'\n      },\n      {\n        id: 4,\n        label: '结束导航',\n        icon: 'fas fa-stop',\n        type: 'danger',\n        action: 'stop'\n      }\n    ])\n\n    // 计算属性\n    const cardTitle = computed(() => {\n      if (currentNavigation.value) {\n        return `导航中 - ${currentNavigation.value.destination.name}`\n      }\n      return '导航'\n    })\n\n    // 方法\n    const loadNavigationData = async () => {\n      try {\n        isLoading.value = true\n        loadingText.value = '正在获取导航信息...'\n        \n        const navigationData = await mockDataService.getNavigationData()\n        \n        currentNavigation.value = navigationData.currentNavigation || null\n        trafficAlerts.value = navigationData.trafficAlerts || []\n        alternativeRoutes.value = navigationData.alternativeRoutes || []\n        aiSuggestions.value = navigationData.aiSuggestions || []\n        recentDestinations.value = navigationData.recentDestinations || []\n        \n      } catch (error) {\n        console.error('Failed to load navigation data:', error)\n      } finally {\n        isLoading.value = false\n      }\n    }\n    \n    const getModeIcon = (mode) => {\n      const icons = {\n        driving: 'fas fa-car',\n        walking: 'fas fa-walking',\n        transit: 'fas fa-bus',\n        cycling: 'fas fa-bicycle'\n      }\n      return icons[mode] || 'fas fa-route'\n    }\n    \n    const getModeText = (mode) => {\n      const texts = {\n        driving: '驾车',\n        walking: '步行',\n        transit: '公交',\n        cycling: '骑行'\n      }\n      return texts[mode] || '导航'\n    }\n    \n    const getDirectionIcon = (action) => {\n      const icons = {\n        straight: 'fas fa-arrow-up',\n        left: 'fas fa-arrow-left',\n        right: 'fas fa-arrow-right',\n        uturn: 'fas fa-undo',\n        merge: 'fas fa-code-branch',\n        exit: 'fas fa-sign-out-alt'\n      }\n      return icons[action] || 'fas fa-arrow-up'\n    }\n    \n    const getAlertIcon = (type) => {\n      const icons = {\n        accident: 'fas fa-car-crash',\n        construction: 'fas fa-hard-hat',\n        traffic: 'fas fa-traffic-light',\n        weather: 'fas fa-cloud-rain',\n        closure: 'fas fa-ban'\n      }\n      return icons[type] || 'fas fa-exclamation-triangle'\n    }\n    \n    const getTrafficText = (level) => {\n      const texts = {\n        light: '畅通',\n        moderate: '缓慢',\n        heavy: '拥堵',\n        severe: '严重拥堵'\n      }\n      return texts[level] || '未知'\n    }\n    \n    const toggleRouteView = () => {\n      routeViewMode.value = routeViewMode.value === 'map' ? 'list' : 'map'\n    }\n    \n    const startNavigation = async () => {\n      if (!destinationInput.value.trim()) return\n      \n      try {\n        isLoading.value = true\n        loadingText.value = '正在规划路线...'\n        \n        const destination = {\n          name: destinationInput.value.trim(),\n          address: destinationInput.value.trim()\n        }\n        \n        const navigationResult = await mockDataService.startNavigation(destination)\n        \n        currentNavigation.value = navigationResult\n        destinationInput.value = ''\n        \n        emit('navigation-start', navigationResult)\n        \n      } catch (error) {\n        console.error('Failed to start navigation:', error)\n      } finally {\n        isLoading.value = false\n      }\n    }\n    \n    const selectDestination = async (destination) => {\n      try {\n        isLoading.value = true\n        loadingText.value = '正在规划路线...'\n        \n        const navigationResult = await mockDataService.startNavigation(destination)\n        \n        currentNavigation.value = navigationResult\n        \n        emit('navigation-start', navigationResult)\n        \n      } catch (error) {\n        console.error('Failed to start navigation:', error)\n      } finally {\n        isLoading.value = false\n      }\n    }\n    \n    const selectAlternativeRoute = async (route) => {\n      try {\n        isProcessing.value = true\n        \n        const updatedNavigation = await mockDataService.selectRoute(route.id)\n        \n        currentNavigation.value = updatedNavigation\n        \n        emit('route-changed', route)\n        \n        // 重新加载数据以获取新的备选路线\n        await loadNavigationData()\n        \n      } catch (error) {\n        console.error('Failed to select alternative route:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n    \n    const handleQuickAction = async (action) => {\n      switch (action.action) {\n        case 'replan':\n          await replanRoute()\n          break\n        case 'voice':\n          toggleVoiceNavigation()\n          break\n        case 'share':\n          shareLocation()\n          break\n        case 'stop':\n          await stopNavigation()\n          break\n      }\n    }\n    \n    const replanRoute = async () => {\n      if (!currentNavigation.value) return\n      \n      try {\n        isProcessing.value = true\n        loadingText.value = '正在重新规划路线...'\n        \n        const updatedNavigation = await mockDataService.replanRoute()\n        \n        currentNavigation.value = updatedNavigation\n        \n        await loadNavigationData()\n        \n      } catch (error) {\n        console.error('Failed to replan route:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n    \n    const toggleVoiceNavigation = () => {\n      if (currentNavigation.value) {\n        currentNavigation.value.voiceEnabled = !currentNavigation.value.voiceEnabled\n      }\n    }\n    \n    const shareLocation = () => {\n      // 模拟分享位置功能\n      console.log('Sharing current location...')\n    }\n    \n    const stopNavigation = async () => {\n      try {\n        isProcessing.value = true\n        \n        await mockDataService.stopNavigation()\n        \n        const stoppedNavigation = currentNavigation.value\n        currentNavigation.value = null\n        currentStepIndex.value = 0\n        \n        emit('navigation-end', stoppedNavigation)\n        \n      } catch (error) {\n        console.error('Failed to stop navigation:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n    \n    const applySuggestion = async (suggestion) => {\n      try {\n        isProcessing.value = true\n        \n        await mockDataService.applySuggestion(suggestion.id)\n        \n        // 从建议列表中移除\n        const index = aiSuggestions.value.findIndex(s => s.id === suggestion.id)\n        if (index > -1) {\n          aiSuggestions.value.splice(index, 1)\n        }\n        \n        emit('suggestion-applied', suggestion)\n        \n        // 重新加载导航数据\n        await loadNavigationData()\n        \n      } catch (error) {\n        console.error('Failed to apply suggestion:', error)\n      } finally {\n        isProcessing.value = false\n      }\n    }\n    \n    const updateNavigationProgress = () => {\n      if (currentNavigation.value && currentNavigation.value.directions) {\n        // 模拟导航进度更新\n        const totalSteps = currentNavigation.value.directions.length\n        if (currentStepIndex.value < totalSteps - 1) {\n          // 随机推进到下一步（实际应用中基于GPS位置）\n          if (Math.random() < 0.1) { // 10%概率推进\n            currentStepIndex.value++\n          }\n        }\n      }\n    }\n\n    // 生命周期\n    let refreshTimer = null\n    let progressTimer = null\n    \n    onMounted(async () => {\n      await mockDataService.initialize()\n      await loadNavigationData()\n      \n      // 设置自动刷新\n      if (props.autoRefresh) {\n        refreshTimer = setInterval(() => {\n          loadNavigationData()\n        }, props.refreshInterval)\n        \n        // 设置导航进度更新\n        progressTimer = setInterval(() => {\n          updateNavigationProgress()\n        }, 5000) // 每5秒检查一次进度\n      }\n    })\n\n    onUnmounted(() => {\n      if (refreshTimer) {\n        clearInterval(refreshTimer)\n      }\n      if (progressTimer) {\n        clearInterval(progressTimer)\n      }\n    })\n\n    return {\n      // 响应式数据\n      isLoading,\n      isProcessing,\n      loadingText,\n      routeViewMode,\n      currentStepIndex,\n      destinationInput,\n      currentNavigation,\n      trafficAlerts,\n      alternativeRoutes,\n      aiSuggestions,\n      recentDestinations,\n      quickActions,\n      \n      // 计算属性\n      cardTitle,\n      \n      // 方法\n      getModeIcon,\n      getModeText,\n      getDirectionIcon,\n      getAlertIcon,\n      getTrafficText,\n      toggleRouteView,\n      startNavigation,\n      selectDestination,\n      selectAlternativeRoute,\n      handleQuickAction,\n      applySuggestion\n    }\n  }\n}\n</script>\n\n<style scoped>\n.navigation-card {\n  height: 100%;\n}\n\n.navigation-container {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  padding: 16px;\n  position: relative;\n}\n\n/* 导航状态 */\n.navigation-status {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.status-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 16px;\n}\n\n.destination-info h3 {\n  margin: 0 0 4px 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.destination-address {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.navigation-mode {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 6px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 500;\n  background: rgba(74, 144, 226, 0.2);\n  color: #4a90e2;\n}\n\n.route-summary {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 12px;\n}\n\n.summary-item {\n  text-align: center;\n}\n\n.summary-value {\n  font-size: 16px;\n  font-weight: 600;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 4px;\n}\n\n.summary-label {\n  font-size: 11px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n/* 路线预览 */\n.route-preview {\n  background: rgba(0, 0, 0, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n  flex: 1;\n  min-height: 0;\n}\n\n.preview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.view-toggle-btn {\n  width: 28px;\n  height: 28px;\n  border: none;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0.2);\n  color: rgba(255, 255, 255, 0.7);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n}\n\n.view-toggle-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  color: rgba(255, 255, 255, 0.9);\n}\n\n/* 地图视图 */\n.map-view {\n  height: 200px;\n}\n\n.map-container {\n  width: 100%;\n  height: 100%;\n  border-radius: 8px;\n  overflow: hidden;\n  position: relative;\n}\n\n.map-placeholder {\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.route-line {\n  position: absolute;\n  top: 30%;\n  left: 20%;\n  right: 20%;\n  height: 3px;\n  background: linear-gradient(90deg, #4a90e2 0%, #7ed321 100%);\n  border-radius: 2px;\n  transform: rotate(-15deg);\n}\n\n.start-point,\n.end-point {\n  position: absolute;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 10px;\n  color: white;\n  background: rgba(0, 0, 0, 0.5);\n  padding: 4px 8px;\n  border-radius: 12px;\n}\n\n.start-point {\n  top: 20%;\n  left: 15%;\n  color: #4a90e2;\n}\n\n.end-point {\n  bottom: 20%;\n  right: 15%;\n  color: #7ed321;\n}\n\n.current-position {\n  position: absolute;\n  top: 50%;\n  left: 40%;\n  color: #ff6b6b;\n  font-size: 16px;\n  animation: pulse 2s infinite;\n}\n\n/* 方向列表 */\n.directions-list {\n  max-height: 200px;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.direction-step {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n  transition: all 0.3s ease;\n}\n\n.direction-step.current {\n  background: rgba(74, 144, 226, 0.2);\n  border-left: 3px solid #4a90e2;\n}\n\n.step-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.8);\n  flex-shrink: 0;\n}\n\n.direction-step.current .step-icon {\n  background: rgba(74, 144, 226, 0.3);\n  color: #4a90e2;\n}\n\n.step-content {\n  flex: 1;\n}\n\n.step-instruction {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n}\n\n.step-distance {\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.step-status {\n  width: 8px;\n  height: 8px;\n}\n\n.status-indicator {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  background: #4a90e2;\n  animation: pulse 1.5s infinite;\n}\n\n/* 交通信息 */\n.traffic-info {\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.traffic-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 12px;\n}\n\n.traffic-alerts {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  margin-bottom: 16px;\n}\n\n.traffic-alert {\n  display: flex;\n  align-items: flex-start;\n  gap: 8px;\n  padding: 8px;\n  border-radius: 8px;\n  border-left: 3px solid transparent;\n}\n\n.traffic-alert.severity-low {\n  background: rgba(16, 185, 129, 0.1);\n  border-left-color: #10b981;\n}\n\n.traffic-alert.severity-medium {\n  background: rgba(245, 158, 11, 0.1);\n  border-left-color: #f59e0b;\n}\n\n.traffic-alert.severity-high {\n  background: rgba(239, 68, 68, 0.1);\n  border-left-color: #ef4444;\n}\n\n.alert-icon {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  flex-shrink: 0;\n}\n\n.alert-content {\n  flex: 1;\n}\n\n.alert-title {\n  font-size: 12px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n}\n\n.alert-description {\n  font-size: 11px;\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 2px;\n}\n\n.alert-impact {\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.5);\n}\n\n/* 备选路线 */\n.alternative-routes {\n  margin-top: 12px;\n}\n\n.alternatives-header {\n  font-size: 12px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.8);\n  margin-bottom: 8px;\n}\n\n.alternative-route {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  margin-bottom: 4px;\n}\n\n.alternative-route:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateX(2px);\n}\n\n.route-info {\n  flex: 1;\n}\n\n.route-name {\n  font-size: 12px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2px;\n}\n\n.route-details {\n  display: flex;\n  gap: 8px;\n  font-size: 10px;\n}\n\n.route-time {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.route-distance {\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.route-traffic.light {\n  color: #10b981;\n}\n\n.route-traffic.moderate {\n  color: #f59e0b;\n}\n\n.route-traffic.heavy {\n  color: #ef4444;\n}\n\n.route-savings {\n  font-size: 10px;\n  color: #7ed321;\n}\n\n/* 快速操作 */\n.quick-actions {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 8px;\n}\n\n.quick-action-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 6px;\n  padding: 8px 12px;\n  border: none;\n  border-radius: 8px;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.quick-action-btn.primary {\n  background: rgba(74, 144, 226, 0.2);\n  color: #4a90e2;\n}\n\n.quick-action-btn.secondary {\n  background: rgba(255, 255, 255, 0.1);\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.quick-action-btn.danger {\n  background: rgba(239, 68, 68, 0.2);\n  color: #ef4444;\n}\n\n.quick-action-btn:hover {\n  transform: translateY(-1px);\n}\n\n.quick-action-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* AI建议 */\n.ai-navigation-suggestions {\n  background: rgba(126, 211, 33, 0.1);\n  border-radius: 12px;\n  padding: 16px;\n}\n\n.suggestions-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  color: #7ed321;\n  margin-bottom: 12px;\n}\n\n.suggestions-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.suggestion-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n}\n\n.suggestion-content {\n  flex: 1;\n}\n\n.suggestion-text {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 4px;\n}\n\n.suggestion-meta {\n  display: flex;\n  gap: 12px;\n  font-size: 10px;\n}\n\n.time-saved {\n  color: #7ed321;\n}\n\n.confidence {\n  color: #4a90e2;\n}\n\n.apply-suggestion-btn {\n  width: 28px;\n  height: 28px;\n  border: none;\n  border-radius: 6px;\n  background: rgba(126, 211, 33, 0.3);\n  color: #7ed321;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.apply-suggestion-btn:hover {\n  background: rgba(126, 211, 33, 0.5);\n  transform: scale(1.05);\n}\n\n/* 无导航状态 */\n.no-navigation {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  padding: 32px 16px;\n  flex: 1;\n}\n\n.no-nav-icon {\n  font-size: 48px;\n  color: rgba(255, 255, 255, 0.3);\n  margin-bottom: 16px;\n}\n\n.no-nav-text h3 {\n  margin: 0 0 8px 0;\n  font-size: 18px;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.no-nav-text p {\n  margin: 0 0 24px 0;\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.destination-input {\n  display: flex;\n  gap: 8px;\n  width: 100%;\n  max-width: 300px;\n  margin-bottom: 24px;\n}\n\n.destination-field {\n  flex: 1;\n  padding: 12px 16px;\n  border: none;\n  border-radius: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  color: rgba(255, 255, 255, 0.9);\n  font-size: 14px;\n  outline: none;\n  transition: all 0.3s ease;\n}\n\n.destination-field::placeholder {\n  color: rgba(255, 255, 255, 0.5);\n}\n\n.destination-field:focus {\n  background: rgba(255, 255, 255, 0.15);\n  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.3);\n}\n\n.start-nav-btn {\n  padding: 12px;\n  border: none;\n  border-radius: 8px;\n  background: rgba(74, 144, 226, 0.3);\n  color: #4a90e2;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.start-nav-btn:hover:not(:disabled) {\n  background: rgba(74, 144, 226, 0.5);\n  transform: scale(1.05);\n}\n\n.start-nav-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* 最近目的地 */\n.recent-destinations {\n  width: 100%;\n  max-width: 300px;\n}\n\n.recent-header {\n  font-size: 12px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 8px;\n  text-align: left;\n}\n\n.recent-list {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.recent-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border: none;\n  border-radius: 8px;\n  color: rgba(255, 255, 255, 0.8);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-align: left;\n  width: 100%;\n}\n\n.recent-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateX(2px);\n}\n\n.recent-icon {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.recent-info {\n  flex: 1;\n}\n\n.recent-name {\n  font-size: 12px;\n  font-weight: 500;\n  margin-bottom: 2px;\n}\n\n.recent-address {\n  font-size: 10px;\n  color: rgba(255, 255, 255, 0.5);\n}\n\n/* 加载状态 */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.3);\n  backdrop-filter: blur(5px);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 12px;\n  border-radius: 12px;\n}\n\n.loading-spinner {\n  width: 32px;\n  height: 32px;\n  border: 3px solid rgba(255, 255, 255, 0.2);\n  border-top: 3px solid #4a90e2;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.loading-text {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* 动画 */\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n}\n\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* 响应式设计 */\n.size-small .navigation-container {\n  padding: 12px;\n  gap: 12px;\n}\n\n.size-small .route-summary {\n  grid-template-columns: repeat(2, 1fr);\n}\n\n.size-small .quick-actions {\n  grid-template-columns: 1fr;\n}\n\n.mode-compact .route-preview,\n.mode-compact .traffic-info,\n.mode-compact .ai-navigation-suggestions {\n  display: none;\n}\n\n.mode-compact .quick-actions {\n  grid-template-columns: repeat(4, 1fr);\n}\n</style>"], "mappings": "AAmPA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAU,QAAS,KAAI;AAC1D,OAAOC,QAAO,MAAO,iCAAgC;AACrD,OAAOC,eAAc,MAAO,+BAA8B;AAE1D,eAAe;EACbC,IAAI,EAAE,gBAAgB;EACtBC,UAAU,EAAE;IACVH;EACF,CAAC;EACDI,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAGC,KAAK,IAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAACC,QAAQ,CAACD,KAAK;IACnE,CAAC;IACDE,QAAQ,EAAE;MACRN,IAAI,EAAEO,MAAM;MACZL,OAAO,EAAEA,CAAA,MAAO;QAAEM,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;IAChC,CAAC;IACDC,KAAK,EAAE;MACLV,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDS,WAAW,EAAE;MACXX,IAAI,EAAEO,MAAM;MACZL,OAAO,EAAEA,CAAA,MAAO,CAAC,CAAC;IACpB,CAAC;IACDU,WAAW,EAAE;MACXZ,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAGC,KAAK,IAAK,CAAC,SAAS,EAAE,UAAU,EAAE,MAAM,CAAC,CAACC,QAAQ,CAACD,KAAK;IACtE,CAAC;IACDS,gBAAgB,EAAE;MAChBb,IAAI,EAAEc,OAAO;MACbZ,OAAO,EAAE;IACX,CAAC;IACDa,eAAe,EAAE;MACff,IAAI,EAAEc,OAAO;MACbZ,OAAO,EAAE;IACX,CAAC;IACDc,gBAAgB,EAAE;MAChBhB,IAAI,EAAEc,OAAO;MACbZ,OAAO,EAAE;IACX,CAAC;IACDe,WAAW,EAAE;MACXjB,IAAI,EAAEc,OAAO;MACbZ,OAAO,EAAE;IACX,CAAC;IACDgB,eAAe,EAAE;MACflB,IAAI,EAAEmB,MAAM;MACZjB,OAAO,EAAE,KAAI,CAAE;IACjB;EACF,CAAC;EACDkB,KAAK,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,eAAe,EAAE,oBAAoB,CAAC;EACpFC,KAAKA,CAACvB,KAAK,EAAE;IAAEwB;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,SAAQ,GAAIjC,GAAG,CAAC,KAAK;IAC3B,MAAMkC,YAAW,GAAIlC,GAAG,CAAC,KAAK;IAC9B,MAAMmC,WAAU,GAAInC,GAAG,CAAC,WAAW;IACnC,MAAMoC,aAAY,GAAIpC,GAAG,CAAC,KAAK;IAC/B,MAAMqC,gBAAe,GAAIrC,GAAG,CAAC,CAAC;IAC9B,MAAMsC,gBAAe,GAAItC,GAAG,CAAC,EAAE;IAE/B,MAAMuC,iBAAgB,GAAIvC,GAAG,CAAC,IAAI;IAClC,MAAMwC,aAAY,GAAIxC,GAAG,CAAC,EAAE;IAC5B,MAAMyC,iBAAgB,GAAIzC,GAAG,CAAC,EAAE;IAChC,MAAM0C,aAAY,GAAI1C,GAAG,CAAC,EAAE;IAC5B,MAAM2C,kBAAiB,GAAI3C,GAAG,CAAC,EAAE;IAEjC,MAAM4C,YAAW,GAAI5C,GAAG,CAAC,CACvB;MACE6C,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,aAAa;MACnBrC,IAAI,EAAE,SAAS;MACfsC,MAAM,EAAE;IACV,CAAC,EACD;MACEH,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,kBAAkB;MACxBrC,IAAI,EAAE,WAAW;MACjBsC,MAAM,EAAE;IACV,CAAC,EACD;MACEH,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,cAAc;MACpBrC,IAAI,EAAE,WAAW;MACjBsC,MAAM,EAAE;IACV,CAAC,EACD;MACEH,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,aAAa;MACnBrC,IAAI,EAAE,QAAQ;MACdsC,MAAM,EAAE;IACV,EACD;;IAED;IACA,MAAMC,SAAQ,GAAIhD,QAAQ,CAAC,MAAM;MAC/B,IAAIsC,iBAAiB,CAACzB,KAAK,EAAE;QAC3B,OAAO,SAASyB,iBAAiB,CAACzB,KAAK,CAACoC,WAAW,CAAC5C,IAAI,EAAC;MAC3D;MACA,OAAO,IAAG;IACZ,CAAC;;IAED;IACA,MAAM6C,kBAAiB,GAAI,MAAAA,CAAA,KAAY;MACrC,IAAI;QACFlB,SAAS,CAACnB,KAAI,GAAI,IAAG;QACrBqB,WAAW,CAACrB,KAAI,GAAI,aAAY;QAEhC,MAAMsC,cAAa,GAAI,MAAM/C,eAAe,CAACgD,iBAAiB,CAAC;QAE/Dd,iBAAiB,CAACzB,KAAI,GAAIsC,cAAc,CAACb,iBAAgB,IAAK,IAAG;QACjEC,aAAa,CAAC1B,KAAI,GAAIsC,cAAc,CAACZ,aAAY,IAAK,EAAC;QACvDC,iBAAiB,CAAC3B,KAAI,GAAIsC,cAAc,CAACX,iBAAgB,IAAK,EAAC;QAC/DC,aAAa,CAAC5B,KAAI,GAAIsC,cAAc,CAACV,aAAY,IAAK,EAAC;QACvDC,kBAAkB,CAAC7B,KAAI,GAAIsC,cAAc,CAACT,kBAAiB,IAAK,EAAC;MAEnE,EAAE,OAAOW,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK;MACxD,UAAU;QACRrB,SAAS,CAACnB,KAAI,GAAI,KAAI;MACxB;IACF;IAEA,MAAM0C,WAAU,GAAKC,IAAI,IAAK;MAC5B,MAAMC,KAAI,GAAI;QACZC,OAAO,EAAE,YAAY;QACrBC,OAAO,EAAE,gBAAgB;QACzBC,OAAO,EAAE,YAAY;QACrBC,OAAO,EAAE;MACX;MACA,OAAOJ,KAAK,CAACD,IAAI,KAAK,cAAa;IACrC;IAEA,MAAMM,WAAU,GAAKN,IAAI,IAAK;MAC5B,MAAMO,KAAI,GAAI;QACZL,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE;MACX;MACA,OAAOE,KAAK,CAACP,IAAI,KAAK,IAAG;IAC3B;IAEA,MAAMQ,gBAAe,GAAKjB,MAAM,IAAK;MACnC,MAAMU,KAAI,GAAI;QACZQ,QAAQ,EAAE,iBAAiB;QAC3BC,IAAI,EAAE,mBAAmB;QACzBC,KAAK,EAAE,oBAAoB;QAC3BC,KAAK,EAAE,aAAa;QACpBC,KAAK,EAAE,oBAAoB;QAC3BC,IAAI,EAAE;MACR;MACA,OAAOb,KAAK,CAACV,MAAM,KAAK,iBAAgB;IAC1C;IAEA,MAAMwB,YAAW,GAAK9D,IAAI,IAAK;MAC7B,MAAMgD,KAAI,GAAI;QACZe,QAAQ,EAAE,kBAAkB;QAC5BC,YAAY,EAAE,iBAAiB;QAC/BC,OAAO,EAAE,sBAAsB;QAC/BC,OAAO,EAAE,mBAAmB;QAC5BC,OAAO,EAAE;MACX;MACA,OAAOnB,KAAK,CAAChD,IAAI,KAAK,6BAA4B;IACpD;IAEA,MAAMoE,cAAa,GAAKC,KAAK,IAAK;MAChC,MAAMf,KAAI,GAAI;QACZgB,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE;MACV;MACA,OAAOnB,KAAK,CAACe,KAAK,KAAK,IAAG;IAC5B;IAEA,MAAMK,eAAc,GAAIA,CAAA,KAAM;MAC5BhD,aAAa,CAACtB,KAAI,GAAIsB,aAAa,CAACtB,KAAI,KAAM,KAAI,GAAI,MAAK,GAAI,KAAI;IACrE;IAEA,MAAMuE,eAAc,GAAI,MAAAA,CAAA,KAAY;MAClC,IAAI,CAAC/C,gBAAgB,CAACxB,KAAK,CAACwE,IAAI,CAAC,CAAC,EAAE;MAEpC,IAAI;QACFrD,SAAS,CAACnB,KAAI,GAAI,IAAG;QACrBqB,WAAW,CAACrB,KAAI,GAAI,WAAU;QAE9B,MAAMoC,WAAU,GAAI;UAClB5C,IAAI,EAAEgC,gBAAgB,CAACxB,KAAK,CAACwE,IAAI,CAAC,CAAC;UACnCC,OAAO,EAAEjD,gBAAgB,CAACxB,KAAK,CAACwE,IAAI,CAAC;QACvC;QAEA,MAAME,gBAAe,GAAI,MAAMnF,eAAe,CAACgF,eAAe,CAACnC,WAAW;QAE1EX,iBAAiB,CAACzB,KAAI,GAAI0E,gBAAe;QACzClD,gBAAgB,CAACxB,KAAI,GAAI,EAAC;QAE1BkB,IAAI,CAAC,kBAAkB,EAAEwD,gBAAgB;MAE3C,EAAE,OAAOlC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK;MACpD,UAAU;QACRrB,SAAS,CAACnB,KAAI,GAAI,KAAI;MACxB;IACF;IAEA,MAAM2E,iBAAgB,GAAI,MAAOvC,WAAW,IAAK;MAC/C,IAAI;QACFjB,SAAS,CAACnB,KAAI,GAAI,IAAG;QACrBqB,WAAW,CAACrB,KAAI,GAAI,WAAU;QAE9B,MAAM0E,gBAAe,GAAI,MAAMnF,eAAe,CAACgF,eAAe,CAACnC,WAAW;QAE1EX,iBAAiB,CAACzB,KAAI,GAAI0E,gBAAe;QAEzCxD,IAAI,CAAC,kBAAkB,EAAEwD,gBAAgB;MAE3C,EAAE,OAAOlC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK;MACpD,UAAU;QACRrB,SAAS,CAACnB,KAAI,GAAI,KAAI;MACxB;IACF;IAEA,MAAM4E,sBAAqB,GAAI,MAAOC,KAAK,IAAK;MAC9C,IAAI;QACFzD,YAAY,CAACpB,KAAI,GAAI,IAAG;QAExB,MAAM8E,iBAAgB,GAAI,MAAMvF,eAAe,CAACwF,WAAW,CAACF,KAAK,CAAC9C,EAAE;QAEpEN,iBAAiB,CAACzB,KAAI,GAAI8E,iBAAgB;QAE1C5D,IAAI,CAAC,eAAe,EAAE2D,KAAK;;QAE3B;QACA,MAAMxC,kBAAkB,CAAC;MAE3B,EAAE,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK;MAC5D,UAAU;QACRpB,YAAY,CAACpB,KAAI,GAAI,KAAI;MAC3B;IACF;IAEA,MAAMgF,iBAAgB,GAAI,MAAO9C,MAAM,IAAK;MAC1C,QAAQA,MAAM,CAACA,MAAM;QACnB,KAAK,QAAQ;UACX,MAAM+C,WAAW,CAAC;UAClB;QACF,KAAK,OAAO;UACVC,qBAAqB,CAAC;UACtB;QACF,KAAK,OAAO;UACVC,aAAa,CAAC;UACd;QACF,KAAK,MAAM;UACT,MAAMC,cAAc,CAAC;UACrB;MACJ;IACF;IAEA,MAAMH,WAAU,GAAI,MAAAA,CAAA,KAAY;MAC9B,IAAI,CAACxD,iBAAiB,CAACzB,KAAK,EAAE;MAE9B,IAAI;QACFoB,YAAY,CAACpB,KAAI,GAAI,IAAG;QACxBqB,WAAW,CAACrB,KAAI,GAAI,aAAY;QAEhC,MAAM8E,iBAAgB,GAAI,MAAMvF,eAAe,CAAC0F,WAAW,CAAC;QAE5DxD,iBAAiB,CAACzB,KAAI,GAAI8E,iBAAgB;QAE1C,MAAMzC,kBAAkB,CAAC;MAE3B,EAAE,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK;MAChD,UAAU;QACRpB,YAAY,CAACpB,KAAI,GAAI,KAAI;MAC3B;IACF;IAEA,MAAMkF,qBAAoB,GAAIA,CAAA,KAAM;MAClC,IAAIzD,iBAAiB,CAACzB,KAAK,EAAE;QAC3ByB,iBAAiB,CAACzB,KAAK,CAACqF,YAAW,GAAI,CAAC5D,iBAAiB,CAACzB,KAAK,CAACqF,YAAW;MAC7E;IACF;IAEA,MAAMF,aAAY,GAAIA,CAAA,KAAM;MAC1B;MACA1C,OAAO,CAAC6C,GAAG,CAAC,6BAA6B;IAC3C;IAEA,MAAMF,cAAa,GAAI,MAAAA,CAAA,KAAY;MACjC,IAAI;QACFhE,YAAY,CAACpB,KAAI,GAAI,IAAG;QAExB,MAAMT,eAAe,CAAC6F,cAAc,CAAC;QAErC,MAAMG,iBAAgB,GAAI9D,iBAAiB,CAACzB,KAAI;QAChDyB,iBAAiB,CAACzB,KAAI,GAAI,IAAG;QAC7BuB,gBAAgB,CAACvB,KAAI,GAAI;QAEzBkB,IAAI,CAAC,gBAAgB,EAAEqE,iBAAiB;MAE1C,EAAE,OAAO/C,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK;MACnD,UAAU;QACRpB,YAAY,CAACpB,KAAI,GAAI,KAAI;MAC3B;IACF;IAEA,MAAMwF,eAAc,GAAI,MAAOC,UAAU,IAAK;MAC5C,IAAI;QACFrE,YAAY,CAACpB,KAAI,GAAI,IAAG;QAExB,MAAMT,eAAe,CAACiG,eAAe,CAACC,UAAU,CAAC1D,EAAE;;QAEnD;QACA,MAAM2D,KAAI,GAAI9D,aAAa,CAAC5B,KAAK,CAAC2F,SAAS,CAACC,CAAA,IAAKA,CAAC,CAAC7D,EAAC,KAAM0D,UAAU,CAAC1D,EAAE;QACvE,IAAI2D,KAAI,GAAI,CAAC,CAAC,EAAE;UACd9D,aAAa,CAAC5B,KAAK,CAAC6F,MAAM,CAACH,KAAK,EAAE,CAAC;QACrC;QAEAxE,IAAI,CAAC,oBAAoB,EAAEuE,UAAU;;QAErC;QACA,MAAMpD,kBAAkB,CAAC;MAE3B,EAAE,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK;MACpD,UAAU;QACRpB,YAAY,CAACpB,KAAI,GAAI,KAAI;MAC3B;IACF;IAEA,MAAM8F,wBAAuB,GAAIA,CAAA,KAAM;MACrC,IAAIrE,iBAAiB,CAACzB,KAAI,IAAKyB,iBAAiB,CAACzB,KAAK,CAAC+F,UAAU,EAAE;QACjE;QACA,MAAMC,UAAS,GAAIvE,iBAAiB,CAACzB,KAAK,CAAC+F,UAAU,CAACE,MAAK;QAC3D,IAAI1E,gBAAgB,CAACvB,KAAI,GAAIgG,UAAS,GAAI,CAAC,EAAE;UAC3C;UACA,IAAIE,IAAI,CAACC,MAAM,CAAC,IAAI,GAAG,EAAE;YAAE;YACzB5E,gBAAgB,CAACvB,KAAK,EAAC;UACzB;QACF;MACF;IACF;;IAEA;IACA,IAAIoG,YAAW,GAAI,IAAG;IACtB,IAAIC,aAAY,GAAI,IAAG;IAEvBjH,SAAS,CAAC,YAAY;MACpB,MAAMG,eAAe,CAAC+G,UAAU,CAAC;MACjC,MAAMjE,kBAAkB,CAAC;;MAEzB;MACA,IAAI3C,KAAK,CAACmB,WAAW,EAAE;QACrBuF,YAAW,GAAIG,WAAW,CAAC,MAAM;UAC/BlE,kBAAkB,CAAC;QACrB,CAAC,EAAE3C,KAAK,CAACoB,eAAe;;QAExB;QACAuF,aAAY,GAAIE,WAAW,CAAC,MAAM;UAChCT,wBAAwB,CAAC;QAC3B,CAAC,EAAE,IAAI,GAAE;MACX;IACF,CAAC;IAEDzG,WAAW,CAAC,MAAM;MAChB,IAAI+G,YAAY,EAAE;QAChBI,aAAa,CAACJ,YAAY;MAC5B;MACA,IAAIC,aAAa,EAAE;QACjBG,aAAa,CAACH,aAAa;MAC7B;IACF,CAAC;IAED,OAAO;MACL;MACAlF,SAAS;MACTC,YAAY;MACZC,WAAW;MACXC,aAAa;MACbC,gBAAgB;MAChBC,gBAAgB;MAChBC,iBAAiB;MACjBC,aAAa;MACbC,iBAAiB;MACjBC,aAAa;MACbC,kBAAkB;MAClBC,YAAY;MAEZ;MACAK,SAAS;MAET;MACAO,WAAW;MACXO,WAAW;MACXE,gBAAgB;MAChBO,YAAY;MACZM,cAAc;MACdM,eAAe;MACfC,eAAe;MACfI,iBAAiB;MACjBC,sBAAsB;MACtBI,iBAAiB;MACjBQ;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}