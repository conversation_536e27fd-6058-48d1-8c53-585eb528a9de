<template>
  <div class="immersive-interface">
    <!-- 左侧对话区域 -->
    <div class="conversation-panel">
      <div class="welcome-message">
        <p>你好，我是小智，欢迎来到AI-HMI，我是智能桌面助手，可以根据场景定制专属桌面，这是对我说：</p>
      </div>
      
      <div class="preset-buttons">
        <button 
          v-for="preset in presetScenes" 
          :key="preset.id"
          @click="generateWallpaper(preset.prompt)"
          class="preset-btn"
        >
          {{ preset.name }}
        </button>
      </div>
      
      <div class="description">
        <p>根据你的使用习惯，后续会全面智能化桌面，或者也可以在AI-LAB设置你的更多介绍</p>
      </div>
    </div>

    <!-- 右侧VPA区域 -->
    <div class="vpa-panel">
      <div class="vpa-container">
        <img src="/images/vpa2.gif" alt="VPA智能助手" class="vpa-avatar" />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import EmotionalPromptGenerator from '@/services/EmotionalPromptGenerator'
import SceneContextManager from '@/services/SceneContextManager'

export default {
  name: 'ImmersiveWallpaperInterface',
  emits: ['wallpaper-prompt-ready'],
  
  setup(props, { emit }) {
    const emotionalPromptGenerator = new EmotionalPromptGenerator()
    const contextManager = new SceneContextManager()
    
    const presetScenes = ref([
      {
        id: 'commute',
        name: '生成通勤桌面',
        prompt: '动漫卡通风格的通勤场景，可爱的卡通车厢，柔和的晨光，温馨的通勤氛围'
      },
      {
        id: 'navigation',
        name: '导航3D效果',
        prompt: '动漫卡通风格的导航界面，可爱的地图元素，卡通化的道路标识，温馨的导航体验'
      },
      {
        id: 'solitude',
        name: '帮我规划一个独处的桌面',
        prompt: '动漫卡通风格的独处空间，舒适的小屋，柔和的灯光，温馨治愈的独处氛围'
      },
      {
        id: 'spring',
        name: '生成春游桌面',
        prompt: '动漫卡通风格的春游场景，可爱的花草树木，明媚的春光，温馨的春日氛围'
      },
      {
        id: 'weekend',
        name: '帮我规划一个周末一日游',
        prompt: '动漫卡通风格的周末出游，可爱的风景元素，轻松的氛围，温馨的周末时光'
      }
    ])

    const generateWallpaper = async (prompt) => {
      console.log('生成壁纸:', prompt)
      
      try {
        // 获取当前上下文
        const currentContext = contextManager.getPromptGenerationContext()
        console.log('📋 当前上下文:', currentContext)
        
        // 创建场景信息对象
        const sceneInfo = {
          id: 'immersive',
          name: '沉浸式模式',
          description: prompt,
          theme: 'immersive'
        }
        
        // 生成情感化提示词
        const emotionalPrompt = await emotionalPromptGenerator.generateEmotionalPrompt(
          sceneInfo,
          currentContext
        )
        
        console.log('🎭 情感化提示词生成成功:', emotionalPrompt)
        
        // 发送完整的事件数据
        emit('wallpaper-prompt-ready', {
          prompt: emotionalPrompt,
          originalPrompt: prompt,
          scene: sceneInfo,
          context: currentContext
        })
        
      } catch (error) {
        console.error('情感化提示词生成失败，使用原始提示词:', error)
        
        // 降级到原始提示词
        emit('wallpaper-prompt-ready', {
          prompt,
          originalPrompt: prompt,
          scene: {
            id: 'immersive',
            name: '沉浸式模式',
            description: prompt,
            theme: 'immersive'
          },
          context: contextManager.getPromptGenerationContext()
        })
      }
    }

    return {
      presetScenes,
      generateWallpaper,
      // 暴露上下文信息用于调试
      contextInfo: computed(() => contextManager.getStatistics())
    }
  }
}
</script>

<style scoped>
.immersive-interface {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40px;
  box-sizing: border-box;
}

.conversation-panel {
  flex: 1;
  max-width: 400px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.welcome-message {
  margin-bottom: 25px;
}

.welcome-message p {
  color: white;
  font-size: 16px;
  line-height: 1.6;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.preset-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 25px;
}

.preset-btn {
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  text-align: left;
}

.preset-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.description {
  margin-top: 20px;
}

.description p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  line-height: 1.5;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.vpa-panel {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 500px;
}

.vpa-container {
  width: 300px;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.vpa-avatar {
  width: 200px;
  height: 200px;
  object-fit: contain;
  border-radius: 50%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .immersive-interface {
    flex-direction: column;
    padding: 20px;
    gap: 30px;
  }
  
  .conversation-panel {
    max-width: 100%;
  }
  
  .vpa-container {
    width: 200px;
    height: 200px;
  }
  
  .vpa-avatar {
    width: 150px;
    height: 150px;
  }
}
</style>
