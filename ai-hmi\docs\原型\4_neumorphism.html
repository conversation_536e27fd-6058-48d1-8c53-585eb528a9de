<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI HMI - Neumorphism Theme</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            overflow: hidden;
            font-family: 'Poppins', sans-serif;
            background-color: #e0e5ec;
        }
        .grid-container {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            grid-template-rows: repeat(4, 1fr);
            gap: 24px;
            width: 100vw;
            height: 100vh;
            padding: 24px;
            box-sizing: border-box;
        }
        .card {
            border-radius: 20px;
            background: #e0e5ec;
            box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
            transition: all 0.3s ease;
        }
        .card-inner {
             border-radius: 20px;
             background: #e0e5ec;
             box-shadow: inset 5px 5px 10px #a3b1c6, inset -5px -5px 10px #ffffff;
        }
        .card-pressed {
            border-radius: 20px;
            background: #e0e5ec;
            box-shadow: inset 9px 9px 16px #a3b1c6, inset -9px -9px 16px #ffffff;
        }
        .vpa-container { grid-column: span 2; grid-row: span 4; }
        .island-container { grid-column: 3 / span 4; grid-row: 1; display: flex; align-items: center; justify-content: center; }
        .small-card-container { grid-column: 3 / span 4; grid-row: 2; display: grid; grid-template-columns: repeat(2, 1fr); gap: 24px; }
        .large-card-container { grid-column: 7 / span 2; grid-row: span 4; }
        .temp-interaction-container { position: absolute; bottom: 40px; left: 50%; transform: translateX(-50%); z-index: 50; }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="grid-container text-gray-600">
        <!-- VPA 数字人 -->
        <div class="vpa-container card flex flex-col items-center justify-center p-6">
            <div class="w-36 h-36 rounded-full card-inner flex items-center justify-center mb-5">
                <img src="https://images.unsplash.com/photo-1580489944761-15a19d654956?q=80&w=2561&auto=format&fit=crop" alt="VPA Avatar" class="w-32 h-32 rounded-full object-cover">
            </div>
            <h2 class="text-2xl font-bold text-gray-700">Eve</h2>
            <p class="text-gray-500 text-center mt-2">Everything is calm and collected.</p>
        </div>

        <!-- 灵动岛 -->
        <div class="island-container card-inner p-3">
            <div class="flex items-center space-x-4">
                <svg class="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 20 20"><path d="M10 2a6 6 0 00-6 6v3.586l-1.707 1.707A1 1 0 003 15h14a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"></path></svg>
                <div>
                    <p class="font-semibold text-gray-700">Reminder</p>
                    <p class="text-sm text-gray-500">Project submission due at 5 PM.</p>
                </div>
            </div>
        </div>

        <!-- 横向小卡片 -->
        <div class="small-card-container">
            <div class="card p-5 flex flex-col justify-between">
                <h3 class="font-bold text-lg text-gray-700">Home Climate</h3>
                <div class="text-center">
                    <p class="text-4xl font-bold text-blue-500">22°</p>
                    <p class="text-gray-500">Cooling On</p>
                </div>
            </div>
            <div class="card p-5 flex flex-col justify-between">
                <h3 class="font-bold text-lg text-gray-700">Battery</h3>
                <div class="text-center">
                    <p class="text-4xl font-bold text-green-500">82%</p>
                    <p class="text-gray-500">Range: 320 km</p>
                </div>
            </div>
        </div>

        <!-- 纵向大卡片 -->
        <div class="large-card-container card p-5 flex flex-col">
            <h3 class="font-bold text-lg text-gray-700 mb-3">Tasks for Today</h3>
            <div class="flex-grow space-y-4 overflow-y-auto">
                <div class="card-inner p-3 rounded-lg flex justify-between items-center">
                    <p>Finalize report</p>
                    <div class="w-6 h-6 rounded-full card-pressed"></div>
                </div>
                <div class="card-inner p-3 rounded-lg flex justify-between items-center">
                    <p>Call contractor</p>
                    <div class="w-6 h-6 rounded-full card-pressed"></div>
                </div>
                 <div class="card-inner p-3 rounded-lg flex justify-between items-center">
                    <p>Pick up groceries</p>
                    <div class="w-6 h-6 rounded-full card"></div>
                </div>
            </div>
        </div>

        <!-- 临时交互组件 -->
        <div class="temp-interaction-container card p-4 flex items-center space-x-4">
            <p class="font-semibold text-gray-700">Confirm Navigation?</p>
            <p class="text-gray-500">To: Downtown Office</p>
            <button class="px-6 py-2 rounded-lg text-white bg-blue-500 shadow-md hover:bg-blue-600">Start</button>
        </div>

        <div style="grid-column: 3 / span 4; grid-row: 3 / span 2;"></div>

    </div>
</body>
</html>