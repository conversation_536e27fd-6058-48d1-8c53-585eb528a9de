/**
 * AI HMI 增强过渡特效管理器
 * 基于设计文档中的蒙版切换效果规范
 * 支持页面快照捕获和高质量过渡动画
 * 使用GSAP和CSS clip-path实现60fps流畅过渡
 */

class EnhancedTransitionManager {
    constructor(options = {}) {
        this.isTransitioning = false;
        this.currentPage = null;
        this.transitionContainer = null;
        this.snapshotHistory = new Map(); // 存储页面快照历史
        this.maxSnapshotHistory = 5; // 最大快照历史数量
        
        // 配置选项
        this.options = {
            enableSnapshots: true,
            snapshotQuality: 0.8,
            transitionDuration: 1.2,
            enableDebug: false,
            ...options
        };
        
        this.init();
    }

    init() {
        this.createTransitionContainer();
        this.bindEvents();
        this.setupPerformanceMonitoring();
        
        if (this.options.enableDebug) {
            console.log('Enhanced Transition Manager initialized with options:', this.options);
        }
    }

    createTransitionContainer() {
        if (document.getElementById('enhanced-transition-container')) return;
        
        const container = document.createElement('div');
        container.id = 'enhanced-transition-container';
        container.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 99999;
            pointer-events: none;
            overflow: hidden;
            background: transparent;
        `;
        
        document.body.appendChild(container);
        this.transitionContainer = container;
    }

    /**
     * 执行页面过渡
     * @param {string|HTMLElement} target - 目标页面URL或元素
     * @param {string} effectType - 过渡效果类型
     * @param {Object} options - 过渡选项
     */
    async transitionToPage(target, effectType = 'circle_expand', options = {}) {
        if (this.isTransitioning) {
            this.log('Transition already in progress');
            return Promise.reject(new Error('Transition in progress'));
        }

        this.isTransitioning = true;
        const startTime = performance.now();
        
        try {
            this.log(`Starting transition to ${target} with effect ${effectType}`);
            
            // 1. 捕获当前页面快照
            const currentSnapshot = await this.capturePageSnapshot();
            
            // 2. 预处理目标内容
            const targetContent = await this.prepareTargetContent(target);
            
            // 3. 执行过渡动画
            await this.executeTransition(currentSnapshot, targetContent, effectType, options);
            
            // 4. 完成页面切换
            await this.completeTransition(target);
            
            const duration = performance.now() - startTime;
            this.log(`Transition completed in ${duration.toFixed(2)}ms`);
            
        } catch (error) {
            this.log('Transition failed:', error);
            await this.fallbackTransition(target);
        } finally {
            this.isTransitioning = false;
        }
    }

    /**
     * 捕获当前页面快照
     */
    async capturePageSnapshot() {
        if (!this.options.enableSnapshots) {
            return this.createPlaceholderSnapshot();
        }

        return new Promise((resolve) => {
            try {
                // 尝试使用html2canvas
                if (window.html2canvas) {
                    const captureOptions = {
                        useCORS: true,
                        allowTaint: true,
                        scale: this.options.snapshotQuality,
                        width: window.innerWidth,
                        height: window.innerHeight,
                        scrollX: 0,
                        scrollY: 0,
                        backgroundColor: null
                    };
                    
                    html2canvas(document.body, captureOptions)
                        .then(canvas => {
                            this.log('Snapshot captured successfully');
                            this.storeSnapshot(window.location.href, canvas);
                            resolve(canvas);
                        })
                        .catch(error => {
                            this.log('html2canvas failed, using fallback:', error);
                            resolve(this.createPlaceholderSnapshot());
                        });
                } else {
                    this.log('html2canvas not available, using placeholder');
                    resolve(this.createPlaceholderSnapshot());
                }
            } catch (error) {
                this.log('Snapshot capture failed:', error);
                resolve(this.createPlaceholderSnapshot());
            }
        });
    }

    /**
     * 创建占位符快照
     */
    createPlaceholderSnapshot() {
        const canvas = document.createElement('canvas');
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        const ctx = canvas.getContext('2d');
        
        // 创建渐变背景
        const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
        gradient.addColorStop(0, '#1e3c72');
        gradient.addColorStop(0.5, '#2a5298');
        gradient.addColorStop(1, '#764ba2');
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // 添加装饰元素
        ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
        for (let i = 0; i < 20; i++) {
            const x = Math.random() * canvas.width;
            const y = Math.random() * canvas.height;
            const radius = Math.random() * 50 + 10;
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, Math.PI * 2);
            ctx.fill();
        }
        
        // 添加文字
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.font = 'bold 32px -apple-system, BlinkMacSystemFont, sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('页面快照', canvas.width / 2, canvas.height / 2);
        
        return canvas;
    }

    /**
     * 准备目标内容
     */
    async prepareTargetContent(target) {
        if (typeof target === 'string') {
            // URL字符串，需要预加载
            return await this.preloadPage(target);
        } else if (target instanceof HTMLElement) {
            // 直接使用元素
            return target;
        } else {
            throw new Error('Invalid target type');
        }
    }

    /**
     * 预加载页面
     */
    async preloadPage(url) {
        return new Promise((resolve, reject) => {
            // 检查是否有缓存的快照
            if (this.snapshotHistory.has(url)) {
                this.log('Using cached snapshot for', url);
                resolve(this.snapshotHistory.get(url));
                return;
            }
            
            const iframe = document.createElement('iframe');
            iframe.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border: none;
                opacity: 0;
                pointer-events: none;
                z-index: -1;
            `;
            
            const timeout = setTimeout(() => {
                this.log('Preload timeout for', url);
                cleanup();
                resolve(this.createLoadingPlaceholder());
            }, 3000);
            
            const cleanup = () => {
                clearTimeout(timeout);
                if (iframe.parentNode) {
                    iframe.parentNode.removeChild(iframe);
                }
            };
            
            iframe.onload = () => {
                this.log('Page preloaded successfully:', url);
                cleanup();
                
                // 创建目标内容容器
                const container = document.createElement('div');
                container.style.cssText = `
                    width: 100%;
                    height: 100%;
                    background: white;
                    position: relative;
                `;
                
                // 尝试复制iframe内容
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    if (iframeDoc && iframeDoc.body) {
                        container.innerHTML = iframeDoc.body.innerHTML;
                        
                        // 复制样式
                        const styles = iframeDoc.head.innerHTML;
                        const styleContainer = document.createElement('div');
                        styleContainer.innerHTML = styles;
                        container.appendChild(styleContainer);
                    }
                } catch (e) {
                    this.log('Cannot access iframe content due to CORS:', e);
                    container.appendChild(this.createLoadingPlaceholder());
                }
                
                resolve(container);
            };
            
            iframe.onerror = () => {
                this.log('Failed to preload page:', url);
                cleanup();
                resolve(this.createLoadingPlaceholder());
            };
            
            iframe.src = url;
            document.body.appendChild(iframe);
        });
    }

    /**
     * 创建加载占位符
     */
    createLoadingPlaceholder() {
        const container = document.createElement('div');
        container.style.cssText = `
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
        `;
        
        container.innerHTML = `
            <div style="text-align: center;">
                <div style="font-size: 24px; margin-bottom: 20px;">正在加载新页面...</div>
                <div style="
                    width: 40px;
                    height: 40px;
                    border: 3px solid rgba(255,255,255,0.3);
                    border-top: 3px solid white;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto;
                "></div>
            </div>
            <style>
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        `;
        
        return container;
    }

    /**
     * 执行过渡动画
     */
    async executeTransition(currentSnapshot, targetContent, effectType, options) {
        return new Promise((resolve) => {
            // 设置过渡容器
            this.transitionContainer.style.pointerEvents = 'auto';
            this.transitionContainer.innerHTML = '';
            
            // 创建快照层
            const snapshotLayer = document.createElement('div');
            snapshotLayer.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 2;
                overflow: hidden;
            `;
            
            if (currentSnapshot instanceof HTMLCanvasElement) {
                currentSnapshot.style.cssText = `
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    display: block;
                `;
                snapshotLayer.appendChild(currentSnapshot);
            } else {
                snapshotLayer.appendChild(currentSnapshot);
            }
            
            // 创建目标层
            const targetLayer = document.createElement('div');
            targetLayer.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 1;
                overflow: hidden;
            `;
            
            targetLayer.appendChild(targetContent);
            
            this.transitionContainer.appendChild(targetLayer);
            this.transitionContainer.appendChild(snapshotLayer);
            
            // 执行具体的过渡效果
            this.executeEffect(snapshotLayer, effectType, options, () => {
                this.cleanupTransition();
                resolve();
            });
        });
    }

    /**
     * 执行具体的过渡效果
     */
    executeEffect(snapshotLayer, effectType, options, onComplete) {
        const duration = options.duration || this.options.transitionDuration;
        
        const effects = {
            // 滑动面板擦除 - 自然风格
            sliding_panel: () => {
                gsap.set(snapshotLayer, { 
                    clipPath: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)' 
                });
                gsap.to(snapshotLayer, {
                    clipPath: 'polygon(100% 0%, 100% 0%, 100% 100%, 100% 100%)',
                    duration: duration,
                    ease: 'power2.inOut',
                    onComplete
                });
            },
            
            // 斜切面板擦除 - 赛博朋克风格
            angled_panel: () => {
                gsap.set(snapshotLayer, { 
                    clipPath: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)' 
                });
                gsap.to(snapshotLayer, {
                    clipPath: 'polygon(100% 0%, 110% 0%, 110% 100%, 100% 100%)',
                    duration: duration * 0.8,
                    ease: 'power3.inOut',
                    onComplete
                });
            },
            
            // 圆形扩展擦除 - 玻璃拟态/可爱风格
            circle_expand: () => {
                const centerX = options.centerX || 50;
                const centerY = options.centerY || 50;
                
                gsap.set(snapshotLayer, { 
                    clipPath: `circle(150% at ${centerX}% ${centerY}%)` 
                });
                gsap.to(snapshotLayer, {
                    clipPath: `circle(0% at ${centerX}% ${centerY}%)`,
                    duration: duration * 1.2,
                    ease: 'power2.inOut',
                    onComplete
                });
            },
            
            // 幕布开启擦除 - 新拟态风格
            curtain_open: () => {
                gsap.set(snapshotLayer, { 
                    clipPath: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)' 
                });
                gsap.to(snapshotLayer, {
                    clipPath: 'polygon(50% 0%, 50% 0%, 50% 100%, 50% 100%)',
                    duration: duration * 1.5,
                    ease: 'power2.inOut',
                    onComplete
                });
            },
            
            // 波纹扩散 - 新增效果
            ripple_expand: () => {
                const centerX = options.centerX || 50;
                const centerY = options.centerY || 50;
                
                // 创建多层波纹效果
                const ripples = [];
                for (let i = 0; i < 3; i++) {
                    const ripple = snapshotLayer.cloneNode(true);
                    ripple.style.zIndex = 2 - i;
                    this.transitionContainer.appendChild(ripple);
                    ripples.push(ripple);
                }
                
                ripples.forEach((ripple, index) => {
                    gsap.set(ripple, { 
                        clipPath: `circle(150% at ${centerX}% ${centerY}%)`,
                        opacity: 1 - index * 0.3
                    });
                    gsap.to(ripple, {
                        clipPath: `circle(0% at ${centerX}% ${centerY}%)`,
                        duration: duration + index * 0.2,
                        ease: 'power2.out',
                        delay: index * 0.1,
                        onComplete: index === 0 ? onComplete : undefined
                    });
                });
            },
            
            // 默认淡出效果
            fade: () => {
                gsap.to(snapshotLayer, {
                    opacity: 0,
                    duration: duration * 0.6,
                    ease: 'power2.inOut',
                    onComplete
                });
            }
        };
        
        const effect = effects[effectType] || effects.fade;
        this.log(`Executing effect: ${effectType}`);
        effect();
    }

    /**
     * 清理过渡效果
     */
    cleanupTransition() {
        setTimeout(() => {
            this.transitionContainer.style.pointerEvents = 'none';
            this.transitionContainer.innerHTML = '';
        }, 100);
    }

    /**
     * 完成页面切换
     */
    async completeTransition(target) {
        if (typeof target === 'string') {
            // 如果是URL，进行页面导航
            window.location.href = target;
        }
        // 如果是元素，已经在过渡中处理了
    }

    /**
     * 回退过渡方案
     */
    async fallbackTransition(target) {
        this.log('Using fallback transition');
        if (typeof target === 'string') {
            window.location.href = target;
        }
    }

    /**
     * 存储快照到历史记录
     */
    storeSnapshot(url, snapshot) {
        if (this.snapshotHistory.size >= this.maxSnapshotHistory) {
            const firstKey = this.snapshotHistory.keys().next().value;
            this.snapshotHistory.delete(firstKey);
        }
        this.snapshotHistory.set(url, snapshot);
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 监听页面链接点击
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[data-transition]');
            if (link && !this.isTransitioning) {
                e.preventDefault();
                
                const url = link.href;
                const effect = link.dataset.transition || 'circle_expand';
                const options = {};
                
                // 如果是圆形扩展，计算点击位置
                if (effect === 'circle_expand' || effect === 'ripple_expand') {
                    const rect = link.getBoundingClientRect();
                    options.centerX = ((rect.left + rect.width / 2) / window.innerWidth) * 100;
                    options.centerY = ((rect.top + rect.height / 2) / window.innerHeight) * 100;
                }
                
                this.transitionToPage(url, effect, options);
            }
        });
        
        // 键盘快捷键支持
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key >= '1' && e.key <= '5' && !this.isTransitioning) {
                e.preventDefault();
                
                const pages = [
                    '1_natural_commute.html',
                    '2_cyberpunk_drive.html',
                    '3_glassmorphism_wait.html',
                    '4_neumorphism_rainy.html',
                    '5_kawaii_family_trip.html'
                ];
                
                const effects = [
                    'sliding_panel',
                    'angled_panel',
                    'circle_expand',
                    'curtain_open',
                    'ripple_expand'
                ];
                
                const index = parseInt(e.key) - 1;
                if (pages[index]) {
                    const basePath = window.location.pathname.replace(/\/[^/]*$/, '');
                    const targetUrl = basePath + '/' + pages[index];
                    this.transitionToPage(targetUrl, effects[index]);
                }
            }
        });
    }

    /**
     * 设置性能监控
     */
    setupPerformanceMonitoring() {
        if (this.options.enableDebug) {
            // 监控内存使用
            setInterval(() => {
                if (performance.memory) {
                    this.log('Memory usage:', {
                        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
                        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB'
                    });
                }
            }, 10000);
        }
    }

    /**
     * 日志输出
     */
    log(...args) {
        if (this.options.enableDebug) {
            console.log('[EnhancedTransitionManager]', ...args);
        }
    }

    /**
     * 手动触发过渡
     */
    triggerTransition(target, effectType = 'circle_expand', options = {}) {
        return this.transitionToPage(target, effectType, options);
    }

    /**
     * 获取快照历史
     */
    getSnapshotHistory() {
        return Array.from(this.snapshotHistory.keys());
    }

    /**
     * 清理快照历史
     */
    clearSnapshotHistory() {
        this.snapshotHistory.clear();
        this.log('Snapshot history cleared');
    }

    /**
     * 销毁管理器
     */
    destroy() {
        if (this.transitionContainer && this.transitionContainer.parentNode) {
            this.transitionContainer.parentNode.removeChild(this.transitionContainer);
        }
        this.clearSnapshotHistory();
        this.log('Enhanced Transition Manager destroyed');
    }
}

// 自动初始化
if (typeof window !== 'undefined') {
    window.EnhancedTransitionManager = EnhancedTransitionManager;
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            if (!window.enhancedTransitionManager) {
                window.enhancedTransitionManager = new EnhancedTransitionManager({
                    enableDebug: true,
                    enableSnapshots: true
                });
            }
        });
    } else {
        if (!window.enhancedTransitionManager) {
            window.enhancedTransitionManager = new EnhancedTransitionManager({
                enableDebug: true,
                enableSnapshots: true
            });
        }
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedTransitionManager;
}