<template>
  <BaseCard
    :card-type="'navigation'"
    :size="size"
    :position="position"
    :theme="theme"
    :theme-colors="themeColors"
    :show-header="true"
    :show-footer="false"
    :title="cardTitle"
    :icon="'fas fa-route'"
    class="navigation-card"
  >
    <div class="navigation-container" :class="[`size-${size}`, `mode-${displayMode}`]">
      <!-- 当前导航状态 -->
      <div class="navigation-status" v-if="currentNavigation">
        <div class="status-header">
          <div class="destination-info">
            <h3 class="destination-name">{{ currentNavigation.destination.name }}</h3>
            <div class="destination-address">{{ currentNavigation.destination.address }}</div>
          </div>
          <div class="navigation-mode" :class="currentNavigation.mode">
            <i :class="getModeIcon(currentNavigation.mode)"></i>
            <span>{{ getModeText(currentNavigation.mode) }}</span>
          </div>
        </div>
        
        <div class="route-summary">
          <div class="summary-item">
            <div class="summary-value">{{ currentNavigation.eta }}</div>
            <div class="summary-label">预计到达</div>
          </div>
          <div class="summary-item">
            <div class="summary-value">{{ currentNavigation.distance }}</div>
            <div class="summary-label">剩余距离</div>
          </div>
          <div class="summary-item">
            <div class="summary-value">{{ currentNavigation.duration }}</div>
            <div class="summary-label">剩余时间</div>
          </div>
          <div class="summary-item">
            <div class="summary-value">{{ currentNavigation.traffic }}</div>
            <div class="summary-label">路况</div>
          </div>
        </div>
      </div>

      <!-- 路线预览 -->
      <div class="route-preview" v-if="showRoutePreview && currentNavigation">
        <div class="preview-header">
          <span>路线预览</span>
          <button @click="toggleRouteView" class="view-toggle-btn">
            <i :class="routeViewMode === 'map' ? 'fas fa-list' : 'fas fa-map'"></i>
          </button>
        </div>
        
        <!-- 地图视图 -->
        <div v-if="routeViewMode === 'map'" class="map-view">
          <div class="map-container">
            <!-- 模拟地图显示 -->
            <div class="map-placeholder">
              <div class="route-line"></div>
              <div class="start-point">
                <i class="fas fa-circle"></i>
                <span>起点</span>
              </div>
              <div class="end-point">
                <i class="fas fa-map-marker-alt"></i>
                <span>{{ currentNavigation.destination.name }}</span>
              </div>
              <div class="current-position">
                <i class="fas fa-location-arrow"></i>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 列表视图 -->
        <div v-else class="directions-list">
          <div 
            v-for="(step, index) in currentNavigation.directions" 
            :key="index"
            :class="['direction-step', { current: index === currentStepIndex }]"
          >
            <div class="step-icon">
              <i :class="getDirectionIcon(step.action)"></i>
            </div>
            <div class="step-content">
              <div class="step-instruction">{{ step.instruction }}</div>
              <div class="step-distance">{{ step.distance }}</div>
            </div>
            <div class="step-status" v-if="index === currentStepIndex">
              <div class="status-indicator"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 交通信息 -->
      <div class="traffic-info" v-if="showTrafficInfo">
        <div class="traffic-header">
          <i class="fas fa-traffic-light"></i>
          <span>实时路况</span>
        </div>
        
        <div class="traffic-alerts">
          <div 
            v-for="alert in trafficAlerts" 
            :key="alert.id"
            :class="['traffic-alert', `severity-${alert.severity}`]"
          >
            <div class="alert-icon">
              <i :class="getAlertIcon(alert.type)"></i>
            </div>
            <div class="alert-content">
              <div class="alert-title">{{ alert.title }}</div>
              <div class="alert-description">{{ alert.description }}</div>
              <div class="alert-impact">影响时间: {{ alert.impact }}</div>
            </div>
          </div>
        </div>
        
        <div class="alternative-routes" v-if="alternativeRoutes.length > 0">
          <div class="alternatives-header">备选路线</div>
          <div 
            v-for="route in alternativeRoutes" 
            :key="route.id"
            @click="selectAlternativeRoute(route)"
            class="alternative-route"
          >
            <div class="route-info">
              <div class="route-name">{{ route.name }}</div>
              <div class="route-details">
                <span class="route-time">{{ route.duration }}</span>
                <span class="route-distance">{{ route.distance }}</span>
                <span :class="['route-traffic', route.trafficLevel]">
                  {{ getTrafficText(route.trafficLevel) }}
                </span>
              </div>
            </div>
            <div class="route-savings" v-if="route.timeSaved">
              <span class="savings-text">节省 {{ route.timeSaved }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="quick-actions" v-if="showQuickActions">
        <button 
          v-for="action in quickActions" 
          :key="action.id"
          @click="handleQuickAction(action)"
          :class="['quick-action-btn', action.type]"
          :disabled="isProcessing"
        >
          <i :class="action.icon"></i>
          <span>{{ action.label }}</span>
        </button>
      </div>

      <!-- AI导航建议 -->
      <div class="ai-navigation-suggestions" v-if="aiSuggestions.length > 0">
        <div class="suggestions-header">
          <i class="fas fa-magic"></i>
          <span>AI导航建议</span>
        </div>
        <div class="suggestions-list">
          <div
            v-for="suggestion in aiSuggestions"
            :key="suggestion.id"
            class="suggestion-item"
          >
            <div class="suggestion-content">
              <div class="suggestion-text">{{ suggestion.text }}</div>
              <div class="suggestion-meta">
                <span class="time-saved">节省 {{ suggestion.timeSaved }}</span>
                <span class="confidence">可信度 {{ Math.round(suggestion.confidence * 100) }}%</span>
              </div>
            </div>
            <button 
              @click="applySuggestion(suggestion)"
              class="apply-suggestion-btn"
              :disabled="isProcessing"
            >
              <i class="fas fa-check"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 无导航状态 -->
      <div v-if="!currentNavigation" class="no-navigation">
        <div class="no-nav-icon">
          <i class="fas fa-map-marked-alt"></i>
        </div>
        <div class="no-nav-text">
          <h3>开始导航</h3>
          <p>输入目的地开始您的旅程</p>
        </div>
        <div class="destination-input">
          <input 
            v-model="destinationInput"
            @keyup.enter="startNavigation"
            placeholder="输入目的地..."
            class="destination-field"
          >
          <button @click="startNavigation" class="start-nav-btn" :disabled="!destinationInput.trim()">
            <i class="fas fa-search"></i>
          </button>
        </div>
        
        <div class="recent-destinations" v-if="recentDestinations.length > 0">
          <div class="recent-header">最近目的地</div>
          <div class="recent-list">
            <button
              v-for="destination in recentDestinations"
              :key="destination.id"
              @click="selectDestination(destination)"
              class="recent-item"
            >
              <div class="recent-icon">
                <i :class="destination.icon"></i>
              </div>
              <div class="recent-info">
                <div class="recent-name">{{ destination.name }}</div>
                <div class="recent-address">{{ destination.address }}</div>
              </div>
            </button>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">{{ loadingText }}</div>
      </div>
    </div>
  </BaseCard>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import BaseCard from '@/components/cards/BaseCard.vue'
import mockDataService from '@/services/MockDataService.js'

export default {
  name: 'NavigationCard',
  components: {
    BaseCard
  },
  props: {
    size: {
      type: String,
      default: 'large',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    position: {
      type: Object,
      default: () => ({ x: 0, y: 0 })
    },
    theme: {
      type: String,
      default: 'glassmorphism'
    },
    themeColors: {
      type: Object,
      default: () => ({})
    },
    displayMode: {
      type: String,
      default: 'full',
      validator: (value) => ['compact', 'standard', 'full'].includes(value)
    },
    showRoutePreview: {
      type: Boolean,
      default: true
    },
    showTrafficInfo: {
      type: Boolean,
      default: true
    },
    showQuickActions: {
      type: Boolean,
      default: true
    },
    autoRefresh: {
      type: Boolean,
      default: true
    },
    refreshInterval: {
      type: Number,
      default: 30000 // 30秒
    }
  },
  emits: ['navigation-start', 'navigation-end', 'route-changed', 'suggestion-applied'],
  setup(props, { emit }) {
    // 响应式数据
    const isLoading = ref(false)
    const isProcessing = ref(false)
    const loadingText = ref('正在规划路线...')
    const routeViewMode = ref('map')
    const currentStepIndex = ref(0)
    const destinationInput = ref('')
    
    const currentNavigation = ref(null)
    const trafficAlerts = ref([])
    const alternativeRoutes = ref([])
    const aiSuggestions = ref([])
    const recentDestinations = ref([])
    
    const quickActions = ref([
      {
        id: 1,
        label: '重新规划',
        icon: 'fas fa-redo',
        type: 'primary',
        action: 'replan'
      },
      {
        id: 2,
        label: '语音导航',
        icon: 'fas fa-volume-up',
        type: 'secondary',
        action: 'voice'
      },
      {
        id: 3,
        label: '分享位置',
        icon: 'fas fa-share',
        type: 'secondary',
        action: 'share'
      },
      {
        id: 4,
        label: '结束导航',
        icon: 'fas fa-stop',
        type: 'danger',
        action: 'stop'
      }
    ])

    // 计算属性
    const cardTitle = computed(() => {
      if (currentNavigation.value) {
        return `导航中 - ${currentNavigation.value.destination.name}`
      }
      return '导航'
    })

    // 方法
    const loadNavigationData = async () => {
      try {
        isLoading.value = true
        loadingText.value = '正在获取导航信息...'
        
        const navigationData = await mockDataService.getNavigationData()
        
        currentNavigation.value = navigationData.currentNavigation || null
        trafficAlerts.value = navigationData.trafficAlerts || []
        alternativeRoutes.value = navigationData.alternativeRoutes || []
        aiSuggestions.value = navigationData.aiSuggestions || []
        recentDestinations.value = navigationData.recentDestinations || []
        
      } catch (error) {
        console.error('Failed to load navigation data:', error)
      } finally {
        isLoading.value = false
      }
    }
    
    const getModeIcon = (mode) => {
      const icons = {
        driving: 'fas fa-car',
        walking: 'fas fa-walking',
        transit: 'fas fa-bus',
        cycling: 'fas fa-bicycle'
      }
      return icons[mode] || 'fas fa-route'
    }
    
    const getModeText = (mode) => {
      const texts = {
        driving: '驾车',
        walking: '步行',
        transit: '公交',
        cycling: '骑行'
      }
      return texts[mode] || '导航'
    }
    
    const getDirectionIcon = (action) => {
      const icons = {
        straight: 'fas fa-arrow-up',
        left: 'fas fa-arrow-left',
        right: 'fas fa-arrow-right',
        uturn: 'fas fa-undo',
        merge: 'fas fa-code-branch',
        exit: 'fas fa-sign-out-alt'
      }
      return icons[action] || 'fas fa-arrow-up'
    }
    
    const getAlertIcon = (type) => {
      const icons = {
        accident: 'fas fa-car-crash',
        construction: 'fas fa-hard-hat',
        traffic: 'fas fa-traffic-light',
        weather: 'fas fa-cloud-rain',
        closure: 'fas fa-ban'
      }
      return icons[type] || 'fas fa-exclamation-triangle'
    }
    
    const getTrafficText = (level) => {
      const texts = {
        light: '畅通',
        moderate: '缓慢',
        heavy: '拥堵',
        severe: '严重拥堵'
      }
      return texts[level] || '未知'
    }
    
    const toggleRouteView = () => {
      routeViewMode.value = routeViewMode.value === 'map' ? 'list' : 'map'
    }
    
    const startNavigation = async () => {
      if (!destinationInput.value.trim()) return
      
      try {
        isLoading.value = true
        loadingText.value = '正在规划路线...'
        
        const destination = {
          name: destinationInput.value.trim(),
          address: destinationInput.value.trim()
        }
        
        const navigationResult = await mockDataService.startNavigation(destination)
        
        currentNavigation.value = navigationResult
        destinationInput.value = ''
        
        emit('navigation-start', navigationResult)
        
      } catch (error) {
        console.error('Failed to start navigation:', error)
      } finally {
        isLoading.value = false
      }
    }
    
    const selectDestination = async (destination) => {
      try {
        isLoading.value = true
        loadingText.value = '正在规划路线...'
        
        const navigationResult = await mockDataService.startNavigation(destination)
        
        currentNavigation.value = navigationResult
        
        emit('navigation-start', navigationResult)
        
      } catch (error) {
        console.error('Failed to start navigation:', error)
      } finally {
        isLoading.value = false
      }
    }
    
    const selectAlternativeRoute = async (route) => {
      try {
        isProcessing.value = true
        
        const updatedNavigation = await mockDataService.selectRoute(route.id)
        
        currentNavigation.value = updatedNavigation
        
        emit('route-changed', route)
        
        // 重新加载数据以获取新的备选路线
        await loadNavigationData()
        
      } catch (error) {
        console.error('Failed to select alternative route:', error)
      } finally {
        isProcessing.value = false
      }
    }
    
    const handleQuickAction = async (action) => {
      switch (action.action) {
        case 'replan':
          await replanRoute()
          break
        case 'voice':
          toggleVoiceNavigation()
          break
        case 'share':
          shareLocation()
          break
        case 'stop':
          await stopNavigation()
          break
      }
    }
    
    const replanRoute = async () => {
      if (!currentNavigation.value) return
      
      try {
        isProcessing.value = true
        loadingText.value = '正在重新规划路线...'
        
        const updatedNavigation = await mockDataService.replanRoute()
        
        currentNavigation.value = updatedNavigation
        
        await loadNavigationData()
        
      } catch (error) {
        console.error('Failed to replan route:', error)
      } finally {
        isProcessing.value = false
      }
    }
    
    const toggleVoiceNavigation = () => {
      if (currentNavigation.value) {
        currentNavigation.value.voiceEnabled = !currentNavigation.value.voiceEnabled
      }
    }
    
    const shareLocation = () => {
      // 模拟分享位置功能
      console.log('Sharing current location...')
    }
    
    const stopNavigation = async () => {
      try {
        isProcessing.value = true
        
        await mockDataService.stopNavigation()
        
        const stoppedNavigation = currentNavigation.value
        currentNavigation.value = null
        currentStepIndex.value = 0
        
        emit('navigation-end', stoppedNavigation)
        
      } catch (error) {
        console.error('Failed to stop navigation:', error)
      } finally {
        isProcessing.value = false
      }
    }
    
    const applySuggestion = async (suggestion) => {
      try {
        isProcessing.value = true
        
        await mockDataService.applySuggestion(suggestion.id)
        
        // 从建议列表中移除
        const index = aiSuggestions.value.findIndex(s => s.id === suggestion.id)
        if (index > -1) {
          aiSuggestions.value.splice(index, 1)
        }
        
        emit('suggestion-applied', suggestion)
        
        // 重新加载导航数据
        await loadNavigationData()
        
      } catch (error) {
        console.error('Failed to apply suggestion:', error)
      } finally {
        isProcessing.value = false
      }
    }
    
    const updateNavigationProgress = () => {
      if (currentNavigation.value && currentNavigation.value.directions) {
        // 模拟导航进度更新
        const totalSteps = currentNavigation.value.directions.length
        if (currentStepIndex.value < totalSteps - 1) {
          // 随机推进到下一步（实际应用中基于GPS位置）
          if (Math.random() < 0.1) { // 10%概率推进
            currentStepIndex.value++
          }
        }
      }
    }

    // 生命周期
    let refreshTimer = null
    let progressTimer = null
    
    onMounted(async () => {
      await mockDataService.initialize()
      await loadNavigationData()
      
      // 设置自动刷新
      if (props.autoRefresh) {
        refreshTimer = setInterval(() => {
          loadNavigationData()
        }, props.refreshInterval)
        
        // 设置导航进度更新
        progressTimer = setInterval(() => {
          updateNavigationProgress()
        }, 5000) // 每5秒检查一次进度
      }
    })

    onUnmounted(() => {
      if (refreshTimer) {
        clearInterval(refreshTimer)
      }
      if (progressTimer) {
        clearInterval(progressTimer)
      }
    })

    return {
      // 响应式数据
      isLoading,
      isProcessing,
      loadingText,
      routeViewMode,
      currentStepIndex,
      destinationInput,
      currentNavigation,
      trafficAlerts,
      alternativeRoutes,
      aiSuggestions,
      recentDestinations,
      quickActions,
      
      // 计算属性
      cardTitle,
      
      // 方法
      getModeIcon,
      getModeText,
      getDirectionIcon,
      getAlertIcon,
      getTrafficText,
      toggleRouteView,
      startNavigation,
      selectDestination,
      selectAlternativeRoute,
      handleQuickAction,
      applySuggestion
    }
  }
}
</script>

<style scoped>
.navigation-card {
  height: 100%;
}

.navigation-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  position: relative;
}

/* 导航状态 */
.navigation-status {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.destination-info h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.destination-address {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.navigation-mode {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  background: rgba(74, 144, 226, 0.2);
  color: #4a90e2;
}

.route-summary {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.summary-item {
  text-align: center;
}

.summary-value {
  font-size: 16px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 4px;
}

.summary-label {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
}

/* 路线预览 */
.route-preview {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  padding: 16px;
  flex: 1;
  min-height: 0;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.view-toggle-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.view-toggle-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.9);
}

/* 地图视图 */
.map-view {
  height: 200px;
}

.map-container {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.map-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.route-line {
  position: absolute;
  top: 30%;
  left: 20%;
  right: 20%;
  height: 3px;
  background: linear-gradient(90deg, #4a90e2 0%, #7ed321 100%);
  border-radius: 2px;
  transform: rotate(-15deg);
}

.start-point,
.end-point {
  position: absolute;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  color: white;
  background: rgba(0, 0, 0, 0.5);
  padding: 4px 8px;
  border-radius: 12px;
}

.start-point {
  top: 20%;
  left: 15%;
  color: #4a90e2;
}

.end-point {
  bottom: 20%;
  right: 15%;
  color: #7ed321;
}

.current-position {
  position: absolute;
  top: 50%;
  left: 40%;
  color: #ff6b6b;
  font-size: 16px;
  animation: pulse 2s infinite;
}

/* 方向列表 */
.directions-list {
  max-height: 200px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.direction-step {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.direction-step.current {
  background: rgba(74, 144, 226, 0.2);
  border-left: 3px solid #4a90e2;
}

.step-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  flex-shrink: 0;
}

.direction-step.current .step-icon {
  background: rgba(74, 144, 226, 0.3);
  color: #4a90e2;
}

.step-content {
  flex: 1;
}

.step-instruction {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2px;
}

.step-distance {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
}

.step-status {
  width: 8px;
  height: 8px;
}

.status-indicator {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #4a90e2;
  animation: pulse 1.5s infinite;
}

/* 交通信息 */
.traffic-info {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 16px;
}

.traffic-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 12px;
}

.traffic-alerts {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.traffic-alert {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px;
  border-radius: 8px;
  border-left: 3px solid transparent;
}

.traffic-alert.severity-low {
  background: rgba(16, 185, 129, 0.1);
  border-left-color: #10b981;
}

.traffic-alert.severity-medium {
  background: rgba(245, 158, 11, 0.1);
  border-left-color: #f59e0b;
}

.traffic-alert.severity-high {
  background: rgba(239, 68, 68, 0.1);
  border-left-color: #ef4444;
}

.alert-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  flex-shrink: 0;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2px;
}

.alert-description {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 2px;
}

.alert-impact {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
}

/* 备选路线 */
.alternative-routes {
  margin-top: 12px;
}

.alternatives-header {
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8px;
}

.alternative-route {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 4px;
}

.alternative-route:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(2px);
}

.route-info {
  flex: 1;
}

.route-name {
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2px;
}

.route-details {
  display: flex;
  gap: 8px;
  font-size: 10px;
}

.route-time {
  color: rgba(255, 255, 255, 0.8);
}

.route-distance {
  color: rgba(255, 255, 255, 0.6);
}

.route-traffic.light {
  color: #10b981;
}

.route-traffic.moderate {
  color: #f59e0b;
}

.route-traffic.heavy {
  color: #ef4444;
}

.route-savings {
  font-size: 10px;
  color: #7ed321;
}

/* 快速操作 */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-action-btn.primary {
  background: rgba(74, 144, 226, 0.2);
  color: #4a90e2;
}

.quick-action-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
}

.quick-action-btn.danger {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.quick-action-btn:hover {
  transform: translateY(-1px);
}

.quick-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* AI建议 */
.ai-navigation-suggestions {
  background: rgba(126, 211, 33, 0.1);
  border-radius: 12px;
  padding: 16px;
}

.suggestions-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #7ed321;
  margin-bottom: 12px;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.suggestion-content {
  flex: 1;
}

.suggestion-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 4px;
}

.suggestion-meta {
  display: flex;
  gap: 12px;
  font-size: 10px;
}

.time-saved {
  color: #7ed321;
}

.confidence {
  color: #4a90e2;
}

.apply-suggestion-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background: rgba(126, 211, 33, 0.3);
  color: #7ed321;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.apply-suggestion-btn:hover {
  background: rgba(126, 211, 33, 0.5);
  transform: scale(1.05);
}

/* 无导航状态 */
.no-navigation {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 32px 16px;
  flex: 1;
}

.no-nav-icon {
  font-size: 48px;
  color: rgba(255, 255, 255, 0.3);
  margin-bottom: 16px;
}

.no-nav-text h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
}

.no-nav-text p {
  margin: 0 0 24px 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

.destination-input {
  display: flex;
  gap: 8px;
  width: 100%;
  max-width: 300px;
  margin-bottom: 24px;
}

.destination-field {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  outline: none;
  transition: all 0.3s ease;
}

.destination-field::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.destination-field:focus {
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.3);
}

.start-nav-btn {
  padding: 12px;
  border: none;
  border-radius: 8px;
  background: rgba(74, 144, 226, 0.3);
  color: #4a90e2;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.start-nav-btn:hover:not(:disabled) {
  background: rgba(74, 144, 226, 0.5);
  transform: scale(1.05);
}

.start-nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 最近目的地 */
.recent-destinations {
  width: 100%;
  max-width: 300px;
}

.recent-header {
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8px;
  text-align: left;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.recent-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
}

.recent-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(2px);
}

.recent-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.recent-info {
  flex: 1;
}

.recent-name {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 2px;
}

.recent-address {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  border-radius: 12px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-top: 3px solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

/* 动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
.size-small .navigation-container {
  padding: 12px;
  gap: 12px;
}

.size-small .route-summary {
  grid-template-columns: repeat(2, 1fr);
}

.size-small .quick-actions {
  grid-template-columns: 1fr;
}

.mode-compact .route-preview,
.mode-compact .traffic-info,
.mode-compact .ai-navigation-suggestions {
  display: none;
}

.mode-compact .quick-actions {
  grid-template-columns: repeat(4, 1fr);
}
</style>