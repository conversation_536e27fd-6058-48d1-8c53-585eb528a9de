# AI-HMI 玻璃态主题系统

基于Vue 3的智能车载界面系统，集成了文生图API和玻璃态主题，支持语音交互。

## 功能特性

- 🎨 **动态玻璃态主题**: 根据生成的壁纸自动调整卡片样式
- 🖼️ **AI壁纸生成**: 集成文生图API，根据用户描述生成个性化壁纸
- 🎤 **语音交互**: 支持中文语音识别和语音合成
- 🔧 **模块化设计**: 组件化架构，易于扩展和维护
- 📱 **响应式设计**: 适配不同屏幕尺寸的车载显示

## 快速开始

### 安装依赖

```bash
npm install
```

### 环境配置

复制环境变量配置文件：

```bash
cp .env.example .env
```

根据实际情况修改 `.env` 文件中的配置。

### 开发环境

```bash
npm run serve
```

### 生产构建

```bash
npm run build
```

### 代码检查

```bash
npm run lint
```

## 项目结构

```
ai-hmi/
├── src/
│   ├── components/
│   │   ├── GlassThemeManager.vue      # 玻璃态主题管理器
│   │   ├── GlassCard.vue             # 玻璃态卡片组件
│   │   └── VoiceInteractionManager.vue # 语音交互管理器
│   ├── services/
│   │   ├── ImageGenerationService.js # 文生图服务
│   │   ├── AsrService.js             # 语音识别服务
│   │   ├── TtsService.js             # 语音合成服务
│   │   └── LlmService.js             # 大语言模型服务
│   ├── utils/
│   │   ├── ColorExtractor.js         # 颜色提取器
│   │   └── glassStyleUtils.js       # 玻璃态样式工具
│   ├── App.vue                       # 主应用组件
│   └── main.js                       # 应用入口
├── public/
│   └── images/
│       └── default-glass-wallpaper.jpg # 默认壁纸
├── docs/                             # 项目文档
└── package.json
```

## 核心组件

### GlassThemeManager
玻璃态主题管理器，负责：
- 壁纸生成和管理
- 颜色提取和分析
- 玻璃态样式计算
- 主题应用和更新

### GlassCard
通用玻璃态卡片组件，支持：
- 动态样式应用
- 悬停效果
- 自定义内容插槽

### VoiceInteractionManager
语音交互管理器，提供：
- 语音识别输入
- 语音合成反馈
- 文本输入备用方案
- 状态指示器

## 服务模块

### ImageGenerationService
集成文生图API，支持：
- 壁纸生成
- 提示词优化
- 错误处理和降级
- 生成历史管理

### ColorExtractor
颜色提取工具，功能包括：
- 图片颜色分析
- 玻璃态色彩计算
- 默认配色方案

### GlassStyleUtils
玻璃态样式工具，提供：
- 动态样式计算
- 颜色亮度分析
- 样式变体生成

## 配置说明

### 环境变量

- `VUE_APP_API_BASE_URL`: API基础地址
- `VUE_APP_LLM_API_KEY`: 大语言模型API密钥
- `VUE_APP_VOICE_LANG`: 语音语言设置

### API接口

系统集成了以下API接口：
- `/kolors/text-to-image`: 文生图接口
- 大语言模型对话接口
- 语音识别和合成接口

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 注意事项

1. **语音功能**: 需要浏览器支持Web Speech API
2. **跨域**: 确保API接口配置正确的CORS策略
3. **网络**: 文生图功能需要稳定的网络连接
4. **性能**: 图片生成和颜色提取是异步操作

## 开发指南

### 添加新组件

1. 在 `src/components/` 目录下创建新组件
2. 在 `App.vue` 中注册和使用
3. 遵循现有的代码风格和命名规范

### 扩展样式

1. 在 `glassStyleUtils.js` 中添加新的样式计算方法
2. 在 `ColorExtractor.js` 中扩展颜色分析功能
3. 更新样式配置以支持新的主题变体

### 集成新API

1. 在 `src/services/` 目录下创建新的服务类
2. 实现标准的接口方法
3. 在相应的组件中集成新服务

## 自定义配置

See [Configuration Reference](https://cli.vuejs.org/config/).

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
