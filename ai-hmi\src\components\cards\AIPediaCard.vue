<template>
  <BaseCard
    :card-type="'ai-pedia'"
    :size="size"
    :position="position"
    :theme="theme"
    :theme-colors="themeColors"
    :show-header="true"
    :show-footer="false"
    :title="cardTitle"
    :icon="'fas fa-brain'"
    class="ai-pedia-card"
  >
    <div class="pedia-container" :class="[`size-${size}`, `mode-${displayMode}`]">
      <!-- 搜索输入 -->
      <div class="search-section" v-if="showSearch">
        <div class="search-input-container">
          <input 
            v-model="searchQuery"
            @keyup.enter="performSearch"
            @input="onSearchInput"
            placeholder="问我任何问题..."
            class="search-input"
            :disabled="isSearching"
          >
          <button 
            @click="performSearch" 
            class="search-btn"
            :disabled="!searchQuery.trim() || isSearching"
          >
            <i :class="isSearching ? 'fas fa-spinner fa-spin' : 'fas fa-search'"></i>
          </button>
          <button 
            v-if="searchQuery"
            @click="clearSearch" 
            class="clear-btn"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <!-- 搜索建议 -->
        <div class="search-suggestions" v-if="searchSuggestions.length > 0 && !isSearching">
          <div class="suggestions-header">热门问题</div>
          <div class="suggestions-list">
            <button
              v-for="suggestion in searchSuggestions.slice(0, 3)"
              :key="suggestion.id"
              @click="selectSuggestion(suggestion)"
              class="suggestion-item"
            >
              <i :class="suggestion.icon"></i>
              <span>{{ suggestion.text }}</span>
            </button>
          </div>
        </div>
      </div>

      <!-- 当前问答 -->
      <div class="current-qa" v-if="currentAnswer">
        <div class="question-section">
          <div class="question-header">
            <i class="fas fa-question-circle"></i>
            <span>您的问题</span>
          </div>
          <div class="question-text">{{ currentAnswer.question }}</div>
        </div>
        
        <div class="answer-section">
          <div class="answer-header">
            <i class="fas fa-lightbulb"></i>
            <span>AI解答</span>
            <div class="confidence-indicator">
              <span class="confidence-text">可信度</span>
              <div class="confidence-bar">
                <div 
                  class="confidence-fill" 
                  :style="{ width: `${currentAnswer.confidence * 100}%` }"
                ></div>
              </div>
              <span class="confidence-value">{{ Math.round(currentAnswer.confidence * 100) }}%</span>
            </div>
          </div>
          
          <div class="answer-content">
            <div class="answer-text" v-html="formatAnswer(currentAnswer.answer)"></div>
            
            <!-- 相关图片 -->
            <div class="answer-images" v-if="currentAnswer.images && currentAnswer.images.length > 0">
              <div class="images-grid">
                <div 
                  v-for="(image, index) in currentAnswer.images.slice(0, 3)" 
                  :key="index"
                  class="image-item"
                  @click="viewImage(image)"
                >
                  <img :src="image.url" :alt="image.caption" />
                  <div class="image-caption">{{ image.caption }}</div>
                </div>
              </div>
            </div>
            
            <!-- 相关链接 -->
            <div class="related-links" v-if="currentAnswer.links && currentAnswer.links.length > 0">
              <div class="links-header">相关资料</div>
              <div class="links-list">
                <a 
                  v-for="link in currentAnswer.links.slice(0, 3)" 
                  :key="link.id"
                  :href="link.url"
                  target="_blank"
                  class="link-item"
                >
                  <i class="fas fa-external-link-alt"></i>
                  <span>{{ link.title }}</span>
                </a>
              </div>
            </div>
          </div>
          
          <div class="answer-actions">
            <button @click="likeAnswer" :class="['action-btn', { active: currentAnswer.liked }]">
              <i class="fas fa-thumbs-up"></i>
              <span>有用</span>
            </button>
            <button @click="dislikeAnswer" :class="['action-btn', { active: currentAnswer.disliked }]">
              <i class="fas fa-thumbs-down"></i>
              <span>无用</span>
            </button>
            <button @click="shareAnswer" class="action-btn">
              <i class="fas fa-share"></i>
              <span>分享</span>
            </button>
            <button @click="saveAnswer" class="action-btn">
              <i class="fas fa-bookmark"></i>
              <span>收藏</span>
            </button>
          </div>
        </div>
      </div>

      <!-- 知识分类 -->
      <div class="knowledge-categories" v-if="showCategories && !currentAnswer">
        <div class="categories-header">
          <h3>知识分类</h3>
          <div class="categories-subtitle">探索不同领域的知识</div>
        </div>
        
        <div class="categories-grid">
          <div 
            v-for="category in knowledgeCategories" 
            :key="category.id"
            @click="exploreCategory(category)"
            class="category-item"
          >
            <div class="category-icon">
              <i :class="category.icon"></i>
            </div>
            <div class="category-content">
              <div class="category-name">{{ category.name }}</div>
              <div class="category-description">{{ category.description }}</div>
              <div class="category-count">{{ category.count }}个话题</div>
            </div>
            <div class="category-arrow">
              <i class="fas fa-chevron-right"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 今日推荐 -->
      <div class="daily-recommendations" v-if="dailyRecommendations.length > 0">
        <div class="recommendations-header">
          <div class="header-content">
            <i class="fas fa-calendar-day"></i>
            <span>今日推荐</span>
          </div>
          <div class="date-info">{{ formatDate(new Date()) }}</div>
        </div>
        
        <div class="recommendations-list">
          <div 
            v-for="recommendation in dailyRecommendations" 
            :key="recommendation.id"
            @click="viewRecommendation(recommendation)"
            class="recommendation-item"
          >
            <div class="recommendation-icon">
              <i :class="recommendation.icon"></i>
            </div>
            
            <div class="recommendation-content">
              <div class="recommendation-title">{{ recommendation.title }}</div>
              <div class="recommendation-summary">{{ recommendation.summary }}</div>
              
              <div class="recommendation-meta">
                <div class="meta-item">
                  <i class="fas fa-tag"></i>
                  <span>{{ recommendation.category }}</span>
                </div>
                <div class="meta-item">
                  <i class="fas fa-clock"></i>
                  <span>{{ recommendation.readTime }}分钟阅读</span>
                </div>
                <div class="meta-item">
                  <i class="fas fa-eye"></i>
                  <span>{{ recommendation.views }}次浏览</span>
                </div>
              </div>
            </div>
            
            <div class="recommendation-badge" v-if="recommendation.badge">
              {{ recommendation.badge }}
            </div>
          </div>
        </div>
      </div>

      <!-- 搜索历史 -->
      <div class="search-history" v-if="showHistory && searchHistory.length > 0">
        <div class="history-header">
          <h3>最近搜索</h3>
          <button @click="clearHistory" class="clear-history-btn">
            <i class="fas fa-trash"></i>
            <span>清空</span>
          </button>
        </div>
        
        <div class="history-list">
          <div 
            v-for="item in searchHistory.slice(0, 5)" 
            :key="item.id"
            @click="searchFromHistory(item)"
            class="history-item"
          >
            <div class="history-icon">
              <i class="fas fa-history"></i>
            </div>
            
            <div class="history-content">
              <div class="history-question">{{ item.question }}</div>
              <div class="history-time">{{ formatTime(item.timestamp) }}</div>
            </div>
            
            <div class="history-actions">
              <button 
                @click.stop="removeFromHistory(item)"
                class="remove-btn"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 学习统计 -->
      <div class="learning-stats" v-if="showStats">
        <div class="stats-header">
          <i class="fas fa-chart-line"></i>
          <span>学习统计</span>
        </div>
        
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ learningStats.questionsAsked }}</div>
            <div class="stat-label">今日提问</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ learningStats.knowledgeGained }}</div>
            <div class="stat-label">知识点</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ learningStats.readingTime }}</div>
            <div class="stat-label">阅读时长</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ learningStats.streak }}</div>
            <div class="stat-label">连续天数</div>
          </div>
        </div>
        
        <div class="achievement-section" v-if="learningStats.achievements.length > 0">
          <div class="achievement-header">最新成就</div>
          <div class="achievement-list">
            <div 
              v-for="achievement in learningStats.achievements.slice(0, 2)" 
              :key="achievement.id"
              class="achievement-item"
            >
              <div class="achievement-icon">
                <i :class="achievement.icon"></i>
              </div>
              <div class="achievement-content">
                <div class="achievement-name">{{ achievement.name }}</div>
                <div class="achievement-description">{{ achievement.description }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 无内容状态 -->
      <div v-if="!hasAnyContent" class="no-content">
        <div class="no-content-icon">
          <i class="fas fa-brain"></i>
        </div>
        <div class="no-content-text">
          <h3>AI百科助手</h3>
          <p>问我任何问题，获取智能解答</p>
        </div>
        <div class="quick-start">
          <div class="quick-start-header">试试这些问题</div>
          <div class="quick-questions">
            <button
              v-for="question in quickStartQuestions"
              :key="question.id"
              @click="askQuickQuestion(question)"
              class="quick-question-btn"
            >
              {{ question.text }}
            </button>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="isSearching" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">{{ loadingText }}</div>
      </div>
    </div>
  </BaseCard>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import BaseCard from '@/components/cards/BaseCard.vue'
import mockDataService from '@/services/MockDataService.js'

export default {
  name: 'AIPediaCard',
  components: {
    BaseCard
  },
  props: {
    size: {
      type: String,
      default: 'large',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    position: {
      type: Object,
      default: () => ({ x: 0, y: 0 })
    },
    theme: {
      type: String,
      default: 'glassmorphism'
    },
    themeColors: {
      type: Object,
      default: () => ({})
    },
    displayMode: {
      type: String,
      default: 'full',
      validator: (value) => ['compact', 'standard', 'full'].includes(value)
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    showCategories: {
      type: Boolean,
      default: true
    },
    showHistory: {
      type: Boolean,
      default: true
    },
    showStats: {
      type: Boolean,
      default: true
    },
    autoRefresh: {
      type: Boolean,
      default: true
    },
    refreshInterval: {
      type: Number,
      default: 300000 // 5分钟
    }
  },
  emits: ['question-asked', 'answer-received', 'category-explored', 'knowledge-shared'],
  setup(props, { emit }) {
    // 响应式数据
    const isSearching = ref(false)
    const loadingText = ref('正在思考中...')
    const searchQuery = ref('')
    
    const currentAnswer = ref(null)
    const searchSuggestions = ref([])
    const knowledgeCategories = ref([])
    const dailyRecommendations = ref([])
    const searchHistory = ref([])
    const learningStats = ref({
      questionsAsked: 0,
      knowledgeGained: 0,
      readingTime: '0分钟',
      streak: 0,
      achievements: []
    })
    
    const quickStartQuestions = ref([
      { id: 1, text: '什么是人工智能？' },
      { id: 2, text: '如何保持健康的生活方式？' },
      { id: 3, text: '太阳系有多少颗行星？' },
      { id: 4, text: '如何学习编程？' }
    ])

    // 计算属性
    const cardTitle = computed(() => {
      if (currentAnswer.value) {
        return 'AI解答'
      }
      return 'AI百科'
    })
    
    const hasAnyContent = computed(() => {
      return currentAnswer.value || 
             searchSuggestions.value.length > 0 || 
             knowledgeCategories.value.length > 0 || 
             dailyRecommendations.value.length > 0 || 
             searchHistory.value.length > 0
    })

    // 方法
    const loadPediaData = async () => {
      try {
        const pediaData = await mockDataService.getPediaData()
        
        searchSuggestions.value = pediaData.searchSuggestions || []
        knowledgeCategories.value = pediaData.knowledgeCategories || []
        dailyRecommendations.value = pediaData.dailyRecommendations || []
        searchHistory.value = pediaData.searchHistory || []
        learningStats.value = { ...learningStats.value, ...pediaData.learningStats }
        
      } catch (error) {
        console.error('Failed to load pedia data:', error)
      }
    }
    
    const performSearch = async () => {
      if (!searchQuery.value.trim() || isSearching.value) return
      
      try {
        isSearching.value = true
        loadingText.value = '正在搜索知识库...'
        
        const question = searchQuery.value.trim()
        
        emit('question-asked', question)
        
        const answer = await mockDataService.searchKnowledge(question)
        
        currentAnswer.value = {
          question,
          answer: answer.content,
          confidence: answer.confidence,
          images: answer.images || [],
          links: answer.links || [],
          liked: false,
          disliked: false,
          timestamp: new Date()
        }
        
        // 添加到搜索历史
        searchHistory.value.unshift({
          id: Date.now(),
          question,
          timestamp: new Date()
        })
        
        // 限制历史记录数量
        if (searchHistory.value.length > 20) {
          searchHistory.value = searchHistory.value.slice(0, 20)
        }
        
        emit('answer-received', currentAnswer.value)
        
        // 清空搜索框
        searchQuery.value = ''
        
      } catch (error) {
        console.error('Failed to search knowledge:', error)
      } finally {
        isSearching.value = false
      }
    }
    
    const onSearchInput = () => {
      // 实时搜索建议可以在这里实现
    }
    
    const clearSearch = () => {
      searchQuery.value = ''
      currentAnswer.value = null
    }
    
    const selectSuggestion = (suggestion) => {
      searchQuery.value = suggestion.text
      performSearch()
    }
    
    const exploreCategory = (category) => {
      console.log('Exploring category:', category)
      emit('category-explored', category)
      // 实际应用中会显示该分类下的知识内容
    }
    
    const viewRecommendation = (recommendation) => {
      console.log('Viewing recommendation:', recommendation)
      // 实际应用中会显示推荐内容的详情
    }
    
    const searchFromHistory = (historyItem) => {
      searchQuery.value = historyItem.question
      performSearch()
    }
    
    const removeFromHistory = (historyItem) => {
      const index = searchHistory.value.findIndex(item => item.id === historyItem.id)
      if (index > -1) {
        searchHistory.value.splice(index, 1)
      }
    }
    
    const clearHistory = () => {
      searchHistory.value = []
    }
    
    const askQuickQuestion = (question) => {
      searchQuery.value = question.text
      performSearch()
    }
    
    const formatAnswer = (answer) => {
      // 简单的文本格式化
      return answer
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n/g, '<br>')
    }
    
    const formatDate = (date) => {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }
    
    const formatTime = (timestamp) => {
      const date = new Date(timestamp)
      const now = new Date()
      const diffTime = Math.abs(now - date)
      const diffMinutes = Math.ceil(diffTime / (1000 * 60))
      
      if (diffMinutes < 60) {
        return `${diffMinutes}分钟前`
      } else if (diffMinutes < 1440) {
        return `${Math.ceil(diffMinutes / 60)}小时前`
      } else {
        return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
      }
    }
    
    const likeAnswer = () => {
      if (currentAnswer.value) {
        currentAnswer.value.liked = !currentAnswer.value.liked
        if (currentAnswer.value.liked) {
          currentAnswer.value.disliked = false
        }
      }
    }
    
    const dislikeAnswer = () => {
      if (currentAnswer.value) {
        currentAnswer.value.disliked = !currentAnswer.value.disliked
        if (currentAnswer.value.disliked) {
          currentAnswer.value.liked = false
        }
      }
    }
    
    const shareAnswer = () => {
      if (currentAnswer.value) {
        console.log('Sharing answer:', currentAnswer.value)
        emit('knowledge-shared', currentAnswer.value)
        // 实际应用中会打开分享功能
      }
    }
    
    const saveAnswer = () => {
      if (currentAnswer.value) {
        console.log('Saving answer:', currentAnswer.value)
        // 实际应用中会保存到收藏夹
      }
    }
    
    const viewImage = (image) => {
      console.log('Viewing image:', image)
      // 实际应用中会打开图片查看器
    }

    // 生命周期
    let refreshTimer = null
    
    onMounted(async () => {
      await mockDataService.initialize()
      await loadPediaData()
      
      // 设置自动刷新
      if (props.autoRefresh) {
        refreshTimer = setInterval(() => {
          loadPediaData()
        }, props.refreshInterval)
      }
    })

    onUnmounted(() => {
      if (refreshTimer) {
        clearInterval(refreshTimer)
      }
    })

    return {
      // 响应式数据
      isSearching,
      loadingText,
      searchQuery,
      currentAnswer,
      searchSuggestions,
      knowledgeCategories,
      dailyRecommendations,
      searchHistory,
      learningStats,
      quickStartQuestions,
      
      // 计算属性
      cardTitle,
      hasAnyContent,
      
      // 方法
      performSearch,
      onSearchInput,
      clearSearch,
      selectSuggestion,
      exploreCategory,
      viewRecommendation,
      searchFromHistory,
      removeFromHistory,
      clearHistory,
      askQuickQuestion,
      formatAnswer,
      formatDate,
      formatTime,
      likeAnswer,
      dislikeAnswer,
      shareAnswer,
      saveAnswer,
      viewImage
    }
  }
}
</script>

<style scoped>
.ai-pedia-card {
  height: 100%;
}

.pedia-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  position: relative;
  overflow-y: auto;
}

/* 搜索部分 */
.search-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
}

.search-input-container {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.search-input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  outline: none;
  transition: all 0.3s ease;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.search-input:focus {
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.3);
}

.search-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.search-btn,
.clear-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 8px;
  background: rgba(74, 144, 226, 0.3);
  color: #4a90e2;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-btn:hover:not(:disabled) {
  background: rgba(74, 144, 226, 0.5);
  transform: scale(1.05);
}

.search-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.clear-btn {
  background: rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

.clear-btn:hover {
  background: rgba(239, 68, 68, 0.5);
}

/* 搜索建议 */
.search-suggestions {
  margin-top: 12px;
}

.suggestions-header {
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8px;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
  font-size: 12px;
}

.suggestion-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(2px);
}

/* 当前问答 */
.current-qa {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 16px;
  flex: 1;
  min-height: 0;
}

.question-section {
  margin-bottom: 16px;
}

.question-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #4a90e2;
  margin-bottom: 8px;
}

.question-text {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
}

.answer-section {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 16px;
}

.answer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.answer-header > div:first-child {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #7ed321;
}

.confidence-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
}

.confidence-text {
  color: rgba(255, 255, 255, 0.6);
}

.confidence-bar {
  width: 40px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.confidence-fill {
  height: 100%;
  background: linear-gradient(90deg, #ef4444 0%, #f59e0b 50%, #10b981 100%);
  transition: width 0.3s ease;
}

.confidence-value {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.answer-content {
  margin-bottom: 16px;
}

.answer-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 16px;
}

/* 相关图片 */
.answer-images {
  margin-bottom: 16px;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 8px;
}

.image-item {
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.image-item:hover {
  transform: scale(1.02);
}

.image-item img {
  width: 100%;
  height: 80px;
  object-fit: cover;
}

.image-caption {
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.7);
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

/* 相关链接 */
.related-links {
  margin-bottom: 16px;
}

.links-header {
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8px;
}

.links-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.link-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: #4a90e2;
  text-decoration: none;
  font-size: 12px;
  transition: all 0.3s ease;
}

.link-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(2px);
}

/* 答案操作 */
.answer-actions {
  display: flex;
  gap: 8px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
  font-size: 11px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.9);
}

.action-btn.active {
  background: rgba(74, 144, 226, 0.3);
  color: #4a90e2;
}

/* 知识分类 */
.knowledge-categories {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 16px;
}

.categories-header h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.categories-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 16px;
}

.categories-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.category-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(2px);
}

.category-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(74, 144, 226, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #4a90e2;
  flex-shrink: 0;
}

.category-content {
  flex: 1;
}

.category-name {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2px;
}

.category-description {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 2px;
}

.category-count {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
}

.category-arrow {
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
}

/* 今日推荐 */
.daily-recommendations {
  background: rgba(126, 211, 33, 0.1);
  border-radius: 12px;
  padding: 16px;
}

.recommendations-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #7ed321;
}

.date-info {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.recommendation-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.recommendation-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.recommendation-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(126, 211, 33, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #7ed321;
  flex-shrink: 0;
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 4px;
}

.recommendation-summary {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 8px;
  line-height: 1.4;
}

.recommendation-meta {
  display: flex;
  gap: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
}

.recommendation-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #ef4444;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
}

/* 搜索历史 */
.search-history {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 16px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.history-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.clear-history-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-history-btn:hover {
  background: rgba(239, 68, 68, 0.3);
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.history-item:hover {
  background: rgba(255, 255, 255, 0.15);
}

.history-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
  flex-shrink: 0;
}

.history-content {
  flex: 1;
}

.history-question {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2px;
}

.history-time {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
}

.history-actions {
  display: flex;
}

.remove-btn {
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 4px;
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
}

.remove-btn:hover {
  background: rgba(239, 68, 68, 0.3);
}

/* 学习统计 */
.learning-stats {
  background: rgba(139, 92, 246, 0.1);
  border-radius: 12px;
  padding: 16px;
}

.stats-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #8b5cf6;
  margin-bottom: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.stat-item {
  text-align: center;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
}

.achievement-section {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 12px;
}

.achievement-header {
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8px;
}

.achievement-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
}

.achievement-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(245, 158, 11, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #f59e0b;
  flex-shrink: 0;
}

.achievement-content {
  flex: 1;
}

.achievement-name {
  font-size: 11px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1px;
}

.achievement-description {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
}

/* 无内容状态 */
.no-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 32px 16px;
  flex: 1;
}

.no-content-icon {
  font-size: 48px;
  color: rgba(255, 255, 255, 0.3);
  margin-bottom: 16px;
}

.no-content-text h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
}

.no-content-text p {
  margin: 0 0 24px 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

.quick-start {
  width: 100%;
  max-width: 300px;
}

.quick-start-header {
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 12px;
}

.quick-questions {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.quick-question-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background: rgba(74, 144, 226, 0.2);
  color: #4a90e2;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.quick-question-btn:hover {
  background: rgba(74, 144, 226, 0.3);
  transform: translateX(2px);
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  border-radius: 12px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-top: 3px solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

/* 动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
.size-small .pedia-container {
  padding: 12px;
  gap: 12px;
}

.size-small .stats-grid {
  grid-template-columns: 1fr;
}

.mode-compact .daily-recommendations,
.mode-compact .search-history,
.mode-compact .learning-stats {
  display: none;
}

.mode-compact .categories-grid {
  grid-template-columns: 1fr;
}
</style>