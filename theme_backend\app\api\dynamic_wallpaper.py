from fastapi import APIRouter, UploadFile, File, Form, HTTPException
from pydantic import BaseModel
import json
import traceback
import httpx
import tempfile
import os
from pathlib import Path
from .common import logger, load_workflow, upload_image, send_prompt, wait_for_video

router = APIRouter()

server_name = "dynamic_wallpaper_server"

# 请求模型
class GenerateFromUrlRequest(BaseModel):
    image_url: str
    task_id: str

async def download_image_from_url(image_url: str) -> tuple[bytes, str]:
    """从URL下载图片并返回内容和文件扩展名"""
    try:
        logger.info(f"开始下载图片: {image_url}")

        # 验证URL格式
        if not image_url.startswith(('http://', 'https://')):
            raise HTTPException(status_code=400, detail="无效的图片URL格式")

        # 设置超时和大小限制
        timeout = httpx.Timeout(30.0)  # 30秒超时

        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(image_url)
            response.raise_for_status()

            # 检查Content-Type
            content_type = response.headers.get('content-type', '').lower()
            if not content_type.startswith('image/'):
                raise HTTPException(status_code=415, detail="URL不是有效的图片文件")

            # 检查文件大小（限制10MB）
            content_length = response.headers.get('content-length')
            if content_length and int(content_length) > 10 * 1024 * 1024:
                raise HTTPException(status_code=413, detail="图片文件过大，超过10MB限制")

            image_content = response.content

            # 再次检查实际下载的文件大小
            if len(image_content) > 10 * 1024 * 1024:
                raise HTTPException(status_code=413, detail="图片文件过大，超过10MB限制")

            # 根据Content-Type确定文件扩展名
            ext = '.jpg'  # 默认
            if 'png' in content_type:
                ext = '.png'
            elif 'webp' in content_type:
                ext = '.webp'
            elif 'gif' in content_type:
                ext = '.gif'

            logger.info(f"成功下载图片，大小: {len(image_content)} 字节，类型: {content_type}")
            return image_content, ext

    except httpx.HTTPError as e:
        logger.error(f"下载图片失败: {str(e)}")
        raise HTTPException(status_code=400, detail=f"无法下载图片: {str(e)}")
    except Exception as e:
        logger.error(f"下载图片时发生未知错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下载图片失败: {str(e)}")

def build_dynamic_wallpaper_workflow(image_url: str, task_id: str) -> dict:
    """构建动态壁纸工作流"""
    try:
        # 加载工作流
        workflow = load_workflow("动态壁纸.json")
        
        # 设置输入图片（节点11）
        if "11" not in workflow:
            logger.error("工作流中找不到节点11")
            raise HTTPException(status_code=500, detail="Node 11 not found in workflow")
        
        workflow["11"]["inputs"]["image"] = image_url
        logger.info(f"设置输入图片: {image_url}")
        
        # 设置输出文件名前缀（节点20）
        if "20" not in workflow:
            logger.error("工作流中找不到节点20")
            raise HTTPException(status_code=500, detail="Node 20 not found in workflow")
        
        workflow["20"]["inputs"]["filename_prefix"] = f"dynamic_wallpaper/{task_id}"
        logger.info(f"设置输出文件名前缀: dynamic_wallpaper/{task_id}")
        
        # 设置帧数（节点23）
        if "23" not in workflow:
            logger.error("工作流中找不到节点23")
            raise HTTPException(status_code=500, detail="Node 23 not found in workflow")
        
        workflow["23"]["inputs"]["value"] = 81  # 81帧，约5秒的视频
        logger.info(f"设置帧数: 81")
        
        return workflow, "20"  # 返回工作流和输出节点ID
    except Exception as e:
        logger.error(f"构建动态壁纸工作流时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error building workflow: {str(e)}")

# 定义接口
@router.post("/dynamic-wallpaper", summary="生成动态壁纸", description="根据上传的静态壁纸生成动态壁纸视频")
async def generate_dynamic_wallpaper(file: UploadFile = File(...),
                                    task_id: str = Form(...)):
    try:
        logger.info(f"\n========== 开始生成动态壁纸 ==========")
        logger.info(f"文件名: {file.filename}")
        logger.info(f"任务ID: {task_id}")
        
        # 上传文件到远程服务器
        uploaded_filename = await upload_image(server_name, file.file, "动态壁纸输入图片")
        logger.info(f"成功上传文件，获得文件名称: {uploaded_filename}")
        
        # 构建工作流
        workflow, output_node_id = build_dynamic_wallpaper_workflow(
            uploaded_filename,
            task_id
        )
        logger.info(f"\n当前工作流配置: {json.dumps(workflow, indent=2)}")
        
        # 发送生成请求
        data = await send_prompt(server_name, workflow)
        logger.info(f"ComfyUI API 响应: {data}")
        
        # 等待视频生成
        video_url = await wait_for_video(server_name, data["prompt_id"], output_node_id, "")
        logger.info(f"生成的动态壁纸URL: {video_url}")
        
        return {
            "prompt_id": data["prompt_id"],
            "video_url": video_url,
            "task_id": task_id
        }
    except HTTPException as e:
        logger.error(f"HTTP异常: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"调用dynamic_wallpaper接口出错: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

# 新增URL接口
@router.post("/generate-from-url", summary="通过图片URL生成动态壁纸", description="接收Kolors生成的静态壁纸URL，生成动态壁纸视频")
async def generate_dynamic_wallpaper_from_url(request: GenerateFromUrlRequest):
    try:
        logger.info(f"\n========== 开始通过URL生成动态壁纸 ==========")
        logger.info(f"图片URL: {request.image_url}")
        logger.info(f"任务ID: {request.task_id}")

        # 下载图片
        image_content, file_ext = await download_image_from_url(request.image_url)

        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix=file_ext, delete=False) as temp_file:
            temp_file.write(image_content)
            temp_file_path = temp_file.name

        try:
            # 上传文件到ComfyUI服务器
            with open(temp_file_path, 'rb') as file_obj:
                uploaded_filename = await upload_image(server_name, file_obj, "动态壁纸输入图片")
            logger.info(f"成功上传文件，获得文件名称: {uploaded_filename}")

            # 构建工作流
            workflow, output_node_id = build_dynamic_wallpaper_workflow(
                uploaded_filename,
                request.task_id
            )
            logger.info(f"\n当前工作流配置: {json.dumps(workflow, indent=2)}")

            # 发送生成请求
            data = await send_prompt(server_name, workflow)
            logger.info(f"ComfyUI API 响应: {data}")

            # 等待视频生成
            video_url = await wait_for_video(server_name, data["prompt_id"], output_node_id, "")
            logger.info(f"生成的动态壁纸URL: {video_url}")

            return {
                "prompt_id": data["prompt_id"],
                "video_url": video_url,
                "task_id": request.task_id,
                "source_image_url": request.image_url
            }
        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
                logger.info(f"已清理临时文件: {temp_file_path}")
            except Exception as e:
                logger.warning(f"清理临时文件失败: {str(e)}")

    except HTTPException as e:
        logger.error(f"HTTP异常: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"调用generate-from-url接口出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail="内部服务器错误")

# 健康检查端点
@router.get("/health", summary="健康检查", description="检查动态壁纸服务状态")
async def health_check():
    return {"status": "healthy", "service": "dynamic_wallpaper"}