{"ast": null, "code": "import { ref, computed, onMounted, onUnmounted, watch } from 'vue';\nimport SceneManager from '@/utils/SceneManager';\nimport EmotionalPromptGenerator from '@/services/EmotionalPromptGenerator';\nimport SceneContextManager from '@/services/SceneContextManager';\nimport DefaultCard from './cards/DefaultCard.vue';\nimport KidEducationCard from './cards/KidEducationCard.vue';\nimport MusicControlCard from './cards/MusicControlCard.vue';\nimport AIScheduleAssistantCard from './cards/AIScheduleAssistantCard.vue';\nimport SmartHomeControlCard from './cards/SmartHomeControlCard.vue';\nimport NavigationCard from './cards/NavigationCard.vue';\nimport AIOrderCard from './cards/AIOrderCard.vue';\nimport AIPediaCard from './cards/AIPediaCard.vue';\nimport VPAAvatarWidget from './vpa/VPAAvatarWidget.vue';\nimport ImmersiveWallpaperInterface from './ImmersiveWallpaperInterface.vue';\nimport VoiceInteractionManager from './VoiceInteractionManager.vue';\nexport default {\n  name: 'SceneManager',\n  components: {\n    DefaultCard,\n    KidEducationCard,\n    MusicControlCard,\n    AIScheduleAssistantCard,\n    SmartHomeControlCard,\n    NavigationCard,\n    AIOrderCard,\n    AIPediaCard,\n    VPAAvatarWidget,\n    ImmersiveWallpaperInterface,\n    VoiceInteractionManager\n  },\n  props: {\n    initialScene: {\n      type: String,\n      default: 'default'\n    },\n    showIndicator: {\n      type: Boolean,\n      default: true\n    },\n    autoSwitch: {\n      type: Boolean,\n      default: false // 默认禁用自动切换，避免意外的场景切换\n    },\n    themeColors: {\n      type: Object,\n      default: null\n    }\n  },\n  emits: ['scene-changed', 'context-updated', 'wallpaper-prompt-ready'],\n  setup(props, {\n    emit\n  }) {\n    const sceneManager = new SceneManager();\n    const emotionalPromptGenerator = new EmotionalPromptGenerator();\n    const contextManager = new SceneContextManager();\n    const showSceneSelector = ref(false);\n    const autoSwitchEnabled = ref(props.autoSwitch);\n    const isTransitioning = ref(false);\n\n    // 当前场景\n    const currentScene = ref(sceneManager.getCurrentScene());\n\n    // 可用场景\n    const availableScenes = computed(() => sceneManager.getAllScenes());\n\n    // 推荐场景\n    const recommendedScenes = ref([]);\n\n    // 场景样式\n    const sceneStyles = computed(() => {\n      const scene = currentScene.value;\n\n      // 移除背景样式，让DynamicWallpaperManager管理背景\n      let backgroundStyle = {};\n      return {\n        '--scene-primary-color': getThemeColor(scene.theme, 'primary'),\n        '--scene-secondary-color': getThemeColor(scene.theme, 'secondary'),\n        '--scene-background': 'transparent',\n        /* 使用透明背景，让动态壁纸显示 */\n        '--scene-text-color': getThemeColor(scene.theme, 'text'),\n        ...backgroundStyle\n      };\n    });\n\n    // 获取场景图标\n    const getSceneIcon = computed(() => {\n      return getSceneIconByType(currentScene.value.id);\n    });\n\n    // 选择场景\n    const selectScene = async sceneId => {\n      if (sceneId === currentScene.value.id) return;\n      isTransitioning.value = true;\n      try {\n        // 触发场景切换\n        const success = sceneManager.switchScene(sceneId, 'manual');\n        if (success) {\n          // 更新当前场景的响应式状态\n          currentScene.value = sceneManager.getCurrentScene();\n\n          // 更新上下文管理器\n          contextManager.updateContext({\n            recentScenes: [...contextManager.context.recentScenes, sceneId].slice(-5),\n            sceneSwitchCount: contextManager.context.sceneSwitchCount + 1\n          });\n\n          // 生成场景对应的壁纸\n          await generateSceneWallpaper(currentScene.value);\n\n          // 发送事件\n          emit('scene-changed', {\n            from: sceneManager.sceneHistory[0]?.from,\n            to: sceneId,\n            scene: currentScene.value,\n            context: contextManager.getContext()\n          });\n\n          // 关闭选择器\n          showSceneSelector.value = false;\n        }\n      } catch (error) {\n        console.error('场景切换失败:', error);\n      } finally {\n        setTimeout(() => {\n          isTransitioning.value = false;\n        }, 1000);\n      }\n    };\n\n    // 生成场景壁纸\n    const generateSceneWallpaper = async scene => {\n      if (!scene.wallpaper || scene.wallpaper.startsWith('/')) {\n        // 使用默认壁纸\n        return scene.wallpaper;\n      }\n      try {\n        console.log('🎨 开始生成情感化壁纸提示词...');\n\n        // 获取当前上下文信息\n        const promptContext = contextManager.getPromptGenerationContext();\n        console.log('📋 当前上下文:', promptContext);\n\n        // 生成情感化提示词\n        const emotionalPrompt = await emotionalPromptGenerator.generateEmotionalPrompt(scene, promptContext);\n        console.log('🎭 情感化提示词生成成功:', emotionalPrompt);\n\n        // 触发壁纸生成事件\n        emit('wallpaper-prompt-ready', {\n          prompt: emotionalPrompt,\n          scene: scene,\n          context: promptContext\n        });\n        return emotionalPrompt;\n      } catch (error) {\n        console.error('情感化壁纸生成失败:', error);\n\n        // 使用增强的场景描述作为降级方案\n        const fallbackPrompt = emotionalPromptGenerator.getFallbackPrompt(scene, contextManager.getPromptGenerationContext());\n        console.log('🔄 使用降级提示词:', fallbackPrompt);\n\n        // 触发壁纸生成事件\n        emit('wallpaper-prompt-ready', {\n          prompt: fallbackPrompt,\n          scene: scene,\n          context: contextManager.getPromptGenerationContext()\n        });\n        return fallbackPrompt;\n      }\n    };\n\n    // 切换场景选择器\n    const toggleSceneSelector = () => {\n      showSceneSelector.value = !showSceneSelector.value;\n      if (showSceneSelector.value) {\n        updateRecommendations();\n      }\n    };\n\n    // 关闭场景选择器\n    const closeSceneSelector = () => {\n      showSceneSelector.value = false;\n    };\n\n    // 切换自动切换\n    const toggleAutoSwitch = () => {\n      autoSwitchEnabled.value = !autoSwitchEnabled.value;\n    };\n\n    // 更新推荐场景\n    const updateRecommendations = () => {\n      const context = getCurrentContext();\n      recommendedScenes.value = sceneManager.getRecommendedScenes(context);\n    };\n\n    // 获取当前上下文\n    const getCurrentContext = () => {\n      const now = new Date();\n      return {\n        time: now.getHours(),\n        day: now.getDay(),\n        isWeekend: now.getDay() === 0 || now.getDay() === 6,\n        passengers: ['driver'],\n        // 可以通过传感器获取\n        passengerCount: 1,\n        destination: '',\n        // 可以通过导航获取\n        gear: 'D',\n        // 可以通过车辆系统获取\n        drivingDuration: 0,\n        // 可以通过车辆系统获取\n        weather: 'clear',\n        // 可以通过天气API获取\n        roadType: 'city',\n        // 可以通过GPS获取\n        batteryLevel: 85,\n        // 可以通过车辆系统获取\n        locationType: 'city',\n        // 可以通过GPS获取\n        fatigueDetected: false,\n        // 可以通过疲劳检测系统获取\n        manualTrigger: null,\n        calendarEvents: [],\n        // 可以通过日历API获取\n        newUserDetected: false,\n        accidentDetected: false,\n        airbagDeployed: false\n      };\n    };\n\n    // 模拟上下文更新\n    const simulateContextUpdate = () => {\n      contextManager.simulateDrivingUpdate();\n    };\n\n    // 获取卡片组件\n    const getCardComponent = card => {\n      // 根据卡片类型返回对应的组件名称\n      const cardComponents = {\n        // 导航相关\n        'navigation': 'NavigationCard',\n        'tempNavigation': 'NavigationCard',\n        // 音乐相关\n        'music': 'MusicCard',\n        'basicMusic': 'MusicCard',\n        'romanticMusic': 'MusicCard',\n        // 待办事项\n        'todo': 'TodoCard',\n        // 儿童教育\n        'kidEducation': 'KidEducationCard',\n        'pedia': 'PediaCard',\n        // 娱乐相关\n        'videoPlayer': 'VideoPlayerCard',\n        'news': 'NewsCard',\n        'ambientSound': 'AmbientSoundCard',\n        // 智能家居\n        'smartHome': 'SmartHomeCard',\n        // 家庭出行\n        'rearSeatControl': 'RearSeatControlCard',\n        'facilityFinder': 'FacilityFinderCard',\n        'tripReminder': 'TripReminderCard',\n        // 长途驾驶\n        'serviceArea': 'ServiceAreaCard',\n        'driverStatus': 'DriverStatusCard',\n        'vehicleStatus': 'VehicleStatusCard',\n        // 充电相关\n        'chargingStatus': 'ChargingStatusCard',\n        'entertainment': 'EntertainmentCard',\n        'nearbyShops': 'NearbyShopsCard',\n        // 其他功能\n        'orderStatus': 'OrderStatusCard',\n        'basicControl': 'BasicControlCard',\n        'petInfo': 'PetInfoCard',\n        'climateControl': 'ClimateControlCard',\n        'carWashChecklist': 'CarWashChecklistCard',\n        'ambientLight': 'AmbientLightCard',\n        // 安全相关\n        'fatigueWarning': 'FatigueWarningCard',\n        'restArea': 'RestAreaCard',\n        'refreshment': 'RefreshmentCard',\n        'emergencyContact': 'EmergencyContactCard',\n        'emergencyInfo': 'EmergencyInfoCard',\n        'firstAid': 'FirstAidCard',\n        // 用户管理\n        'userSelector': 'UserSelectorCard',\n        'userPreferences': 'UserPreferencesCard',\n        'privacySettings': 'PrivacySettingsCard',\n        // 泊车相关\n        'parkingSearch': 'ParkingSearchCard',\n        'parkingAssist': 'ParkingAssistCard',\n        'costInfo': 'CostInfoCard'\n      };\n      return cardComponents[card] || 'DefaultCard';\n    };\n\n    // 获取场景图标\n    const getSceneIconByType = sceneId => {\n      const iconMap = {\n        default: 'fas fa-home',\n        morningCommuteFamily: 'fas fa-child',\n        morningCommuteFocus: 'fas fa-briefcase',\n        eveningCommute: 'fas fa-sun',\n        waitingMode: 'fas fa-couch',\n        rainyNight: 'fas fa-cloud-rain',\n        familyTrip: 'fas fa-car',\n        longDistance: 'fas fa-road',\n        guestMode: 'fas fa-user-shield',\n        petMode: 'fas fa-paw',\n        carWashMode: 'fas fa-car-side',\n        romanticMode: 'fas fa-heart',\n        chargingMode: 'fas fa-charging-station',\n        fatigueDetection: 'fas fa-exclamation-triangle',\n        userSwitch: 'fas fa-users',\n        parkingMode: 'fas fa-parking',\n        emergencyMode: 'fas fa-ambulance'\n      };\n      return iconMap[sceneId] || 'fas fa-circle';\n    };\n\n    // 获取优先级颜色\n    const getPriorityColor = priority => {\n      const colorMap = {\n        1: '#e74c3c',\n        // 红色 - 最高优先级\n        2: '#f39c12',\n        // 橙色 - 高优先级\n        3: '#f1c40f',\n        // 黄色 - 中优先级\n        4: '#2ecc71',\n        // 绿色 - 低优先级\n        5: '#95a5a6' // 灰色 - 最低优先级\n      };\n      return colorMap[priority] || '#95a5a6';\n    };\n\n    // 获取主题颜色\n    const getThemeColor = (theme, type) => {\n      const themeColors = {\n        light: {\n          primary: '#4a90e2',\n          secondary: '#7ed321',\n          background: 'rgba(255, 255, 255, 0.1)',\n          text: '#333333'\n        },\n        dark: {\n          primary: '#2c3e50',\n          secondary: '#3498db',\n          background: 'rgba(0, 0, 0, 0.3)',\n          text: '#ffffff'\n        },\n        warm: {\n          primary: '#e74c3c',\n          secondary: '#f39c12',\n          background: 'rgba(255, 193, 7, 0.1)',\n          text: '#2c3e50'\n        },\n        calm: {\n          primary: '#3498db',\n          secondary: '#2ecc71',\n          background: 'rgba(52, 152, 219, 0.1)',\n          text: '#2c3e50'\n        },\n        evening: {\n          primary: '#8e44ad',\n          secondary: '#e67e22',\n          background: 'rgba(142, 68, 173, 0.1)',\n          text: '#ffffff'\n        },\n        relax: {\n          primary: '#27ae60',\n          secondary: '#2ecc71',\n          background: 'rgba(39, 174, 96, 0.1)',\n          text: '#ffffff'\n        },\n        bright: {\n          primary: '#f39c12',\n          secondary: '#e67e22',\n          background: 'rgba(243, 156, 18, 0.1)',\n          text: '#2c3e50'\n        }\n      };\n      return themeColors[theme]?.[type] || themeColors.light[type];\n    };\n\n    // 监听自动切换\n    let autoSwitchInterval;\n    const startAutoSwitch = () => {\n      if (!autoSwitchEnabled.value) return;\n      autoSwitchInterval = setInterval(() => {\n        updateRecommendations();\n\n        // 如果有高优先级的推荐场景，自动切换\n        const topRecommendation = recommendedScenes.value[0];\n        if (topRecommendation && topRecommendation.score > 8) {\n          selectScene(topRecommendation.sceneId);\n        }\n      }, 30000); // 每30秒检查一次\n    };\n    const stopAutoSwitch = () => {\n      if (autoSwitchInterval) {\n        clearInterval(autoSwitchInterval);\n        autoSwitchInterval = null;\n      }\n    };\n\n    // 监听自动切换状态\n    watch(autoSwitchEnabled, newValue => {\n      if (newValue) {\n        startAutoSwitch();\n      } else {\n        stopAutoSwitch();\n      }\n    });\n\n    // 初始化\n    onMounted(() => {\n      // 设置初始场景\n      if (props.initialScene !== 'default') {\n        sceneManager.switchScene(props.initialScene, 'initial');\n      }\n\n      // 初始化上下文\n      contextManager.updateContext({\n        isDriving: true,\n        // 假设车辆在行驶中\n        recentScenes: [props.initialScene === 'default' ? 'default' : props.initialScene]\n      });\n\n      // 启动自动切换\n      if (autoSwitchEnabled.value) {\n        startAutoSwitch();\n      }\n\n      // 启动上下文更新定时器\n      setInterval(simulateContextUpdate, 60000); // 每分钟更新一次\n\n      // 监听键盘事件\n      window.addEventListener('keydown', handleKeyDown);\n    });\n    onUnmounted(() => {\n      stopAutoSwitch();\n      window.removeEventListener('keydown', handleKeyDown);\n    });\n\n    // 键盘事件处理\n    const handleKeyDown = event => {\n      if (event.key === 'Escape') {\n        closeSceneSelector();\n      } else if (event.ctrlKey && event.key === 's') {\n        event.preventDefault();\n        toggleSceneSelector();\n      }\n    };\n\n    // 处理语音场景切换请求\n    const handleVoiceSceneSwitch = async sceneRequest => {\n      console.log('收到语音场景切换请求:', sceneRequest);\n      if (sceneRequest.sceneId && sceneRequest.confidence > 0.6) {\n        try {\n          await selectScene(sceneRequest.sceneId);\n          console.log(`语音场景切换成功: ${sceneRequest.sceneId} (置信度: ${sceneRequest.confidence})`);\n        } catch (error) {\n          console.error('语音场景切换失败:', error);\n        }\n      } else {\n        console.log(`语音场景切换置信度过低: ${sceneRequest.confidence}`);\n      }\n    };\n\n    // 处理壁纸生成请求\n    function handleWallpaperPrompt(prompt) {\n      console.log('收到壁纸生成请求:', prompt);\n\n      // 如果是直接字符串，包装为对象格式\n      const promptData = typeof prompt === 'string' ? {\n        prompt,\n        scene: currentScene.value,\n        context: contextManager.getPromptGenerationContext()\n      } : prompt;\n\n      // 触发壁纸生成事件，传递给父组件\n      emit('wallpaper-prompt-ready', promptData);\n    }\n\n    // 新组件事件处理方法\n    const handleCardClick = cardType => {\n      console.log('卡片被点击:', cardType);\n      // 可以在这里处理卡片点击事件，比如打开详细界面\n    };\n    const handleEducationModeChanged = mode => {\n      console.log('教育模式变更:', mode);\n      // 可以在这里处理教育模式变更\n    };\n    const handleLessonCompleted = lessonType => {\n      console.log('课程完成:', lessonType);\n      // 可以在这里处理课程完成事件，比如更新进度\n    };\n    const handleSongChanged = song => {\n      console.log('歌曲变更:', song);\n      // 可以在这里处理歌曲变更事件\n    };\n    const handlePlayStateChanged = state => {\n      console.log('播放状态变更:', state);\n      // 可以在这里处理播放状态变更\n    };\n    const handleVpaClick = data => {\n      console.log('VPA头像被点击:', data);\n      // 可以在这里处理VPA交互\n    };\n    const handleVpaModeChanged = mode => {\n      console.log('VPA模式变更:', mode);\n      // 可以在这里处理VPA模式变更\n    };\n    const handleVpaAnimationChanged = animationData => {\n      console.log('VPA动画状态变更:', animationData);\n      // 可以在这里处理VPA动画状态变更\n    };\n\n    // 处理AI百科卡片事件\n    const handleQuestionAsked = question => {\n      console.log('Question asked:', question);\n      // 可以记录用户提问历史\n    };\n    const handleAnswerReceived = answer => {\n      console.log('Answer received:', answer);\n      // 可以分析答案质量，优化推荐\n    };\n    const handleCategoryExplored = category => {\n      console.log('Category explored:', category);\n      // 可以根据用户兴趣调整推荐\n    };\n    const handleKnowledgeShared = knowledge => {\n      console.log('Knowledge shared:', knowledge);\n      // 可以记录分享行为\n    };\n\n    // 处理AI日程助手卡片事件\n    const handleScheduleUpdated = schedule => {\n      console.log('Schedule updated:', schedule);\n      // 可以同步到其他系统\n    };\n    const handleReminderSet = reminder => {\n      console.log('Reminder set:', reminder);\n      // 可以设置系统提醒\n    };\n    const handleOptimizationApplied = optimization => {\n      console.log('Optimization applied:', optimization);\n      // 可以记录优化效果\n    };\n\n    // 处理AI订单助手卡片事件\n    const handleOrderPlaced = order => {\n      console.log('Order placed:', order);\n      // 可以跟踪订单状态\n    };\n    const handleOrderTracked = tracking => {\n      console.log('Order tracked:', tracking);\n      // 可以更新订单信息\n    };\n    const handleRecommendationSelected = recommendation => {\n      console.log('Recommendation selected:', recommendation);\n      // 可以优化推荐算法\n    };\n\n    // 处理智能家居控制卡片事件\n    const handleDeviceControlled = device => {\n      console.log('Device controlled:', device);\n      // 可以同步设备状态\n    };\n    const handleSceneModeChanged = mode => {\n      console.log('Scene mode changed:', mode);\n      // 可以调整车内环境\n    };\n    const handleSuggestionApplied = suggestion => {\n      console.log('Suggestion applied:', suggestion);\n      // 可以学习用户偏好\n    };\n\n    // 处理导航卡片事件\n    const handleNavigationStarted = destination => {\n      console.log('Navigation started:', destination);\n      // 可以切换到导航模式\n    };\n    const handleRouteChanged = route => {\n      console.log('Route changed:', route);\n      // 可以更新ETA和路况\n    };\n    const handleNavigationStopped = () => {\n      console.log('Navigation stopped');\n      // 可以切换回其他模式\n    };\n    return {\n      currentScene,\n      availableScenes,\n      recommendedScenes,\n      showSceneSelector,\n      autoSwitchEnabled,\n      isTransitioning,\n      sceneStyles,\n      getSceneIcon,\n      getSceneIconByType,\n      getPriorityColor,\n      getThemeColor,\n      selectScene,\n      toggleSceneSelector,\n      closeSceneSelector,\n      toggleAutoSwitch,\n      getCardComponent,\n      handleWallpaperPrompt,\n      handleVoiceSceneSwitch,\n      // 新组件事件处理方法\n      handleCardClick,\n      handleEducationModeChanged,\n      handleLessonCompleted,\n      handleSongChanged,\n      handlePlayStateChanged,\n      handleVpaClick,\n      handleVpaModeChanged,\n      handleVpaAnimationChanged,\n      // AI百科卡片事件处理方法\n      handleQuestionAsked,\n      handleAnswerReceived,\n      handleCategoryExplored,\n      handleKnowledgeShared,\n      // AI日程助手卡片事件处理方法\n      handleScheduleUpdated,\n      handleReminderSet,\n      handleOptimizationApplied,\n      // AI订单助手卡片事件处理方法\n      handleOrderPlaced,\n      handleOrderTracked,\n      handleRecommendationSelected,\n      // 智能家居控制卡片事件处理方法\n      handleDeviceControlled,\n      handleSceneModeChanged,\n      handleSuggestionApplied,\n      // 导航卡片事件处理方法\n      handleNavigationStarted,\n      handleRouteChanged,\n      handleNavigationStopped,\n      // 暴露给模板的上下文信息\n      contextStats: computed(() => contextManager.getStatistics())\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "onUnmounted", "watch", "SceneManager", "EmotionalPromptGenerator", "SceneContextManager", "DefaultCard", "KidEducationCard", "MusicControlCard", "AIScheduleAssistantCard", "SmartHomeControlCard", "NavigationCard", "AIOrderCard", "AIPediaCard", "VPAAvatarWidget", "ImmersiveWallpaperInterface", "VoiceInteractionManager", "name", "components", "props", "initialScene", "type", "String", "default", "showIndicator", "Boolean", "autoSwitch", "themeColors", "Object", "emits", "setup", "emit", "sceneManager", "emotionalPromptGenerator", "contextManager", "showSceneSelector", "autoSwitchEnabled", "isTransitioning", "currentScene", "getCurrentScene", "availableScenes", "getAllScenes", "recommendedScenes", "sceneStyles", "scene", "value", "backgroundStyle", "getThemeColor", "theme", "getSceneIcon", "getSceneIconByType", "id", "selectScene", "sceneId", "success", "switchScene", "updateContext", "recentScenes", "context", "slice", "sceneSwitchCount", "generateSceneWallpaper", "from", "sceneHistory", "to", "getContext", "error", "console", "setTimeout", "wallpaper", "startsWith", "log", "promptContext", "getPromptGenerationContext", "emotionalPrompt", "generateEmotionalPrompt", "prompt", "fallback<PERSON>rompt", "getFallbackPrompt", "toggleSceneSelector", "updateRecommendations", "closeSceneSelector", "toggleAutoSwitch", "getCurrentContext", "getRecommendedScenes", "now", "Date", "time", "getHours", "day", "getDay", "isWeekend", "passengers", "passengerCount", "destination", "gear", "drivingDuration", "weather", "roadType", "batteryLevel", "locationType", "fatigueDetected", "manualTrigger", "calendarEvents", "newUserDetected", "accidentDetected", "airbagDeployed", "simulateContextUpdate", "simulateDrivingUpdate", "getCardComponent", "card", "cardComponents", "iconMap", "morningCommuteFamily", "morningCommuteFocus", "eveningCommute", "waitingMode", "rainyNight", "familyTrip", "longDistance", "<PERSON><PERSON><PERSON>", "petMode", "carWashMode", "<PERSON><PERSON><PERSON>", "chargingMode", "fatigueDetection", "userSwitch", "parkingMode", "emergencyMode", "getPriorityColor", "priority", "colorMap", "light", "primary", "secondary", "background", "text", "dark", "warm", "calm", "evening", "relax", "bright", "autoSwitchInterval", "startAutoSwitch", "setInterval", "topRecommendation", "score", "stopAutoSwitch", "clearInterval", "newValue", "isDriving", "window", "addEventListener", "handleKeyDown", "removeEventListener", "event", "key", "ctrl<PERSON>ey", "preventDefault", "handleVoiceSceneSwitch", "sceneRequest", "confidence", "handleWallpaperPrompt", "promptData", "handleCardClick", "cardType", "handleEducationModeChanged", "mode", "handleLessonCompleted", "lessonType", "handleSongChanged", "song", "handlePlayStateChanged", "state", "handleVpaClick", "data", "handleVpaModeChanged", "handleVpaAnimationChanged", "animationData", "handleQuestionAsked", "question", "handleAnswerReceived", "answer", "handleCategoryExplored", "category", "handleKnowledgeShared", "knowledge", "handleScheduleUpdated", "schedule", "handleReminderSet", "reminder", "handleOptimizationApplied", "optimization", "handleOrderPlaced", "order", "handleOrderTracked", "tracking", "handleRecommendationSelected", "recommendation", "handleDeviceControlled", "device", "handleSceneModeChanged", "handleSuggestionApplied", "suggestion", "handleNavigationStarted", "handleRouteChanged", "route", "handleNavigationStopped", "contextStats", "getStatistics"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\SceneManager.vue"], "sourcesContent": ["<template>\r\n  <div class=\"scene-manager\" :style=\"sceneStyles\">\r\n    <!-- 场景指示器 -->\r\n    <div class=\"scene-indicator\" v-if=\"showIndicator\">\r\n      <div class=\"scene-info\">\r\n        <i :class=\"getSceneIcon\"></i>\r\n        <span class=\"scene-name\">{{ currentScene.name }}</span>\r\n        <span class=\"scene-description\">{{ currentScene.description }}</span>\r\n      </div>\r\n      <div class=\"scene-controls\">\r\n        <button @click=\"toggleSceneSelector\" class=\"control-btn\">\r\n          <i class=\"fas fa-th-large\"></i>\r\n        </button>\r\n        <button @click=\"toggleAutoSwitch\" class=\"control-btn\" :class=\"{ active: autoSwitchEnabled }\">\r\n          <i class=\"fas fa-magic\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 场景选择器 -->\r\n    <div v-if=\"showSceneSelector\" class=\"scene-selector\">\r\n      <div class=\"selector-header\">\r\n        <h3>选择场景</h3>\r\n        <button @click=\"closeSceneSelector\" class=\"close-btn\">\r\n          <i class=\"fas fa-times\"></i>\r\n        </button>\r\n      </div>\r\n      \r\n      <div class=\"scene-grid\">\r\n        <div \r\n          v-for=\"scene in availableScenes\" \r\n          :key=\"scene.id\"\r\n          :class=\"['scene-card', { active: currentScene.id === scene.id }]\"\r\n          @click=\"selectScene(scene.id)\"\r\n        >\r\n          <div class=\"scene-card-header\">\r\n            <i :class=\"getSceneIconByType(scene.id)\"></i>\r\n            <h4>{{ scene.name }}</h4>\r\n          </div>\r\n          <p class=\"scene-description\">{{ scene.description }}</p>\r\n          <div class=\"scene-meta\">\r\n            <span class=\"scene-priority\" :style=\"{ backgroundColor: getPriorityColor(scene.priority) }\">\r\n              优先级 {{ scene.priority }}\r\n            </span>\r\n            <span class=\"scene-auto-switch\" v-if=\"scene.autoSwitch\">\r\n              <i class=\"fas fa-magic\"></i> 自动\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 推荐场景 -->\r\n      <div v-if=\"recommendedScenes.length > 0\" class=\"recommended-scenes\">\r\n        <h4>推荐场景</h4>\r\n        <div class=\"recommendation-list\">\r\n          <div \r\n            v-for=\"recommendation in recommendedScenes\" \r\n            :key=\"recommendation.sceneId\"\r\n            class=\"recommendation-item\"\r\n            @click=\"selectScene(recommendation.sceneId)\"\r\n          >\r\n            <div class=\"recommendation-info\">\r\n              <i :class=\"getSceneIconByType(recommendation.sceneId)\"></i>\r\n              <div>\r\n                <div class=\"recommendation-name\">{{ recommendation.scene.name }}</div>\r\n                <div class=\"recommendation-score\">匹配度: {{ recommendation.score }}</div>\r\n              </div>\r\n            </div>\r\n            <button class=\"apply-btn\">\r\n              <i class=\"fas fa-check\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 场景内容区域 -->\r\n    <div class=\"scene-content\" :class=\"currentScene.layout\">\r\n      <slot :scene=\"currentScene\" :layout=\"currentScene.layout\">\r\n        <!-- 场景布局内容 -->\r\n        <div :class=\"['scene-layout', `layout-${currentScene.layout}`]\">\r\n          <!-- 根据场景文档的ASCII图来布局卡片 -->\r\n          <div v-if=\"currentScene.layout === 'family'\" class=\"family-layout\">\r\n            <!-- 灵动岛 16x1 -->\r\n            <div class=\"dynamic-island\">\r\n              <DefaultCard\r\n                :scene=\"currentScene\"\r\n                card-type=\"dynamicIsland\"\r\n                :theme-colors=\"themeColors\"\r\n              />\r\n            </div>\r\n\r\n            <!-- 儿童教育卡片 8x8 -->\r\n            <div class=\"kid-education-card\">\r\n              <KidEducationCard\r\n                v-if=\"currentScene.cards.includes('kidEducation')\"\r\n                :position=\"{ x: 1, y: 2 }\"\r\n                :theme=\"'glass'\"\r\n                :theme-colors=\"themeColors\"\r\n                @card-click=\"handleCardClick\"\r\n                @mode-changed=\"handleEducationModeChanged\"\r\n                @lesson-completed=\"handleLessonCompleted\"\r\n              />\r\n            </div>\r\n\r\n            <!-- 百科问答卡片 8x3 -->\r\n            <div class=\"pedia-card\">\r\n              <AIPediaCard\r\n                v-if=\"currentScene.cards.includes('pedia')\"\r\n                :position=\"{ x: 9, y: 2 }\"\r\n                :size=\"'medium'\"\r\n                :theme=\"'glass'\"\r\n                :theme-colors=\"themeColors\"\r\n                @question-asked=\"handleQuestionAsked\"\r\n                @answer-received=\"handleAnswerReceived\"\r\n                @category-explored=\"handleCategoryExplored\"\r\n                @knowledge-shared=\"handleKnowledgeShared\"\r\n              />\r\n            </div>\r\n\r\n            <!-- VPA小窗 2x2 -->\r\n            <div class=\"vpa-widget\">\r\n              <VPAAvatarWidget\r\n                :size=\"'small'\"\r\n                :position=\"{ x: 15, y: 7 }\"\r\n                :theme=\"'glass'\"\r\n                :theme-colors=\"themeColors\"\r\n                @avatar-click=\"handleVpaClick\"\r\n                @mode-changed=\"handleVpaModeChanged\"\r\n                @animation-changed=\"handleVpaAnimationChanged\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div v-else-if=\"currentScene.layout === 'focus'\" class=\"focus-layout\">\r\n            <!-- 灵动岛 16x1 -->\r\n            <div class=\"dynamic-island\">\r\n              <DefaultCard\r\n                :scene=\"currentScene\"\r\n                card-type=\"dynamicIsland\"\r\n              />\r\n            </div>\r\n\r\n            <!-- 音乐控制卡片 8x8 -->\r\n            <div class=\"music-card\">\r\n              <MusicControlCard\r\n                v-if=\"currentScene.cards.includes('music')\"\r\n                :position=\"{ x: 1, y: 2 }\"\r\n                :theme=\"'glass'\"\r\n                :theme-colors=\"themeColors\"\r\n                @card-click=\"handleCardClick\"\r\n                @song-changed=\"handleSongChanged\"\r\n                @play-state-changed=\"handlePlayStateChanged\"\r\n              />\r\n            </div>\r\n\r\n            <!-- AI日程助手卡片 8x3 -->\r\n            <div class=\"schedule-card\">\r\n              <AIScheduleAssistantCard\r\n                v-if=\"currentScene.cards.includes('schedule')\"\r\n                :position=\"{ x: 9, y: 2 }\"\r\n                :size=\"'medium'\"\r\n                :theme=\"'glass'\"\r\n                :theme-colors=\"themeColors\"\r\n                @schedule-updated=\"handleScheduleUpdated\"\r\n                @reminder-set=\"handleReminderSet\"\r\n                @optimization-applied=\"handleOptimizationApplied\"\r\n              />\r\n            </div>\r\n\r\n            <!-- AI订单助手卡片 4x2 -->\r\n            <div class=\"order-card\">\r\n              <AIOrderCard\r\n                v-if=\"currentScene.cards.includes('order')\"\r\n                :position=\"{ x: 9, y: 6 }\"\r\n                :size=\"'small'\"\r\n                :theme=\"'glass'\"\r\n                :theme-colors=\"themeColors\"\r\n                @order-placed=\"handleOrderPlaced\"\r\n                @order-tracked=\"handleOrderTracked\"\r\n                @recommendation-selected=\"handleRecommendationSelected\"\r\n              />\r\n            </div>\r\n\r\n            <!-- 智能家居控制卡片 4x4 -->\r\n            <div class=\"smarthome-card\">\r\n              <SmartHomeControlCard\r\n                v-if=\"currentScene.cards.includes('smarthome')\"\r\n                :position=\"{ x: 13, y: 2 }\"\r\n                :size=\"'small'\"\r\n                :theme=\"'glass'\"\r\n                :theme-colors=\"themeColors\"\r\n                @device-controlled=\"handleDeviceControlled\"\r\n                @scene-mode-changed=\"handleSceneModeChanged\"\r\n                @suggestion-applied=\"handleSuggestionApplied\"\r\n              />\r\n            </div>\r\n\r\n            <!-- 导航卡片 4x3 -->\r\n            <div class=\"navigation-card\">\r\n              <NavigationCard\r\n                v-if=\"currentScene.cards.includes('navigation')\"\r\n                :position=\"{ x: 13, y: 6 }\"\r\n                :size=\"'small'\"\r\n                :theme=\"'glass'\"\r\n                :theme-colors=\"themeColors\"\r\n                @navigation-started=\"handleNavigationStarted\"\r\n                @route-changed=\"handleRouteChanged\"\r\n                @navigation-stopped=\"handleNavigationStopped\"\r\n              />\r\n            </div>\r\n\r\n            <!-- VPA小窗 2x2 -->\r\n            <div class=\"vpa-widget\">\r\n              <VPAAvatarWidget\r\n                :size=\"'small'\"\r\n                :position=\"{ x: 15, y: 7 }\"\r\n                :theme=\"'glass'\"\r\n                :theme-colors=\"themeColors\"\r\n                @avatar-click=\"handleVpaClick\"\r\n                @mode-changed=\"handleVpaModeChanged\"\r\n                @animation-changed=\"handleVpaAnimationChanged\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 沉浸式桌面壁纸界面 -->\r\n          <div v-else-if=\"currentScene.layout === 'immersive'\" class=\"immersive-layout\">\r\n            <ImmersiveWallpaperInterface @wallpaper-prompt-ready=\"handleWallpaperPrompt\" />\r\n          </div>\r\n\r\n          <div v-else-if=\"currentScene.layout === 'entertainment'\" class=\"entertainment-layout\">\r\n            <!-- 等待/娱乐模式布局 -->\r\n            <div class=\"layout-row video-row\">\r\n              <div class=\"card-video\">\r\n                <DefaultCard\r\n                  v-if=\"currentScene.cards.includes('videoPlayer')\"\r\n                  :scene=\"currentScene\"\r\n                  card-type=\"videoPlayer\"\r\n                  :theme-colors=\"themeColors\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <!-- VPA小窗 -->\r\n            <div class=\"vpa-widget\">\r\n              <VPAAvatarWidget\r\n                v-if=\"currentScene.cards.includes('vpaWidget')\"\r\n                :size=\"'small'\"\r\n                :position=\"{ x: 1, y: 1 }\"\r\n                :theme=\"'glass'\"\r\n                :theme-colors=\"themeColors\"\r\n                @avatar-click=\"handleVpaClick\"\r\n                @mode-changed=\"handleVpaModeChanged\"\r\n                @animation-changed=\"handleVpaAnimationChanged\"\r\n              />\r\n            </div>\r\n\r\n            <div class=\"layout-row bottom-row\">\r\n              <div class=\"card-small\">\r\n                <DefaultCard\r\n                  v-if=\"currentScene.cards.includes('news')\"\r\n                  :scene=\"currentScene\"\r\n                  card-type=\"news\"\r\n                  :theme-colors=\"themeColors\"\r\n                />\r\n              </div>\r\n              <div class=\"card-small\">\r\n                <DefaultCard\r\n                  v-if=\"currentScene.cards.includes('ambientSound')\"\r\n                  :scene=\"currentScene\"\r\n                  card-type=\"ambientSound\"\r\n                  :theme-colors=\"themeColors\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div v-else-if=\"currentScene.layout === 'minimal'\" class=\"minimal-layout\">\r\n            <!-- 雨夜模式极简布局 -->\r\n            <!-- 灵动岛 -->\r\n            <div class=\"dynamic-island-minimal\">\r\n              <DefaultCard\r\n                v-if=\"currentScene.cards.includes('navigation')\"\r\n                :scene=\"currentScene\"\r\n                card-type=\"dynamicIsland\"\r\n                :theme-colors=\"themeColors\"\r\n              />\r\n            </div>\r\n\r\n            <!-- 底部组件区域 -->\r\n            <div class=\"bottom-components\">\r\n              <!-- VPA组件 (左下角) -->\r\n              <div class=\"vpa-minimal\">\r\n                <DefaultCard\r\n                  v-if=\"currentScene.cards.includes('vpaWidget')\"\r\n                  :scene=\"currentScene\"\r\n                  card-type=\"vpaWidget\"\r\n                  :theme-colors=\"themeColors\"\r\n                />\r\n              </div>\r\n\r\n              <!-- 音乐控制 (右下角) -->\r\n              <div class=\"card-minimal\">\r\n                <DefaultCard\r\n                  v-if=\"currentScene.cards.includes('music')\"\r\n                  :scene=\"currentScene\"\r\n                  card-type=\"music\"\r\n                  :theme-colors=\"themeColors\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div v-else class=\"default-layout\">\r\n            <!-- 默认网格布局 -->\r\n            <div class=\"layout-grid\">\r\n              <div v-for=\"(card, index) in currentScene.cards\" :key=\"index\" class=\"scene-card-slot\">\r\n                <!-- VPA组件使用专用组件 -->\r\n                <VPAAvatarWidget\r\n                  v-if=\"card === 'vpaWidget'\"\r\n                  :size=\"'small'\"\r\n                  :position=\"{ x: 1, y: 1 }\"\r\n                  :theme=\"'glass'\"\r\n                  :theme-colors=\"themeColors\"\r\n                  @avatar-click=\"handleVpaClick\"\r\n                  @mode-changed=\"handleVpaModeChanged\"\r\n                  @animation-changed=\"handleVpaAnimationChanged\"\r\n                />\r\n                <!-- 其他卡片使用DefaultCard -->\r\n                <DefaultCard \r\n                  v-else\r\n                  :scene=\"currentScene\" \r\n                  :card-type=\"card\" \r\n                  :theme-colors=\"themeColors\" \r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </slot>\r\n    </div>\r\n\r\n    <!-- 场景切换动画 -->\r\n    <transition name=\"scene-transition\">\r\n      <div v-if=\"isTransitioning\" class=\"scene-transition-overlay\">\r\n        <div class=\"transition-content\">\r\n          <i class=\"fas fa-spinner fa-spin\"></i>\r\n          <span>正在切换场景...</span>\r\n        </div>\r\n      </div>\r\n    </transition>\r\n\r\n    <!-- 语音交互管理器 -->\r\n    <VoiceInteractionManager \r\n      @scene-switch-requested=\"handleVoiceSceneSwitch\"\r\n      @wallpaper-prompt-ready=\"handleWallpaperPrompt\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, computed, onMounted, onUnmounted, watch } from 'vue'\r\nimport SceneManager from '@/utils/SceneManager'\r\nimport EmotionalPromptGenerator from '@/services/EmotionalPromptGenerator'\r\nimport SceneContextManager from '@/services/SceneContextManager'\r\n\r\nimport DefaultCard from './cards/DefaultCard.vue'\r\nimport KidEducationCard from './cards/KidEducationCard.vue'\r\nimport MusicControlCard from './cards/MusicControlCard.vue'\r\nimport AIScheduleAssistantCard from './cards/AIScheduleAssistantCard.vue'\r\nimport SmartHomeControlCard from './cards/SmartHomeControlCard.vue'\r\nimport NavigationCard from './cards/NavigationCard.vue'\r\nimport AIOrderCard from './cards/AIOrderCard.vue'\r\nimport AIPediaCard from './cards/AIPediaCard.vue'\r\nimport VPAAvatarWidget from './vpa/VPAAvatarWidget.vue'\r\nimport ImmersiveWallpaperInterface from './ImmersiveWallpaperInterface.vue'\r\nimport VoiceInteractionManager from './VoiceInteractionManager.vue'\r\n\r\nexport default {\r\n  name: 'SceneManager',\r\n  components: {\r\n    DefaultCard,\r\n    KidEducationCard,\r\n    MusicControlCard,\r\n    AIScheduleAssistantCard,\r\n    SmartHomeControlCard,\r\n    NavigationCard,\r\n    AIOrderCard,\r\n    AIPediaCard,\r\n    VPAAvatarWidget,\r\n    ImmersiveWallpaperInterface,\r\n    VoiceInteractionManager\r\n  },\r\n  props: {\r\n    initialScene: {\r\n      type: String,\r\n      default: 'default'\r\n    },\r\n    showIndicator: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    autoSwitch: {\r\n      type: Boolean,\r\n      default: false  // 默认禁用自动切换，避免意外的场景切换\r\n    },\r\n    themeColors: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  emits: ['scene-changed', 'context-updated', 'wallpaper-prompt-ready'],\r\n  setup(props, { emit }) {\r\n    const sceneManager = new SceneManager()\r\n    const emotionalPromptGenerator = new EmotionalPromptGenerator()\r\n    const contextManager = new SceneContextManager()\r\n    \r\n    const showSceneSelector = ref(false)\r\n    const autoSwitchEnabled = ref(props.autoSwitch)\r\n    const isTransitioning = ref(false)\r\n    \r\n    // 当前场景\r\n    const currentScene = ref(sceneManager.getCurrentScene())\r\n    \r\n    // 可用场景\r\n    const availableScenes = computed(() => sceneManager.getAllScenes())\r\n    \r\n    // 推荐场景\r\n    const recommendedScenes = ref([])\r\n    \r\n    // 场景样式\r\n    const sceneStyles = computed(() => {\r\n      const scene = currentScene.value\r\n\r\n      // 移除背景样式，让DynamicWallpaperManager管理背景\r\n      let backgroundStyle = {}\r\n\r\n      return {\r\n        '--scene-primary-color': getThemeColor(scene.theme, 'primary'),\r\n        '--scene-secondary-color': getThemeColor(scene.theme, 'secondary'),\r\n        '--scene-background': 'transparent', /* 使用透明背景，让动态壁纸显示 */\r\n        '--scene-text-color': getThemeColor(scene.theme, 'text'),\r\n        ...backgroundStyle\r\n      }\r\n    })\r\n    \r\n    // 获取场景图标\r\n    const getSceneIcon = computed(() => {\r\n      return getSceneIconByType(currentScene.value.id)\r\n    })\r\n    \r\n    // 选择场景\r\n    const selectScene = async (sceneId) => {\r\n      if (sceneId === currentScene.value.id) return\r\n\r\n      isTransitioning.value = true\r\n\r\n      try {\r\n        // 触发场景切换\r\n        const success = sceneManager.switchScene(sceneId, 'manual')\r\n\r\n        if (success) {\r\n          // 更新当前场景的响应式状态\r\n          currentScene.value = sceneManager.getCurrentScene()\r\n\r\n          // 更新上下文管理器\r\n          contextManager.updateContext({\r\n            recentScenes: [...contextManager.context.recentScenes, sceneId].slice(-5),\r\n            sceneSwitchCount: contextManager.context.sceneSwitchCount + 1\r\n          })\r\n\r\n          // 生成场景对应的壁纸\r\n          await generateSceneWallpaper(currentScene.value)\r\n\r\n          // 发送事件\r\n          emit('scene-changed', {\r\n            from: sceneManager.sceneHistory[0]?.from,\r\n            to: sceneId,\r\n            scene: currentScene.value,\r\n            context: contextManager.getContext()\r\n          })\r\n\r\n          // 关闭选择器\r\n          showSceneSelector.value = false\r\n        }\r\n      } catch (error) {\r\n        console.error('场景切换失败:', error)\r\n      } finally {\r\n        setTimeout(() => {\r\n          isTransitioning.value = false\r\n        }, 1000)\r\n      }\r\n    }\r\n    \r\n    // 生成场景壁纸\r\n    const generateSceneWallpaper = async (scene) => {\r\n      if (!scene.wallpaper || scene.wallpaper.startsWith('/')) {\r\n        // 使用默认壁纸\r\n        return scene.wallpaper\r\n      }\r\n      \r\n      try {\r\n        console.log('🎨 开始生成情感化壁纸提示词...')\r\n        \r\n        // 获取当前上下文信息\r\n        const promptContext = contextManager.getPromptGenerationContext()\r\n        console.log('📋 当前上下文:', promptContext)\r\n        \r\n        // 生成情感化提示词\r\n        const emotionalPrompt = await emotionalPromptGenerator.generateEmotionalPrompt(\r\n          scene,\r\n          promptContext\r\n        )\r\n        \r\n        console.log('🎭 情感化提示词生成成功:', emotionalPrompt)\r\n        \r\n        // 触发壁纸生成事件\r\n        emit('wallpaper-prompt-ready', {\r\n          prompt: emotionalPrompt,\r\n          scene: scene,\r\n          context: promptContext\r\n        })\r\n        \r\n        return emotionalPrompt\r\n        \r\n      } catch (error) {\r\n        console.error('情感化壁纸生成失败:', error)\r\n        \r\n        // 使用增强的场景描述作为降级方案\r\n        const fallbackPrompt = emotionalPromptGenerator.getFallbackPrompt(\r\n          scene,\r\n          contextManager.getPromptGenerationContext()\r\n        )\r\n        \r\n        console.log('🔄 使用降级提示词:', fallbackPrompt)\r\n        \r\n        // 触发壁纸生成事件\r\n        emit('wallpaper-prompt-ready', {\r\n          prompt: fallbackPrompt,\r\n          scene: scene,\r\n          context: contextManager.getPromptGenerationContext()\r\n        })\r\n        \r\n        return fallbackPrompt\r\n      }\r\n    }\r\n    \r\n    // 切换场景选择器\r\n    const toggleSceneSelector = () => {\r\n      showSceneSelector.value = !showSceneSelector.value\r\n      if (showSceneSelector.value) {\r\n        updateRecommendations()\r\n      }\r\n    }\r\n    \r\n    // 关闭场景选择器\r\n    const closeSceneSelector = () => {\r\n      showSceneSelector.value = false\r\n    }\r\n    \r\n    // 切换自动切换\r\n    const toggleAutoSwitch = () => {\r\n      autoSwitchEnabled.value = !autoSwitchEnabled.value\r\n    }\r\n    \r\n    // 更新推荐场景\r\n    const updateRecommendations = () => {\r\n      const context = getCurrentContext()\r\n      recommendedScenes.value = sceneManager.getRecommendedScenes(context)\r\n    }\r\n    \r\n    // 获取当前上下文\r\n    const getCurrentContext = () => {\r\n      const now = new Date()\r\n      return {\r\n        time: now.getHours(),\r\n        day: now.getDay(),\r\n        isWeekend: now.getDay() === 0 || now.getDay() === 6,\r\n        passengers: ['driver'], // 可以通过传感器获取\r\n        passengerCount: 1,\r\n        destination: '', // 可以通过导航获取\r\n        gear: 'D', // 可以通过车辆系统获取\r\n        drivingDuration: 0, // 可以通过车辆系统获取\r\n        weather: 'clear', // 可以通过天气API获取\r\n        roadType: 'city', // 可以通过GPS获取\r\n        batteryLevel: 85, // 可以通过车辆系统获取\r\n        locationType: 'city', // 可以通过GPS获取\r\n        fatigueDetected: false, // 可以通过疲劳检测系统获取\r\n        manualTrigger: null,\r\n        calendarEvents: [], // 可以通过日历API获取\r\n        newUserDetected: false,\r\n        accidentDetected: false,\r\n        airbagDeployed: false\r\n      }\r\n    }\r\n    \r\n    // 模拟上下文更新\r\n    const simulateContextUpdate = () => {\r\n      contextManager.simulateDrivingUpdate()\r\n    }\r\n    \r\n    // 获取卡片组件\r\n    const getCardComponent = (card) => {\r\n      // 根据卡片类型返回对应的组件名称\r\n      const cardComponents = {\r\n        // 导航相关\r\n        'navigation': 'NavigationCard',\r\n        'tempNavigation': 'NavigationCard',\r\n\r\n        // 音乐相关\r\n        'music': 'MusicCard',\r\n        'basicMusic': 'MusicCard',\r\n        'romanticMusic': 'MusicCard',\r\n\r\n        // 待办事项\r\n        'todo': 'TodoCard',\r\n\r\n        // 儿童教育\r\n        'kidEducation': 'KidEducationCard',\r\n        'pedia': 'PediaCard',\r\n\r\n        // 娱乐相关\r\n        'videoPlayer': 'VideoPlayerCard',\r\n        'news': 'NewsCard',\r\n        'ambientSound': 'AmbientSoundCard',\r\n\r\n        // 智能家居\r\n        'smartHome': 'SmartHomeCard',\r\n\r\n        // 家庭出行\r\n        'rearSeatControl': 'RearSeatControlCard',\r\n        'facilityFinder': 'FacilityFinderCard',\r\n        'tripReminder': 'TripReminderCard',\r\n\r\n        // 长途驾驶\r\n        'serviceArea': 'ServiceAreaCard',\r\n        'driverStatus': 'DriverStatusCard',\r\n        'vehicleStatus': 'VehicleStatusCard',\r\n\r\n        // 充电相关\r\n        'chargingStatus': 'ChargingStatusCard',\r\n        'entertainment': 'EntertainmentCard',\r\n        'nearbyShops': 'NearbyShopsCard',\r\n\r\n        // 其他功能\r\n        'orderStatus': 'OrderStatusCard',\r\n        'basicControl': 'BasicControlCard',\r\n        'petInfo': 'PetInfoCard',\r\n        'climateControl': 'ClimateControlCard',\r\n        'carWashChecklist': 'CarWashChecklistCard',\r\n        'ambientLight': 'AmbientLightCard',\r\n\r\n        // 安全相关\r\n        'fatigueWarning': 'FatigueWarningCard',\r\n        'restArea': 'RestAreaCard',\r\n        'refreshment': 'RefreshmentCard',\r\n        'emergencyContact': 'EmergencyContactCard',\r\n        'emergencyInfo': 'EmergencyInfoCard',\r\n        'firstAid': 'FirstAidCard',\r\n\r\n        // 用户管理\r\n        'userSelector': 'UserSelectorCard',\r\n        'userPreferences': 'UserPreferencesCard',\r\n        'privacySettings': 'PrivacySettingsCard',\r\n\r\n        // 泊车相关\r\n        'parkingSearch': 'ParkingSearchCard',\r\n        'parkingAssist': 'ParkingAssistCard',\r\n        'costInfo': 'CostInfoCard'\r\n      }\r\n\r\n      return cardComponents[card] || 'DefaultCard'\r\n    }\r\n\r\n    // 获取场景图标\r\n    const getSceneIconByType = (sceneId) => {\r\n      const iconMap = {\r\n        default: 'fas fa-home',\r\n        morningCommuteFamily: 'fas fa-child',\r\n        morningCommuteFocus: 'fas fa-briefcase',\r\n        eveningCommute: 'fas fa-sun',\r\n        waitingMode: 'fas fa-couch',\r\n        rainyNight: 'fas fa-cloud-rain',\r\n        familyTrip: 'fas fa-car',\r\n        longDistance: 'fas fa-road',\r\n        guestMode: 'fas fa-user-shield',\r\n        petMode: 'fas fa-paw',\r\n        carWashMode: 'fas fa-car-side',\r\n        romanticMode: 'fas fa-heart',\r\n        chargingMode: 'fas fa-charging-station',\r\n        fatigueDetection: 'fas fa-exclamation-triangle',\r\n        userSwitch: 'fas fa-users',\r\n        parkingMode: 'fas fa-parking',\r\n        emergencyMode: 'fas fa-ambulance'\r\n      }\r\n      return iconMap[sceneId] || 'fas fa-circle'\r\n    }\r\n\r\n    // 获取优先级颜色\r\n    const getPriorityColor = (priority) => {\r\n      const colorMap = {\r\n        1: '#e74c3c', // 红色 - 最高优先级\r\n        2: '#f39c12', // 橙色 - 高优先级\r\n        3: '#f1c40f', // 黄色 - 中优先级\r\n        4: '#2ecc71', // 绿色 - 低优先级\r\n        5: '#95a5a6'  // 灰色 - 最低优先级\r\n      }\r\n      return colorMap[priority] || '#95a5a6'\r\n    }\r\n\r\n    // 获取主题颜色\r\n    const getThemeColor = (theme, type) => {\r\n      const themeColors = {\r\n        light: {\r\n          primary: '#4a90e2',\r\n          secondary: '#7ed321',\r\n          background: 'rgba(255, 255, 255, 0.1)',\r\n          text: '#333333'\r\n        },\r\n        dark: {\r\n          primary: '#2c3e50',\r\n          secondary: '#3498db',\r\n          background: 'rgba(0, 0, 0, 0.3)',\r\n          text: '#ffffff'\r\n        },\r\n        warm: {\r\n          primary: '#e74c3c',\r\n          secondary: '#f39c12',\r\n          background: 'rgba(255, 193, 7, 0.1)',\r\n          text: '#2c3e50'\r\n        },\r\n        calm: {\r\n          primary: '#3498db',\r\n          secondary: '#2ecc71',\r\n          background: 'rgba(52, 152, 219, 0.1)',\r\n          text: '#2c3e50'\r\n        },\r\n        evening: {\r\n          primary: '#8e44ad',\r\n          secondary: '#e67e22',\r\n          background: 'rgba(142, 68, 173, 0.1)',\r\n          text: '#ffffff'\r\n        },\r\n        relax: {\r\n          primary: '#27ae60',\r\n          secondary: '#2ecc71',\r\n          background: 'rgba(39, 174, 96, 0.1)',\r\n          text: '#ffffff'\r\n        },\r\n        bright: {\r\n          primary: '#f39c12',\r\n          secondary: '#e67e22',\r\n          background: 'rgba(243, 156, 18, 0.1)',\r\n          text: '#2c3e50'\r\n        }\r\n      }\r\n      return themeColors[theme]?.[type] || themeColors.light[type]\r\n    }\r\n    \r\n    // 监听自动切换\r\n    let autoSwitchInterval\r\n    const startAutoSwitch = () => {\r\n      if (!autoSwitchEnabled.value) return\r\n      \r\n      autoSwitchInterval = setInterval(() => {\r\n        updateRecommendations()\r\n        \r\n        // 如果有高优先级的推荐场景，自动切换\r\n        const topRecommendation = recommendedScenes.value[0]\r\n        if (topRecommendation && topRecommendation.score > 8) {\r\n          selectScene(topRecommendation.sceneId)\r\n        }\r\n      }, 30000) // 每30秒检查一次\r\n    }\r\n    \r\n    const stopAutoSwitch = () => {\r\n      if (autoSwitchInterval) {\r\n        clearInterval(autoSwitchInterval)\r\n        autoSwitchInterval = null\r\n      }\r\n    }\r\n    \r\n    // 监听自动切换状态\r\n    watch(autoSwitchEnabled, (newValue) => {\r\n      if (newValue) {\r\n        startAutoSwitch()\r\n      } else {\r\n        stopAutoSwitch()\r\n      }\r\n    })\r\n    \r\n    // 初始化\r\n    onMounted(() => {\r\n      // 设置初始场景\r\n      if (props.initialScene !== 'default') {\r\n        sceneManager.switchScene(props.initialScene, 'initial')\r\n      }\r\n      \r\n      // 初始化上下文\r\n      contextManager.updateContext({\r\n        isDriving: true, // 假设车辆在行驶中\r\n        recentScenes: [props.initialScene === 'default' ? 'default' : props.initialScene]\r\n      })\r\n      \r\n      // 启动自动切换\r\n      if (autoSwitchEnabled.value) {\r\n        startAutoSwitch()\r\n      }\r\n      \r\n      // 启动上下文更新定时器\r\n      setInterval(simulateContextUpdate, 60000) // 每分钟更新一次\r\n      \r\n      // 监听键盘事件\r\n      window.addEventListener('keydown', handleKeyDown)\r\n    })\r\n    \r\n    onUnmounted(() => {\r\n      stopAutoSwitch()\r\n      window.removeEventListener('keydown', handleKeyDown)\r\n    })\r\n    \r\n    // 键盘事件处理\r\n    const handleKeyDown = (event) => {\r\n      if (event.key === 'Escape') {\r\n        closeSceneSelector()\r\n      } else if (event.ctrlKey && event.key === 's') {\r\n        event.preventDefault()\r\n        toggleSceneSelector()\r\n      }\r\n    }\r\n\r\n    // 处理语音场景切换请求\r\n    const handleVoiceSceneSwitch = async (sceneRequest) => {\r\n      console.log('收到语音场景切换请求:', sceneRequest)\r\n      \r\n      if (sceneRequest.sceneId && sceneRequest.confidence > 0.6) {\r\n        try {\r\n          await selectScene(sceneRequest.sceneId)\r\n          console.log(`语音场景切换成功: ${sceneRequest.sceneId} (置信度: ${sceneRequest.confidence})`)\r\n        } catch (error) {\r\n          console.error('语音场景切换失败:', error)\r\n        }\r\n      } else {\r\n        console.log(`语音场景切换置信度过低: ${sceneRequest.confidence}`)\r\n      }\r\n    }\r\n\r\n    // 处理壁纸生成请求\r\n    function handleWallpaperPrompt(prompt) {\r\n      console.log('收到壁纸生成请求:', prompt)\r\n      \r\n      // 如果是直接字符串，包装为对象格式\r\n      const promptData = typeof prompt === 'string' ? {\r\n        prompt,\r\n        scene: currentScene.value,\r\n        context: contextManager.getPromptGenerationContext()\r\n      } : prompt\r\n      \r\n      // 触发壁纸生成事件，传递给父组件\r\n      emit('wallpaper-prompt-ready', promptData)\r\n    }\r\n\r\n    // 新组件事件处理方法\r\n    const handleCardClick = (cardType) => {\r\n      console.log('卡片被点击:', cardType)\r\n      // 可以在这里处理卡片点击事件，比如打开详细界面\r\n    }\r\n\r\n    const handleEducationModeChanged = (mode) => {\r\n      console.log('教育模式变更:', mode)\r\n      // 可以在这里处理教育模式变更\r\n    }\r\n\r\n    const handleLessonCompleted = (lessonType) => {\r\n      console.log('课程完成:', lessonType)\r\n      // 可以在这里处理课程完成事件，比如更新进度\r\n    }\r\n\r\n    const handleSongChanged = (song) => {\r\n      console.log('歌曲变更:', song)\r\n      // 可以在这里处理歌曲变更事件\r\n    }\r\n\r\n    const handlePlayStateChanged = (state) => {\r\n      console.log('播放状态变更:', state)\r\n      // 可以在这里处理播放状态变更\r\n    }\r\n\r\n    const handleVpaClick = (data) => {\r\n      console.log('VPA头像被点击:', data)\r\n      // 可以在这里处理VPA交互\r\n    }\r\n\r\n    const handleVpaModeChanged = (mode) => {\r\n      console.log('VPA模式变更:', mode)\r\n      // 可以在这里处理VPA模式变更\r\n    }\r\n\r\n    const handleVpaAnimationChanged = (animationData) => {\r\n      console.log('VPA动画状态变更:', animationData)\r\n      // 可以在这里处理VPA动画状态变更\r\n    }\r\n\r\n    // 处理AI百科卡片事件\r\n    const handleQuestionAsked = (question) => {\r\n      console.log('Question asked:', question)\r\n      // 可以记录用户提问历史\r\n    }\r\n\r\n    const handleAnswerReceived = (answer) => {\r\n      console.log('Answer received:', answer)\r\n      // 可以分析答案质量，优化推荐\r\n    }\r\n\r\n    const handleCategoryExplored = (category) => {\r\n      console.log('Category explored:', category)\r\n      // 可以根据用户兴趣调整推荐\r\n    }\r\n\r\n    const handleKnowledgeShared = (knowledge) => {\r\n      console.log('Knowledge shared:', knowledge)\r\n      // 可以记录分享行为\r\n    }\r\n\r\n    // 处理AI日程助手卡片事件\r\n    const handleScheduleUpdated = (schedule) => {\r\n      console.log('Schedule updated:', schedule)\r\n      // 可以同步到其他系统\r\n    }\r\n\r\n    const handleReminderSet = (reminder) => {\r\n      console.log('Reminder set:', reminder)\r\n      // 可以设置系统提醒\r\n    }\r\n\r\n    const handleOptimizationApplied = (optimization) => {\r\n      console.log('Optimization applied:', optimization)\r\n      // 可以记录优化效果\r\n    }\r\n\r\n    // 处理AI订单助手卡片事件\r\n    const handleOrderPlaced = (order) => {\r\n      console.log('Order placed:', order)\r\n      // 可以跟踪订单状态\r\n    }\r\n\r\n    const handleOrderTracked = (tracking) => {\r\n      console.log('Order tracked:', tracking)\r\n      // 可以更新订单信息\r\n    }\r\n\r\n    const handleRecommendationSelected = (recommendation) => {\r\n      console.log('Recommendation selected:', recommendation)\r\n      // 可以优化推荐算法\r\n    }\r\n\r\n    // 处理智能家居控制卡片事件\r\n    const handleDeviceControlled = (device) => {\r\n      console.log('Device controlled:', device)\r\n      // 可以同步设备状态\r\n    }\r\n\r\n    const handleSceneModeChanged = (mode) => {\r\n      console.log('Scene mode changed:', mode)\r\n      // 可以调整车内环境\r\n    }\r\n\r\n    const handleSuggestionApplied = (suggestion) => {\r\n      console.log('Suggestion applied:', suggestion)\r\n      // 可以学习用户偏好\r\n    }\r\n\r\n    // 处理导航卡片事件\r\n    const handleNavigationStarted = (destination) => {\r\n      console.log('Navigation started:', destination)\r\n      // 可以切换到导航模式\r\n    }\r\n\r\n    const handleRouteChanged = (route) => {\r\n      console.log('Route changed:', route)\r\n      // 可以更新ETA和路况\r\n    }\r\n\r\n    const handleNavigationStopped = () => {\r\n      console.log('Navigation stopped')\r\n      // 可以切换回其他模式\r\n    }\r\n\r\n    return {\r\n      currentScene,\r\n      availableScenes,\r\n      recommendedScenes,\r\n      showSceneSelector,\r\n      autoSwitchEnabled,\r\n      isTransitioning,\r\n      sceneStyles,\r\n      getSceneIcon,\r\n      getSceneIconByType,\r\n      getPriorityColor,\r\n      getThemeColor,\r\n      selectScene,\r\n      toggleSceneSelector,\r\n      closeSceneSelector,\r\n      toggleAutoSwitch,\r\n      getCardComponent,\r\n      handleWallpaperPrompt,\r\n      handleVoiceSceneSwitch,\r\n      // 新组件事件处理方法\r\n      handleCardClick,\r\n      handleEducationModeChanged,\r\n      handleLessonCompleted,\r\n      handleSongChanged,\r\n      handlePlayStateChanged,\r\n      handleVpaClick,\r\n      handleVpaModeChanged,\r\n      handleVpaAnimationChanged,\r\n      // AI百科卡片事件处理方法\r\n      handleQuestionAsked,\r\n      handleAnswerReceived,\r\n      handleCategoryExplored,\r\n      handleKnowledgeShared,\r\n      // AI日程助手卡片事件处理方法\r\n      handleScheduleUpdated,\r\n      handleReminderSet,\r\n      handleOptimizationApplied,\r\n      // AI订单助手卡片事件处理方法\r\n      handleOrderPlaced,\r\n      handleOrderTracked,\r\n      handleRecommendationSelected,\r\n      // 智能家居控制卡片事件处理方法\r\n      handleDeviceControlled,\r\n      handleSceneModeChanged,\r\n      handleSuggestionApplied,\r\n      // 导航卡片事件处理方法\r\n      handleNavigationStarted,\r\n      handleRouteChanged,\r\n      handleNavigationStopped,\r\n      // 暴露给模板的上下文信息\r\n      contextStats: computed(() => contextManager.getStatistics())\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.scene-manager {\r\n  width: 100%;\r\n  height: 100vh;\r\n  position: relative;\r\n  background: transparent; /* 透明背景，让动态壁纸显示 */\r\n  color: var(--scene-text-color);\r\n  transition: all 0.5s ease;\r\n}\r\n\r\n.scene-indicator {\r\n  position: fixed;\r\n  top: 20px;\r\n  right: 20px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  border-radius: 15px;\r\n  padding: 15px 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  gap: 20px;\r\n  z-index: 1000;\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.scene-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.scene-info i {\r\n  font-size: 18px;\r\n  color: var(--scene-primary-color);\r\n}\r\n\r\n.scene-name {\r\n  font-weight: 600;\r\n  font-size: 14px;\r\n}\r\n\r\n.scene-description {\r\n  font-size: 12px;\r\n  opacity: 0.8;\r\n  margin-left: 10px;\r\n}\r\n\r\n.scene-controls {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.control-btn {\r\n  width: 36px;\r\n  height: 36px;\r\n  border-radius: 50%;\r\n  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));\r\n  background: var(--button-bg, rgba(74, 144, 226, 0.8));\r\n  color: var(--button-color, white);\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n  text-shadow: var(--button-text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.control-btn:hover {\r\n  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));\r\n  transform: scale(1.1);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.control-btn.active {\r\n  background: var(--scene-primary-color);\r\n  color: white;\r\n}\r\n\r\n.scene-selector {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  backdrop-filter: blur(10px);\r\n  z-index: 2000;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.selector-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  padding: 20px;\r\n  border-radius: 15px;\r\n}\r\n\r\n.selector-header h3 {\r\n  margin: 0;\r\n  color: white;\r\n  font-size: 24px;\r\n}\r\n\r\n.close-btn {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));\r\n  background: var(--button-bg, rgba(74, 144, 226, 0.8));\r\n  color: var(--button-color, white);\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n  text-shadow: var(--button-text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.close-btn:hover {\r\n  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));\r\n  transform: scale(1.1);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.scene-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\r\n  gap: 20px;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.scene-card {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  border-radius: 15px;\r\n  padding: 20px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.scene-card:hover {\r\n  background: rgba(255, 255, 255, 0.15);\r\n  transform: translateY(-5px);\r\n}\r\n\r\n.scene-card.active {\r\n  background: var(--scene-primary-color);\r\n  border-color: var(--scene-primary-color);\r\n}\r\n\r\n.scene-card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.scene-card-header i {\r\n  font-size: 20px;\r\n  color: var(--scene-primary-color);\r\n}\r\n\r\n.scene-card.active .scene-card-header i {\r\n  color: white;\r\n}\r\n\r\n.scene-card-header h4 {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  color: white;\r\n}\r\n\r\n.scene-card .scene-description {\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n  margin-bottom: 15px;\r\n  color: white;\r\n}\r\n\r\n.scene-meta {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.scene-priority {\r\n  padding: 4px 8px;\r\n  border-radius: 12px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  color: white;\r\n}\r\n\r\n.scene-auto-switch {\r\n  font-size: 12px;\r\n  color: var(--scene-primary-color);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n}\r\n\r\n.scene-card.active .scene-auto-switch {\r\n  color: white;\r\n}\r\n\r\n.recommended-scenes {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  border-radius: 15px;\r\n  padding: 20px;\r\n}\r\n\r\n.recommended-scenes h4 {\r\n  margin: 0 0 15px 0;\r\n  color: white;\r\n  font-size: 18px;\r\n}\r\n\r\n.recommendation-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n}\r\n\r\n.recommendation-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.recommendation-item:hover {\r\n  background: rgba(255, 255, 255, 0.15);\r\n}\r\n\r\n.recommendation-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.recommendation-info i {\r\n  font-size: 16px;\r\n  color: var(--scene-primary-color);\r\n}\r\n\r\n.recommendation-name {\r\n  font-weight: 600;\r\n  color: white;\r\n  font-size: 14px;\r\n}\r\n\r\n.recommendation-score {\r\n  font-size: 12px;\r\n  opacity: 0.8;\r\n  color: white;\r\n}\r\n\r\n.apply-btn {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));\r\n  background: var(--button-bg, rgba(74, 144, 226, 0.8));\r\n  color: var(--button-color, white);\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n  text-shadow: var(--button-text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.apply-btn:hover {\r\n  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));\r\n  transform: scale(1.1);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.scene-content {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 20px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* 布局样式 */\r\n.scene-content.standard {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  grid-template-rows: 1fr 1fr;\r\n  gap: 20px;\r\n}\r\n\r\n.scene-content.family {\r\n  display: grid;\r\n  grid-template-columns: 2fr 1fr;\r\n  grid-template-rows: 2fr 1fr;\r\n  gap: 20px;\r\n}\r\n\r\n.scene-content.focus {\r\n  display: grid;\r\n  grid-template-columns: 2fr 1fr;\r\n  grid-template-rows: 1fr 1fr;\r\n  gap: 20px;\r\n}\r\n\r\n.scene-content.relax {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n.scene-content.entertainment {\r\n  display: grid;\r\n  grid-template-columns: 2fr 1fr;\r\n  grid-template-rows: 1fr 1fr;\r\n  gap: 20px;\r\n}\r\n\r\n.scene-content.minimal {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.scene-content.driving {\r\n  display: grid;\r\n  grid-template-columns: repeat(3, 1fr);\r\n  gap: 20px;\r\n}\r\n\r\n.scene-content.basic {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 20px;\r\n}\r\n\r\n.scene-content.userSelection {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 40px;\r\n}\r\n\r\n.scene-content.parking {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr 1fr;\r\n  gap: 20px;\r\n}\r\n\r\n.scene-content.emergency {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 20px;\r\n}\r\n\r\n/* 场景布局容器 */\r\n.scene-layout {\r\n  width: 100%;\r\n  height: 100vh;\r\n  min-height: 600px;\r\n  box-sizing: border-box;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n/* 家庭出行模式布局 - 16x9网格 */\r\n.family-layout {\r\n  display: grid;\r\n  grid-template-columns: repeat(16, 1fr);\r\n  grid-template-rows: repeat(9, 1fr);\r\n  gap: min(1vw, 10px);\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: min(1vw, 10px);\r\n}\r\n\r\n/* 灵动岛 16x1 */\r\n.family-layout .dynamic-island {\r\n  grid-column: 1 / 17;\r\n  grid-row: 1 / 2;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 儿童教育卡片 8x8 (调整为给灵动岛让出空间) */\r\n.family-layout .kid-education-card {\r\n  grid-column: 1 / 9;\r\n  grid-row: 2 / 10;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 百科问答卡片 8x3 */\r\n.family-layout .pedia-card {\r\n  grid-column: 9 / 17;\r\n  grid-row: 2 / 5;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* VPA小窗 2x2 */\r\n.family-layout .vpa-widget {\r\n  grid-column: 15 / 17;\r\n  grid-row: 7 / 9;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 专注通勤模式布局 - 16x9网格 */\r\n.focus-layout {\r\n  display: grid;\r\n  grid-template-columns: repeat(16, 1fr);\r\n  grid-template-rows: repeat(9, 1fr);\r\n  gap: min(1vw, 10px);\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: min(1vw, 10px);\r\n}\r\n\r\n/* 灵动岛 16x1 */\r\n.focus-layout .dynamic-island {\r\n  grid-column: 1 / 17;\r\n  grid-row: 1 / 2;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 音乐控制卡片 8x8 (调整为给灵动岛让出空间) */\r\n.focus-layout .music-card {\r\n  grid-column: 1 / 9;\r\n  grid-row: 2 / 10;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 今日待办卡片 8x3 */\r\n.focus-layout .todo-card {\r\n  grid-column: 9 / 17;\r\n  grid-row: 2 / 5;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 订单状态卡片 4x2 */\r\n.focus-layout .order-card {\r\n  grid-column: 9 / 13;\r\n  grid-row: 5 / 7;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 智能家居控制卡片 4x4 */\r\n.focus-layout .smarthome-card {\r\n  grid-column: 13 / 17;\r\n  grid-row: 2 / 6;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 导航卡片 4x3 */\r\n.focus-layout .navigation-card {\r\n  grid-column: 13 / 17;\r\n  grid-row: 6 / 9;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* VPA小窗 2x2 */\r\n.focus-layout .vpa-widget {\r\n  grid-column: 15 / 17;\r\n  grid-row: 7 / 9;\r\n  min-height: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 沉浸式桌面壁纸布局 */\r\n.immersive-layout {\r\n  width: 100%;\r\n  height: 100vh;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 娱乐模式布局 (16x5 视频 + 底部卡片) */\r\n.entertainment-layout {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n  height: 100%;\r\n}\r\n\r\n.entertainment-layout .video-row {\r\n  flex: 3;\r\n}\r\n\r\n.entertainment-layout .card-video {\r\n  width: 100%;\r\n  height: 100%;\r\n  min-height: 250px;\r\n}\r\n\r\n.entertainment-layout .bottom-row {\r\n  flex: 1;\r\n  display: flex;\r\n  gap: 20px;\r\n}\r\n\r\n.entertainment-layout .card-small {\r\n  flex: 1;\r\n  min-height: 120px;\r\n}\r\n\r\n/* 极简模式布局 (雨夜模式) */\r\n.minimal-layout {\r\n  position: relative;\r\n  height: 100%;\r\n  padding: 20px;\r\n}\r\n\r\n.minimal-layout .dynamic-island-minimal {\r\n  position: absolute;\r\n  top: 20px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 400px;\r\n  height: 60px;\r\n}\r\n\r\n.minimal-layout .bottom-components {\r\n  position: absolute;\r\n  bottom: 40px;\r\n  left: 40px;\r\n  right: 40px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-end;\r\n}\r\n\r\n.minimal-layout .vpa-minimal {\r\n  width: 120px;\r\n  height: 120px;\r\n}\r\n\r\n.minimal-layout .card-minimal {\r\n  width: 300px;\r\n  height: 120px;\r\n}\r\n\r\n/* 默认网格布局 */\r\n.default-layout .layout-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n  gap: 20px;\r\n  padding: 20px 0;\r\n}\r\n\r\n.scene-card-slot {\r\n  min-height: 200px;\r\n}\r\n\r\n.scene-transition-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  backdrop-filter: blur(10px);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 3000;\r\n}\r\n\r\n.transition-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 20px;\r\n  color: white;\r\n  font-size: 18px;\r\n}\r\n\r\n.transition-content i {\r\n  font-size: 48px;\r\n  color: var(--scene-primary-color);\r\n}\r\n\r\n/* 场景切换动画 */\r\n.scene-transition-enter-active,\r\n.scene-transition-leave-active {\r\n  transition: all 0.5s ease;\r\n}\r\n\r\n.scene-transition-enter-from {\r\n  opacity: 0;\r\n  transform: scale(0.8);\r\n}\r\n\r\n.scene-transition-leave-to {\r\n  opacity: 0;\r\n  transform: scale(1.2);\r\n}\r\n\r\n.scene-transition-enter-to,\r\n.scene-transition-leave-from {\r\n  opacity: 1;\r\n  transform: scale(1);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .scene-indicator {\r\n    top: 10px;\r\n    right: 10px;\r\n    padding: 10px 15px;\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n  \r\n  .scene-info {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n  \r\n  .scene-description {\r\n    margin-left: 0;\r\n  }\r\n  \r\n  .scene-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .scene-content.standard,\r\n  .scene-content.family,\r\n  .scene-content.focus,\r\n  .scene-content.entertainment,\r\n  .scene-content.driving,\r\n  .scene-content.parking {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n</style>"], "mappings": "AA0WA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,KAAI,QAAS,KAAI;AACjE,OAAOC,YAAW,MAAO,sBAAqB;AAC9C,OAAOC,wBAAuB,MAAO,qCAAoC;AACzE,OAAOC,mBAAkB,MAAO,gCAA+B;AAE/D,OAAOC,WAAU,MAAO,yBAAwB;AAChD,OAAOC,gBAAe,MAAO,8BAA6B;AAC1D,OAAOC,gBAAe,MAAO,8BAA6B;AAC1D,OAAOC,uBAAsB,MAAO,qCAAoC;AACxE,OAAOC,oBAAmB,MAAO,kCAAiC;AAClE,OAAOC,cAAa,MAAO,4BAA2B;AACtD,OAAOC,WAAU,MAAO,yBAAwB;AAChD,OAAOC,WAAU,MAAO,yBAAwB;AAChD,OAAOC,eAAc,MAAO,2BAA0B;AACtD,OAAOC,2BAA0B,MAAO,mCAAkC;AAC1E,OAAOC,uBAAsB,MAAO,+BAA8B;AAElE,eAAe;EACbC,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAE;IACVZ,WAAW;IACXC,gBAAgB;IAChBC,gBAAgB;IAChBC,uBAAuB;IACvBC,oBAAoB;IACpBC,cAAc;IACdC,WAAW;IACXC,WAAW;IACXC,eAAe;IACfC,2BAA2B;IAC3BC;EACF,CAAC;EACDG,KAAK,EAAE;IACLC,YAAY,EAAE;MACZC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDC,aAAa,EAAE;MACbH,IAAI,EAAEI,OAAO;MACbF,OAAO,EAAE;IACX,CAAC;IACDG,UAAU,EAAE;MACVL,IAAI,EAAEI,OAAO;MACbF,OAAO,EAAE,KAAI,CAAG;IAClB,CAAC;IACDI,WAAW,EAAE;MACXN,IAAI,EAAEO,MAAM;MACZL,OAAO,EAAE;IACX;EACF,CAAC;EACDM,KAAK,EAAE,CAAC,eAAe,EAAE,iBAAiB,EAAE,wBAAwB,CAAC;EACrEC,KAAKA,CAACX,KAAK,EAAE;IAAEY;EAAK,CAAC,EAAE;IACrB,MAAMC,YAAW,GAAI,IAAI7B,YAAY,CAAC;IACtC,MAAM8B,wBAAuB,GAAI,IAAI7B,wBAAwB,CAAC;IAC9D,MAAM8B,cAAa,GAAI,IAAI7B,mBAAmB,CAAC;IAE/C,MAAM8B,iBAAgB,GAAIrC,GAAG,CAAC,KAAK;IACnC,MAAMsC,iBAAgB,GAAItC,GAAG,CAACqB,KAAK,CAACO,UAAU;IAC9C,MAAMW,eAAc,GAAIvC,GAAG,CAAC,KAAK;;IAEjC;IACA,MAAMwC,YAAW,GAAIxC,GAAG,CAACkC,YAAY,CAACO,eAAe,CAAC,CAAC;;IAEvD;IACA,MAAMC,eAAc,GAAIzC,QAAQ,CAAC,MAAMiC,YAAY,CAACS,YAAY,CAAC,CAAC;;IAElE;IACA,MAAMC,iBAAgB,GAAI5C,GAAG,CAAC,EAAE;;IAEhC;IACA,MAAM6C,WAAU,GAAI5C,QAAQ,CAAC,MAAM;MACjC,MAAM6C,KAAI,GAAIN,YAAY,CAACO,KAAI;;MAE/B;MACA,IAAIC,eAAc,GAAI,CAAC;MAEvB,OAAO;QACL,uBAAuB,EAAEC,aAAa,CAACH,KAAK,CAACI,KAAK,EAAE,SAAS,CAAC;QAC9D,yBAAyB,EAAED,aAAa,CAACH,KAAK,CAACI,KAAK,EAAE,WAAW,CAAC;QAClE,oBAAoB,EAAE,aAAa;QAAE;QACrC,oBAAoB,EAAED,aAAa,CAACH,KAAK,CAACI,KAAK,EAAE,MAAM,CAAC;QACxD,GAAGF;MACL;IACF,CAAC;;IAED;IACA,MAAMG,YAAW,GAAIlD,QAAQ,CAAC,MAAM;MAClC,OAAOmD,kBAAkB,CAACZ,YAAY,CAACO,KAAK,CAACM,EAAE;IACjD,CAAC;;IAED;IACA,MAAMC,WAAU,GAAI,MAAOC,OAAO,IAAK;MACrC,IAAIA,OAAM,KAAMf,YAAY,CAACO,KAAK,CAACM,EAAE,EAAE;MAEvCd,eAAe,CAACQ,KAAI,GAAI,IAAG;MAE3B,IAAI;QACF;QACA,MAAMS,OAAM,GAAItB,YAAY,CAACuB,WAAW,CAACF,OAAO,EAAE,QAAQ;QAE1D,IAAIC,OAAO,EAAE;UACX;UACAhB,YAAY,CAACO,KAAI,GAAIb,YAAY,CAACO,eAAe,CAAC;;UAElD;UACAL,cAAc,CAACsB,aAAa,CAAC;YAC3BC,YAAY,EAAE,CAAC,GAAGvB,cAAc,CAACwB,OAAO,CAACD,YAAY,EAAEJ,OAAO,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,CAAC;YACzEC,gBAAgB,EAAE1B,cAAc,CAACwB,OAAO,CAACE,gBAAe,GAAI;UAC9D,CAAC;;UAED;UACA,MAAMC,sBAAsB,CAACvB,YAAY,CAACO,KAAK;;UAE/C;UACAd,IAAI,CAAC,eAAe,EAAE;YACpB+B,IAAI,EAAE9B,YAAY,CAAC+B,YAAY,CAAC,CAAC,CAAC,EAAED,IAAI;YACxCE,EAAE,EAAEX,OAAO;YACXT,KAAK,EAAEN,YAAY,CAACO,KAAK;YACzBa,OAAO,EAAExB,cAAc,CAAC+B,UAAU,CAAC;UACrC,CAAC;;UAED;UACA9B,iBAAiB,CAACU,KAAI,GAAI,KAAI;QAChC;MACF,EAAE,OAAOqB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;MAChC,UAAU;QACRE,UAAU,CAAC,MAAM;UACf/B,eAAe,CAACQ,KAAI,GAAI,KAAI;QAC9B,CAAC,EAAE,IAAI;MACT;IACF;;IAEA;IACA,MAAMgB,sBAAqB,GAAI,MAAOjB,KAAK,IAAK;MAC9C,IAAI,CAACA,KAAK,CAACyB,SAAQ,IAAKzB,KAAK,CAACyB,SAAS,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;QACvD;QACA,OAAO1B,KAAK,CAACyB,SAAQ;MACvB;MAEA,IAAI;QACFF,OAAO,CAACI,GAAG,CAAC,oBAAoB;;QAEhC;QACA,MAAMC,aAAY,GAAItC,cAAc,CAACuC,0BAA0B,CAAC;QAChEN,OAAO,CAACI,GAAG,CAAC,WAAW,EAAEC,aAAa;;QAEtC;QACA,MAAME,eAAc,GAAI,MAAMzC,wBAAwB,CAAC0C,uBAAuB,CAC5E/B,KAAK,EACL4B,aACF;QAEAL,OAAO,CAACI,GAAG,CAAC,gBAAgB,EAAEG,eAAe;;QAE7C;QACA3C,IAAI,CAAC,wBAAwB,EAAE;UAC7B6C,MAAM,EAAEF,eAAe;UACvB9B,KAAK,EAAEA,KAAK;UACZc,OAAO,EAAEc;QACX,CAAC;QAED,OAAOE,eAAc;MAEvB,EAAE,OAAOR,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK;;QAEjC;QACA,MAAMW,cAAa,GAAI5C,wBAAwB,CAAC6C,iBAAiB,CAC/DlC,KAAK,EACLV,cAAc,CAACuC,0BAA0B,CAAC,CAC5C;QAEAN,OAAO,CAACI,GAAG,CAAC,aAAa,EAAEM,cAAc;;QAEzC;QACA9C,IAAI,CAAC,wBAAwB,EAAE;UAC7B6C,MAAM,EAAEC,cAAc;UACtBjC,KAAK,EAAEA,KAAK;UACZc,OAAO,EAAExB,cAAc,CAACuC,0BAA0B,CAAC;QACrD,CAAC;QAED,OAAOI,cAAa;MACtB;IACF;;IAEA;IACA,MAAME,mBAAkB,GAAIA,CAAA,KAAM;MAChC5C,iBAAiB,CAACU,KAAI,GAAI,CAACV,iBAAiB,CAACU,KAAI;MACjD,IAAIV,iBAAiB,CAACU,KAAK,EAAE;QAC3BmC,qBAAqB,CAAC;MACxB;IACF;;IAEA;IACA,MAAMC,kBAAiB,GAAIA,CAAA,KAAM;MAC/B9C,iBAAiB,CAACU,KAAI,GAAI,KAAI;IAChC;;IAEA;IACA,MAAMqC,gBAAe,GAAIA,CAAA,KAAM;MAC7B9C,iBAAiB,CAACS,KAAI,GAAI,CAACT,iBAAiB,CAACS,KAAI;IACnD;;IAEA;IACA,MAAMmC,qBAAoB,GAAIA,CAAA,KAAM;MAClC,MAAMtB,OAAM,GAAIyB,iBAAiB,CAAC;MAClCzC,iBAAiB,CAACG,KAAI,GAAIb,YAAY,CAACoD,oBAAoB,CAAC1B,OAAO;IACrE;;IAEA;IACA,MAAMyB,iBAAgB,GAAIA,CAAA,KAAM;MAC9B,MAAME,GAAE,GAAI,IAAIC,IAAI,CAAC;MACrB,OAAO;QACLC,IAAI,EAAEF,GAAG,CAACG,QAAQ,CAAC,CAAC;QACpBC,GAAG,EAAEJ,GAAG,CAACK,MAAM,CAAC,CAAC;QACjBC,SAAS,EAAEN,GAAG,CAACK,MAAM,CAAC,MAAM,KAAKL,GAAG,CAACK,MAAM,CAAC,MAAM,CAAC;QACnDE,UAAU,EAAE,CAAC,QAAQ,CAAC;QAAE;QACxBC,cAAc,EAAE,CAAC;QACjBC,WAAW,EAAE,EAAE;QAAE;QACjBC,IAAI,EAAE,GAAG;QAAE;QACXC,eAAe,EAAE,CAAC;QAAE;QACpBC,OAAO,EAAE,OAAO;QAAE;QAClBC,QAAQ,EAAE,MAAM;QAAE;QAClBC,YAAY,EAAE,EAAE;QAAE;QAClBC,YAAY,EAAE,MAAM;QAAE;QACtBC,eAAe,EAAE,KAAK;QAAE;QACxBC,aAAa,EAAE,IAAI;QACnBC,cAAc,EAAE,EAAE;QAAE;QACpBC,eAAe,EAAE,KAAK;QACtBC,gBAAgB,EAAE,KAAK;QACvBC,cAAc,EAAE;MAClB;IACF;;IAEA;IACA,MAAMC,qBAAoB,GAAIA,CAAA,KAAM;MAClCzE,cAAc,CAAC0E,qBAAqB,CAAC;IACvC;;IAEA;IACA,MAAMC,gBAAe,GAAKC,IAAI,IAAK;MACjC;MACA,MAAMC,cAAa,GAAI;QACrB;QACA,YAAY,EAAE,gBAAgB;QAC9B,gBAAgB,EAAE,gBAAgB;QAElC;QACA,OAAO,EAAE,WAAW;QACpB,YAAY,EAAE,WAAW;QACzB,eAAe,EAAE,WAAW;QAE5B;QACA,MAAM,EAAE,UAAU;QAElB;QACA,cAAc,EAAE,kBAAkB;QAClC,OAAO,EAAE,WAAW;QAEpB;QACA,aAAa,EAAE,iBAAiB;QAChC,MAAM,EAAE,UAAU;QAClB,cAAc,EAAE,kBAAkB;QAElC;QACA,WAAW,EAAE,eAAe;QAE5B;QACA,iBAAiB,EAAE,qBAAqB;QACxC,gBAAgB,EAAE,oBAAoB;QACtC,cAAc,EAAE,kBAAkB;QAElC;QACA,aAAa,EAAE,iBAAiB;QAChC,cAAc,EAAE,kBAAkB;QAClC,eAAe,EAAE,mBAAmB;QAEpC;QACA,gBAAgB,EAAE,oBAAoB;QACtC,eAAe,EAAE,mBAAmB;QACpC,aAAa,EAAE,iBAAiB;QAEhC;QACA,aAAa,EAAE,iBAAiB;QAChC,cAAc,EAAE,kBAAkB;QAClC,SAAS,EAAE,aAAa;QACxB,gBAAgB,EAAE,oBAAoB;QACtC,kBAAkB,EAAE,sBAAsB;QAC1C,cAAc,EAAE,kBAAkB;QAElC;QACA,gBAAgB,EAAE,oBAAoB;QACtC,UAAU,EAAE,cAAc;QAC1B,aAAa,EAAE,iBAAiB;QAChC,kBAAkB,EAAE,sBAAsB;QAC1C,eAAe,EAAE,mBAAmB;QACpC,UAAU,EAAE,cAAc;QAE1B;QACA,cAAc,EAAE,kBAAkB;QAClC,iBAAiB,EAAE,qBAAqB;QACxC,iBAAiB,EAAE,qBAAqB;QAExC;QACA,eAAe,EAAE,mBAAmB;QACpC,eAAe,EAAE,mBAAmB;QACpC,UAAU,EAAE;MACd;MAEA,OAAOA,cAAc,CAACD,IAAI,KAAK,aAAY;IAC7C;;IAEA;IACA,MAAM5D,kBAAiB,GAAKG,OAAO,IAAK;MACtC,MAAM2D,OAAM,GAAI;QACdzF,OAAO,EAAE,aAAa;QACtB0F,oBAAoB,EAAE,cAAc;QACpCC,mBAAmB,EAAE,kBAAkB;QACvCC,cAAc,EAAE,YAAY;QAC5BC,WAAW,EAAE,cAAc;QAC3BC,UAAU,EAAE,mBAAmB;QAC/BC,UAAU,EAAE,YAAY;QACxBC,YAAY,EAAE,aAAa;QAC3BC,SAAS,EAAE,oBAAoB;QAC/BC,OAAO,EAAE,YAAY;QACrBC,WAAW,EAAE,iBAAiB;QAC9BC,YAAY,EAAE,cAAc;QAC5BC,YAAY,EAAE,yBAAyB;QACvCC,gBAAgB,EAAE,6BAA6B;QAC/CC,UAAU,EAAE,cAAc;QAC1BC,WAAW,EAAE,gBAAgB;QAC7BC,aAAa,EAAE;MACjB;MACA,OAAOhB,OAAO,CAAC3D,OAAO,KAAK,eAAc;IAC3C;;IAEA;IACA,MAAM4E,gBAAe,GAAKC,QAAQ,IAAK;MACrC,MAAMC,QAAO,GAAI;QACf,CAAC,EAAE,SAAS;QAAE;QACd,CAAC,EAAE,SAAS;QAAE;QACd,CAAC,EAAE,SAAS;QAAE;QACd,CAAC,EAAE,SAAS;QAAE;QACd,CAAC,EAAE,SAAQ,CAAG;MAChB;MACA,OAAOA,QAAQ,CAACD,QAAQ,KAAK,SAAQ;IACvC;;IAEA;IACA,MAAMnF,aAAY,GAAIA,CAACC,KAAK,EAAE3B,IAAI,KAAK;MACrC,MAAMM,WAAU,GAAI;QAClByG,KAAK,EAAE;UACLC,OAAO,EAAE,SAAS;UAClBC,SAAS,EAAE,SAAS;UACpBC,UAAU,EAAE,0BAA0B;UACtCC,IAAI,EAAE;QACR,CAAC;QACDC,IAAI,EAAE;UACJJ,OAAO,EAAE,SAAS;UAClBC,SAAS,EAAE,SAAS;UACpBC,UAAU,EAAE,oBAAoB;UAChCC,IAAI,EAAE;QACR,CAAC;QACDE,IAAI,EAAE;UACJL,OAAO,EAAE,SAAS;UAClBC,SAAS,EAAE,SAAS;UACpBC,UAAU,EAAE,wBAAwB;UACpCC,IAAI,EAAE;QACR,CAAC;QACDG,IAAI,EAAE;UACJN,OAAO,EAAE,SAAS;UAClBC,SAAS,EAAE,SAAS;UACpBC,UAAU,EAAE,yBAAyB;UACrCC,IAAI,EAAE;QACR,CAAC;QACDI,OAAO,EAAE;UACPP,OAAO,EAAE,SAAS;UAClBC,SAAS,EAAE,SAAS;UACpBC,UAAU,EAAE,yBAAyB;UACrCC,IAAI,EAAE;QACR,CAAC;QACDK,KAAK,EAAE;UACLR,OAAO,EAAE,SAAS;UAClBC,SAAS,EAAE,SAAS;UACpBC,UAAU,EAAE,wBAAwB;UACpCC,IAAI,EAAE;QACR,CAAC;QACDM,MAAM,EAAE;UACNT,OAAO,EAAE,SAAS;UAClBC,SAAS,EAAE,SAAS;UACpBC,UAAU,EAAE,yBAAyB;UACrCC,IAAI,EAAE;QACR;MACF;MACA,OAAO7G,WAAW,CAACqB,KAAK,CAAC,GAAG3B,IAAI,KAAKM,WAAW,CAACyG,KAAK,CAAC/G,IAAI;IAC7D;;IAEA;IACA,IAAI0H,kBAAiB;IACrB,MAAMC,eAAc,GAAIA,CAAA,KAAM;MAC5B,IAAI,CAAC5G,iBAAiB,CAACS,KAAK,EAAE;MAE9BkG,kBAAiB,GAAIE,WAAW,CAAC,MAAM;QACrCjE,qBAAqB,CAAC;;QAEtB;QACA,MAAMkE,iBAAgB,GAAIxG,iBAAiB,CAACG,KAAK,CAAC,CAAC;QACnD,IAAIqG,iBAAgB,IAAKA,iBAAiB,CAACC,KAAI,GAAI,CAAC,EAAE;UACpD/F,WAAW,CAAC8F,iBAAiB,CAAC7F,OAAO;QACvC;MACF,CAAC,EAAE,KAAK,GAAE;IACZ;IAEA,MAAM+F,cAAa,GAAIA,CAAA,KAAM;MAC3B,IAAIL,kBAAkB,EAAE;QACtBM,aAAa,CAACN,kBAAkB;QAChCA,kBAAiB,GAAI,IAAG;MAC1B;IACF;;IAEA;IACA7I,KAAK,CAACkC,iBAAiB,EAAGkH,QAAQ,IAAK;MACrC,IAAIA,QAAQ,EAAE;QACZN,eAAe,CAAC;MAClB,OAAO;QACLI,cAAc,CAAC;MACjB;IACF,CAAC;;IAED;IACApJ,SAAS,CAAC,MAAM;MACd;MACA,IAAImB,KAAK,CAACC,YAAW,KAAM,SAAS,EAAE;QACpCY,YAAY,CAACuB,WAAW,CAACpC,KAAK,CAACC,YAAY,EAAE,SAAS;MACxD;;MAEA;MACAc,cAAc,CAACsB,aAAa,CAAC;QAC3B+F,SAAS,EAAE,IAAI;QAAE;QACjB9F,YAAY,EAAE,CAACtC,KAAK,CAACC,YAAW,KAAM,SAAQ,GAAI,SAAQ,GAAID,KAAK,CAACC,YAAY;MAClF,CAAC;;MAED;MACA,IAAIgB,iBAAiB,CAACS,KAAK,EAAE;QAC3BmG,eAAe,CAAC;MAClB;;MAEA;MACAC,WAAW,CAACtC,qBAAqB,EAAE,KAAK,GAAE;;MAE1C;MACA6C,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEC,aAAa;IAClD,CAAC;IAEDzJ,WAAW,CAAC,MAAM;MAChBmJ,cAAc,CAAC;MACfI,MAAM,CAACG,mBAAmB,CAAC,SAAS,EAAED,aAAa;IACrD,CAAC;;IAED;IACA,MAAMA,aAAY,GAAKE,KAAK,IAAK;MAC/B,IAAIA,KAAK,CAACC,GAAE,KAAM,QAAQ,EAAE;QAC1B5E,kBAAkB,CAAC;MACrB,OAAO,IAAI2E,KAAK,CAACE,OAAM,IAAKF,KAAK,CAACC,GAAE,KAAM,GAAG,EAAE;QAC7CD,KAAK,CAACG,cAAc,CAAC;QACrBhF,mBAAmB,CAAC;MACtB;IACF;;IAEA;IACA,MAAMiF,sBAAqB,GAAI,MAAOC,YAAY,IAAK;MACrD9F,OAAO,CAACI,GAAG,CAAC,aAAa,EAAE0F,YAAY;MAEvC,IAAIA,YAAY,CAAC5G,OAAM,IAAK4G,YAAY,CAACC,UAAS,GAAI,GAAG,EAAE;QACzD,IAAI;UACF,MAAM9G,WAAW,CAAC6G,YAAY,CAAC5G,OAAO;UACtCc,OAAO,CAACI,GAAG,CAAC,aAAa0F,YAAY,CAAC5G,OAAO,UAAU4G,YAAY,CAACC,UAAU,GAAG;QACnF,EAAE,OAAOhG,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAClC;MACF,OAAO;QACLC,OAAO,CAACI,GAAG,CAAC,gBAAgB0F,YAAY,CAACC,UAAU,EAAE;MACvD;IACF;;IAEA;IACA,SAASC,qBAAqBA,CAACvF,MAAM,EAAE;MACrCT,OAAO,CAACI,GAAG,CAAC,WAAW,EAAEK,MAAM;;MAE/B;MACA,MAAMwF,UAAS,GAAI,OAAOxF,MAAK,KAAM,QAAO,GAAI;QAC9CA,MAAM;QACNhC,KAAK,EAAEN,YAAY,CAACO,KAAK;QACzBa,OAAO,EAAExB,cAAc,CAACuC,0BAA0B,CAAC;MACrD,IAAIG,MAAK;;MAET;MACA7C,IAAI,CAAC,wBAAwB,EAAEqI,UAAU;IAC3C;;IAEA;IACA,MAAMC,eAAc,GAAKC,QAAQ,IAAK;MACpCnG,OAAO,CAACI,GAAG,CAAC,QAAQ,EAAE+F,QAAQ;MAC9B;IACF;IAEA,MAAMC,0BAAyB,GAAKC,IAAI,IAAK;MAC3CrG,OAAO,CAACI,GAAG,CAAC,SAAS,EAAEiG,IAAI;MAC3B;IACF;IAEA,MAAMC,qBAAoB,GAAKC,UAAU,IAAK;MAC5CvG,OAAO,CAACI,GAAG,CAAC,OAAO,EAAEmG,UAAU;MAC/B;IACF;IAEA,MAAMC,iBAAgB,GAAKC,IAAI,IAAK;MAClCzG,OAAO,CAACI,GAAG,CAAC,OAAO,EAAEqG,IAAI;MACzB;IACF;IAEA,MAAMC,sBAAqB,GAAKC,KAAK,IAAK;MACxC3G,OAAO,CAACI,GAAG,CAAC,SAAS,EAAEuG,KAAK;MAC5B;IACF;IAEA,MAAMC,cAAa,GAAKC,IAAI,IAAK;MAC/B7G,OAAO,CAACI,GAAG,CAAC,WAAW,EAAEyG,IAAI;MAC7B;IACF;IAEA,MAAMC,oBAAmB,GAAKT,IAAI,IAAK;MACrCrG,OAAO,CAACI,GAAG,CAAC,UAAU,EAAEiG,IAAI;MAC5B;IACF;IAEA,MAAMU,yBAAwB,GAAKC,aAAa,IAAK;MACnDhH,OAAO,CAACI,GAAG,CAAC,YAAY,EAAE4G,aAAa;MACvC;IACF;;IAEA;IACA,MAAMC,mBAAkB,GAAKC,QAAQ,IAAK;MACxClH,OAAO,CAACI,GAAG,CAAC,iBAAiB,EAAE8G,QAAQ;MACvC;IACF;IAEA,MAAMC,oBAAmB,GAAKC,MAAM,IAAK;MACvCpH,OAAO,CAACI,GAAG,CAAC,kBAAkB,EAAEgH,MAAM;MACtC;IACF;IAEA,MAAMC,sBAAqB,GAAKC,QAAQ,IAAK;MAC3CtH,OAAO,CAACI,GAAG,CAAC,oBAAoB,EAAEkH,QAAQ;MAC1C;IACF;IAEA,MAAMC,qBAAoB,GAAKC,SAAS,IAAK;MAC3CxH,OAAO,CAACI,GAAG,CAAC,mBAAmB,EAAEoH,SAAS;MAC1C;IACF;;IAEA;IACA,MAAMC,qBAAoB,GAAKC,QAAQ,IAAK;MAC1C1H,OAAO,CAACI,GAAG,CAAC,mBAAmB,EAAEsH,QAAQ;MACzC;IACF;IAEA,MAAMC,iBAAgB,GAAKC,QAAQ,IAAK;MACtC5H,OAAO,CAACI,GAAG,CAAC,eAAe,EAAEwH,QAAQ;MACrC;IACF;IAEA,MAAMC,yBAAwB,GAAKC,YAAY,IAAK;MAClD9H,OAAO,CAACI,GAAG,CAAC,uBAAuB,EAAE0H,YAAY;MACjD;IACF;;IAEA;IACA,MAAMC,iBAAgB,GAAKC,KAAK,IAAK;MACnChI,OAAO,CAACI,GAAG,CAAC,eAAe,EAAE4H,KAAK;MAClC;IACF;IAEA,MAAMC,kBAAiB,GAAKC,QAAQ,IAAK;MACvClI,OAAO,CAACI,GAAG,CAAC,gBAAgB,EAAE8H,QAAQ;MACtC;IACF;IAEA,MAAMC,4BAA2B,GAAKC,cAAc,IAAK;MACvDpI,OAAO,CAACI,GAAG,CAAC,0BAA0B,EAAEgI,cAAc;MACtD;IACF;;IAEA;IACA,MAAMC,sBAAqB,GAAKC,MAAM,IAAK;MACzCtI,OAAO,CAACI,GAAG,CAAC,oBAAoB,EAAEkI,MAAM;MACxC;IACF;IAEA,MAAMC,sBAAqB,GAAKlC,IAAI,IAAK;MACvCrG,OAAO,CAACI,GAAG,CAAC,qBAAqB,EAAEiG,IAAI;MACvC;IACF;IAEA,MAAMmC,uBAAsB,GAAKC,UAAU,IAAK;MAC9CzI,OAAO,CAACI,GAAG,CAAC,qBAAqB,EAAEqI,UAAU;MAC7C;IACF;;IAEA;IACA,MAAMC,uBAAsB,GAAK/G,WAAW,IAAK;MAC/C3B,OAAO,CAACI,GAAG,CAAC,qBAAqB,EAAEuB,WAAW;MAC9C;IACF;IAEA,MAAMgH,kBAAiB,GAAKC,KAAK,IAAK;MACpC5I,OAAO,CAACI,GAAG,CAAC,gBAAgB,EAAEwI,KAAK;MACnC;IACF;IAEA,MAAMC,uBAAsB,GAAIA,CAAA,KAAM;MACpC7I,OAAO,CAACI,GAAG,CAAC,oBAAoB;MAChC;IACF;IAEA,OAAO;MACLjC,YAAY;MACZE,eAAe;MACfE,iBAAiB;MACjBP,iBAAiB;MACjBC,iBAAiB;MACjBC,eAAe;MACfM,WAAW;MACXM,YAAY;MACZC,kBAAkB;MAClB+E,gBAAgB;MAChBlF,aAAa;MACbK,WAAW;MACX2B,mBAAmB;MACnBE,kBAAkB;MAClBC,gBAAgB;MAChB2B,gBAAgB;MAChBsD,qBAAqB;MACrBH,sBAAsB;MACtB;MACAK,eAAe;MACfE,0BAA0B;MAC1BE,qBAAqB;MACrBE,iBAAiB;MACjBE,sBAAsB;MACtBE,cAAc;MACdE,oBAAoB;MACpBC,yBAAyB;MACzB;MACAE,mBAAmB;MACnBE,oBAAoB;MACpBE,sBAAsB;MACtBE,qBAAqB;MACrB;MACAE,qBAAqB;MACrBE,iBAAiB;MACjBE,yBAAyB;MACzB;MACAE,iBAAiB;MACjBE,kBAAkB;MAClBE,4BAA4B;MAC5B;MACAE,sBAAsB;MACtBE,sBAAsB;MACtBC,uBAAuB;MACvB;MACAE,uBAAuB;MACvBC,kBAAkB;MAClBE,uBAAuB;MACvB;MACAC,YAAY,EAAElN,QAAQ,CAAC,MAAMmC,cAAc,CAACgL,aAAa,CAAC,CAAC;IAC7D;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}