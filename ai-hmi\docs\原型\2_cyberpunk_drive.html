<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>赛博朋克归途 (Cyberpunk Drive)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap');
        body {
            font-family: 'Orbitron', sans-serif;
            background-color: #0a0a1a; /* 深黑蓝背景 */
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            overflow: hidden; /* 禁止滚动 */
            height: 100vh;
            width: 100vw;
            color: #e0e0e0;
            position: relative;
        }
        /* 动态氛围层 - 赛博朋克风格 */
        .ambiance-layer {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }
        .neon-grid {
            position: absolute;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: gridPulse 3s ease-in-out infinite;
        }
        .floating-particles {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #00ffff;
            border-radius: 50%;
            box-shadow: 0 0 10px #00ffff;
            animation: particleFloat 6s infinite ease-in-out;
        }
        .floating-particles:nth-child(2) { left: 20%; animation-delay: 1s; background: #ff00ff; box-shadow: 0 0 10px #ff00ff; }
        .floating-particles:nth-child(3) { left: 40%; animation-delay: 2s; }
        .floating-particles:nth-child(4) { left: 60%; animation-delay: 3s; background: #ff00ff; box-shadow: 0 0 10px #ff00ff; }
        .floating-particles:nth-child(5) { left: 80%; animation-delay: 4s; }
        @keyframes gridPulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.8; }
        }
        @keyframes particleFloat {
            0%, 100% { transform: translateY(100vh) scale(0); opacity: 0; }
            10% { opacity: 1; transform: scale(1); }
            90% { opacity: 1; }
        }
        .card {
            background-color: rgba(10, 20, 40, 0.75); /* 半透明深蓝 */
            backdrop-filter: blur(10px) saturate(120%);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 0px; /* 锐角 */
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.2), inset 0 0 5px rgba(0, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #ff00ff);
            animation: scanline 4s linear infinite;
        }
        @keyframes scanline {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        .card:hover {
            border-color: rgba(255, 0, 255, 0.5);
            box-shadow: 0 0 25px rgba(255, 0, 255, 0.4), inset 0 0 10px rgba(255, 0, 255, 0.3);
        }
        .grid-container {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            grid-template-rows: repeat(4, 1fr);
            gap: 2rem; /* 卡片间距 */
            width: 100%;
            height: 100%;
            padding: 2rem;
            position: relative;
            z-index: 2;
        }
        .text-neon-pink { color: #ff00ff; text-shadow: 0 0 5px #ff00ff, 0 0 10px #ff00ff; }
        .text-neon-cyan { color: #00ffff; text-shadow: 0 0 5px #00ffff, 0 0 10px #00ffff; }
        .text-acid-green { color: #7fff00; text-shadow: 0 0 5px #7fff00, 0 0 10px #7fff00; }
        .glitch-effect {
            animation: glitch 1.5s linear infinite alternate-reverse;
        }
        @keyframes glitch{
          2%,64%{ transform: translate(2px,0) skew(0deg); }
          4%,60%{ transform: translate(-2px,0) skew(0deg); }
          62%{ transform: translate(0,0) skew(5deg); }
        }
        
        /* 返回按钮样式 - 赛博朋克风格 */
        .back-button {
            position: fixed;
            top: 2rem;
            left: 2rem;
            z-index: 1000;
            background: rgba(10, 20, 40, 0.9);
            backdrop-filter: blur(15px);
            border: 2px solid #00ffff;
            border-radius: 0;
            padding: 1rem 1.5rem;
            color: #00ffff;
            text-decoration: none;
            font-weight: 700;
            font-family: 'Orbitron', sans-serif;
            transition: all 0.3s ease;
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
            text-shadow: 0 0 5px #00ffff;
        }
        
        .back-button:hover {
            background: rgba(255, 0, 255, 0.1);
            border-color: #ff00ff;
            color: #ff00ff;
            text-shadow: 0 0 10px #ff00ff;
            box-shadow: 0 0 25px rgba(255, 0, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .back-button i {
            margin-right: 0.5rem;
        }
    </style>
</head>
<body style="background-image: url('https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2125&q=80');">

    <!-- 返回按钮 -->
    <a href="过渡/complete_transition_demo.html" class="back-button" data-transition="angled_panel">
        <i class="fas fa-arrow-left"></i>
        RETURN
    </a>

    <!-- 动态氛围层 -->
    <div class="ambiance-layer">
        <div class="neon-grid"></div>
        <div class="floating-particles"></div>
        <div class="floating-particles"></div>
        <div class="floating-particles"></div>
        <div class="floating-particles"></div>
        <div class="floating-particles"></div>
    </div>

    <div class="grid-container">

        <!-- Dynamic Island (4x1) -->
        <div class="card col-span-4 row-span-1 flex items-center justify-between px-6">
            <div class="flex items-center space-x-3">
                <i class="fas fa-satellite-dish fa-lg text-neon-cyan glitch-effect"></i>
                <div>
                    <p class="text-lg font-bold text-neon-cyan">自动驾驶已接管</p>
                    <p class="text-sm text-gray-400">前往：充电桩</p>
                </div>
            </div>
            <div class="text-right">
                <p class="text-3xl font-bold text-neon-pink">18:30</p>
                <p class="text-sm text-gray-400">PM</p>
            </div>
        </div>

        <!-- Weather Card (4x1) -->
        <div class="card col-span-4 row-span-1 flex items-center justify-between px-6">
            <div class="flex items-center space-x-3">
                <i class="fas fa-cloud-moon fa-lg text-neon-cyan"></i>
                <div>
                    <p class="text-lg font-bold text-neon-cyan">夜间多云</p>
                    <p class="text-sm text-gray-400">空气质量：优</p>
                </div>
            </div>
            <div class="text-right">
                <p class="text-3xl font-bold text-neon-pink">18°C</p>
                <p class="text-sm text-gray-400">体感 20°C</p>
            </div>
        </div>

        <!-- VPA Avatar (2x2) -->
        <div class="card col-span-2 row-span-2 flex flex-col items-center justify-center p-4">
            <img src="https://media.giphy.com/media/3oKIPnAiaMCws8nOsE/giphy.gif" alt="VPA Avatar" class="w-32 h-32 rounded-full border-2 border-neon-pink object-cover opacity-80 glitch-effect" style="filter: brightness(1.2) contrast(1.1);">
            <p class="mt-4 text-xl font-semibold text-neon-pink">小绿</p>
            <p class="text-gray-400">已处理15条新消息</p>
        </div>

        <!-- System Status Card (4x2) -->
        <div class="card col-span-4 row-span-2 flex flex-col p-6">
            <h3 class="text-2xl font-bold text-neon-cyan mb-4">车辆状态 [SYSTEM STATUS]</h3>
            <div class="flex-grow grid grid-cols-2 gap-4 text-lg">
                <p>电池电量: <span class="text-acid-green font-bold">88%</span></p>
                <p>外部温度: <span class="text-acid-green font-bold">28°C</span></p>
                <p>网络连接: <span class="text-acid-green font-bold">5G Quantum Link</span></p>
                <p>轮胎压力: <span class="text-acid-green font-bold">正常</span></p>
                <p>驱动模式: <span class="text-acid-green font-bold">巡航</span></p>
                <p>安全系统: <span class="text-acid-green font-bold">已激活</span></p>
            </div>
        </div>

        <!-- Media Card (2x2) -->
        <div class="card col-span-2 row-span-2 flex flex-col items-center justify-center p-6">
            <i class="fab fa-soundcloud fa-6x text-neon-pink glitch-effect"></i>
            <p class="text-2xl font-bold text-neon-pink mt-4">Synthwave Mix</p>
            <p class="text-lg text-gray-400">正在播放</p>
        </div>

        <!-- Message Card (8x1) -->
        <div class="card col-span-8 row-span-1 flex items-center justify-between px-8">
             <div class="flex items-center space-x-4">
                <i class="fas fa-envelope-open-text fa-2x text-neon-cyan"></i>
                <div>
                    <p class="text-2xl font-bold text-neon-cyan">新消息来自：老婆</p>
                    <p class="text-lg text-gray-400">“下班路上顺便带瓶酱油回来~”</p>
                </div>
            </div>
            <button class="border-2 border-neon-pink text-neon-pink font-bold py-3 px-6 hover:bg-neon-pink hover:text-black transition-all duration-300">
                语音回复
            </button>
        </div>

    </div>

    <!-- 引入过渡管理器 -->
    <script src="过渡/enhanced_transition_manager.js"></script>
    
    <script>
        // 页面加载完成后初始化过渡管理器
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否已有全局过渡管理器
            if (!window.enhancedTransitionManager) {
                window.enhancedTransitionManager = new EnhancedTransitionManager({
                    enableDebug: false,
                    enableSnapshots: true,
                    snapshotQuality: 0.8,
                    transitionDuration: 1.2
                });
            }
        });
    </script>

</body>
</html>