"""
MCP服务器实现
用于将流式输出工具暴露为HTTP API，支持AutoGen调用
"""

import asyncio
import json
import logging
import os
import sys
import traceback
import time  # 添加时间模块
from typing import Any, Dict, List, Optional, Union, Callable
from enum import Enum
from dataclasses import dataclass, asdict, field
from aiohttp import web
import inspect
import uuid

import mcp_tools
from mcp_tools import StreamingTools

logger = logging.getLogger(__name__)

class MCPCapability(str, Enum):
    """MCP能力枚举"""
    TOOLS = "tools"
    RESOURCES = "resources"
    PROMPTS = "prompts"

@dataclass
class MCPServer:
    """MCP服务器实现"""
    
    name: str
    description: str = "流式输出MCP服务器"
    version: str = "1.0.0"
    capabilities: Dict[MCPCapability, Dict[str, Any]] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化服务器"""
        self.app = web.Application()
        self.streaming_tools = StreamingTools()
        self.setup_routes()
        
        # 初始化能力
        if not self.capabilities:
            self.capabilities = {
                MCPCapability.TOOLS: {},
                MCPCapability.RESOURCES: {},
                MCPCapability.PROMPTS: {}
            }
        
        # 注册工具
        self._register_tools()
    
    def _register_tools(self):
        """注册工具"""
        try:
            logger.info("🔍 开始注册MCP工具...")
            
            # 获取工具定义
            tools = self.streaming_tools.get_tool_definitions()
            if not tools:
                logger.warning("⚠️ 获取工具定义返回空列表")
            else:
                logger.info(f"✅ 获取到工具定义: {len(tools)}个工具")
            
            # 获取工具实现
            implementations = self.streaming_tools.get_tool_implementations()
            if not implementations:
                logger.warning("⚠️ 获取工具实现返回空字典")
            else:
                logger.info(f"✅ 获取到工具实现: {len(implementations)}个实现")
            
            # 确保capabilities已初始化
            if MCPCapability.TOOLS not in self.capabilities:
                self.capabilities[MCPCapability.TOOLS] = {}
            
            # 注册工具
            registered_count = 0
            for tool_def in tools:
                tool_name = tool_def.get("name")
                if not tool_name:
                    logger.warning(f"⚠️ 工具定义缺少名称: {tool_def}")
                    continue
                    
                if tool_name in implementations:
                    self.capabilities[MCPCapability.TOOLS][tool_name] = {
                        "definition": tool_def,
                        "implementation": implementations[tool_name]
                    }
                    logger.info(f"✅ 注册工具: {tool_name} - {tool_def.get('description', '无描述')}")
                    registered_count += 1
                else:
                    logger.warning(f"⚠️ 工具 {tool_name} 没有对应的实现")
            
            logger.info(f"🎉 工具注册完成，共注册 {registered_count} 个工具")
            
            # 如果没有注册任何工具，记录警告
            if registered_count == 0:
                logger.warning("⚠️ 没有成功注册任何工具，SSE和tools接口可能无法正常工作")
                
        except Exception as e:
            logger.error(f"❌ 注册工具时出错: {str(e)}")
            logger.exception("详细错误信息:")
    
    def setup_routes(self):
        """设置路由"""
        self.app.router.add_get("/", self.handle_home)
        self.app.router.add_get("/healthz", self.handle_health_check)
        self.app.router.add_get("/health", self.handle_health_check)  # 添加/health端点
        self.app.router.add_get("/capabilities", self.handle_capabilities)
        
        # MCP API路由
        self.app.router.add_get("/api/v1", self.handle_api_info)
        self.app.router.add_get("/api/v1/tools", self.handle_list_tools)
        self.app.router.add_post("/api/v1/tools/{tool_name}", self.handle_call_tool)
        
        # 允许跨域请求
        self.app.router.add_options("/api/v1/tools/{tool_name}", self.handle_preflight)
        
        # WebSocket支持的路由
        self.app.router.add_get("/api/v1/connect", self.handle_websocket_connect)
        
        # 新增SSE支持路由，用于AutoGen连接
        self.app.router.add_get("/api/v1/sse", self.handle_sse_connect)
    
    async def handle_home(self, request: web.Request) -> web.Response:
        """处理首页请求"""
        return web.json_response({
            "name": self.name,
            "description": self.description,
            "version": self.version,
            "status": "active"
        })
    
    async def handle_health_check(self, request: web.Request) -> web.Response:
        """处理健康检查请求"""
        return web.json_response({
            "status": "ok",
            "timestamp": time.time()  # 修复时间函数调用
        })
    
    async def handle_capabilities(self, request: web.Request) -> web.Response:
        """处理能力列表请求"""
        # 过滤掉实现，只返回定义
        capabilities = {}
        for cap_name, cap_items in self.capabilities.items():
            capabilities[cap_name] = {}
            for item_name, item_data in cap_items.items():
                if isinstance(item_data, dict) and "definition" in item_data:
                    capabilities[cap_name][item_name] = item_data["definition"]
                else:
                    capabilities[cap_name][item_name] = item_data
        
        return web.json_response({
            "capabilities": capabilities
        })
    
    async def handle_api_info(self, request: web.Request) -> web.Response:
        """处理API信息请求"""
        return web.json_response({
            "api_version": "v1",
            "server_name": self.name,
            "server_version": self.version,
            "capabilities": list(self.capabilities.keys())
        })
    
    async def handle_list_tools(self, request: web.Request) -> web.Response:
        """处理工具列表请求"""
        logger.info(f"📝 收到工具列表请求: {request.url}")
        try:
            tools = []
            for tool_name, tool_data in self.capabilities[MCPCapability.TOOLS].items():
                if isinstance(tool_data, dict) and "definition" in tool_data:
                    tools.append(tool_data["definition"])
            
            logger.info(f"🔢 返回工具列表，共 {len(tools)} 个工具")
            
            # 记录每个工具
            for i, tool in enumerate(tools):
                logger.info(f"🛠️ 工具 {i+1}: {tool.get('name', '未命名')} - {tool.get('description', '无描述')}")
            
            return web.json_response({
                "tools": tools
            })
        except Exception as e:
            logger.error(f"❌ 处理工具列表请求失败: {str(e)}")
            logger.exception("详细异常信息:")
            return web.json_response({
                "error": f"处理工具列表请求失败: {str(e)}"
            }, status=500)
    
    async def handle_preflight(self, request: web.Request) -> web.Response:
        """处理预检请求（CORS）"""
        response = web.Response()
        response.headers["Access-Control-Allow-Origin"] = "*"
        response.headers["Access-Control-Allow-Methods"] = "POST, GET, OPTIONS"
        response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
        return response
    
    async def handle_call_tool(self, request: web.Request) -> web.Response:
        """处理工具调用请求"""
        # 添加CORS头
        response_headers = {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization"
        }
        
        tool_name = request.match_info.get("tool_name")
        
        # 检查工具是否存在
        if tool_name not in self.capabilities[MCPCapability.TOOLS]:
            return web.json_response({
                "error": f"工具不存在: {tool_name}"
            }, status=404, headers=response_headers)
        
        try:
            # 读取请求体
            body = await request.json()
            
            # 获取工具实现
            tool_impl = self.capabilities[MCPCapability.TOOLS][tool_name]["implementation"]
            tool_def = self.capabilities[MCPCapability.TOOLS][tool_name]["definition"]
            
            # 检查参数
            required_params = tool_def.get("input_schema", {}).get("required", [])
            for param in required_params:
                if param not in body:
                    return web.json_response({
                        "error": f"缺少必要参数: {param}"
                    }, status=400, headers=response_headers)
            
            # 调用工具
            tool_result = await tool_impl(**body)
            
            return web.json_response(tool_result, headers=response_headers)
        
        except json.JSONDecodeError:
            return web.json_response({
                "error": "无效的JSON格式"
            }, status=400, headers=response_headers)
        
        except Exception as e:
            logger.error(f"工具调用失败: {str(e)}")
            logger.error(traceback.format_exc())
            return web.json_response({
                "error": f"工具调用失败: {str(e)}",
                "details": traceback.format_exc()
            }, status=500, headers=response_headers)
    
    async def handle_websocket_connect(self, request: web.Request) -> web.WebSocketResponse:
        """处理WebSocket连接请求"""
        # 创建WebSocket响应
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        
        session_id = None
        
        try:
            # 处理WebSocket消息
            async for msg in ws:
                if msg.type == web.WSMsgType.TEXT:
                    try:
                        data = json.loads(msg.data)
                        cmd = data.get("cmd")
                        
                        if cmd == "call_tool":
                            # 调用工具
                            tool_name = data.get("tool_name")
                            params = data.get("params", {})
                            
                            # 检查工具是否存在
                            if tool_name not in self.capabilities[MCPCapability.TOOLS]:
                                await ws.send_json({
                                    "type": "error",
                                    "error": f"工具不存在: {tool_name}"
                                })
                                continue
                            
                            # 获取工具实现
                            tool_impl = self.capabilities[MCPCapability.TOOLS][tool_name]["implementation"]
                            
                            # 调用工具
                            tool_result = await tool_impl(**params)
                            
                            # 发送结果
                            await ws.send_json({
                                "type": "tool_result",
                                "tool_name": tool_name,
                                "result": tool_result
                            })
                            
                            # 保存会话ID（如果是创建会话）
                            if tool_name == "create_session" and tool_result.get("success"):
                                session_id = tool_result.get("session_id")
                        
                        elif cmd == "close":
                            # 关闭连接
                            break
                        
                        else:
                            # 未知命令
                            await ws.send_json({
                                "type": "error",
                                "error": f"未知命令: {cmd}"
                            })
                    
                    except json.JSONDecodeError:
                        await ws.send_json({
                            "type": "error",
                            "error": "无效的JSON格式"
                        })
                    
                    except Exception as e:
                        logger.error(f"WebSocket处理异常: {str(e)}")
                        await ws.send_json({
                            "type": "error",
                            "error": f"处理失败: {str(e)}"
                        })
                
                elif msg.type == web.WSMsgType.ERROR:
                    logger.error(f"WebSocket连接错误: {ws.exception()}")
            
            logger.info("WebSocket连接关闭")
            
            # 清理会话（如果有）
            if session_id:
                try:
                    await self.streaming_tools.close_session(session_id)
                except Exception as e:
                    logger.error(f"关闭会话失败: {str(e)}")
            
            return ws
        
        except Exception as e:
            logger.error(f"WebSocket处理器异常: {str(e)}")
            if not ws.closed:
                await ws.close()
            return ws
    
    async def handle_sse_connect(self, request: web.Request) -> web.StreamResponse:
        """处理SSE连接请求，用于AutoGen MCP工具连接"""
        try:
            logger.info(f"📝 收到SSE连接请求: {request.url}")
            
            # 设置会话ID（可能来自于查询参数或生成新的）
            session_id = request.query.get("session_id")
            if not session_id:
                session_id = str(uuid.uuid4())
                logger.info(f"💡 生成新会话ID: {session_id}")
            else:
                logger.info(f"💡 使用客户端提供的会话ID: {session_id}")
            
            # 请求ID计数器（用于JSON-RPC响应）
            request_id = 1
            
            # 设置SSE响应
            response = web.StreamResponse()
            response.headers["Content-Type"] = "text/event-stream"
            response.headers["Cache-Control"] = "no-cache"
            response.headers["Connection"] = "keep-alive"
            response.headers["Access-Control-Allow-Origin"] = "*"
            await response.prepare(request)
            
            # 向客户端发送会话ID确认（JSON-RPC格式）
            session_info = {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {
                    "connection_established": True,
                    "session_id": session_id,
                    "server_name": self.name,
                    "server_version": self.version
                }
            }
            request_id += 1
            await response.write(f"data: {json.dumps(session_info)}\n\n".encode())
            logger.info(f"📤 向SSE客户端发送连接确认 (JSON-RPC格式)")
            
            # 获取工具列表
            tools = []
            try:
                for tool_name, tool_data in self.capabilities[MCPCapability.TOOLS].items():
                    if isinstance(tool_data, dict) and "definition" in tool_data:
                        tools.append(tool_data["definition"])
                
                logger.info(f"🔢 将发送工具列表，共 {len(tools)} 个工具")
                
                # 记录每个工具
                for i, tool in enumerate(tools):
                    logger.info(f"🛠️ 工具 {i+1}: {tool.get('name', '未命名')} - {tool.get('description', '无描述')}")
            except Exception as e:
                logger.error(f"❌ 准备工具列表失败: {str(e)}")
                tools = []  # 确保tools是一个列表，即使为空
            
            # 向客户端发送工具列表（JSON-RPC格式）
            tools_info = {
                "jsonrpc": "2.0",
                "id": request_id,
                "method": "tools/list",
                "result": {
                    "tools": tools
                }
            }
            request_id += 1
            
            logger.info(f"📤 发送工具列表到SSE客户端: {len(tools)}个工具 (JSON-RPC格式)")
            await response.write(f"data: {json.dumps(tools_info)}\n\n".encode())
            logger.info(f"📤 工具列表已发送")
            
            # 尝试创建会话（如果尚未存在）
            try:
                session_name = f"sse-client-{session_id}"
                meta_data = {
                    "client_type": "sse",
                    "client_ip": request.remote,
                    "client_url": str(request.url),
                    "user_agent": request.headers.get("User-Agent", "unknown")
                }
                
                logger.info(f"🔄 尝试为SSE客户端创建会话: {session_name}")
                result = self.streaming_tools.create_session(
                    session_name=session_name,
                    meta_data=meta_data
                )
                logger.info(f"✅ SSE会话创建结果: {str(result)}")
                
                # 发送会话创建结果（JSON-RPC格式）
                session_result = {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "result": {
                        "session_created": True,
                        "session_id": session_id,
                        "session_name": session_name
                    }
                }
                request_id += 1
                await response.write(f"data: {json.dumps(session_result)}\n\n".encode())
                
            except Exception as e:
                # 创建会话可能会失败，例如会话已存在
                logger.warning(f"⚠️ SSE会话创建失败，尝试使用已有会话: {str(e)}")
                
                # 发送会话创建失败信息（JSON-RPC格式）
                session_error = {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "error": {
                        "code": -32000,
                        "message": f"会话创建失败: {str(e)}"
                    }
                }
                request_id += 1
                await response.write(f"data: {json.dumps(session_error)}\n\n".encode())
            
            # 事件处理函数
            subscribers = []
            
            async def on_event(event):
                try:
                    # 将原始事件转换为JSON-RPC格式
                    nonlocal request_id
                    event_type = event.get("event_type", "unknown")
                    event_data = event.get("data", {})
                    
                    # 构造JSON-RPC格式的事件
                    jsonrpc_event = {
                        "jsonrpc": "2.0",
                        "id": request_id,
                    }
                    request_id += 1
                    
                    # 根据事件类型设置method或result
                    if event_type in ["thinking", "content", "code"]:
                        jsonrpc_event["method"] = f"events/{event_type}"
                        jsonrpc_event["params"] = event_data
                    else:
                        # 其他事件作为通知（没有id）
                        jsonrpc_event["method"] = f"events/{event_type}"
                        jsonrpc_event["params"] = event_data
                    
                    event_json = json.dumps(jsonrpc_event)
                    logger.debug(f"📤 SSE发送事件: {event_type} (JSON-RPC格式)")
                    await response.write(f"data: {event_json}\n\n".encode())
                except Exception as e:
                    logger.error(f"❌ SSE事件发送错误: {str(e)}")
            
            # 尝试订阅事件
            logger.info(f"🔄 尝试订阅SSE会话事件: {session_id}")
            try:
                # 检查subscribe_to_session方法是否存在
                if hasattr(self.streaming_tools, 'subscribe_to_session'):
                    logger.info("✅ 找到subscribe_to_session方法")
                    sub_result = await self.streaming_tools.subscribe_to_session(session_id, on_event)
                    if sub_result:
                        subscribers.append(on_event)
                        logger.info(f"✅ 成功订阅SSE会话事件: {session_id}")
                        
                        # 发送订阅成功通知（JSON-RPC格式）
                        sub_success = {
                            "jsonrpc": "2.0",
                            "id": request_id,
                            "result": {
                                "subscription": "success",
                                "session_id": session_id
                            }
                        }
                        request_id += 1
                        await response.write(f"data: {json.dumps(sub_success)}\n\n".encode())
                    else:
                        logger.warning(f"⚠️ 订阅SSE会话事件返回失败: {session_id}")
                        
                        # 发送订阅失败通知（JSON-RPC格式）
                        sub_error = {
                            "jsonrpc": "2.0",
                            "id": request_id,
                            "error": {
                                "code": -32001,
                                "message": "订阅事件失败"
                            }
                        }
                        request_id += 1
                        await response.write(f"data: {json.dumps(sub_error)}\n\n".encode())
                else:
                    # 如果没有订阅方法，记录警告
                    logger.warning("⚠️ StreamingTools没有subscribe_to_session方法，SSE将无法接收实时事件")
                    
                    # 发送一个模拟事件，告知客户端连接成功但可能无法接收实时事件（JSON-RPC格式）
                    dummy_event = {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "method": "events/status",
                        "params": {
                            "status": "connected",
                            "message": "SSE连接已建立，但可能无法接收实时事件",
                            "source": "sse-endpoint"
                        }
                    }
                    request_id += 1
                    logger.info(f"📤 发送SSE连接状态事件 (JSON-RPC格式)")
                    await response.write(f"data: {json.dumps(dummy_event)}\n\n".encode())
            except Exception as e:
                logger.error(f"❌ 订阅SSE会话事件失败: {str(e)}")
                logger.error(traceback.format_exc())
                
                # 发送订阅异常通知（JSON-RPC格式）
                sub_exception = {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "error": {
                        "code": -32002,
                        "message": f"订阅事件异常: {str(e)}"
                    }
                }
                request_id += 1
                await response.write(f"data: {json.dumps(sub_exception)}\n\n".encode())
            
            heartbeat_count = 0
            # 保持连接直到客户端断开
            logger.info(f"🔄 开始SSE心跳循环: {session_id}")
            while True:
                # 发送心跳保持连接（JSON-RPC格式的通知）
                heartbeat_count += 1
                
                # 每10次真实心跳发送一次JSON-RPC格式的心跳消息
                if heartbeat_count % 10 == 0:
                    heartbeat_msg = {
                        "jsonrpc": "2.0",
                        "method": "heartbeat",
                        "params": {
                            "count": heartbeat_count,
                            "timestamp": time.time()
                        }
                    }
                    logger.debug(f"💓 发送SSE JSON-RPC心跳 #{heartbeat_count}: {session_id}")
                    await response.write(f"data: {json.dumps(heartbeat_msg)}\n\n".encode())
                else:
                    # 使用SSE注释作为轻量级心跳
                    logger.debug(f"💓 发送SSE心跳 #{heartbeat_count}: {session_id}")
                    await response.write(": heartbeat\n\n".encode())
                
                await asyncio.sleep(30)  # 每30秒发送一次心跳
                
        except ConnectionResetError:
            logger.info(f"🔌 SSE客户端断开连接: {session_id}")
        except asyncio.CancelledError:
            logger.info(f"🛑 SSE连接被取消: {session_id}")
        except Exception as e:
            logger.error(f"❌ SSE处理错误: {str(e)}")
            logger.error(traceback.format_exc())
        finally:
            # 清理会话和订阅
            logger.info(f"🧹 清理SSE会话和订阅: {session_id}")
            try:
                # 尝试取消订阅
                if hasattr(self.streaming_tools, 'unsubscribe_from_session') and subscribers:
                    for callback in subscribers:
                        try:
                            logger.info(f"🔄 尝试取消SSE会话订阅: {session_id}")
                            await self.streaming_tools.unsubscribe_from_session(session_id, callback)
                            logger.info(f"✅ 成功取消SSE会话订阅: {session_id}")
                        except Exception as e:
                            logger.warning(f"⚠️ 取消SSE会话订阅失败: {str(e)}")
            except Exception as e:
                logger.error(f"❌ 清理SSE会话订阅失败: {str(e)}")
            
            return response
    
    async def start(self, host: str = "0.0.0.0", port: int = 8000):
        """启动服务器"""
        runner = web.AppRunner(self.app)
        await runner.setup()
        site = web.TCPSite(runner, host, port)
        await site.start()
        
        logger.info(f"MCP服务器已启动: http://{host}:{port}")
        
        # 保持运行
        while True:
            await asyncio.sleep(3600)
    
    async def stop(self):
        """停止服务器"""
        await self.app.shutdown()
        logger.info("MCP服务器已停止")
        
def import_time():
    """导入时间模块"""
    import time
    return time
    
def create_mcp_server(name: str = "streaming-mcp", 
                    description: str = "流式输出MCP服务器", 
                    version: str = "1.0.0") -> MCPServer:
    """创建MCP服务器实例"""
    return MCPServer(
        name=name,
        description=description,
        version=version
    ) 