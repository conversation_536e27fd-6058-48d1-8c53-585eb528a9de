# AI-HMI 场景库 (V2.1 - 分区融合增强版)

本文档汇集了 `prd.md` 和 `gemini.md` 中定义的核心场景，基于AI大模型的深度优化，旨在通过具体的剧本和原型图，展示AI座舱在不同情境下的智能交互能力。

---

## **核心优化指导思想**

在逐一分析场景前，我们先确立三个核心的升级方向，这将贯穿所有的修改建议：

1.  **从"状态感知"到"情绪共情"**: 不仅知道车里有谁、要去哪，更要通过多模态感知（语音、表情）理解用户的**情绪和潜在意图**，并作出有温度的反馈。
2.  **从"单步指令"到"多步推理规划"**: VPA不应只是一个任务触发器。它应该能**理解任务的最终目标**，综合多源信息（路况、日程、用户偏好），主动规划并提出更优的、多步骤的解决方案。
3.  **从"预设内容"到"个性化生成"**: 最大化利用大模型的生成能力。无论是对话、音乐推荐还是行程故事，都应是**为当前用户、在当前情境下动态生成的**，而非调用固定的库。

---

## **UI分区融合式布局 (Zoned Fusion Layout)**

为了在保证驾驶信息清晰无遮挡的前提下，提供富有沉浸感和整体感的视觉体验，HMI界面采用**分区融合式布局**。此布局将屏幕划分为若干个**功能固定的区域**，但区域之间的边界会进行**羽化或渐变融合**处理，使其在视觉上无缝连接。

### **核心功能分区**

1.  **主驾驶信息区 (Primary Driving Zone)**:
    *   **用途**: 这是屏幕上最大、最重要的区域，**固定用于显示导航地图**。此区域的内容不会被任何卡片覆盖，确保了核心驾驶信息的永久可见性。
    *   **形态**: 通常占据屏幕的大部分空间（例如右侧或中央区域）。

2.  **情景卡片区 (Contextual Card Zone)**:
    *   **用途**: 这是一个独立的区域，用于**动态堆叠和展示所有功能卡片**（如音乐、日程、天气、订单等）。卡片在此区域内出现、消失和滚动，但绝不会溢出到"主驾驶信息区"。
    *   **形态**: 通常位于屏幕的一侧（例如左侧），以列表或网格形式排列卡片。

3.  **顶层状态栏/灵动岛 (Top Status Bar / Dynamic Island)**:
    *   **用途**: 永久显示最关键的状态信息，如时间、驾驶模式、关键警报等。

### **"融合"设计理念**

"融合"体现在**区域边界的柔化处理**上。我们不会在导航区和卡片区之间画一条生硬的分割线，而是通过共享背景、光影过渡、羽化边缘等视觉手段，让两个功能独立的分区看起来像是自然生长在同一块画布上，从而实现结构清晰与视觉美感的统一。

---

## 场景一：用户早高峰通勤

*   **用户故事**: 作为一个需要送孩子上学的上班族，我希望VPA能像一个贴心的副驾，不仅能处理导航和日程，还能在途中安抚孩子，并主动帮我处理好通勤路上的早餐和咖啡问题，让一天从容开始。

*   **VPA决策逻辑**:
    *   **初始**: 检测到多名乘客，并通过多模态识别判断出其中有儿童（标签："毛毛"），时间为工作日早高峰。VPA判断当前为"家庭通勤"阶段。
    *   **中途**: 儿童乘客离车后，系统识别到只剩驾驶员一人，且日程表显示上午有重要会议。VPA判断场景切换为"专注通勤"阶段，并需要提供情绪安抚和效率服务。

*   **UI/UX 交互流程与UI布局 (分阶段)**

    *   **阶段A: 家庭出行模式 (送孩子去幼儿园)**
        *   **触发**: 用户和孩子"毛毛"进入车辆。
        *   **VPA/系统行为**: VPA识别到毛毛，主动问候并确认行程："Hello，主人和毛毛你好。按往常一样先送毛毛上学吧？"。**AI增强**：加入情绪感知，如果检测到毛毛情绪不高，则变为："毛毛早上好，今天看起来有点没精神哦，是没睡好吗？要不要听个笑话开心一下？"
        *   **AI生成式陪伴**: 当毛毛问"地球为什么是圆的"时，VPA不仅调用百科，而是用**儿童化的语言生成回答**："因为呀，有一个叫'万有引力'的大力士，它从地球的中心把所有东西都紧紧地抱住，抱得时间太久了，就把地球抱成了一个圆滚滚的胖子啦！"
        *   **ASCII 原型图 (分区融合版)**:
            ```
            +------------------------------------------------------------------------------------------+
            | [灵动岛: 前往: XX幼儿园, 预计: 15分钟 | 情绪检测: 开心 😊]                             |
            +------------------------------------------------------------------------------------------+
            |  情景卡片区 (左侧)                  | 主驾驶信息区 (右侧)                              |
            | +--------------------------------+ |                                                  |
            | | 儿童教育卡片 (8x9)             | |                                                  |
            | | (KidEducationCard)             | |        <-- 导航地图 -->                          |
            | |      (视频播放中...)           | |          (路线: 家 -> 幼儿园)                    |
            | +--------------------------------+ |                                                  |
            | | AI百科问答卡片 (8x4)           | |                                                  |
            | | (AIPediaCard)                  | |                                                  |
            | | "万有引力大力士..."            | |                                                  |
            | +--------------------------------+ |                                                  |
            |                                    | <------ [边界羽化融合效果] ------>                 |
            +------------------------------------------------------------------------------------------+
            |  [VPA数字人] (^.^) 正在观察毛毛的反应，准备下一个互动话题                                    |
            |             /)_(\|                                                                           |
            |              / \                                                                             |
            +------------------------------------------------------------------------------------------+
            ```

    *   **阶段B: 专注通勤模式 (独自前往公司)**
        *   **触发**: 导航提示"已到达幼儿园"，系统检测到儿童乘客离车。
        *   **VPA/系统行为**: **AI增强**：VPA不只是说"您上午有会议"，而是进行深度分析："毛毛再见！已为您切换到公司路线。检测到路况比平时拥堵，预计会比往常晚10分钟。而您10点的会议很重要，我已经分析过，如果先去常去的那家麦当劳，可能会迟到。**我建议直接去公司，并帮您在公司楼下的瑞幸预订了同款早餐，您到大堂时刚好能取。您看可以吗？**"
        *   **UI 变化**: 右侧**导航区**的路线自动更新为前往公司。左侧**卡片区**的内容发生变化，儿童相关卡片退场，取而代之的是与驾驶员工作通勤相关的卡片堆叠：音乐控制、日程助理和智能订单。灵动岛显示**优化后的方案**："新方案：直达公司，咖啡已预订"。
        *   **ASCII 原型图 (分区融合版)**:
            ```
            +------------------------------------------------------------------------------------------+
            | [灵动岛: 前往: 公司, 预计: 25分钟 | 智能方案: 直达+咖啡预订 🚀]                             |
            +------------------------------------------------------------------------------------------+
            |  情景卡片区 (左侧)                  | 主驾驶信息区 (右侧)                              |
            | +--------------------------------+ |                                                  |
            | | 音乐控制卡片 (8x6)             | |                                                  |
            | | (MusicControlCard)             | |        <-- 导航地图 -->                          |
            | |      (播放: 日落大道)          | |          (路线: 幼儿园 -> 公司)                  |
            | +--------------------------------+ |                                                  |
            | | AI日程助理卡片 (8x3)           | |                                                  |
            | | (AIScheduleAssistantCard)      | |                                                  |
            | | ⚠️ 10:00 重要会议               | |                                                  |
            | | 路况拥堵，已为您优化路线        | |                                                  |
            | +--------------------------------+ |                                                  |
            | | 智能订单卡片 (8x3)             | |                                                  |
            | | (AIOrderCard)                  | |                                                  |
            | | ✅ 瑞幸咖啡已预订 (09:35可取)   | |                                                  |
            | | 预计节省8分钟通勤时间           | |                                                  |
            | +--------------------------------+ |                                                  |
            |                                    | <------ [边界羽化融合效果] ------>                 |
            +------------------------------------------------------------------------------------------+
            |  [VPA数字人] (⚈_⚈) 正在专注分析路况和日程，为您提供最优方案                              |
            |          /)_(\|                                                                           |
            |           / \                                                                             |
            +------------------------------------------------------------------------------------------+
            ```

---

## 场景二：下班通勤

*   **用户故事**: 辛苦工作一天后，我希望我的VPA能帮我放松下来，并智能地处理回家路上的琐事，比如提醒我买菜，或者帮我提前打开家里的空调。

*   **VPA决策逻辑**:
    *   **触发条件**: 时间为下班高峰（18:30），目的地为"家"，驾驶数据显示用户可能处于疲劳状态。
    *   **AI增强决策**: 1. 营造放松氛围（切换壁纸、推荐音乐）；2. **升级为"智能膳食助理"**（通过健康App和冰箱数据分析，智能推荐营养补给）；3. 提供便利（展示智能家居控制面板，实现车家互联）。

*   **UI/UX 交互流程**: 
    *   **AI增强**: VPA对话升级为："辛苦一天了！为您推荐放松歌单。另外，我通过您的健康App和冰箱数据发现，您最近蛋白质摄入有点少，而且冰箱里的鸡蛋和鸡胸肉都不多了。**回家路上的'盒马'正好有新鲜的鸡胸肉和鸡蛋，我已经帮您加入了购物车，路过时需要帮您下单吗？**"

*   **UI布局**: 右侧**导航区**显示回家的路线，左侧**卡片区**显示智能膳食助理对话框和智能家居控制卡片。
*   **ASCII 原型图 (分区融合版)**:
    ```
    +------------------------------------------------------------------------------------------+
    | [灵动岛: <- 18:30 前往: 家, 路况良好 | 检测: 轻度疲劳 😴]                                   |
    +------------------------------------------------------------------------------------------+
    |  情景卡片区 (左侧)                  | 主驾驶信息区 (右侧)                              |
    | +--------------------------------+ |                                                  |
    | | (AI智能膳食助理对话框)            | |        <-- 导航地图 -->                          |
    | | 辛苦一天了！为您推荐放松歌单        | |          (路线: 公司 -> 家)                     |
    | | 检测到您蛋白质摄入不足...          | |          (傍晚城市街景)                         |
    | | 已为您加入盒马购物车：鸡胸肉+鸡蛋    | |                                                  |
    | |  [稍后再说] [立即下单] [查看健康报告] |                                                  |
    | +--------------------------------+ |                                                  |
    | | 音乐控制卡片 (4x2)               | |                                                  |
    | | 日落大道... [K||>]               | |                                                  |
    | +--------------------------------+ |                                                  |
    | | AI智能家居卡片 (8x4)             | |                                                  |
    | | [回家模式] [客厅空调:24°C]      | |                                                  |
    | | [空气净化器:自动] [扫地机:启动] | |                                                  |
    | +--------------------------------+ |                                                  |
    |                                    | <------ [边界羽化融合效果] ------>                 |
    +------------------------------------------------------------------------------------------+
    |  [VPA数字人] (😴) 用柔和的声音说："主人，今天辛苦了，我已经为您准备好放松的氛围"                |
    |            /)_(\|                                                                           |
    |             / \                                                                             |
    +------------------------------------------------------------------------------------------+
    ```

---

## 场景三：车内等待/摸鱼

*   **用户故事**: 在等人或者想在车里休息一下的时候，我希望能有一些娱乐选项，比如看会儿视频或者刷刷新闻，让等待的时间不那么无聊。

*   **VPA决策逻辑**:
    *   **触发条件**: 系统检测到车辆挂入P档（驻车）超过1分钟，且车内有乘客。
    *   **AI增强决策**: 判断用户进入"等待/休息"状态，需要提供娱乐和放松选项。**核心升级为"智能内容推荐"**，基于**跨终端的用户画像**进行个性化推荐。

*   **UI/UX 交互流程**: 
    *   **AI增强**: VPA主动推荐时，会基于**跨终端的用户画像**："主人，我看您昨晚在手机上收藏了一部关于'AI大模型'的纪录片，现在有20分钟空闲，要不要继续观看？" 或者 "您关注的财经博主刚刚发布了新的市场分析视频，需要为您播放吗？"
    *   **生成式内容消费**: 新闻摘要卡片可以由VPA**语音播报摘要**："为您摘要今天的三条要闻：第一，... 您对哪条感兴趣，我可以为您展开阅读或播放相关视频。"

*   **UI布局**: 由于车辆处于P档驻车状态，右侧**主驾驶信息区**暂时不显示导航，而是作为视频播放区域。左侧**卡片区**显示内容推荐和控制卡片。
*   **ASCII 原型图 (分区融合版)**:
    ```
    +------------------------------------------------------------------------------------------+
    | [P] <--- 档位挂入P档, 驻车系统已激活 | AI推荐: 基于您昨晚的观看记录 🎬                          |
    +------------------------------------------------------------------------------------------+
    |  情景卡片区 (左侧)                  | 主驾驶信息区 (右侧)                              |
    | +--------------------------------+ |                                                  |
    | | AI新闻摘要卡片 (4x2)             | |        <-- AI视频播放器 -->                      |
    | | (AINewsDigestCard)               | |          (16x6 视频区域)                         |
    | | 📱 为您语音摘要今日要闻 [▶️ 播放] | |          [ > AI大模型纪录片播放中 ]              |
    | | - AI大模型发布...                | |         (基于您手机收藏记录推荐)                 |
    | +--------------------------------+ |                                                  |
    | | 智能环境音卡片 (4x2)             | |                                                  |
    | | (SmartAmbientCard)               | |                                                  |
    | | [雨声] [森林] [冥想] [自定义]     | |                                                  |
    +--------------------------------+ |                                                  |
    |                                    | <------ [边界羽化融合效果] ------>                 |
    +------------------------------------------------------------------------------------------+
    | <-- VPA数字人 (🎬) 正在观看您的反应，随时准备推荐更感兴趣的内容 -->                             |
    |         \"这部纪录片的观点很有深度，您觉得呢？\"                                               |
    +------------------------------------------------------------------------------------------+
    ```

---

## 场景四：雨夜的归途 

*   **情景**: 用户深夜下班，外面下着雨，导航回家。用户显得有些疲惫。

*   **VPA决策逻辑**: 当前需要**专注导航**，但要营造一个**平静、温暖、有安全感**氛围来舒缓用户的疲劳。**AI增强**：将氛围感与**主动安全**深度结合。

*   **AI增强行为**: 
    *   **语音优化**: VPA的语音会变得更沉稳、语速更慢："外面雨很大，我已经帮您将驾驶模式调整为'湿滑路面'，增强了车身稳定系统。导航已为您避开所有已知的积水路段。您安心开，我陪着您。"
    *   **UI/UX 联动**: 背景的雨滴特效可以和**真实雨刷频率**进行联动，VPA数字人的形象可以披上一件虚拟的雨衣，增加情景代入感。

*   **ASCII 原型图 (分区融合版)**:
    ```
    +------------------------------------------------------------------------------------------+
    | [灵动岛: 回家 - 剩余15分钟 | 雨天模式 🌧️ | 路况: 湿滑]                                    |
    +------------------------------------------------------------------------------------------+
    |  情景卡片区 (左侧极简)            | 主驾驶信息区 (右侧最大化)                              |
    |                                   |                                                          |
    | +-------------------------------+ |                                                          |
    | | VPA数字人 (雨天模式)          | |        <-- 沉浸式雨夜导航地图 -->                      |
    | |  (⚈_⚈)                      | |        (雨夜增强导航模式)                             |
    | |  /)_(\| (披虚拟雨衣)          | |        🏠 目的地: 家 | 🕐 剩余: 15分钟               |
    | |   / \\  (语音沉稳)             | |        ⚠️ 前方500米积水，已规划绕行                   |
    | |  \"雨很大，已避开积水路段，    | |        🌧️ 湿滑路面模式已激活                          |
    | |   您安心开，我陪着您\"        | |        (路线高亮显示)                                 |
    | +-------------------------------+ |                                                          |
    | |                               | |                                                          |
    | | AI音乐控制卡片 (4x2)          | |                                                          |
    | | (雨夜的浪漫 - 雨天专用歌单)   | |                                                          |
    | | [专注模式] [增加雨声]         | |                                                          |
    | +-------------------------------+ |                                                          |
    |                                   | <------ [边界羽化融合效果] ------>                     |
    +------------------------------------------------------------------------------------------+
    ```

---

## 场景五：周末家庭出游

*   **用户故事**: 作为一位家长，在周末带家人出游时，我希望车辆能够帮助我安抚孩子，并有效管理行程，让全家人都能享受一个轻松愉快的旅程。

*   **VPA决策逻辑**:
    *   **触发条件**: 系统检测到周末早晨，目的地设置为公园等家庭友好地点，并通过车载摄像头或座椅传感器识别出车内有多名乘客（包括儿童）。
    *   **AI增强决策**: 判断为"家庭出游"场景，核心任务是服务后排儿童乘客和辅助驾驶员。**AI增强**：途中经过某个古迹时，VPA可以**主动生成一段简短有趣的历史故事**讲给孩子们听。

*   **UI/UX 交互流程**: 
    *   **AI增强**: VPA会根据GPS位置和兴趣点信息，自动识别教育机会："孩子们，我们马上要经过一座古老的桥梁哦！这座桥已经有300年的历史了，想听一个关于这座桥的有趣故事吗？"

*   **ASCII 原型图 (分区融合版)**:
    ```
    +------------------------------------------------------------------------------------------+
    | [灵动岛: 前往: 森林公园 - 预计45分钟 | AI发现: 前方1km有古桥 🌉]                             |
    +------------------------------------------------------------------------------------------+
    |  情景卡片区 (左侧)                  | 主驾驶信息区 (右侧)                              |
    | +--------------------------------+ |                                                  |
    | | VPA数字人 (故事讲述模式)        | |        <-- 导航地图 (路线高亮) -->              |
    | |  (^.^) 正在给孩子们讲故事        | |          (前往森林公园)                         |
    | |  /)_(\| \"孩子们，这座古桥有300年 | |          (AI发现前方1km有古桥)                   |
    | |   / \\  的历史了...\"             | |                                                  |
    | +--------------------------------+ |                                                  |
    | | AI后座娱乐控制 (4x2)            | |                                                  |
    | | [动画片] [AI故事]               | |                                                  |
    | +--------------------------------+ |                                                  |
    | | AI智能设施查找 (4x2)            | |                                                  |
    | | [查找儿童洗手间]                 | |                                                  |
    | | [推荐休息区]                     | |                                                  |
    | +--------------------------------+ |                                                  |
    | | AI行程助理卡片 (4x2)            | |                                                  |
    | | 🍪 零食提醒: 30分钟后            | |                                                  |
    | | 🚗 服务区推荐: 星巴克            | |                                                  |
    | +--------------------------------+ |                                                  |
    |                                    | <------ [边界羽化融合效果] ------>                 |
    +------------------------------------------------------------------------------------------+
    |  (后座屏幕独立显示) -->  +--------------------------------------------------------------------+
    |                         |                      AI故事时间：古桥的秘密...                          |
    |                         |         (VPA正在用儿童化语言讲述历史故事)                           |
    |                         +--------------------------------------------------------------------+
    +------------------------------------------------------------------------------------------+
    ```

---

## 场景六：长途高速驾驶 

*   **用户故事**: 在长途驾驶时，我希望车辆能帮助我保持警惕，管理驾驶状态，并及时找到必要的休息站，从而使旅程更安全、更轻松。

*   **VPA决策逻辑**:
    *   **触发条件**: 系统检测到车辆在高速公路上已连续行驶超过一小时，或驾驶员疲劳监测系统发出警报。
    *   **AI增强决策**: 判断为"长途驾驶"场景，核心任务是保障驾驶安全和提供便利信息。**AI增强**：推荐服务区时，可以**更个性化**："前方15公里的服务区有您常去的'星巴克'，而且充电桩当前空闲较多，建议在这里休息。"

*   **UI/UX 交互流程**: 
    
    *   **AI增强**: VPA会基于用户历史数据和实时情况提供个性化建议："根据您的驾驶习惯，建议在2小时后休息。我已经为您找到了沿途评价最高的服务区，有您喜欢的餐厅品牌。"
    
*   **ASCII 原型图 (AI增强版)**:
    ```
    +------------------------------------------------------------------------------------------+
    | [灵动岛: G2高速 - 距离下一出口 25km | 驾驶时长: 1h45min ⏱️]                                 |
    +------------------------------------------------------------------------------------------+
    |                          <-- 简化导航视图，突出前方路线 -->                                |
    |                                                                                          |
    | +-----------------------------+                                     +------------------+ |
    | | AI服务区推荐卡片 (4x2)      |                                     | VPA              | |
    | | (AIServiceAreaCard)        |                                     |         (^.^)    | |
    | | 推荐: 星巴克服务区 15km    |                                       |         /)_(\|    | |
    | | 充电桩空闲较多 ✅           |                                     |          / \     | |
    | +-----------------------------+                                     |                  | |
    | | AI驾驶员状态卡片 (4x2)      |                                     +------------------+ |
    | | (AIDriverStatusCard)        |                                                          |
    | | 状态: 良好 [建议2h后休息]   |                                                          |
    | | 基于您的驾驶习惯分析        |                                                          |
    | +-----------------------------+                                                          |
    | | AI车辆状态卡片 (4x2)        |                                                          |
    | | (AIVehicleStatusCard)       |                                                          |
    | | 续航里程: 350km [智能规划充电站]                                                     |
    | | 下次充电建议: 推荐服务区     |                                                          |
    | +-----------------------------+                                                          |
    |                                                                                          |
    +------------------------------------------------------------------------------------------+
    ```

---

## 场景七：访客/代驾模式  

*   **用户故事**: 当我把车交给朋友或代驾司机时，我希望能隐藏我的个人信息（如家庭住址、联系人、行程历史），同时为他们提供必要的驾驶功能，确保我的隐私安全。

*   **VPA决策逻辑**:
    *   **触发条件**: 用户通过设置菜单或语音指令（"嘿VPA，开启访客模式"）手动激活。
    *   **AI增强决策**: 判断进入"访客模式"，核心任务是保护用户隐私。**AI增强**：可以增加一个**"临时授权"**功能，车主可以通过手机App，临时将某个目的地或音乐播放列表授权给访客车辆，既方便了访客，又保护了隐私。

*   **UI/UX 交互流程**: 
    *   **AI增强**: VPA会说明临时授权功能："访客模式已激活。您可以通过手机App临时授权某些功能给访客，比如特定目的地或音乐播放列表。"

*   **ASCII 原型图 (AI增强版)**:
    ```
    +------------------------------------------------------------------------------------------+
    | [访客模式已激活 | 临时授权功能可用 🔐]                                                     |
    +------------------------------------------------------------------------------------------+
    |                            <-- 画布层: 中性/默认壁纸 -->                                   |
    |                                                                                          |
    |  +--------------------------------------------------------------------------------------+  |
    |  |                                AI临时导航输入模块 (16x2)                               |  |
    |  |     请输入临时目的地: [__________________] [ 前往 ] [请求授权]                      |  |
    |  +--------------------------------------------------------------------------------------+  |
    |                                                                                          |
    | +--------------------------+                                        +------------------+ |
    | | AI基础音乐卡片 (4x2)     |                                        | VPA (访客模式)   | |
    | | (AIBasicMusicCard)       |                                        |         (⚆_⚆)    | |
    | | [FM 97.4] [蓝牙音乐]     |                                        |         /)_(\|    | |
    | | [请求音乐授权]           |                                        |          / \     | |
    | +--------------------------+                                        |                  | |
    | | AI基础控制卡片 (4x2)     |                                        +------------------+ |
    | | (AIBasicControlCard)     |                                                             |
    | | [空调] [车窗] [座椅调节]  |                                                             |
    | | [请求高级功能授权]       |                                                             |
    | +--------------------------+                                                             |
    |                                                                                          |
    +------------------------------------------------------------------------------------------+
    ```
    **AI增强说明**: 桌面切换为通用的默认壁纸。所有个性化卡片被移除。新增**临时授权请求功能**，访客可以请求访问特定功能，车主通过手机App远程授权。灵动岛持续显示"访客模式"字样。

---

## 场景八：宠物模式

*   **用户故事**: 当我需要短暂地将我的宠物留在车内时（例如去便利店买东西），我希望车辆能保持一个对宠物友好和安全的温度，并向车外的路人明确展示我的宠物是安全的，车主很快就会回来。

*   **AI增强功能**: 屏幕上的宠物动画可以**模拟车内真实宠物的品种**（如果摄像头能识别），或者让主人自定义。VPA会模拟宠物的声音和行为模式，增加真实感。

*   **ASCII 原型图 (AI增强版)**:
    ```
    +------------------------------------------------------------------------------------------+
    |                                                                                          |
    |                                                                                          |
    |                                                                                          |
    |             +--------------------------------------------------------------+             |
    |             |                                                              |             |
    |             |                  我的主人很快就回来！                        |             |
    |             |                                                              |             |
    |             |              车内温度现在是 22°C，很舒适。                   |             |
    |             |                                                              |             |
    |             |        🐕 AI识别: 金毛犬 - 模拟"旺财"行为模式                |             |
    |             |        (正在模拟真实宠物的动作和声音) 🎵                   |             |
    |             |                                                              |             |
    |             +--------------------------------------------------------------+             |
    |                                                                                          |
    |                                                                                          |
    |                                                                                          |
    |                                                                                          |
    |                                                                                          |
    +------------------------------------------------------------------------------------------+
    | [宠物模式] [锁定车门] [空调: 22°C] [AI宠物模拟: 金毛犬] [手机App监控] 📱                      |
    +------------------------------------------------------------------------------------------+
    ```

*   **UI/UX 交互流程**:
    1.  **触发**: 用户在停车后，通过中控屏幕或手机App选择"宠物模式"。
    2.  **AI增强VPA行为**: 系统确认激活。VPA会说："宠物模式已启动！我已经识别到车里的金毛犬'旺财'，现在将模拟它的行为模式。车内温度保持在22°C，车门已锁定。您可以通过手机App随时查看旺财的情况。"
    3.  **AI增强UI变化**: 中控大屏会显示一个醒目的信息页面，告知车外路人宠物的情况。**AI增强**：系统会通过摄像头识别宠物品种，并模拟该宠物的典型行为模式。页面上会实时显示车内温度，并显示AI生成的宠物动画，该动画会模拟真实宠物的动作。同时，用户的手机App会收到通知，并可以远程监控车内温度、摄像头画面和宠物状态。

---

## 场景九：洗车模式 

*   **用户故事**: 当我准备进入自动洗车机时，我希望能一键完成所有必要的车辆准备工作，比如关闭车窗、折叠后视镜、关闭自动雨刷等，避免任何意外发生。

*   **AI增强功能**: 激活时，可以播放一段15秒的**"变形金刚"式的动画和音效**，增加趣味性。VPA会用机器人的声音进行播报，增加仪式感。

*   **ASCII 原型图 (AI增强版)**:
    ```
    +--------------------------------------------------------------------------+
    |                                                                          |
    |                                                                          |
    |        🤖 变形金刚式动画播放中 - "汽车人，变形！" 🎵                       |
    |     (车辆各个部件按照顺序进行变形，配合机械音效和灯光效果)                |
    |                                                                          |
    |             +------------------------------------------+                 |
    |             |                                          |                 |
    |             |            洗车模式 - 变形完成！          |                 |
    |             |                                          |                 |
    |             |      [✓] 车窗已关闭并锁定 🤖            |                 |
    |             |      [✓] 后视镜已折叠 ⚙️                |                 |
    |             |      [✓] 充电口已锁定 🔌                |                 |
    |             |      [✓] 自动雨刷已禁用 🛡️              |                 |
    |             |      [✓] 空调切换为内循环 💨             |                 |
    |             |                                          |                 |
    |             |       汽车人，准备就绪！可以洗车！      |                 |
    |             |                                          |                 |
    |             +------------------------------------------+                 |
    |                                                                          |
    |                                                                          |
    +--------------------------------------------------------------------------+
    | [触摸屏幕任意位置或踩下刹车以退出] | [🔊 重复播放变形动画]                      |
    +--------------------------------------------------------------------------+
    ```

*   **UI/UX 交互流程**:
    1.  **触发**: 用户通过车辆设置菜单或语音指令（"嘿VPA，打开洗车模式"）激活。
    2.  **AI增强VPA行为**: VPA会用变形金刚的声音确认："汽车人，变形！正在为您准备洗车模式，关闭车窗、折叠后视镜、禁用雨刷。变形完成！" 
    3.  **AI增强UI变化**: 中控屏幕首先播放15秒的变形金刚式动画，车辆各个部件按顺序变形，配合机械音效和灯光效果。然后显示准备完成清单，给用户明确的反馈和仪式感。屏幕触摸功能会被临时禁用，以防误触，并提示用户如何退出该模式。新增"重复播放变形动画"按钮。

---

## 场景十：浪漫二人世界 

*   **用户故事**: 在一个特别的纪念晚上，我希望能和伴侣在车里享受一个不被打扰的浪漫时刻，车辆能主动营造出温馨、私密的氛围。

*   **AI增强功能**: VPA可以**生成一段浪漫的话语**："根据日历，今天是你们的纪念日。愿这片星空和音乐，为你们留下又一个美好的回忆。" 音乐列表可以基于**两人共同喜欢的歌曲风格**来生成。

*   **ASCII 原型图 (AI增强版)**:
    ```
    +--------------------------------------------------------------------------+
    |                                                                          |
    |                                                                          |
    |           <-- 背景为动态的星空或壁炉火焰视频 -->                          |
    |      (AI分析两人共同喜好，选择最合适的浪漫氛围)                          |
    |                                                                          |
    |                                                                          |
    |                                                                          |
    |                                                                          |
    |                                                                          |
    |                                                                          |
    | +----------------------+ +---------------------------------------------+ |
    | | AI浪漫音乐推荐         | | AI氛围灯: [玫瑰粉] [烛光黄] [星空紫]        | |
    | | [ K || > ]            | | [亮度: 50%] [智能调节] [情侣模式]          | |
    | | 基于两人共同喜好生成   | +---------------------------------------------+ |
    | +----------------------+                                               |
    |                                                                          |
    | +------------------------------------------------------------------+     |
    | | 💕 AI浪漫话语: "今天是你们的结婚纪念日，愿这片星空见证你们的爱情！" |     |
    | +------------------------------------------------------------------+     |
    +--------------------------------------------------------------------------+
    ```

*   **UI/UX 交互流程**:
    1.  **触发**: 系统通过日历事件（如"结婚纪念日"）或用户语音指令（"嘿VPA，开启浪漫模式"）激活。
    2.  **AI增强VPA行为**: VPA会用温柔的声音说："根据日历，今天是你们的结婚纪念日，我已经为你们准备了最浪漫的氛围。音乐是根据你们两人都喜欢的小众爵士乐生成的，氛围灯会随着音乐节奏智能调节。愿这片星空和音乐，为你们留下又一个美好的回忆。"
    3.  **AI增强UI变化**: 中控屏幕背景切换为动态的星空或壁炉火焰等富有氛围感的视频。**AI增强**：系统会分析两人共同的音乐偏好，生成专属的浪漫歌单。氛围灯会智能地随音乐节奏变化。新增AI浪漫话语卡片，显示AI生成的浪漫话语。界面上只保留必要的音乐控制和氛围灯调节卡片，最大程度地减少信息干扰，让用户专注于享受二人世界。

---

## 场景十：智能充电场景

*   **用户故事**: 作为电动车车主，当我的车辆电量不足时，我希望VPA能智能地为我找到最合适的充电站，并在充电期间提供娱乐和便民服务，让充电时间变得有价值。

*   **VPA决策逻辑**:
    *   **触发条件**: 车辆电量低于30%，或用户主动搜索充电站，或系统根据行程预测充电需求。
    *   **AI增强决策**: 判断为"智能充电"场景，核心任务是优化充电体验和时间利用。**AI增强**：不仅推荐充电站，还会分析充电时间和周边服务，提供"充电+用餐"、"充电+购物"等组合方案。

*   **UI/UX 交互流程**: 
    *   **AI增强**: VPA会综合分析多个因素："根据您的行程和当前电量，建议在前方3公里的充电站充电。那里有您常去的星巴克，充电30分钟刚好够您喝杯咖啡。我已经帮您预订了充电桩和咖啡，到达时可直接使用。"
    *   **智能娱乐推荐**: 充电期间，VPA会根据充电时间推荐合适长度的内容："充电还需25分钟，为您推荐一集您在追的剧，时长刚好合适。"

*   **ASCII 原型图 (分区融合版)**:
    ```
    +------------------------------------------------------------------------------------------+
    | [灵动岛: 充电中 45% | 预计完成: 14:30 | AI方案: 充电+咖啡 ☕]                              |
    +------------------------------------------------------------------------------------------+
    |  情景卡片区 (左侧)                  | 主驾驶信息区 (右侧)                              |
    | +--------------------------------+ |                                                  |
    | | VPA数字人 (充电陪伴模式)        | |        <-- 充电站环境背景 -->                   |
    | |  (^.^) "充电期间，为您推荐      | |          (显示充电桩和周边设施)                 |
    | |  /)_(\| 一集您在追的剧，时长刚好" | |          🔌 充电桩状态: 连接中                   |
    | |   / \  合适25分钟"              | |          ⚡ 当前功率: 60kW                       |
    | +--------------------------------+ |                                                  |
    | | AI充电状态卡片 (8x4)            | |                                                  |
    | | (AIChargingStatusCard)          | |                                                  |
    | | 当前: 45% | 剩余: 25分钟         | |                                                  |
    | | 费用: ¥18.5 | 功率: 60kW         | |                                                  |
    | +--------------------------------+ |                                                  |
    | | AI娱乐推荐卡片 (8x3)            | |                                                  |
    | | (AIEntertainmentCard)           | |                                                  |
    | | [追剧25分钟] [音乐放松]         | |                                                  |
    | | [播客学习] [游戏娱乐]           | |                                                  |
    | +--------------------------------+ |                                                  |
    | | AI周边服务卡片 (8x3)            | |                                                  |
    | | (AINearbyServiceCard)           | |                                                  |
    | | ☕ 星巴克: 已预订 (步行2分钟)    | |                                                  |
    | | 🛒 超市: 步行3分钟               | |                                                  |
    | | 🍽️ 餐厅: 预约取餐               | |                                                  |
    | +--------------------------------+ |                                                  |
    |                                    | <------ [边界羽化融合效果] ------>                 |
    +------------------------------------------------------------------------------------------+
    ```

## 场景十一：疲劳驾驶检测与干预场景

*   **用户故事**: 作为长途驾驶者，当我出现疲劳状态时，我希望车辆能及时发现并温和地提醒我，同时提供实用的解决方案，确保我和乘客的安全。

*   **VPA决策逻辑**:
    *   **触发条件**: 系统通过多传感器检测到驾驶员疲劳信号（眼部状态、驾驶行为异常、连续驾驶时间过长等）。
    *   **AI增强决策**: 判断为"疲劳驾驶干预"场景，核心任务是保障驾驶安全。**AI增强**：不仅检测疲劳，还会分析疲劳程度和原因，提供个性化的恢复建议。

*   **UI/UX 交互流程**: 
    *   **AI增强**: VPA会温和但坚定地提醒："检测到您已连续驾驶2小时15分钟，眨眼频率和注意力都有所下降。前方5公里有您常去的服务区，有星巴克和全套休息设施。为了您和家人的安全，强烈建议现在休息20分钟。"
    *   **智能安全辅助**: 疲劳状态下，系统会自动增强ADAS功能，提高车道保持和碰撞预警的敏感度。

*   **ASCII 原型图 (分区融合版)**:
    ```
    +------------------------------------------------------------------------------------------+
    | [灵动岛: ⚠️ 疲劳驾驶预警 | 连续驾驶: 2h15min | 建议立即休息]                             |
    +------------------------------------------------------------------------------------------+
    |  情景卡片区 (左侧)                  | 主驾驶信息区 (右侧)                              |
    | +--------------------------------+ |                                                  |
    | | VPA数字人 (安全关怀模式)        | |        <-- 警示色背景渐变 -->                   |
    | |  (⚠️) "为了您和家人的安全，     | |          (橙红色渐变，引起注意)                 |
    | |  /)_(\| 强烈建议现在休息20分钟"  | |          🛣️ 前方5km: 推荐服务区                 |
    | |   / \  (语音温和但坚定)         | |          ⚠️ ADAS增强模式已激活                   |
    | +--------------------------------+ |                                                  |
    | | AI疲劳检测卡片 (8x4)            | |                                                  |
    | | (AIFatigueDetectionCard)        | |                                                  |
    | | 眨眼频率: 异常 ⚠️               | |                                                  |
    | | 注意力: 分散 ⚠️                 | |                                                  |
    | | 驾驶时长: 2h15min               | |                                                  |
    | | 疲劳等级: 中度                  | |                                                  |
    | +--------------------------------+ |                                                  |
    | | AI休息建议卡片 (8x3)            | |                                                  |
    | | (AIRestSuggestionCard)          | |                                                  |
    | | 🛣️ 星巴克服务区 (5km)           | |                                                  |
    | | ☕ 建议: 咖啡+洗脸+轻度运动      | |                                                  |
    | | ⏰ 推荐休息时长: 20分钟          | |                                                  |
    | +--------------------------------+ |                                                  |
    | | AI紧急联系卡片 (8x3)            | |                                                  |
    | | (AIEmergencyContactCard)        | |                                                  |
    | | 📞 [联系配偶] [联系朋友]        | |                                                  |
    | | 🚗 [一键呼叫代驾]               | |                                                  |
    | | 🆘 [紧急停车辅助]               | |                                                  |
    | +--------------------------------+ |                                                  |
    |                                    | <------ [边界羽化融合效果] ------>                 |
    +------------------------------------------------------------------------------------------+
    ```

## 场景十二：多用户识别与切换场景

*   **用户故事**: 作为家庭用车的主要使用者，当不同的家庭成员使用车辆时，我希望车辆能快速识别每个人的身份，并自动切换到他们的个人偏好设置，让每个人都能享受个性化的驾驶体验。

*   **VPA决策逻辑**:
    *   **触发条件**: 检测到新用户进入车辆（通过人脸识别、声纹识别、手机蓝牙等），或用户主动切换身份。
    *   **AI增强决策**: 判断为"多用户切换"场景，核心任务是提供个性化服务和隐私保护。**AI增强**：不仅识别身份，还会学习每个用户的使用习惯，主动优化个人设置。

*   **UI/UX 交互流程**: 
    *   **AI增强**: VPA会个性化问候："李女士，欢迎回来！已为您调整座椅到习惯位置，空调设为24°C，并准备播放您喜欢的轻音乐。检测到您今天的日程比较紧张，需要为您规划最快路线吗？"
    *   **智能权限管理**: 系统会根据用户角色自动分配功能权限，如儿童模式会限制某些功能，访客模式会隐藏个人信息。

*   **ASCII 原型图 (分区融合版)**:
    ```
    +------------------------------------------------------------------------------------------+
    | [灵动岛: 用户切换模式 | 检测到: 李女士 | 正在加载个人设置...]                             |
    +------------------------------------------------------------------------------------------+
    |  情景卡片区 (左侧)                  | 主驾驶信息区 (右侧)                              |
    | +--------------------------------+ |                                                  |
    | | VPA数字人 (个性化问候模式)      | |        <-- 中性背景主题 -->                     |
    | |  (^.^) "李女士，欢迎回来！      | |          (简洁的用户选择界面)                   |
    | |  /)_(\| 已为您调整个人设置"      | |                                                  |
    | |   / \  (语音温和亲切)           | |          👥 用户配置文件选择                     |
    | +--------------------------------+ |                                                  |
    | | AI用户识别卡片 (8x4)            | |          [👨 张先生] [👩 李女士] [👶 儿童模式]    |
    | | (AIUserRecognitionCard)         | |            主驾驶员    副驾驶员     后排乘客    |
    | | 识别方式: 人脸+声纹 ✅          | |            ○ 离线     ✅ 当前      ○ 未激活    |
    | | 置信度: 98%                     | |                                                  |
    | | 上次使用: 昨天 18:30            | |          [🎤 语音识别] [👤 访客模式]             |
    | +--------------------------------+ |                                                  |
    | | AI个人偏好卡片 (8x3)            | |                                                  |
    | | (AIPersonalPreferenceCard)      | |                                                  |
    | | 🎵 音乐: 轻音乐                 | |                                                  |
    | | 🌡️ 温度: 24°C                   | |                                                  |
    | | 💺 座椅: 位置2 (已调整)         | |                                                  |
    | | 🛣️ 路线: 避开拥堵               | |                                                  |
    | +--------------------------------+ |                                                  |
    | | AI隐私设置卡片 (8x3)            | |                                                  |
    | | (AIPrivacySettingsCard)         | |                                                  |
    | | 🔒 数据共享: 关闭               | |                                                  |
    | | 📍 位置记录: 仅导航             | |                                                  |
    | | 🎤 语音存储: 本地               | |                                                  |
    | | [详细隐私设置]                  | |                                                  |
    | +--------------------------------+ |                                                  |
    |                                    | <------ [边界羽化融合效果] ------>                 |
    +------------------------------------------------------------------------------------------+
    ```

## 场景十三：智能泊车辅助场景

*   **用户故事**: 作为城市驾驶者，当我到达目的地需要停车时，我希望车辆能帮我快速找到合适的车位，并协助我安全完成泊车，让停车不再是一件令人头疼的事情。

*   **VPA决策逻辑**:
    *   **触发条件**: 接近目的地或用户主动搜索停车位，系统检测到需要泊车。
    *   **AI增强决策**: 判断为"智能泊车"场景，核心任务是优化泊车体验和效率。**AI增强**：不仅找车位，还会综合考虑费用、距离、安全性等因素，提供最优停车方案。

*   **UI/UX 交互流程**: 
    *   **AI增强**: VPA会智能分析："已为您找到3个可用车位。推荐B2-15号位，虽然不是最近的，但费用最优惠，且有监控覆盖更安全。如果您赶时间，B1-08号位距离更近，但费用稍高。您的选择是？"
    *   **全程泊车辅助**: 从搜索车位到完成泊车，再到离车后的车辆管理，提供一站式服务。

*   **ASCII 原型图 (分区融合版)**:
    ```
    +------------------------------------------------------------------------------------------+
    | [灵动岛: 智能泊车 | 已找到3个车位 | AI推荐: B2-15 (性价比最高)]                           |
    +------------------------------------------------------------------------------------------+
    |  情景卡片区 (左侧)                  | 主驾驶信息区 (右侧)                              |
    | +--------------------------------+ |                                                  |
    | | VPA数字人 (泊车助手模式)        | |        <-- 停车场环境背景 -->                   |
    | |  (o.o) "推荐B2-15号位，        | |          (显示停车场布局和可用车位)             |
    | |  /)_(\| 性价比最高且更安全"      | |          🅿️ 停车场3D布局图                       |
    | |   / \  (专业建议语调)           | |          📍 推荐车位高亮显示                     |
    | +--------------------------------+ |                                                  |
    | | AI车位推荐卡片 (8x4)            | |                                                  |
    | | (AIParkingRecommendationCard)   | |                                                  |
    | | 🅿️ B2-15 (推荐) ⭐             | |                                                  |
    | | 距离: 50m | 宽度: 标准          | |                                                  |
    | | 费用: ¥8/2h | 安全: 有监控      | |                                                  |
    | | 🅿️ B1-08 (备选)               | |                                                  |
    | | 距离: 30m | 费用: ¥12/2h       | |                                                  |
    | +--------------------------------+ |                                                  |
    | | AI泊车辅助卡片 (8x3)            | |                                                  |
    | | (AIParkingAssistCard)           | |                                                  |
    | | 🚗 [自动泊车] [辅助泊车]        | |                                                  |
    | | 📹 环视影像: [前][后][左][右]   | |                                                  |
    | | 🎯 泊车精度: ±5cm               | |                                                  |
    | +--------------------------------+ |                                                  |
    | | AI费用优化卡片 (8x3)            | |                                                  |
    | | (AICostOptimizationCard)        | |                                                  |
    | | 💰 预计费用: ¥8 (2小时)         | |                                                  |
    | | 🎫 可用优惠: 新用户9折          | |                                                  |
    | | 📱 支付方式: 微信/支付宝        | |                                                  |
    | | [使用优惠券] [预付费]           | |                                                  |
    | +--------------------------------+ |                                                  |
    |                                    | <------ [边界羽化融合效果] ------>                 |
    +------------------------------------------------------------------------------------------+
    ```

## 场景十四：紧急情况处理场景

*   **用户故事**: 作为车辆使用者，当遇到紧急情况（事故、健康问题、车辆故障等）时，我希望车辆能立即识别情况的严重性，自动启动救援流程，并为我提供专业的应急指导，确保我和乘客的安全。

*   **VPA决策逻辑**:
    *   **触发条件**: 系统检测到碰撞、气囊弹出、驾驶员异常生理指标、车辆严重故障等紧急信号。
    *   **AI增强决策**: 判断为"紧急救援"场景，核心任务是保障生命安全和快速救援。**AI增强**：能够智能评估紧急情况的严重程度，自动选择最合适的救援方案。

*   **UI/UX 交互流程**: 
    *   **AI增强**: VPA会冷静而专业地处理："检测到严重碰撞，已自动拨打120和110。根据碰撞数据分析，建议您不要移动，保持现有姿势。救护车预计12分钟到达，我已将您的位置和车辆信息发送给救援中心。"
    *   **智能急救指导**: 根据检测到的伤情类型，提供相应的急救指导和安全建议。

*   **ASCII 原型图 (分区融合版)**:
    ```
    +------------------------------------------------------------------------------------------+
    | [灵动岛: 🚨 紧急情况 | 已自动报警 | 救援ETA: 12分钟]                                      |
    +------------------------------------------------------------------------------------------+
    |  情景卡片区 (左侧)                  | 主驾驶信息区 (右侧)                              |
    | +--------------------------------+ |                                                  |
    | | VPA数字人 (紧急救援模式)        | |        <-- 紧急红色背景 -->                     |
    | |  (🚨) "请保持冷静，不要移动，   | |          (闪烁的红色警示背景)                   |
    | |  /)_(\| 救援已在路上"            | |          🚨 紧急状态激活                         |
    | |   / \  (语音冷静专业)           | |          📍 精确位置已发送                       |
    | +--------------------------------+ |                                                  |
    | | AI紧急状态卡片 (8x4)            | |                                                  |
    | | (AIEmergencyStatusCard)         | |                                                  |
    | | 🚨 检测: 严重碰撞               | |                                                  |
    | | ⏰ 报警时间: 14:23:15           | |                                                  |
    | | 🚑 救护车: 12分钟到达           | |                                                  |
    | | 🚓 交警: 15分钟到达             | |                                                  |
    | +--------------------------------+ |                                                  |
    | | AI位置信息卡片 (8x3)            | |                                                  |
    | | (AILocationInfoCard)            | |                                                  |
    | | 📍 精确坐标: 116.3974,39.9093  | |                                                  |
    | | 🏢 地址: 朝阳区建国路88号       | |                                                  |
    | | 🛣️ 路段: 三环路内侧车道         | |                                                  |
    | | ✅ 位置已发送给救援中心         | |                                                  |
    | +--------------------------------+ |                                                  |
    | | AI急救指导卡片 (8x3)            | |                                                  |
    | | (AIFirstAidCard)                | |                                                  |
    | | 🏥 [检查意识] [检查呼吸]        | |                                                  |
    | | 🩹 [止血方法] [保持体温]        | |                                                  |
    | | ⚠️ 建议: 不要移动，等待救援     | |                                                  |
    | +--------------------------------+ |                                                  |
    | | AI紧急联系卡片 (8x3)            | |                                                  |
    | | (AIEmergencyContactCard)        | |                                                  |
    | | 📞 120: ✅已拨打 (14:23:15)     | |                                                  |
    | | 👨‍👩‍👧‍👦 家人: 正在联系中             | |                                                  |
    | | 🏥 保险: 已自动通知             | |                                                  |
    | | [手动拨打] [发送消息]           | |                                                  |
    | +--------------------------------+ |                                                  |
    |                                    | <------ [边界羽化融合效果] ------>                 |
    +------------------------------------------------------------------------------------------+
    ```

---

## 场景融合与智能切换

### 智能场景融合的概念

传统的场景切换是**离散的、排他的**，而AI大模型驱动的场景系统应该是**连续的、融合的**。一辆车在某一时刻可能同时处于多个场景的叠加状态，VPA需要综合所有信息作出最优决策。

### 场景融合示例

#### 示例1：下班通勤 + 雨天 + 低电量
```
+------------------------------------------------------------------------------------------+
| [灵动岛: 回家 | 雨天 🌧️ | 电量15% 🔋 | 智能方案: 充电+晚餐]                              |
+------------------------------------------------------------------------------------------+
|                        <-- 雨夜背景，充电站环境 -->                                   |
|                                                                                          |
|    +--------------------------------------------------+                                |
|    | 🌧️ 外面雨很大，路况湿滑，已启动雨天安全模式                                  |
|    | 🔋 电量不足，已为您规划最优充电方案                                           |
|    | 🍽️ 检测到您冰箱食材不足，充电站旁有您喜欢的餐厅                                |
|    |                                                                                  |
|    | 建议路线：先去充电站充电，然后在隔壁餐厅用餐，最后回家                          |
|    | [接受方案] [修改方案] [仅充电]                                                 |
|    +--------------------------------------------------+                                |
|                                                                                          |
| +------------------+ +-------------------+ +----------------------------+               |
| | ⚡ 充电信息        | | 🍽️ 餐厅预订         | | 🏠 家居预设                |               |
| | 距离: 3km        | | 充电站旁餐厅      | | [回家模式] [空调24°C]     |               |
| | 充电桩: 空闲     | | 已预订2人座位     | | [空气净化器] [热水器]     |               |
| | 预计: 30分钟     | | 菜系: 川菜         | |                            |               |
| +------------------+ +-------------------+ +----------------------------+               |
+------------------------------------------------------------------------------------------+
```

#### 示例2：长途驾驶 + 疲劳检测 + 多用户识别
```
+------------------------------------------------------------------------------------------+
| [灵动岛: ⚠️ 疲劳预警 | 驾驶员: 张先生 | 乘客: 李女士 | 建议休息]                       |
+------------------------------------------------------------------------------------------+
|  情景卡片区 (左侧)                  | 主驾驶信息区 (右侧)                              |
| +--------------------------------+ |                                                  |
| | VPA数字人 (疲劳关怀模式)        | |        <-- 警示色背景 -->                       |
| |  (⚠️) "张先生，检测到您已       | |          (突出安全信息)                         |
| |  /)_(\| 连续驾驶3小时，出现疲劳" | |          🛣️ 前方8km: 推荐服务区                 |
| |   / \  (语音关切提醒)           | |          👥 乘客状态: 李女士可接替               |
| +--------------------------------+ |                                                  |
| | AI疲劳检测卡片 (8x4)            | |                                                  |
| | (AIFatigueDetectionCard)        | |                                                  |
| | 😴 眨眼频率: 异常 ⚠️           | |                                                  |
| | 🧠 注意力: 分散 ⚠️             | |                                                  |
| | ⏰ 连续驾驶: 3小时              | |                                                  |
| | 💡 建议休息: 20分钟             | |                                                  |
| +--------------------------------+ |                                                  |
| | AI服务区推荐卡片 (8x3)          | |                                                  |
| | (AIServiceAreaCard)             | |                                                  |
| | ☕ 星巴克 + 全套设施            | |                                                  |
| | ⚡ 充电桩: 4个空闲              | |                                                  |
| | ⏰ 预计到达: 10分钟             | |                                                  |
| | [导航至服务区] [预约咖啡]       | |                                                  |
| +--------------------------------+ |                                                  |
| | AI乘客状态卡片 (8x3)            | |                                                  |
| | (AIPassengerStatusCard)         | |                                                  |
| | 👩 李女士: 状态良好             | |                                                  |
| | 🎵 音乐偏好: 轻音乐             | |                                                  |
| | 🚗 驾驶能力: 可接替             | |                                                  |
| | [切换驾驶员] [播放音乐]         | |                                                  |
| +--------------------------------+ |                                                  |
|                                    | <------ [边界羽化融合效果] ------>                 |
+------------------------------------------------------------------------------------------+
```

